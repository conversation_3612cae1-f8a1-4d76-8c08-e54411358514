# Danh sách các thành phần Security đã loại bỏ

Tài liệu này liệt kê các thành phần security đã được loại bỏ khỏi module future-core để triển khai sau trong module khác.

## Các file trong thư mục `infrastructure/security`

### Các filter và validator
- ApiAuthenticationFilter.java - Filter xác thực API
- ApiSecurityValidator.java - Validator bảo mật cho API
- BruteForceProtectionFilter.java - Filter bảo vệ chống tấn công brute force
- EnhancedCsrfProtection.java - Bảo vệ CSRF nâng cao
- JwtTokenFilter.java - Filter xác thực JWT token
- FileUploadSecurityValidator.java - Validator bảo mật cho upload file

### Các service
- BruteForceProtectionService.java - Service bảo vệ chống tấn công brute force
- SignatureService.java - Service xác thực chữ ký
- AnomalyDetectionService.java - Service phát hiện giao dịch bất thường
- TempFileSecurityManager.java - Quản lý bảo mật tệp tạm thời
- TransactionLimitService.java - Service giới hạn giao dịch
- TwoFactorAuthService.java - Service xác thực hai yếu tố

### Các provider
- JwtTokenProvider.java - Provider tạo và xác thực JWT token
- EnhancedJwtTokenProvider.java - Provider JWT token nâng cao

### Các utility
- DataMaskingUtil.java - Tiện ích che dấu dữ liệu nhạy cảm
- DatabaseDataEncryptor.java - Tiện ích mã hóa dữ liệu trong cơ sở dữ liệu
- DatabaseEncryptionUtil.java - Tiện ích mã hóa cơ sở dữ liệu
- LogSanitizer.java - Tiện ích làm sạch log
- SensitiveDataHandler.java - Xử lý dữ liệu nhạy cảm
- PasswordHashingUtil.java - Tiện ích băm mật khẩu
- PasswordSecurityManager.java - Quản lý bảo mật mật khẩu
- SensitiveDataVault.java - Kho lưu trữ dữ liệu nhạy cảm
- EncryptionService.java - Service mã hóa
- EncryptionUtil.java - Tiện ích mã hóa
- InputSanitizer.java - Tiện ích làm sạch đầu vào

### Các implementation
- UserDetailsServiceImpl.java - Implementation của UserDetailsService
- SessionSecurityManager.java - Quản lý bảo mật phiên
- SessionDataSanitizer.java - Làm sạch dữ liệu phiên

## Các file cấu hình

- SecurityConfig.java - Cấu hình bảo mật chung
- SecurityHeadersConfig.java - Cấu hình header bảo mật
- ApiSecurityConfig.java - Cấu hình bảo mật API
- CsrfSecurityConfig.java - Cấu hình bảo mật CSRF

## Cấu hình trong application.properties

```properties
# JWT Configuration
jwt.secret=${JWT_SECRET:}
jwt.expiration=${JWT_EXPIRATION:86400000}
jwt.header=${JWT_HEADER:Authorization}
jwt.prefix=${JWT_PREFIX:Bearer}
```

## Các dependency cần thiết

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
</dependency>
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-api</artifactId>
    <version>${jjwt.version}</version>
</dependency>
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-impl</artifactId>
    <version>${jjwt.version}</version>
    <scope>runtime</scope>
</dependency>
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-jackson</artifactId>
    <version>${jjwt.version}</version>
    <scope>runtime</scope>
</dependency>
```

## Hướng dẫn triển khai

Khi triển khai các thành phần security trong module khác, cần lưu ý:

1. Thêm các dependency cần thiết vào file pom.xml của module đó
2. Triển khai các thành phần theo thứ tự:
   - Các utility và helper class
   - Các service
   - Các provider
   - Các filter
   - Các cấu hình
3. Cấu hình các thuộc tính trong application.properties hoặc application.yml
4. Đảm bảo tích hợp với các thành phần khác của hệ thống

## Lưu ý bảo mật

- Sử dụng các thuật toán mã hóa mạnh (AES-256, RSA-2048)
- Sử dụng salt khi băm mật khẩu
- Xử lý dữ liệu nhạy cảm cẩn thận
- Cấu hình CORS và CSRF đúng cách
- Sử dụng HTTPS cho tất cả các kết nối
- Cấu hình header bảo mật (X-XSS-Protection, Content-Security-Policy, etc.)
- Triển khai rate limiting để chống tấn công DDoS
- Triển khai brute force protection
- Sử dụng JWT token với thời gian hết hạn ngắn
- Triển khai xác thực hai yếu tố khi cần thiết
