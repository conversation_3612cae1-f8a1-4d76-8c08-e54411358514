# Position WebSocket Implementation Guide

## Overview

This implementation adds WebSocket support for position updates similar to Binance's futures WebSocket streams, along with corrected calculation formulas for entry price, break even price, liquidation price, margin ratio, margin, and PNL (ROI%).

## Key Features Implemented

### 1. WebSocket Handlers
- **PositionHandler**: Interface for position WebSocket operations
- **WebSocketPositionHandler**: Implementation using STOMP messaging
- **PositionWebSocketController**: Controller for WebSocket endpoints

### 2. Enhanced Position Calculations (Binance-like)

#### Liquidation Price Formula (Corrected)
```java
// LONG Position
Liquidation Price = (Entry Price * Size - Initial Margin + Maintenance Margin) / Size

// SHORT Position  
Liquidation Price = (Entry Price * Size + Initial Margin - Maintenance Margin) / Size
```

#### Break Even Price Formula (Corrected)
```java
// LONG Position (includes opening + closing fees)
Break Even Price = Entry Price * (1 + 2 * Fee Rate)

// SHORT Position (includes opening + closing fees)
Break Even Price = Entry Price * (1 - 2 * Fee Rate)
```

#### Margin Ratio Formula (Binance Standard)
```java
Margin Ratio = (Maintenance Margin / Wallet Balance) * 100
Where Wallet Balance = Initial Margin + Unrealized PNL
```

### 3. Enhanced PositionDto Fields
```java
public class PositionDto {
    // Existing fields...
    
    // New Binance-like fields
    private BigDecimal entryPrice;      // Same as openPrice but for clarity
    private BigDecimal breakEvenPrice;  // Break even price
    private BigDecimal marginRatio;     // Margin ratio percentage
    private String status;              // OPEN, CLOSED, LIQUIDATED, etc.
}
```

### 4. WebSocket Endpoints

#### User-Specific Endpoints (Private)
- `/user/position/update/{symbol}` - Position updates for specific symbol
- `/user/position/closed/{symbol}` - Position closed notifications
- `/user/position/liquidated/{symbol}` - Position liquidation notifications

#### Public Endpoints
- `/topic/liquidation/{symbol}` - Public liquidation notifications
- `/topic/position/{symbol}/{memberId}` - General position updates

### 5. Real-time Updates

#### Scheduled Updates
- **Position Updates**: Every 5 seconds for all open positions
- **Liquidation Risk Check**: Every 1 second for margin call warnings
- **Heartbeat**: Every 30 seconds to maintain connections

#### Event-Driven Updates
- Position updates after trades
- Position close events
- Liquidation events
- Margin call warnings

## Usage Examples

### Frontend WebSocket Subscription

```javascript
// Subscribe to position updates for BTCUSDT
stompClient.subscribe('/user/position/update/BTCUSDT', function(message) {
    const position = JSON.parse(message.body);
    console.log('Position update:', position);
    
    // Available fields:
    // position.entryPrice
    // position.breakEvenPrice
    // position.liquidationPrice
    // position.marginRatio
    // position.unrealizedProfit
    // position.profitRatio
    // position.status
});

// Subscribe to all positions
stompClient.subscribe('/user/positions', function(message) {
    const positions = JSON.parse(message.body);
    console.log('All positions:', positions);
});

// Subscribe to public liquidations
stompClient.subscribe('/topic/liquidation/BTCUSDT', function(message) {
    const liquidation = JSON.parse(message.body);
    console.log('Liquidation event:', liquidation);
});
```

### Backend Event Publishing

```java
// Publish position update event
applicationEventPublisher.publishEvent(
    new PositionUpdatedEvent(position, "TRADE")
);

// Publish position close event
applicationEventPublisher.publishEvent(
    new PositionClosedEvent(position, closePrice, "USER_CLOSE")
);

// Publish liquidation event
applicationEventPublisher.publishEvent(
    new PositionLiquidatedEvent(position, liquidationPrice, "FORCED_LIQUIDATION")
);
```

## Integration Points

### 1. Position Service Integration
```java
@Autowired
private EnhancedPositionService enhancedPositionService;

// Update position with WebSocket notification
enhancedPositionService.updatePositionWithWebSocket(position, contract);

// Close position with WebSocket notification
enhancedPositionService.closePositionWithWebSocket(position, contract);

// Liquidate position with WebSocket notification
enhancedPositionService.liquidatePositionWithWebSocket(position, contract);
```

### 2. Margin Call Warnings
```java
// Automatic margin call warnings when margin ratio > 80%
enhancedPositionService.checkAndSendMarginCallWarning(position, contract);
```

## Configuration

### WebSocket Configuration
```java
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {
    
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        config.enableSimpleBroker("/topic", "/user");
        config.setApplicationDestinationPrefixes("/app");
        config.setUserDestinationPrefix("/user");
    }
    
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/contract-ws")
                .setAllowedOriginPatterns("*")
                .withSockJS();
    }
}
```

## Data Flow

1. **Position Change** → Event Published → Event Listener → Enhanced Position Service → WebSocket Handler → Client
2. **Scheduled Update** → Position Repository → Enhanced Position Service → WebSocket Handler → Client
3. **Margin Call Check** → Calculate Margin Ratio → Send Warning if > 80% → Client Alert

## Benefits

1. **Real-time Updates**: Instant position updates like Binance
2. **Accurate Calculations**: Corrected formulas matching Binance standards
3. **Risk Management**: Automatic margin call warnings
4. **Scalable Architecture**: Event-driven with scheduled fallbacks
5. **User Experience**: Private channels for sensitive data, public for general market info

## Error Handling

- All WebSocket operations have try-catch blocks
- Fallback to basic position data if enhanced calculation fails
- Retry mechanisms for database operations
- Graceful degradation if WebSocket connections fail

## Performance Considerations

- Scheduled updates run every 5 seconds (configurable)
- Event-driven updates for immediate response
- Efficient queries using repository patterns
- Caching for frequently accessed contract data
- Connection pooling for database operations

## Testing

Test WebSocket connections using STOMP client or browser WebSocket API:

```javascript
const socket = new SockJS('/contract-ws');
const stompClient = Stomp.over(socket);

stompClient.connect({}, function(frame) {
    console.log('Connected: ' + frame);
    
    // Subscribe to position updates
    stompClient.subscribe('/user/position/update/BTCUSDT', function(message) {
        console.log('Received:', JSON.parse(message.body));
    });
});
```

## Monitoring

- Log all position events with appropriate levels
- Track WebSocket connection count
- Monitor margin call warning frequency
- Alert on liquidation events
- Performance metrics for calculation times