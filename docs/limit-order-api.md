# Tài liệu API Đặt Lệnh Limit (Limit Order)

## Giới thiệu

Tài liệu này mô tả chi tiết về API đặt lệnh limit (limit order) trong hệ thống giao dịch hợp đồng tương lai. Lệnh limit là loại lệnh cho phép người dùng đặt lệnh mua hoặc bán với một giá cụ thể. Lệnh chỉ được khớp khi giá thị trường đạt đến hoặc tốt hơn giá đã chỉ định.

## Endpoint

### Đặt lệnh limit mới

```
POST /api/v1/trading/orders
```

## Request

### Headers

| Tên | Mô tả | Bắt buộc |
|-----|-------|----------|
| Content-Type | application/json | Có |
| Authorization | Bearer {token} | Có |

### Request Body

```json
{
  "memberId": 123,
  "symbol": "BTC-USDT",
  "direction": "BUY",
  "type": "LIMIT",
  "price": 50000.00,
  "volume": 0.1,
  "timeInForce": "GTC",
  "leverage": 10,
  "reduceOnly": false,
  "postOnly": false
}
```

### Tham số

| Tên | Kiểu | Mô tả | Bắt buộc | Giá trị mặc định |
|-----|------|-------|----------|-----------------|
| memberId | Long | ID của thành viên | Có | - |
| symbol | String | Symbol của hợp đồng (ví dụ: BTC-USDT) | Có | - |
| direction | String | Hướng của lệnh (BUY, SELL) | Có | - |
| type | String | Loại lệnh (LIMIT) | Có | - |
| price | BigDecimal | Giá đặt lệnh | Có | - |
| volume | BigDecimal | Khối lượng đặt lệnh | Có | - |
| timeInForce | String | Thời gian hiệu lực (GTC, IOC, FOK) | Không | GTC |
| leverage | BigDecimal | Đòn bẩy | Không | 1 |
| reduceOnly | Boolean | Chỉ giảm vị thế | Không | false |
| postOnly | Boolean | Chỉ đặt lệnh (không khớp ngay) | Không | false |

### Giải thích các tham số

- **memberId**: ID của thành viên đặt lệnh
- **symbol**: Symbol của hợp đồng, định dạng {coin}-{base}, ví dụ: BTC-USDT
- **direction**: Hướng của lệnh
  - BUY: Mua
  - SELL: Bán
- **type**: Loại lệnh, trong trường hợp này là LIMIT
- **price**: Giá đặt lệnh, phải lớn hơn 0
- **volume**: Khối lượng đặt lệnh, phải lớn hơn 0
- **timeInForce**: Thời gian hiệu lực của lệnh
  - GTC (Good Till Cancel): Lệnh có hiệu lực cho đến khi bị hủy
  - IOC (Immediate Or Cancel): Lệnh được khớp ngay lập tức hoặc bị hủy
  - FOK (Fill Or Kill): Lệnh được khớp toàn bộ ngay lập tức hoặc bị hủy
- **leverage**: Đòn bẩy, phải lớn hơn hoặc bằng 1
- **reduceOnly**: Nếu true, lệnh chỉ được sử dụng để giảm vị thế hiện tại
- **postOnly**: Nếu true, lệnh chỉ được thêm vào sổ lệnh, không được khớp ngay lập tức

## Response

### Thành công

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "orderId": "ORD202305010011",
    "success": true,
    "message": "Đặt lệnh thành công",
    "order": {
      "orderId": "ORD202305010011",
      "memberId": 123,
      "symbol": "BTC-USDT",
      "direction": "BUY",
      "type": "LIMIT",
      "price": 50000.00,
      "triggerPrice": null,
      "volume": 0.1,
      "dealVolume": 0,
      "dealMoney": 0,
      "fee": 0,
      "status": "NEW",
      "createTime": "2023-05-01T12:00:00",
      "completeTime": null,
      "timeInForce": "GTC",
      "expireTime": null,
      "leverage": 10,
      "reduceOnly": false,
      "callbackRate": null,
      "activationPrice": null,
      "postOnly": false,
      "cancelReason": null
    }
  },
  "timestamp": "2023-05-01T12:00:00"
}
```

### Thất bại

```json
{
  "code": 400,
  "message": "Bad Request",
  "data": {
    "orderId": null,
    "success": false,
    "message": "Đặt lệnh thất bại: Số dư không đủ",
    "order": null
  },
  "timestamp": "2023-05-01T12:00:00"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 200 | Thành công |
| 400 | Yêu cầu không hợp lệ (ví dụ: tham số không hợp lệ) |
| 401 | Không được xác thực |
| 403 | Không có quyền truy cập |
| 404 | Không tìm thấy tài nguyên |
| 409 | Xung đột (ví dụ: lệnh đã tồn tại) |
| 429 | Quá nhiều yêu cầu |
| 500 | Lỗi máy chủ nội bộ |

## Lưu ý

1. Khi đặt lệnh limit, hệ thống sẽ kiểm tra số dư trong ví của thành viên. Nếu số dư không đủ, lệnh sẽ bị từ chối.
2. Lệnh limit sẽ được thêm vào sổ lệnh và chờ khớp với lệnh đối ứng.
3. Nếu lệnh limit được khớp ngay lập tức (có lệnh đối ứng thỏa mãn điều kiện), trạng thái của lệnh sẽ là FILLED hoặc PARTIALLY_FILLED.
4. Nếu lệnh limit không được khớp ngay lập tức, trạng thái của lệnh sẽ là NEW.
5. Lệnh limit có thể được hủy bất kỳ lúc nào nếu chưa được khớp hoàn toàn.

## Ví dụ

### Đặt lệnh limit mua

```bash
curl -X POST "http://localhost:8080/api/v1/trading/orders" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "memberId": 123,
    "symbol": "BTC-USDT",
    "direction": "BUY",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "timeInForce": "GTC",
    "leverage": 10,
    "reduceOnly": false,
    "postOnly": false
  }'
```

### Đặt lệnh limit bán

```bash
curl -X POST "http://localhost:8080/api/v1/trading/orders" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "memberId": 123,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "LIMIT",
    "price": 55000.00,
    "volume": 0.1,
    "timeInForce": "GTC",
    "leverage": 10,
    "reduceOnly": false,
    "postOnly": false
  }'
```
