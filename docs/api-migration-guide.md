# Hướng dẫn di chuyển API

Tài liệu này mô tả việc di chuyển từ các API `/matching` c<PERSON> sang các API mới trong hệ thống giao dịch hợp đồng tương lai.

## Loại bỏ future-api

Module future-api đã được loại bỏ khỏi luồng làm việc. Tất cả các API đã được chuyển sang future-core để đơn giản hóa kiến trúc và tránh sự phức tạp không cần thiết. Việc này giúp tập trung toàn bộ logic nghiệp vụ và API vào một module duy nhất, gi<PERSON><PERSON> dễ dàng bảo trì và mở rộng.

### Cấu trúc API trong future-core

Các API trong future-core được tổ chức như sau:

1. **Controller**: <PERSON><PERSON> lý các request HTTP và trả về response
   - `TradingController`: <PERSON><PERSON><PERSON><PERSON> lý lệnh giao dịch
   - `PriceController`: <PERSON><PERSON><PERSON><PERSON> lý giá đ<PERSON> dấu, g<PERSON><PERSON> chỉ số, giá giao dịch cuối cùng
   - `TradingManagementController`: Quản lý giao dịch (tạm dừng/tiếp tục giao dịch, kiểm tra lệnh chờ, thanh lý vị thế, v.v.)
   - `WebSocketController`: Quản lý kết nối WebSocket

2. **Request**: Định nghĩa cấu trúc dữ liệu đầu vào
   - `PlaceOrderRequest`: Request cho việc đặt lệnh
   - `UpdateOrderRequest`: Request cho việc cập nhật lệnh

3. **Validator**: Xác thực dữ liệu đầu vào
   - `ApiInputValidator`: Validator cho các input từ API

## Tổng quan

Các API `/matching` cũ đã được loại bỏ để tránh nhầm lẫn khi sử dụng. Thay vào đó, các API mới đã được triển khai trong các controller sau:

1. `TradingController`: Quản lý lệnh giao dịch
2. `PriceController`: Quản lý giá đánh dấu, giá chỉ số, giá giao dịch cuối cùng
3. `TradingManagementController`: Quản lý giao dịch (tạm dừng/tiếp tục giao dịch, kiểm tra lệnh chờ, thanh lý vị thế, v.v.)

> **Lưu ý**:
> - Các API quản lý sổ lệnh đã được loại bỏ vì đã có WebSocket để lấy dữ liệu sổ lệnh nhanh hơn. Vui lòng sử dụng WebSocket thay thế.
> - Tất cả các API quản lý lệnh đều được triển khai trong `TradingController` với đường dẫn `/api/v1/trading/orders/...`.

## Bảng ánh xạ API

### Quản lý lệnh

| API cũ | API mới | Mô tả |
|--------|---------|-------|
| `POST /api/v1/order-matching/place-order` | `POST /api/v1/trading/orders` | Đặt lệnh mới |
| `POST /api/v1/order-matching/cancel-order/{orderId}` | `DELETE /api/v1/trading/orders/{orderId}` | Hủy lệnh |
| `POST /api/v1/order-matching/cancel-all-orders/{memberId}` | `DELETE /api/v1/trading/orders/member/{memberId}/symbol/{symbol}` | Hủy tất cả lệnh của một thành viên cho một symbol |
| `POST /api/v1/trading/matching/place-order` | `POST /api/v1/trading/orders` | Đặt lệnh mới |
| `POST /api/v1/trading/matching/cancel-order/{orderId}` | `DELETE /api/v1/trading/orders/{orderId}` | Hủy lệnh |
| `POST /api/v1/trading/matching/cancel-all-orders/{memberId}` | `DELETE /api/v1/trading/orders/member/{memberId}/symbol/{symbol}` | Hủy tất cả lệnh của một thành viên cho một symbol |
| N/A | `GET /api/v1/trading/orders/{orderId}` | Lấy thông tin lệnh |
| N/A | `GET /api/v1/trading/orders/member/{memberId}/symbol/{symbol}` | Lấy danh sách lệnh |
| N/A | `GET /api/v1/trading/orders/member/{memberId}/symbol/{symbol}/active` | Lấy danh sách lệnh đang hoạt động |

### Quản lý giá

| API cũ | API mới | Mô tả |
|--------|---------|-------|
| `GET /api/v1/order-matching/mark-price/{symbol}` | `GET /api/v1/prices/mark-price/{symbol}` | Lấy giá đánh dấu |
| `GET /api/v1/order-matching/index-price/{symbol}` | `GET /api/v1/prices/index-price/{symbol}` | Lấy giá chỉ số |
| `GET /api/v1/order-matching/last-price/{symbol}` | `GET /api/v1/prices/last-price/{symbol}` | Lấy giá giao dịch cuối cùng |
| `POST /api/v1/order-matching/update-mark-price` | `POST /api/v1/prices/update-mark-price` | Cập nhật giá đánh dấu |
| `GET /api/v1/trading/matching/mark-price/{symbol}` | `GET /api/v1/prices/mark-price/{symbol}` | Lấy giá đánh dấu |
| `GET /api/v1/trading/matching/index-price/{symbol}` | `GET /api/v1/prices/index-price/{symbol}` | Lấy giá chỉ số |
| `GET /api/v1/trading/matching/last-price/{symbol}` | `GET /api/v1/prices/last-price/{symbol}` | Lấy giá giao dịch cuối cùng |
| `POST /api/v1/trading/matching/update-mark-price` | `POST /api/v1/prices/update-mark-price` | Cập nhật giá đánh dấu |

### Quản lý sổ lệnh

Các API REST cho quản lý sổ lệnh đã được loại bỏ vì đã có WebSocket để lấy dữ liệu sổ lệnh nhanh hơn. Vui lòng sử dụng WebSocket thay thế.

| API cũ | WebSocket mới | Mô tả |
|--------|--------------|-------|
| `GET /api/v1/order-matching/order-book/{symbol}` | `/topic/market/orderbook/{symbol}` | Lấy sổ lệnh |
| `GET /api/v1/order-matching/order-book/{symbol}/depth/{depth}` | `/topic/market/trade-depth/{symbol}` | Lấy sổ lệnh theo độ sâu |
| `GET /api/v1/trading/matching/order-book/{symbol}` | `/topic/market/orderbook/{symbol}` | Lấy sổ lệnh |
| `GET /api/v1/trading/matching/order-book/{symbol}/depth/{depth}` | `/topic/market/trade-depth/{symbol}` | Lấy sổ lệnh theo độ sâu |

#### Hướng dẫn sử dụng WebSocket

1. Kết nối đến WebSocket endpoint:
   ```javascript
   const socket = new SockJS('/contract-ws');
   const stompClient = Stomp.over(socket);
   stompClient.connect({}, function(frame) {
       console.log('Connected: ' + frame);
   });
   ```

2. Đăng ký nhận cập nhật sổ lệnh:
   ```javascript
   stompClient.subscribe('/topic/market/orderbook/BTC-USDT', function(response) {
       const orderBook = JSON.parse(response.body);
       console.log('Received order book:', orderBook);
   });
   ```

3. Đăng ký nhận cập nhật sổ lệnh theo độ sâu:
   ```javascript
   stompClient.subscribe('/topic/market/trade-depth/BTC-USDT', function(response) {
       const orderBookDepth = JSON.parse(response.body);
       console.log('Received order book depth:', orderBookDepth);
   });
   ```

> **Lưu ý**: Sổ lệnh được cập nhật tự động mỗi giây và khi có thay đổi trong matching engine.

### Quản lý giao dịch

| API cũ | API mới | Mô tả |
|--------|---------|-------|
| `POST /api/v1/trading/matching/pause-trading/{symbol}` | `POST /api/v1/trading-management/pause-trading/{symbol}` | Tạm dừng giao dịch |
| `POST /api/v1/trading/matching/resume-trading/{symbol}` | `POST /api/v1/trading-management/resume-trading/{symbol}` | Tiếp tục giao dịch |
| `POST /api/v1/trading/matching/check-trigger-orders/{symbol}` | `POST /api/v1/trading-management/check-trigger-orders/{symbol}` | Kiểm tra lệnh chờ |
| `POST /api/v1/trading/matching/check-liquidations/{symbol}` | `POST /api/v1/trading-management/check-liquidations/{symbol}` | Kiểm tra thanh lý |
| `POST /api/v1/trading/matching/liquidate-position` | `POST /api/v1/trading-management/liquidate-position` | Thanh lý vị thế |
| `POST /api/v1/trading/matching/synchronize-with-contracts` | `POST /api/v1/trading-management/synchronize-with-contracts` | Đồng bộ hóa matching engine với hợp đồng |

> **Lưu ý**: Các API quản lý giao dịch đã được triển khai trong `TradingManagementController` mới.

## Ví dụ

### Đặt lệnh mới

**API cũ:**
```http
POST /api/v1/order-matching/place-order
Content-Type: application/json

{
  "orderId": "ORD123456",
  "memberId": 123,
  "symbol": "BTC-USDT",
  "direction": "BUY",
  "type": "LIMIT",
  "price": 50000,
  "volume": 0.1
}
```

**API mới:**
```http
POST /api/v1/trading/orders
Content-Type: application/json

{
  "memberId": 123,
  "symbol": "BTC-USDT",
  "direction": "BUY",
  "type": "LIMIT",
  "price": 50000,
  "volume": 0.1
}
```

### Hủy lệnh

**API cũ:**
```http
POST /api/v1/order-matching/cancel-order/ORD123456?symbol=BTC-USDT
```

**API mới:**
```http
DELETE /api/v1/trading/orders/ORD123456?memberId=123&symbol=BTC-USDT
```

### Lấy danh sách lệnh đang hoạt động

**API mới:**
```http
GET /api/v1/trading/orders/member/123/symbol/BTC-USDT/active
```

## Triển khai API mới trong future-core

Tất cả các API mới đã được triển khai trong future-core với các controller sau:

1. `TradingController`: Triển khai các API quản lý lệnh
   - `POST /api/v1/trading/orders`: Đặt lệnh mới
   - `DELETE /api/v1/trading/orders/{orderId}`: Hủy lệnh
   - `DELETE /api/v1/trading/orders/member/{memberId}/symbol/{symbol}`: Hủy tất cả lệnh của một thành viên cho một symbol
   - `GET /api/v1/trading/orders/{orderId}`: Lấy thông tin lệnh
   - `GET /api/v1/trading/orders/member/{memberId}/symbol/{symbol}`: Lấy danh sách lệnh
   - `GET /api/v1/trading/orders/member/{memberId}/symbol/{symbol}/active`: Lấy danh sách lệnh đang hoạt động

2. `PriceController`: Triển khai các API quản lý giá
   - `GET /api/v1/prices/mark-price/{symbol}`: Lấy giá đánh dấu
   - `GET /api/v1/prices/index-price/{symbol}`: Lấy giá chỉ số
   - `GET /api/v1/prices/last-price/{symbol}`: Lấy giá giao dịch cuối cùng
   - `POST /api/v1/prices/update-mark-price`: Cập nhật giá đánh dấu

3. `WebSocketController`: Triển khai các endpoint WebSocket cho sổ lệnh
   - `/topic/market/orderbook/{symbol}`: Kênh WebSocket cho sổ lệnh
   - `/topic/market/trade-plate/{symbol}`: Kênh WebSocket cho sổ lệnh dạng plate
   - `/topic/market/trade-depth/{symbol}`: Kênh WebSocket cho sổ lệnh dạng depth

4. `TradingManagementController`: Triển khai các API quản lý giao dịch
   - `POST /api/v1/trading-management/pause-trading/{symbol}`: Tạm dừng giao dịch
   - `POST /api/v1/trading-management/resume-trading/{symbol}`: Tiếp tục giao dịch
   - `POST /api/v1/trading-management/check-trigger-orders/{symbol}`: Kiểm tra lệnh chờ
   - `POST /api/v1/trading-management/check-liquidations/{symbol}`: Kiểm tra thanh lý
   - `POST /api/v1/trading-management/liquidate-position`: Thanh lý vị thế
   - `POST /api/v1/trading-management/synchronize-with-contracts`: Đồng bộ hóa matching engine với hợp đồng

## Lưu ý quan trọng

1. Các API `/matching` cũ đã bị loại bỏ và sẽ không còn hoạt động.
2. Module future-api đã được loại bỏ khỏi luồng làm việc, tất cả các API đã được chuyển sang future-core.
3. Vui lòng cập nhật tất cả các ứng dụng khách để sử dụng các API mới.
4. API mới tuân theo các nguyên tắc RESTful và cung cấp trải nghiệm nhất quán hơn.
5. API mới bao gồm xác thực đầu vào và xử lý lỗi tốt hơn.
6. Tất cả các API quản lý lệnh đều được triển khai trong `TradingController` với đường dẫn `/api/v1/trading/orders/...`.
7. Tất cả các API mới đều có xử lý fallback để tăng tính sẵn sàng và khả năng chịu lỗi.
