# Đề xuất triển khai tính năng Take Profit và Stop Loss

## 1. Tổng quan

Tài liệu này đề xuất phương án triển khai tính năng Take Profit (TP) và Stop Loss (SL) trong hệ thống giao dịch hợp đồng tương lai. Tính năng này cho phép người dùng cài đặt lệnh chốt lời và dừng lỗ tự động, gi<PERSON><PERSON> quản lý rủi ro và tối ưu hóa lợi nhuận.

## 2. Hiện trạng

Hiện tại, hệ thống đã hỗ trợ các loại lệnh trigger như TAKE_PROFIT, TAKE_PROFIT_MARKET, STOP_LOSS, và STOP_MARKET thông qua API `/api/v1/trading/orders`. <PERSON><PERSON>, hệ thống chưa hỗ trợ:

1. Cài đặt TP/SL cùng lúc khi đặt lệnh thị trường
2. API riêng biệt để cài đặt TP/SL cho vị thế đã mở

Trong tài liệu API (`api-documentation.md`), đã có mô tả về các API `/api/v1/positions/{id}/take-profit` và `/api/v1/positions/{id}/stop-loss`, nhưng chưa được triển khai trong mã nguồn.

## 3. Yêu cầu

### 3.1. Yêu cầu chức năng

1. **Cài đặt TP/SL khi đặt lệnh thị trường**:
   - Người dùng có thể cài đặt TP/SL cùng lúc khi đặt lệnh thị trường
   - Hỗ trợ cài đặt TP/SL theo giá tuyệt đối hoặc theo phần trăm so với giá mở vị thế

2. **Cài đặt TP/SL sau khi đã mở vị thế**:
   - Người dùng có thể cài đặt TP/SL cho vị thế đã mở
   - Người dùng có thể cập nhật hoặc hủy TP/SL đã cài đặt

3. **Quản lý TP/SL**:
   - Hệ thống tự động theo dõi và kích hoạt lệnh TP/SL khi giá đạt đến mức kích hoạt
   - Hỗ trợ nhiều loại giá tham chiếu: giá giao dịch gần nhất, giá đánh dấu, giá chỉ số

### 3.2. Yêu cầu phi chức năng

1. **Hiệu suất**:
   - Thời gian phản hồi khi đặt lệnh TP/SL < 100ms
   - Thời gian kiểm tra và kích hoạt lệnh TP/SL < 50ms

2. **Độ tin cậy**:
   - Đảm bảo lệnh TP/SL được kích hoạt chính xác khi giá đạt đến mức kích hoạt
   - Xử lý đúng đắn trong trường hợp giá biến động mạnh (price gap)

3. **Khả năng mở rộng**:
   - Hỗ trợ nhiều loại lệnh TP/SL khác nhau trong tương lai
   - Dễ dàng tích hợp với các tính năng khác như trailing stop

## 4. Thiết kế

### 4.1. Mở rộng PlaceOrderRequest

Mở rộng `PlaceOrderRequest` để hỗ trợ cài đặt TP/SL khi đặt lệnh thị trường:

```java
public class PlaceOrderRequest {
    // Các trường hiện có
    
    /**
     * Cài đặt Take Profit
     */
    @Schema(description = "Cài đặt Take Profit")
    private TakeProfitSettings takeProfitSettings;
    
    /**
     * Cài đặt Stop Loss
     */
    @Schema(description = "Cài đặt Stop Loss")
    private StopLossSettings stopLossSettings;
}

public class TakeProfitSettings {
    /**
     * Giá kích hoạt Take Profit
     */
    @Schema(description = "Giá kích hoạt Take Profit", example = "52000.00")
    private BigDecimal triggerPrice;
    
    /**
     * Phần trăm lợi nhuận để kích hoạt Take Profit
     */
    @Schema(description = "Phần trăm lợi nhuận để kích hoạt Take Profit", example = "5.0")
    private BigDecimal profitPercentage;
    
    /**
     * Loại lệnh khi kích hoạt (MARKET hoặc LIMIT)
     */
    @Schema(description = "Loại lệnh khi kích hoạt", example = "MARKET")
    private String orderType;
    
    /**
     * Giá đặt lệnh (chỉ áp dụng khi orderType là LIMIT)
     */
    @Schema(description = "Giá đặt lệnh", example = "51500.00")
    private BigDecimal price;
}

public class StopLossSettings {
    /**
     * Giá kích hoạt Stop Loss
     */
    @Schema(description = "Giá kích hoạt Stop Loss", example = "48000.00")
    private BigDecimal triggerPrice;
    
    /**
     * Phần trăm lỗ để kích hoạt Stop Loss
     */
    @Schema(description = "Phần trăm lỗ để kích hoạt Stop Loss", example = "5.0")
    private BigDecimal lossPercentage;
    
    /**
     * Loại lệnh khi kích hoạt (MARKET hoặc LIMIT)
     */
    @Schema(description = "Loại lệnh khi kích hoạt", example = "MARKET")
    private String orderType;
    
    /**
     * Giá đặt lệnh (chỉ áp dụng khi orderType là LIMIT)
     */
    @Schema(description = "Giá đặt lệnh", example = "48500.00")
    private BigDecimal price;
}
```

### 4.2. Triển khai API cài đặt TP/SL cho vị thế

#### 4.2.1. Controller

```java
@RestController
@RequestMapping("/api/v1/positions")
public class PositionController {
    // Các phương thức hiện có
    
    /**
     * Đặt lệnh Take Profit cho vị thế
     */
    @PostMapping("/{id}/take-profit")
    public ResponseEntity<ApiResponse<OrderDto>> setTakeProfit(
            @PathVariable Long id,
            @Valid @RequestBody TakeProfitRequest request) {
        // Triển khai
    }
    
    /**
     * Đặt lệnh Stop Loss cho vị thế
     */
    @PostMapping("/{id}/stop-loss")
    public ResponseEntity<ApiResponse<OrderDto>> setStopLoss(
            @PathVariable Long id,
            @Valid @RequestBody StopLossRequest request) {
        // Triển khai
    }
    
    /**
     * Hủy lệnh Take Profit
     */
    @DeleteMapping("/{id}/take-profit")
    public ResponseEntity<ApiResponse<Boolean>> cancelTakeProfit(@PathVariable Long id) {
        // Triển khai
    }
    
    /**
     * Hủy lệnh Stop Loss
     */
    @DeleteMapping("/{id}/stop-loss")
    public ResponseEntity<ApiResponse<Boolean>> cancelStopLoss(@PathVariable Long id) {
        // Triển khai
    }
}
```

#### 4.2.2. Service

```java
public interface PositionOrderService {
    // Các phương thức hiện có
    
    /**
     * Đặt lệnh Take Profit cho vị thế
     */
    OrderDto setTakeProfit(Long positionId, TakeProfitRequest request);
    
    /**
     * Đặt lệnh Stop Loss cho vị thế
     */
    OrderDto setStopLoss(Long positionId, StopLossRequest request);
    
    /**
     * Hủy lệnh Take Profit
     */
    boolean cancelTakeProfit(Long positionId);
    
    /**
     * Hủy lệnh Stop Loss
     */
    boolean cancelStopLoss(Long positionId);
}
```

### 4.3. Xử lý TP/SL khi đặt lệnh thị trường

Mở rộng `PlaceOrderService` để xử lý TP/SL khi đặt lệnh thị trường:

```java
public class PlaceOrderService {
    // Các phương thức hiện có
    
    /**
     * Xử lý TP/SL sau khi đặt lệnh thị trường
     */
    private void processTakeProfitStopLoss(Order order, PlaceOrderRequest request) {
        // Nếu có cài đặt TP
        if (request.getTakeProfitSettings() != null) {
            // Tạo lệnh TP
            createTakeProfitOrder(order, request.getTakeProfitSettings());
        }
        
        // Nếu có cài đặt SL
        if (request.getStopLossSettings() != null) {
            // Tạo lệnh SL
            createStopLossOrder(order, request.getStopLossSettings());
        }
    }
    
    /**
     * Tạo lệnh Take Profit
     */
    private Order createTakeProfitOrder(Order originalOrder, TakeProfitSettings settings) {
        // Triển khai
    }
    
    /**
     * Tạo lệnh Stop Loss
     */
    private Order createStopLossOrder(Order originalOrder, StopLossSettings settings) {
        // Triển khai
    }
}
```

## 5. Kế hoạch triển khai

### 5.1. Giai đoạn 1: Cài đặt TP/SL sau khi đã mở vị thế

1. Triển khai API `/api/v1/positions/{id}/take-profit` và `/api/v1/positions/{id}/stop-loss`
2. Triển khai các service và repository liên quan
3. Cập nhật tài liệu API
4. Viết unit test và integration test
5. Triển khai lên môi trường test

### 5.2. Giai đoạn 2: Cài đặt TP/SL khi đặt lệnh thị trường

1. Mở rộng `PlaceOrderRequest` để hỗ trợ cài đặt TP/SL
2. Triển khai xử lý TP/SL trong `PlaceOrderService`
3. Cập nhật tài liệu API
4. Viết unit test và integration test
5. Triển khai lên môi trường test

### 5.3. Giai đoạn 3: Tối ưu hóa và mở rộng

1. Tối ưu hóa hiệu suất xử lý lệnh TP/SL
2. Mở rộng hỗ trợ các loại lệnh TP/SL khác (ví dụ: trailing stop)
3. Cải thiện UI/UX cho việc cài đặt TP/SL
4. Triển khai lên môi trường production

## 6. Ước tính thời gian

| Giai đoạn | Công việc | Thời gian ước tính |
|-----------|-----------|-------------------|
| Giai đoạn 1 | Triển khai API cài đặt TP/SL sau khi đã mở vị thế | 2 tuần |
| Giai đoạn 2 | Triển khai cài đặt TP/SL khi đặt lệnh thị trường | 2 tuần |
| Giai đoạn 3 | Tối ưu hóa và mở rộng | 2 tuần |
| Tổng cộng | | 6 tuần |

## 7. Kết luận

Việc triển khai tính năng Take Profit và Stop Loss sẽ giúp người dùng quản lý rủi ro và tối ưu hóa lợi nhuận một cách hiệu quả. Tính năng này cũng sẽ nâng cao trải nghiệm người dùng và tăng tính cạnh tranh của sàn giao dịch.

Đề xuất triển khai theo 3 giai đoạn, với tổng thời gian ước tính là 6 tuần. Sau khi hoàn thành, người dùng sẽ có thể cài đặt TP/SL cả khi đặt lệnh thị trường và sau khi đã mở vị thế.
