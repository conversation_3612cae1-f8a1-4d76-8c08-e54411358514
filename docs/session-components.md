# Danh sách các thành phần Session đã loại bỏ

Tài liệu này liệt kê các thành phần session đã được loại bỏ khỏi module future-core để triển khai sau trong module khác.

## Các file trong thư mục `infrastructure/session`

### Các event
- SessionEvent.java - Interface cơ bản cho các sự kiện phiên
- SessionExpirationWarningEvent.java - Sự kiện cảnh báo phiên sắp hết hạn
- SessionExpiredEvent.java - Sự kiện phiên đã hết hạn
- SessionExtendedEvent.java - Sự kiện phiên đã được gia hạn
- SessionInvalidatedEvent.java - Sự kiện phiên đã bị vô hiệu hóa
- SessionRemovedEvent.java - Sự kiện phiên đã bị xóa

### Các handler và publisher
- SessionEventPublisher.java - Publisher cho các sự kiện phiên
- SessionExpirationHandler.java - Handler xử lý hết hạn phiên
- SessionValidationHandler.java - Handler xác thực phiên

## Chức năng của các thành phần

### SessionEvent
Interface cơ bản định nghĩa các sự kiện liên quan đến phiên. Các sự kiện cụ thể kế thừa từ interface này.

### SessionEventPublisher
Thành phần phát hành các sự kiện phiên để các thành phần khác trong hệ thống có thể lắng nghe và xử lý.

### SessionExpirationHandler
Xử lý việc hết hạn phiên, bao gồm:
- Kiểm tra phiên hết hạn
- Gửi cảnh báo trước khi phiên hết hạn
- Xóa phiên hết hạn
- Gửi sự kiện khi phiên hết hạn

### SessionValidationHandler
Xác thực tính hợp lệ của phiên, bao gồm:
- Kiểm tra tính toàn vẹn của phiên
- Kiểm tra quyền truy cập
- Kiểm tra IP và thông tin thiết bị
- Phát hiện các hoạt động đáng ngờ

## Cấu hình trong application.properties

```properties
# Session Configuration
session.timeout-minutes=30
session.warning-before-timeout-minutes=5
session.max-concurrent-sessions=5
session.prevent-concurrent-login=true
session.track-user-agent=true
session.track-ip-address=true
session.validate-ip-change=true
session.validate-user-agent-change=true
session.encrypt-sensitive-data=true
```

## Các dependency cần thiết

```xml
<dependency>
    <groupId>org.springframework.session</groupId>
    <artifactId>spring-session-data-redis</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

## Hướng dẫn triển khai

Khi triển khai các thành phần session trong module khác, cần lưu ý:

1. Thêm các dependency cần thiết vào file pom.xml của module đó
2. Triển khai các thành phần theo thứ tự:
   - Các event
   - Các publisher
   - Các handler
3. Cấu hình các thuộc tính trong application.properties hoặc application.yml
4. Đảm bảo tích hợp với các thành phần khác của hệ thống, đặc biệt là các thành phần security

## Lưu ý bảo mật

- Sử dụng HTTPS để bảo vệ cookie phiên
- Đặt thuộc tính HttpOnly và Secure cho cookie phiên
- Sử dụng SameSite=Strict hoặc SameSite=Lax cho cookie phiên
- Tạo ID phiên ngẫu nhiên và đủ dài
- Xác thực lại người dùng khi thực hiện các hoạt động nhạy cảm
- Thiết lập thời gian hết hạn phiên hợp lý
- Cung cấp tùy chọn "Đăng xuất khỏi tất cả các thiết bị"
- Theo dõi và ghi nhật ký các hoạt động phiên đáng ngờ
- Triển khai cơ chế phát hiện và ngăn chặn session fixation
- Xóa dữ liệu phiên khi đăng xuất
