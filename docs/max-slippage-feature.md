# Tính năng <PERSON> Trượt Giá <PERSON> (<PERSON> Slippage)

## Giới thiệu

Tính năng Mức Trượt G<PERSON><PERSON> (Max Slippage) cho phép người dùng chỉ định mức trượt giá tối đa mà họ chấp nhận khi đặt lệnh. Nếu giá thị trường biến động vượt quá ngưỡng này, lệnh sẽ không được thực hiện hoặc chỉ thực hiện một phần.

## Mục đích

- Bảo vệ người dùng khỏi biến động thị trường mạnh
- Giảm thiểu rủi ro khi thị trường biến động mạnh
- Cho phép người dùng kiểm soát việc thực hiện lệnh

## C<PERSON><PERSON> tham số mới

### 1. maxSlippage

- **<PERSON><PERSON> tả**: <PERSON><PERSON><PERSON> trư<PERSON><PERSON> gi<PERSON> tối đa mà người dùng chấp nh<PERSON> (%)
- **Kiểu dữ liệu**: BigDecimal
- **Mặc định**: 3.0%
- **V<PERSON> dụ**: 2.5 (2.5%)

### 2. fillOrKill

- **Mô tả**: Nếu true, lệnh phải được khớp toàn bộ ngay lập tức hoặc bị hủy
- **Ki<PERSON>u dữ liệu**: Boolean
- **Mặc định**: false
- **Ví dụ**: true

### 3. immediateOrCancel

- **Mô tả**: Nếu true, phần lệnh không được khớp ngay lập tức sẽ bị hủy
- **Kiểu dữ liệu**: Boolean
- **Mặc định**: false
- **Ví dụ**: true

## Cách sử dụng

### Trong API Request

```json
{
  "symbol": "BTC-USDT",
  "direction": "BUY",
  "type": "LIMIT",
  "price": 50000,
  "volume": 1.0,
  "maxSlippage": 2.5,
  "fillOrKill": false,
  "immediateOrCancel": false
}
```

### Trong giao diện người dùng

Người dùng có thể chỉ định các tham số này khi đặt lệnh thông qua giao diện người dùng:

1. **Mức trượt giá tối đa**: Người dùng có thể chỉ định mức trượt giá tối đa mà họ chấp nhận.
2. **Fill-or-Kill**: Người dùng có thể chọn lệnh phải được khớp toàn bộ ngay lập tức hoặc bị hủy.
3. **Immediate-or-Cancel**: Người dùng có thể chọn phần lệnh không được khớp ngay lập tức sẽ bị hủy.

## Cách hoạt động

1. Khi người dùng đặt lệnh với các tham số này, hệ thống sẽ kiểm tra biến động thị trường tức thời.
2. Nếu biến động tức thời vượt quá mức trượt giá tối đa, hệ thống sẽ xử lý lệnh theo các quy tắc sau:
   - Nếu `fillOrKill = true`, lệnh sẽ bị từ chối.
   - Nếu `immediateOrCancel = true`, chỉ phần có thể khớp ngay lập tức sẽ được thực hiện.
   - Nếu cả hai đều là `false`, lệnh vẫn được thực hiện nhưng với thuật toán FIFO.
3. Nếu biến động tức thời không vượt quá mức trượt giá tối đa, lệnh sẽ được thực hiện bình thường.

## Cấu hình hệ thống

Các tham số cấu hình trong `application-matching-engine.yml`:

```yaml
algorithm-switch:
  # Ngưỡng biến động tức thời (%)
  instant-volatility-threshold: 2.0
  # Khoảng thời gian để tính biến động tức thời (phút)
  instant-volatility-time-frame: 5
  # Mức trượt giá tối đa mặc định (%)
  default-max-slippage: 3.0
  # Cờ bật/tắt cơ chế bảo vệ lệnh
  enable-order-protection: true
```

## Lợi ích

1. **Bảo vệ người dùng**: Giúp người dùng tránh bị ảnh hưởng bởi biến động thị trường mạnh.
2. **Kiểm soát rủi ro**: Cho phép người dùng kiểm soát mức độ rủi ro khi đặt lệnh.
3. **Linh hoạt**: Người dùng có thể tùy chỉnh các tham số theo nhu cầu.
4. **Tự động**: Hệ thống tự động áp dụng các biện pháp bảo vệ khi cần thiết.
