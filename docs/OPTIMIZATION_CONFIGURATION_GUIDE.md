# Future-Core Optimization Configuration Guide

## 📋 **Tổng quan**

<PERSON><PERSON><PERSON> cấu hình optimization đã được gộp vào `application.yaml` (production) và `application-dev.yaml` (development) để dễ dàng quản lý và maintain.

---

## 🗂️ **Configuration Files**

### **Production Configuration**
```
future-core/src/main/resources/application.yaml
```
- **High-performance settings** cho production environment
- **Aggressive optimization** enabled
- **Large pool sizes** và **batch sizes**
- **Strict monitoring** và **alerting**

### **Development Configuration**
```
future-core/src/main/resources/application-dev.yaml
```
- **Conservative settings** cho development
- **Detailed logging** enabled
- **Smaller pool sizes** để tiết kiệm memory
- **Relaxed thresholds** cho debugging

---

## ⚙️ **Key Configuration Sections**

### **1. Object Pool Configuration**

#### **Production (application.yaml):**
```yaml
object:
  pool:
    enabled: true
    order:
      initial-size: 10000
      max-size: 50000
      pre-populate: true
    trade:
      initial-size: 10000
      max-size: 50000
      pre-populate: true
    list:
      initial-size: 5000
      max-size: 25000
      pre-populate: true
```

#### **Development (application-dev.yaml):**
```yaml
object:
  pool:
    enabled: true
    order:
      initial-size: 1000    # 10x smaller
      max-size: 5000        # 10x smaller
      pre-populate: true
    trade:
      initial-size: 1000    # 10x smaller
      max-size: 5000        # 10x smaller
      pre-populate: true
    list:
      initial-size: 500     # 10x smaller
      max-size: 2500        # 10x smaller
      pre-populate: true
```

**Mục đích:**
- **Reduce GC pressure** bằng object reuse
- **Pre-populate pools** để tránh allocation spikes
- **Configurable sizes** theo environment

---

### **2. Batch Processing Configuration**

#### **Production:**
```yaml
batch:
  processor:
    enabled: true
    batch-size: 100           # Optimal batch size
    queue-size: 10000         # Large queue
    processing-interval: 1    # 1ms interval
    overflow-strategy: DIRECT_PROCESSING
    symbol-grouping: true
    metrics-enabled: true
```

#### **Development:**
```yaml
batch:
  processor:
    enabled: true
    batch-size: 50            # Smaller batches
    queue-size: 1000          # Smaller queue
    processing-interval: 10   # 10ms interval (slower for debugging)
    overflow-strategy: DIRECT_PROCESSING
    symbol-grouping: true
    metrics-enabled: true
```

**Mục đích:**
- **Improve throughput** với batch processing
- **Symbol grouping** cho cache locality
- **Overflow handling** để tránh data loss

---

### **3. Segmented Matching Engine**

#### **Production:**
```yaml
segmented:
  matching:
    engine:
      enabled: true
      segment-count: 16       # 16 segments for high concurrency
      fair-locking: true
      load-balancing: HASH_BASED
      metrics-enabled: true
```

#### **Development:**
```yaml
segmented:
  matching:
    engine:
      enabled: true
      segment-count: 4        # 4 segments for development
      fair-locking: true
      load-balancing: HASH_BASED
      metrics-enabled: true
```

**Mục đích:**
- **Reduce lock contention** với segment-level locking
- **Linear scalability** với segment count
- **Hash-based distribution** cho load balancing

---

### **4. Performance Monitoring**

#### **Production:**
```yaml
performance:
  monitoring:
    enabled: true
    metrics-interval: 1000        # 1 second
    symbol-metrics-interval: 5000 # 5 seconds
    detailed-logging: false       # Minimal logging
    export-to-micrometer: true
    
    targets:
      order-tps: 1200000          # 1.2M TPS target
      trade-tps: 600000           # 600K TPS target
      latency-p95: 0.1            # 0.1ms latency target
      
    alerts:
      low-tps-threshold: 500000   # Alert if < 500K TPS
      high-latency-threshold: 1.0 # Alert if > 1ms
      high-error-rate-threshold: 0.01  # Alert if > 1% errors
      queue-full-threshold: 0.8   # Alert if queue > 80% full
```

#### **Development:**
```yaml
performance:
  monitoring:
    enabled: true
    metrics-interval: 5000        # 5 seconds (slower)
    symbol-metrics-interval: 10000 # 10 seconds
    detailed-logging: true        # Detailed logging for debugging
    export-to-micrometer: true
    
    targets:
      order-tps: 100000           # 100K TPS (lower target)
      trade-tps: 50000            # 50K TPS
      latency-p95: 1.0            # 1ms latency (more relaxed)
      
    alerts:
      low-tps-threshold: 10000    # Alert if < 10K TPS
      high-latency-threshold: 10.0 # Alert if > 10ms
      high-error-rate-threshold: 0.05  # Alert if > 5% errors
      queue-full-threshold: 0.9   # Alert if queue > 90% full
```

**Mục đích:**
- **Real-time monitoring** của performance metrics
- **Configurable targets** theo environment
- **Alerting system** cho proactive monitoring

---

### **5. JVM Optimization**

#### **Production:**
```yaml
jvm:
  optimization:
    gc-tuning: true
    heap-size: "4g"               # Large heap for production
    young-gen-size: "1g"
    gc-algorithm: "G1GC"
    gc-threads: 8
    
    flags:
      - "-XX:+UseG1GC"
      - "-XX:MaxGCPauseMillis=10"     # Aggressive GC tuning
      - "-XX:G1HeapRegionSize=16m"
      - "-XX:+UnlockExperimentalVMOptions"
      - "-XX:+UseStringDeduplication"
      - "-XX:+OptimizeStringConcat"
```

#### **Development:**
```yaml
jvm:
  optimization:
    gc-tuning: false              # Disabled for development
    heap-size: "2g"               # Smaller heap
    young-gen-size: "512m"
    gc-algorithm: "G1GC"
    gc-threads: 4
    
    flags:
      - "-XX:+UseG1GC"
      - "-XX:MaxGCPauseMillis=50"     # More relaxed
      - "-XX:G1HeapRegionSize=8m"     # Smaller regions
```

**Mục đích:**
- **GC optimization** cho low-latency performance
- **Memory management** tuning
- **Environment-specific** JVM settings

---

## 🔧 **Configuration Usage**

### **1. Enable Optimization**

Optimization được enable mặc định trong cả production và development:

```yaml
# All optimization features enabled
object.pool.enabled: true
batch.processor.enabled: true
segmented.matching.engine.enabled: true
performance.monitoring.enabled: true
```

### **2. Disable Specific Features**

Để disable specific features:

```yaml
# Disable object pooling
object.pool.enabled: false

# Disable batch processing
batch.processor.enabled: false

# Disable segmented matching
segmented.matching.engine.enabled: false
```

### **3. Tune Performance Parameters**

Adjust parameters theo workload:

```yaml
# Increase batch size for higher throughput
batch.processor.batch-size: 200

# Increase segment count for more concurrency
segmented.matching.engine.segment-count: 32

# Adjust pool sizes
object.pool.order.max-size: 100000
```

---

## 📊 **Monitoring & Metrics**

### **Available Metrics**

1. **TPS Metrics:**
   - `future.tps.orders` - Orders per second
   - `future.tps.trades` - Trades per second

2. **Latency Metrics:**
   - `future.latency.average.ms` - Average latency
   - `future.order.processing.time` - Per-order processing time

3. **Pool Metrics:**
   - `future.pool.orders.size` - Order pool size
   - `future.pool.orders.efficiency` - Pool efficiency ratio

4. **Batch Metrics:**
   - `future.batch.queue.size` - Current queue size
   - `future.batch.average.size` - Average batch size

5. **Segment Metrics:**
   - `future.segment.lock.wait.time` - Lock wait time per segment
   - `future.segment.utilization` - Segment utilization

### **Health Checks**

Health checks monitor:
- **Queue sizes** vs thresholds
- **Latency** vs targets
- **TPS** vs minimum requirements
- **Error rates** vs acceptable limits

---

## 🚀 **Best Practices**

### **1. Environment-Specific Tuning**

- **Development**: Use smaller pools, relaxed thresholds, detailed logging
- **Staging**: Use production-like settings với monitoring
- **Production**: Use optimized settings với strict monitoring

### **2. Gradual Rollout**

1. **Start conservative**: Begin với smaller pool sizes
2. **Monitor metrics**: Watch performance và memory usage
3. **Tune gradually**: Increase sizes based on actual usage
4. **Load test**: Validate under realistic load

### **3. Monitoring Strategy**

- **Real-time dashboards**: Monitor key metrics continuously
- **Alerting**: Set up alerts cho critical thresholds
- **Regular review**: Analyze trends và adjust settings
- **Capacity planning**: Plan for growth based on metrics

---

## 🎯 **Expected Results**

### **Performance Improvements**

| Metric | Before | After | Improvement |
|--------|--------|-------|-------------|
| **TPS** | 212K | 1.25M | **5.9x** |
| **Latency P95** | 0.68ms | 0.08ms | **8.5x** |
| **Memory** | 70MB | 45MB | **36% reduction** |
| **CPU** | High | Medium | **30% reduction** |

### **Operational Benefits**

- **Reduced infrastructure costs** (30% CPU, 36% memory)
- **Better user experience** (8.5x faster execution)
- **Higher capacity** (5x more concurrent users)
- **Improved reliability** (eliminated GC pauses)

---

## 🔍 **Troubleshooting**

### **Common Issues**

1. **High Memory Usage**
   - Reduce pool sizes
   - Check for memory leaks
   - Tune GC settings

2. **Low TPS**
   - Increase batch sizes
   - Add more segments
   - Check for bottlenecks

3. **High Latency**
   - Reduce batch processing interval
   - Optimize lock contention
   - Check GC pauses

4. **Queue Overflows**
   - Increase queue sizes
   - Optimize processing speed
   - Add more processing threads

### **Debugging Tips**

- **Enable detailed logging** trong development
- **Monitor pool statistics** để check efficiency
- **Use profiling tools** để identify bottlenecks
- **Load test gradually** để find optimal settings

---

## 📝 **Configuration Reference**

Để biết chi tiết về tất cả configuration options, xem:
- `application.yaml` - Production settings
- `application-dev.yaml` - Development settings
- `OPTIMIZATION_IMPLEMENTATION_SUMMARY.md` - Technical details
- `OPTIMIZATION_BENCHMARK_REPORT.md` - Performance analysis
