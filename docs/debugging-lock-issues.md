# Debugging Lock Issues trong Future Core

## Tổng quan

Document này hướng dẫn cách debug và giải quyết các vấn đề liên quan đến distributed locking trong Future Core.

## C<PERSON><PERSON> loại lỗi lock thường gặp

### 1. Lock Timeout Error
```
ERROR c.i.l.i.m.DistributedLockingMatchingEngine - Could not acquire lock for symbol: {0}
```

**Nguyên nhân có thể:**
- Redis connection không ổn định
- Lock đang được giữ bởi thread khác quá lâu
- Deadlock giữa các threads
- Redis server overload
- Network latency cao

### 2. Lock Interrupted Error
```
ERROR c.i.l.i.m.DistributedLockingMatchingEngine - Interrupted while waiting for lock for symbol: {0}
```

**Nguyên nhân có thể:**
- Thread bị interrupt trong quá trình chờ lock
- Application shutdown
- Thread pool exhaustion

## Cách debug

### 1. Enable Debug Logging

Trong `application-dev.yaml`:
```yaml
logging:
  level:
    com.icetea.lotus.infrastructure.matching.DistributedLockingMatchingEngine: DEBUG
    com.icetea.lotus.infrastructure.monitoring.RedisHealthMonitor: DEBUG
    org.redisson: WARN
```

### 2. Sử dụng Monitoring Endpoints

#### Kiểm tra thống kê lock:
```bash
curl http://localhost:6010/api/monitoring/lock-statistics
```

Response:
```json
{
  "statistics": "Lock Statistics - Acquisitions: 1000, Timeouts: 5, Success Rate: 99.50%",
  "redisHealthy": true,
  "timestamp": 1640995200000
}
```

#### Kiểm tra sức khỏe Redis:
```bash
curl http://localhost:6010/api/monitoring/redis-health
```

#### Lấy tổng quan hệ thống:
```bash
curl http://localhost:6010/api/monitoring/system-overview
```

### 3. Phân tích Log

#### Debug logs sẽ hiển thị:
- Thread name đang cố gắng lấy lock
- Thời gian lấy lock
- Lock key được sử dụng
- Thông tin về lock holder hiện tại
- Redis connection health status

#### Ví dụ debug log:
```
DEBUG c.i.l.i.m.DistributedLockingMatchingEngine - Thread orderbook-9 đang cố gắng lấy lock cho symbol: BTC/USDT, lockKey: order_matching:BTC/USDT
DEBUG c.i.l.i.m.DistributedLockingMatchingEngine - Thread orderbook-9 đã lấy được lock cho symbol: BTC/USDT trong 15ms
DEBUG c.i.l.i.m.DistributedLockingMatchingEngine - Thread orderbook-9 đã xử lý xong lệnh cho symbol: BTC/USDT, tạo ra 2 giao dịch
DEBUG c.i.l.i.m.DistributedLockingMatchingEngine - Thread orderbook-9 đã giải phóng lock cho symbol: BTC/USDT
```

## Giải pháp cho các vấn đề thường gặp

### 1. Lock Timeout trong cùng một Pod

**Vấn đề:** Cùng một pod nhưng không thể lấy được lock

**Nguyên nhân có thể:**
- Multiple threads cạnh tranh cùng một lock
- Thread không release lock đúng cách
- Redis connection issues

**Giải pháp:**
1. Kiểm tra Redis connection health
2. Tăng connection pool size
3. Giảm lock timeout để fail fast
4. Kiểm tra thread pool configuration

### 2. Redis Connection Issues

**Triệu chứng:**
- Lock timeout liên tục
- Redis health check fail
- High response time

**Giải pháp:**
1. Kiểm tra Redis server status
2. Tăng connection pool size
3. Tối ưu hóa network configuration
4. Enable TCP keep-alive

### 3. High Lock Contention

**Triệu chứng:**
- Lock success rate thấp
- Nhiều timeout errors
- High latency

**Giải pháp:**
1. Optimize business logic để giảm lock time
2. Implement lock-free algorithms nếu có thể
3. Shard data tốt hơn
4. Tăng Redis performance

## Configuration Tuning

### Redis Connection Pool
```java
config.useSingleServer()
      .setConnectionMinimumIdleSize(10)
      .setConnectionPoolSize(50)
      .setTimeout(3000)
      .setConnectTimeout(5000)
      .setRetryAttempts(3)
      .setRetryInterval(1000);
```

### Lock Timeout Settings
```java
// Wait time: 5 seconds, Lease time: 30 seconds
lock.tryLock(5, 30, TimeUnit.SECONDS)
```

## Monitoring và Alerting

### Key Metrics để monitor:
1. Lock success rate
2. Average lock acquisition time
3. Redis connection health
4. Thread pool utilization
5. Memory usage

### Alerts nên setup:
1. Lock success rate < 95%
2. Redis health check fail
3. High lock acquisition time (> 1s)
4. Memory usage > 80%

## Troubleshooting Checklist

1. ✅ Kiểm tra Redis server status
2. ✅ Verify Redis connection configuration
3. ✅ Check lock statistics
4. ✅ Review debug logs
5. ✅ Monitor thread pool status
6. ✅ Check memory usage
7. ✅ Verify network connectivity
8. ✅ Review business logic for lock usage

## Emergency Actions

### Khi gặp lock timeout storm:
1. Reset lock statistics để clear metrics
2. Restart Redis connection pool
3. Scale up Redis resources
4. Temporarily increase lock timeout
5. Review và optimize code paths

### Commands:
```bash
# Reset lock statistics
curl -X POST http://localhost:6010/api/monitoring/lock-statistics/reset

# Trigger Redis health check
curl -X POST http://localhost:6010/api/monitoring/redis-health/check
```
