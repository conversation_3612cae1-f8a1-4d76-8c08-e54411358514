# Luồng xử lý Command-Event Sourcing

Tài liệu này mô tả luồng xử lý lệnh mới dựa trên mô hình Command-Event Sourcing trong hệ thống giao dịch hợp đồng tương lai.

## Tổng quan

Luồng xử lý mới sử dụng mô hình Command-Event Sourcing để xử lý lệnh trong môi trường phân tán. Mô hình này giúp tăng hiệu suất, hỗ trợ xử lý đa luồng, tăng tính sẵn sàng và nhất quán của hệ thống.

### C<PERSON><PERSON> thành phần chính

1. **OrderCommandProducer**: G<PERSON>i lệnh đến Kafka.
2. **OrderCommandConsumer**: Nhận lệnh từ Kafka và xử lý.
3. **OrderEventProducer**: Gửi event đến Kafka.
4. **OrderEventConsumer**: Nhận event từ Kafka và xử lý.
5. **DistributedLockingMatchingEngine**: Sử dụng distributed lock đ<PERSON> đảm bảo chỉ có một instance xử lý lệnh cho một symbol tại một thời điểm.
6. **SymbolShardingManager**: Quản lý phân phối các symbol giữa các instance.

## Luồng xử lý lệnh

### 1. Đặt lệnh (Place Order)

```mermaid
sequenceDiagram
    participant Client
    participant PlaceOrderService
    participant OrderCommandProducer
    participant Kafka
    participant OrderCommandConsumer
    participant DistributedLockingMatchingEngine
    participant OrderEventProducer
    participant OrderEventConsumer
    
    Client->>PlaceOrderService: Đặt lệnh
    PlaceOrderService->>PlaceOrderService: Lưu lệnh vào DB
    PlaceOrderService->>OrderCommandProducer: sendPlaceOrderCommand(order)
    OrderCommandProducer->>Kafka: Gửi lệnh đến topic "order-commands"
    Kafka->>OrderCommandConsumer: Nhận lệnh
    OrderCommandConsumer->>OrderCommandConsumer: Kiểm tra quyền sở hữu symbol
    OrderCommandConsumer->>DistributedLockingMatchingEngine: processOrder(order)
    DistributedLockingMatchingEngine->>DistributedLockingMatchingEngine: Lấy distributed lock
    DistributedLockingMatchingEngine->>DistributedLockingMatchingEngine: Khớp lệnh và tạo giao dịch
    DistributedLockingMatchingEngine-->>OrderCommandConsumer: Trả về danh sách giao dịch
    OrderCommandConsumer->>OrderEventProducer: publishOrderPlacedEvent(order, trades)
    OrderEventProducer->>Kafka: Gửi event đến topic "order-events"
    Kafka->>OrderEventConsumer: Nhận event
    OrderEventConsumer->>OrderEventConsumer: Cập nhật trạng thái order book
```

#### Chi tiết luồng đặt lệnh

1. **Người dùng gọi API đặt lệnh**:
   - API Controller nhận yêu cầu và chuyển tiếp đến PlaceOrderService.

2. **PlaceOrderService xử lý lệnh**:
   - Kiểm tra tính hợp lệ của lệnh.
   - Tính toán margin cần thiết.
   - Đóng băng margin trong ví của người dùng.
   - Lưu lệnh vào cơ sở dữ liệu.
   - Gọi OrderCommandProducer.sendPlaceOrderCommand(order).

3. **OrderCommandProducer gửi lệnh đến Kafka**:
   - Tạo OrderCommand với type=PLACE_ORDER.
   - Gửi command đến topic "order-commands" trên Kafka.

4. **OrderCommandConsumer nhận và xử lý lệnh**:
   - Lắng nghe topic "order-commands" và nhận lệnh.
   - Kiểm tra xem symbol có được gán cho instance này không thông qua SymbolShardingManager.
   - Nếu có, gọi DistributedLockingMatchingEngine.processOrder(order).

5. **DistributedLockingMatchingEngine xử lý lệnh**:
   - Lấy distributed lock cho symbol để đảm bảo chỉ có một instance xử lý lệnh cho symbol này tại một thời điểm.
   - Gọi DistributedLockFreeMatchingEngine hoặc LockFreeMatchingEngine để khớp lệnh và tạo ra các giao dịch.
   - Trả về danh sách các giao dịch đã tạo.

6. **OrderCommandConsumer gửi event**:
   - Gọi OrderEventProducer.publishOrderPlacedEvent(order, trades).

7. **OrderEventProducer gửi event đến Kafka**:
   - Tạo OrderEvent với type=ORDER_PLACED.
   - Gửi event đến topic "order-events" trên Kafka.

8. **OrderEventConsumer nhận và xử lý event**:
   - Lắng nghe topic "order-events" và nhận event.
   - Kiểm tra xem symbol có được gán cho instance này không.
   - Nếu có, cập nhật trạng thái của order book.

### 2. Hủy lệnh (Cancel Order)

```mermaid
sequenceDiagram
    participant Client
    participant PlaceOrderService
    participant OrderCommandProducer
    participant Kafka
    participant OrderCommandConsumer
    participant DistributedLockingMatchingEngine
    participant OrderEventProducer
    participant OrderEventConsumer
    
    Client->>PlaceOrderService: Hủy lệnh
    PlaceOrderService->>PlaceOrderService: Cập nhật trạng thái lệnh
    PlaceOrderService->>OrderCommandProducer: sendCancelOrderCommand(order)
    OrderCommandProducer->>Kafka: Gửi lệnh hủy đến topic "order-commands"
    Kafka->>OrderCommandConsumer: Nhận lệnh hủy
    OrderCommandConsumer->>OrderCommandConsumer: Kiểm tra quyền sở hữu symbol
    OrderCommandConsumer->>DistributedLockingMatchingEngine: cancelOrder(orderId, symbol)
    DistributedLockingMatchingEngine->>DistributedLockingMatchingEngine: Lấy distributed lock
    DistributedLockingMatchingEngine->>DistributedLockingMatchingEngine: Hủy lệnh
    DistributedLockingMatchingEngine-->>OrderCommandConsumer: Trả về kết quả
    OrderCommandConsumer->>OrderEventProducer: publishOrderCancelledEvent(order)
    OrderEventProducer->>Kafka: Gửi event đến topic "order-events"
    Kafka->>OrderEventConsumer: Nhận event
    OrderEventConsumer->>OrderEventConsumer: Cập nhật trạng thái order book
```

#### Chi tiết luồng hủy lệnh

1. **Người dùng gọi API hủy lệnh**:
   - API Controller nhận yêu cầu và chuyển tiếp đến PlaceOrderService.

2. **PlaceOrderService xử lý lệnh hủy**:
   - Tìm lệnh trong cơ sở dữ liệu.
   - Kiểm tra tính hợp lệ của lệnh hủy.
   - Cập nhật trạng thái lệnh thành CANCELED.
   - Giải phóng margin đã đóng băng.
   - Gọi OrderCommandProducer.sendCancelOrderCommand(order).

3. **OrderCommandProducer gửi lệnh hủy đến Kafka**:
   - Tạo OrderCommand với type=CANCEL_ORDER.
   - Gửi command đến topic "order-commands" trên Kafka.

4. **OrderCommandConsumer nhận và xử lý lệnh hủy**:
   - Lắng nghe topic "order-commands" và nhận lệnh hủy.
   - Kiểm tra xem symbol có được gán cho instance này không.
   - Nếu có, gọi DistributedLockingMatchingEngine.cancelOrder(orderId, symbol).

5. **DistributedLockingMatchingEngine xử lý lệnh hủy**:
   - Lấy distributed lock cho symbol.
   - Gọi DistributedLockFreeMatchingEngine hoặc LockFreeMatchingEngine để hủy lệnh.
   - Trả về kết quả hủy lệnh.

6. **OrderCommandConsumer gửi event**:
   - Gọi OrderEventProducer.publishOrderCancelledEvent(order).

7. **OrderEventProducer gửi event đến Kafka**:
   - Tạo OrderEvent với type=ORDER_CANCELLED.
   - Gửi event đến topic "order-events" trên Kafka.

8. **OrderEventConsumer nhận và xử lý event**:
   - Lắng nghe topic "order-events" và nhận event.
   - Kiểm tra xem symbol có được gán cho instance này không.
   - Nếu có, cập nhật trạng thái của order book.

### 3. Cập nhật lệnh (Update Order)

```mermaid
sequenceDiagram
    participant Client
    participant PlaceOrderService
    participant OrderCommandProducer
    participant Kafka
    participant OrderCommandConsumer
    participant DistributedLockingMatchingEngine
    participant OrderEventProducer
    participant OrderEventConsumer
    
    Client->>PlaceOrderService: Cập nhật lệnh
    PlaceOrderService->>PlaceOrderService: Cập nhật lệnh trong DB
    PlaceOrderService->>OrderCommandProducer: sendUpdateOrderCommand(order)
    OrderCommandProducer->>Kafka: Gửi lệnh cập nhật đến topic "order-commands"
    Kafka->>OrderCommandConsumer: Nhận lệnh cập nhật
    OrderCommandConsumer->>OrderCommandConsumer: Kiểm tra quyền sở hữu symbol
    OrderCommandConsumer->>DistributedLockingMatchingEngine: updateOrder(order)
    DistributedLockingMatchingEngine->>DistributedLockingMatchingEngine: Lấy distributed lock
    DistributedLockingMatchingEngine->>DistributedLockingMatchingEngine: Cập nhật lệnh
    DistributedLockingMatchingEngine-->>OrderCommandConsumer: Trả về kết quả
    OrderCommandConsumer->>OrderEventProducer: publishOrderUpdatedEvent(order)
    OrderEventProducer->>Kafka: Gửi event đến topic "order-events"
    Kafka->>OrderEventConsumer: Nhận event
    OrderEventConsumer->>OrderEventConsumer: Cập nhật trạng thái order book
```

## Lợi ích của luồng xử lý mới

1. **Tăng hiệu suất**: Luồng mới sử dụng cấu trúc dữ liệu lock-free và xử lý phân tán để tăng hiệu suất.
2. **Hỗ trợ xử lý đa luồng**: Luồng mới hỗ trợ xử lý đa luồng trong môi trường phân tán, cho phép mở rộng hệ thống dễ dàng.
3. **Tăng tính sẵn sàng**: Luồng mới có thể tiếp tục hoạt động ngay cả khi một số instance gặp sự cố.
4. **Tăng tính nhất quán**: Luồng mới sử dụng event sourcing để đảm bảo tính nhất quán giữa các instance.
5. **Dễ dàng mở rộng**: Luồng mới được thiết kế để dễ dàng mở rộng bằng cách thêm instance mới.

## Cấu hình và triển khai

### Cấu hình Kafka

```properties
# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=contract-perpetual-futures
spring.kafka.consumer.auto-offset-reset=latest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.JsonDeserializer
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.springframework.kafka.support.serializer.JsonSerializer
spring.kafka.producer.acks=all
spring.kafka.producer.retries=3
spring.kafka.consumer.properties.spring.json.trusted.packages=com.icetea.lotus.*
```

### Cấu hình Redis

```properties
# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.database=0
spring.redis.timeout=5000
```

### Cấu hình Matching Engine

```properties
# Matching Engine Configuration
lotus.matching-engine.use-distributed=true
lotus.matching-engine.use-lock-free=true
lotus.matching-engine.use-optimized=true
lotus.matching-engine.default-algorithm=FIFO
```
