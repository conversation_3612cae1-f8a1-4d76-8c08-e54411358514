# Self-Trade Prevention (STP) Implementation

## Tóm tắt

Đã hoàn thành bổ sung cơ chế Self-Trade Prevention (STP) toàn diện vào hệ thống matching engine của future-core. Implementation này ngăn chặn tự giao dịch (self-trade) giữa các lệnh của cùng một member v<PERSON><PERSON> nhiều chế độ xử lý khác nhau.

## Các STP Modes được hỗ trợ

### 1. **NONE**
- **Mô tả:** Không áp dụng STP - cho phép self-trade
- **Use case:** Market making, arbitrage bots
- **Hành động:** Tiếp tục matching bình thường

### 2. **EXPIRE_TAKER**
- **Mô tả:** Hủy lệnh taker (lệnh mới) khi phát hiện self-trade
- **Use case:** <PERSON><PERSON><PERSON> v<PERSON> lệnh đã đặt trong order book
- **Hành động:** H<PERSON><PERSON> lệnh mới, giữ lại lệnh trong order book

### 3. **EXPIRE_MAKER**
- **<PERSON><PERSON> tả:** Hủy lệnh maker (lệnh trong order book) khi phát hiện self-trade
- **Use case:** Ưu tiên lệnh mới hơn lệnh cũ
- **Hành động:** Hủy lệnh trong order book, tiếp tục xử lý lệnh mới

### 4. **EXPIRE_BOTH**
- **Mô tả:** Hủy cả hai lệnh khi phát hiện self-trade
- **Use case:** Ngăn chặn hoàn toàn self-trade
- **Hành động:** Hủy cả taker và maker orders

### 5. **REDUCE_TAKER**
- **Mô tả:** Giảm khối lượng của lệnh taker để tránh self-trade
- **Use case:** Tối ưu hóa execution, chỉ khớp phần không bị self-trade
- **Hành động:** Giảm volume taker order

### 6. **REDUCE_MAKER**
- **Mô tả:** Bỏ qua lệnh maker hiện tại, tiếp tục với maker khác
- **Use case:** Bảo vệ lệnh mới
- **Hành động:** Skip maker order, tiếp tục matching

## Kiến trúc STP System

### **Core Components**

#### 1. **SelfTradePreventionMode (Enum)**
```java
public enum SelfTradePreventionMode {
    NONE,           // Cho phép self-trade
    EXPIRE_TAKER,   // Hủy lệnh taker
    EXPIRE_MAKER,   // Hủy lệnh maker
    EXPIRE_BOTH,    // Hủy cả hai
    REDUCE_TAKER,   // Giảm volume taker
    REDUCE_MAKER    // Skip maker
}
```

#### 2. **SelfTradePreventionAction (Enum)**
```java
public enum SelfTradePreventionAction {
    CONTINUE,       // Tiếp tục matching
    EXPIRE_TAKER,   // Hủy taker
    EXPIRE_MAKER,   // Hủy maker
    EXPIRE_BOTH,    // Hủy cả hai
    REDUCE_TAKER,   // Giảm taker
    REDUCE_MAKER,   // Giảm maker
    SKIP_MAKER      // Bỏ qua maker
}
```

#### 3. **SelfTradePreventionResult (Class)**
```java
@Data
@Builder
public class SelfTradePreventionResult {
    private boolean selfTradeDetected;
    private SelfTradePreventionMode appliedMode;
    private SelfTradePreventionAction action;
    private Order processedTakerOrder;
    private List<Order> affectedMakerOrders;
    private BigDecimal rejectedVolume;
    private String reason;
    private boolean canContinueMatching;
}
```

#### 4. **SelfTradePreventionService (Service)**
```java
@Service
public class SelfTradePreventionService {
    
    // Kiểm tra và xử lý STP
    public SelfTradePreventionResult checkAndPreventSelfTrade(
        Order takerOrder, Order makerOrder, SelfTradePreventionMode stpMode);
    
    // Kiểm tra có phải self-trade không
    private boolean isSelfTrade(Order takerOrder, Order makerOrder);
    
    // Áp dụng STP mode
    private SelfTradePreventionResult applySelfTradePreventionMode(...);
    
    // Lấy default STP mode
    public SelfTradePreventionMode getDefaultStpMode(Order order);
}
```

## Luồng xử lý STP

### **Bước 1: Order Request với STP Mode**
```json
{
  "symbol": "BTC/USDT",
  "direction": "BUY",
  "type": "LIMIT",
  "price": 50000,
  "volume": 0.1,
  "selfTradePreventionMode": "EXPIRE_TAKER"
}
```

### **Bước 2: STP Detection trong Matching Engine**
```java
// Trong DistributedLockFreeMatchingEngine
SelfTradePreventionMode stpMode = buyOrder.getSelfTradePreventionMode() != null ? 
    buyOrder.getSelfTradePreventionMode() : stpService.getDefaultStpMode(buyOrder);

SelfTradePreventionResult stpResult = stpService.checkAndPreventSelfTrade(
    buyOrder, sellOrder, stpMode);
```

### **Bước 3: STP Logic Processing**
```java
if (stpResult.isSelfTradeDetected()) {
    switch (stpResult.getAction()) {
        case EXPIRE_TAKER:
            snapshot.removeOrder(buyOrder.getOrderId());
            return; // Dừng matching
            
        case EXPIRE_MAKER:
            snapshot.removeOrder(sellOrder.getOrderId());
            continue; // Tiếp tục với maker khác
            
        case EXPIRE_BOTH:
            snapshot.removeOrder(buyOrder.getOrderId());
            snapshot.removeOrder(sellOrder.getOrderId());
            return;
            
        case SKIP_MAKER:
            continue; // Bỏ qua maker này
    }
}
```

### **Bước 4: Self-Trade Detection**
```java
private boolean isSelfTrade(Order takerOrder, Order makerOrder) {
    // Kiểm tra cùng memberId
    if (takerOrder.getMemberId().equals(makerOrder.getMemberId())) {
        return true;
    }
    
    // Kiểm tra cùng orderId (safety check)
    if (takerOrder.getOrderId().equals(makerOrder.getOrderId())) {
        return true;
    }
    
    return false;
}
```

## Tích hợp với Matching Algorithms

### **FIFO Matching với STP**
```java
// Trong matchOrderFIFO()
SelfTradePreventionResult stpResult = stpService.checkAndPreventSelfTrade(
    buyOrder, sellOrder, stpMode);

if (stpResult.isSelfTradeDetected()) {
    // Xử lý theo STP action
    handleStpAction(stpResult, snapshot);
    continue; // hoặc return tùy theo action
}

// Tiếp tục matching bình thường
```

### **Pro-Rata Matching với STP**
```java
// Trong matchOrderProRata()
SelfTradePreventionResult stpResult = stpService.checkAndPreventSelfTrade(
    order, oppositeOrder, stpMode);

if (stpResult.isSelfTradeDetected()) {
    handleStpAction(stpResult, snapshot);
    continue;
}
```

### **Hybrid Matching với STP**
```java
// Trong matchOrderHybrid() - cả FIFO và Pro-Rata phases
SelfTradePreventionResult stpResult = stpService.checkAndPreventSelfTrade(
    order, oppositeOrder, stpMode);

if (stpResult.isSelfTradeDetected()) {
    handleStpAction(stpResult, snapshot);
    continue;
}
```

## Database Schema Updates

### **Order Table**
```sql
ALTER TABLE orders ADD COLUMN self_trade_prevention_mode VARCHAR(20);
```

### **OrderJpaEntity**
```java
@Enumerated(EnumType.STRING)
@Column(name = "self_trade_prevention_mode")
private SelfTradePreventionMode selfTradePreventionMode;
```

## API Integration

### **PlaceOrderRequest**
```java
@Schema(description = "Chế độ ngăn chặn tự giao dịch", example = "EXPIRE_TAKER")
private SelfTradePreventionMode selfTradePreventionMode;
```

### **PlaceOrderCommand**
```java
private SelfTradePreventionMode selfTradePreventionMode;
```

### **Order Entity**
```java
private SelfTradePreventionMode selfTradePreventionMode;
```

## Default STP Behavior

### **Market Orders**
- Default: `EXPIRE_TAKER`
- Lý do: Market orders cần execution nhanh, tránh impact order book

### **Limit Orders**
- Default: `EXPIRE_TAKER`
- Lý do: Conservative approach, bảo vệ liquidity trong order book

### **Stop Orders**
- Default: `EXPIRE_TAKER`
- Lý do: Stop orders thường là risk management, ưu tiên safety

## Logging và Monitoring

### **STP Event Logging**
```java
log.info("=== PHÁT HIỆN SELF-TRADE ===");
log.info("Taker Order: {}, Maker Order: {}, STP Mode: {}", 
        takerOrder.getOrderId(), makerOrder.getOrderId(), stpMode);

log.info("STP triggered: {}", stpResult.getReason());
log.info("STP Action: {}, Affected Orders: {}", 
        stpResult.getAction(), stpResult.getAffectedMakerOrders().size());
```

### **Performance Impact**
- **Minimal overhead:** STP check chỉ thực hiện khi có potential match
- **Early exit:** Nếu không phải self-trade, return ngay lập tức
- **Efficient detection:** Chỉ so sánh memberId và orderId

## Testing STP

### **Test Case 1: EXPIRE_TAKER**
```json
// Đặt lệnh SELL trước
{
  "memberId": 123,
  "symbol": "BTC/USDT",
  "direction": "SELL",
  "price": 50000,
  "volume": 0.1
}

// Đặt lệnh BUY với STP
{
  "memberId": 123,
  "symbol": "BTC/USDT", 
  "direction": "BUY",
  "price": 50000,
  "volume": 0.1,
  "selfTradePreventionMode": "EXPIRE_TAKER"
}

// Expected: BUY order bị hủy, SELL order vẫn trong order book
```

### **Test Case 2: EXPIRE_MAKER**
```json
// Tương tự nhưng với "EXPIRE_MAKER"
// Expected: SELL order bị hủy, BUY order được execute
```

### **Test Case 3: NONE Mode**
```json
// Với "NONE" mode
// Expected: Self-trade được phép, tạo trade bình thường
```

## Lợi ích đạt được

### 1. **Compliance**
- Tuân thủ quy định về self-trade prevention
- Hỗ trợ các trading strategies khác nhau
- Flexible configuration per order

### 2. **Risk Management**
- Ngăn chặn wash trading
- Bảo vệ market integrity
- Kiểm soát trading behavior

### 3. **Performance**
- Minimal impact on matching performance
- Efficient self-trade detection
- Early exit optimization

### 4. **Flexibility**
- Multiple STP modes
- Per-order configuration
- Default behavior configuration

## Khuyến nghị tiếp theo

### 1. **Enhanced STP Features**
- Account-level STP configuration
- Symbol-level STP rules
- Time-based STP policies

### 2. **Monitoring & Analytics**
- STP event metrics
- Self-trade prevention statistics
- Performance impact analysis

### 3. **Advanced STP Modes**
- Partial fill STP
- Price-based STP
- Volume-based STP

## Kết luận

STP implementation đã hoàn thành với đầy đủ chức năng cần thiết cho trading system. Hệ thống bây giờ có thể ngăn chặn self-trade hiệu quả với nhiều chế độ xử lý linh hoạt, đảm bảo compliance và market integrity.
