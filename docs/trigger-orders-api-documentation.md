# Tài liệu API Lệnh Kích Ho<PERSON>t (Trigger Orders)

Tài liệu này mô tả các API liên quan đến lệnh kích hoạt (trigger orders) trong hệ thống giao dịch hợp đồng tương lai. Lệnh kích hoạt là các lệnh chỉ được thực thi khi giá thị trường đạt đến một mức giá nhất định.

## 1. <PERSON><PERSON><PERSON> loại lệnh kích hoạt

Hệ thống hỗ trợ các loại lệnh kích hoạt sau:

| Loại lệnh | Mô tả |
|-----------|-------|
| `STOP` | Lệnh dừng lỗ - khi giá đạt đến mức kích hoạt, tạo lệnh thị trường |
| `STOP_LIMIT` | Lệnh dừng lỗ giới hạn - khi giá đạt đến mức kích ho<PERSON>, tạo lệnh giới hạn với giá đã định |
| `STOP_MARKET` | Lệnh dừng lỗ thị trường - khi giá đạt đến mức kích hoạt, tạo lệnh thị trường |
| `STOP_LOSS` | Lệnh dừng lỗ - khi giá đạt đến mức kích hoạt, tạo lệnh thị trường để đóng vị thế |
| `STOP_LOSS_LIMIT` | Lệnh dừng lỗ giới hạn - khi giá đạt đến mức kích hoạt, tạo lệnh giới hạn để đóng vị thế |
| `TAKE_PROFIT` | Lệnh chốt lời - khi giá đạt đến mức kích hoạt, tạo lệnh thị trường để đóng vị thế |
| `TAKE_PROFIT_LIMIT` | Lệnh chốt lời giới hạn - khi giá đạt đến mức kích hoạt, tạo lệnh giới hạn để đóng vị thế |
| `TAKE_PROFIT_MARKET` | Lệnh chốt lời thị trường - khi giá đạt đến mức kích hoạt, tạo lệnh thị trường để đóng vị thế |
| `TRAILING_STOP` | Lệnh dừng theo dõi - tự động điều chỉnh giá kích hoạt theo biến động thị trường |

## 2. Loại kích hoạt (TriggerType)

Hệ thống hỗ trợ các loại kích hoạt sau:

| Loại kích hoạt | Mô tả |
|----------------|-------|
| `LAST_PRICE` | Kích hoạt dựa trên giá giao dịch gần nhất |
| `MARK_PRICE` | Kích hoạt dựa trên giá đánh dấu |
| `INDEX_PRICE` | Kích hoạt dựa trên giá chỉ số |

## 3. API Đặt lệnh kích hoạt

### 3.1. Đặt lệnh Stop (STOP)

**Endpoint:** `POST /api/v1/trading/orders`

**Request:**
```json
{
  "memberId": 600896,
  "symbol": "BTC-USDT",
  "direction": "SELL",
  "type": "STOP",
  "price": 48000.00,
  "triggerPrice": 49000.00,
  "volume": 0.1,
  "leverage": 10,
  "reduceOnly": true,
  "timeInForce": "GTC"
}
```

**Lưu ý:**
- `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
- `price` là giá đặt lệnh sau khi lệnh được kích hoạt (bắt buộc)
- Đối với lệnh BUY, lệnh được kích hoạt khi giá <= triggerPrice
- Đối với lệnh SELL, lệnh được kích hoạt khi giá >= triggerPrice

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "orderId": "ORD600896456789",
    "memberId": 600896,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "STOP",
    "price": 48000.00,
    "triggerPrice": 49000.00,
    "volume": 0.1,
    "status": "NEW",
    "createTime": "2023-05-01T12:00:00"
  },
  "timestamp": "2023-05-01T12:00:00"
}
```

### 3.2. Đặt lệnh Stop Limit (STOP_LIMIT)

**Endpoint:** `POST /api/v1/trading/orders`

**Request:**
```json
{
  "memberId": 600896,
  "symbol": "BTC-USDT",
  "direction": "SELL",
  "type": "STOP_LIMIT",
  "price": 48000.00,
  "triggerPrice": 49000.00,
  "volume": 0.1,
  "leverage": 10,
  "reduceOnly": true,
  "timeInForce": "GTC"
}
```

**Lưu ý:**
- `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
- `price` là giá đặt lệnh sau khi lệnh được kích hoạt (bắt buộc)
- Khi giá thị trường chạm mức `triggerPrice`, lệnh sẽ được kích hoạt và chuyển thành lệnh giới hạn với giá `price`

### 3.3. Đặt lệnh Take Profit (TAKE_PROFIT)

**Endpoint:** `POST /api/v1/trading/orders`

**Request:**
```json
{
  "memberId": 600896,
  "symbol": "BTC-USDT",
  "direction": "SELL",
  "type": "TAKE_PROFIT",
  "triggerPrice": 52000.00,
  "volume": 0.1,
  "leverage": 10,
  "reduceOnly": true
}
```

**Lưu ý:**
- `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
- Khi giá thị trường chạm mức `triggerPrice`, lệnh sẽ được kích hoạt và chuyển thành lệnh thị trường
- Thường được sử dụng với `reduceOnly: true` để đảm bảo chỉ đóng vị thế hiện có

### 3.4. Đặt lệnh Trailing Stop (TRAILING_STOP)

**Endpoint:** `POST /api/v1/trading/orders`

**Request:**
```json
{
  "memberId": 600896,
  "symbol": "BTC-USDT",
  "direction": "SELL",
  "type": "TRAILING_STOP",
  "price": 48000.00,
  "callbackRate": 0.05,
  "volume": 0.1,
  "leverage": 10,
  "reduceOnly": true,
  "timeInForce": "GTC"
}
```

**Lưu ý:**
- `callbackRate` là tỷ lệ phần trăm thay đổi giá để kích hoạt lệnh (bắt buộc)
- `price` là giá đặt lệnh sau khi lệnh được kích hoạt (bắt buộc) - lệnh TRAILING_STOP được xử lý như lệnh giới hạn sau khi kích hoạt
- Giá kích hoạt sẽ tự động điều chỉnh theo biến động thị trường
- Đối với lệnh BUY, giá kích hoạt = giá thấp nhất * (1 + callbackRate)
- Đối với lệnh SELL, giá kích hoạt = giá cao nhất * (1 - callbackRate)

## 4. API Kiểm tra lệnh kích hoạt

### 4.1. Kiểm tra lệnh kích hoạt thủ công

**Endpoint:** `POST /api/v1/trading/matching/check-trigger-orders/{symbol}`

**Path Parameters:**
- `symbol`: Symbol của hợp đồng (ví dụ: BTC-USDT)

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "data": true,
  "timestamp": "2023-05-01T12:00:00"
}
```

**Lưu ý:**
- API này sẽ kích hoạt quá trình kiểm tra lệnh trigger cho symbol cụ thể
- Nếu có lệnh thỏa mãn điều kiện kích hoạt, lệnh sẽ được chuyển đổi và thực thi

### 4.2. Lấy danh sách lệnh kích hoạt đang chờ

**Endpoint:** `GET /api/v1/trading/orders/member/{memberId}/symbol/{symbol}/trigger`

**Path Parameters:**
- `memberId`: ID của thành viên
- `symbol`: Symbol của hợp đồng (ví dụ: BTC-USDT)

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "orderId": "ORD600896456789",
      "memberId": 600896,
      "symbol": "BTC-USDT",
      "direction": "SELL",
      "type": "STOP",
      "price": 48000.00,
      "triggerPrice": 49000.00,
      "volume": 0.1,
      "status": "NEW",
      "createTime": "2023-05-01T12:00:00"
    }
  ],
  "timestamp": "2023-05-01T12:00:00"
}
```

## 5. Cơ chế xử lý lệnh kích hoạt

Hệ thống xử lý lệnh kích hoạt thông qua các cơ chế sau:

1. **Scheduler tự động**: `SpecialOrderScheduler` chạy định kỳ (mặc định 1 giây/lần) để kiểm tra và kích hoạt lệnh trigger.
2. **API kiểm tra thủ công**: Người dùng có thể gọi API `check-trigger-orders` để kích hoạt quá trình kiểm tra lệnh trigger.
3. **Xử lý sau giao dịch**: Sau mỗi giao dịch, hệ thống tự động kiểm tra lệnh trigger dựa trên giá mới.

Khi một lệnh trigger được kích hoạt:
1. Trạng thái lệnh được cập nhật thành `TRIGGERED`
2. Một lệnh mới được tạo dựa trên loại lệnh trigger
3. Lệnh mới được gửi đến matching engine để thực thi

## 6. Ví dụ CURL để test

### 6.1. Đặt lệnh Stop (STOP)

```bash
curl -X POST "http://localhost:8080/api/v1/trading/orders" \
  -H "Content-Type: application/json" \
  -d '{
    "memberId": 600896,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "STOP",
    "price": 48000.00,
    "triggerPrice": 49000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": true,
    "timeInForce": "GTC"
  }'
```

### 6.2. Đặt lệnh Trailing Stop (TRAILING_STOP)

```bash
curl -X POST "http://localhost:8080/api/v1/trading/orders" \
  -H "Content-Type: application/json" \
  -d '{
    "memberId": 600896,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "TRAILING_STOP",
    "price": 48000.00,
    "callbackRate": 0.05,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": true,
    "timeInForce": "GTC"
  }'
```

### 6.3. Kiểm tra lệnh kích hoạt thủ công

```bash
curl -X POST "http://localhost:8080/api/v1/trading/matching/check-trigger-orders/BTC-USDT"
```
