# PostgreSQL Operator Deployment Guide

This guide provides instructions for deploying and undeploying the Zalando PostgreSQL Operator using the provided scripts.

## Overview

The PostgreSQL Operator manages PostgreSQL clusters deployed to Kubernetes and automates tasks such as:

- Creating and deleting PostgreSQL clusters
- Resizing PostgreSQL clusters
- Managing users and databases
- Performing PostgreSQL version upgrades
- Managing PostgreSQL configuration
- Applying security updates

## Prerequisites

- Kubernetes cluster (v1.19 or higher), K3s.
- kubectl command-line tool configured to connect to your cluster
- Access to the container registry (ghcr.io)

## Deployment to Worker Nodes

This deployment is configured to deploy the PostgreSQL Operator and PostgreSQL clusters to worker nodes in your Kubernetes cluster. This is achieved through the use of nodeSelector configurations in the deployment manifests.

### Node Labels

The deployment assumes that your worker nodes are labeled with `kubernetes.io/role: worker`. If your cluster uses different labels for worker nodes, you'll need to modify the nodeSelector configurations in the following files:

- `postgres-operator/manifests/postgres-operator.yaml`
- `postgres-operator/ui/manifests/deployment.yaml`
- `postgres-operator/manifests/future-db_complete-postgres-without-loadbalancer.yaml`

## Deployment Scripts

### Interactive Deployment

The `deploy-postgres-operator.sh` script automates the deployment of the PostgreSQL Operator and its components with interactive prompts.

```bash
# Make the script executable (if not already)
chmod +x deploy-postgres-operator.sh

# Run the deployment script
./deploy-postgres-operator.sh
```

### Non-Interactive Deployment

The `deploy-postgres-operator-noninteractive.sh` script provides a non-interactive way to deploy the PostgreSQL Operator, useful for automated deployments or CI/CD pipelines.

```bash
# Make the script executable (if not already)
chmod +x deploy-postgres-operator-noninteractive.sh

# Run the deployment script with options
./deploy-postgres-operator-noninteractive.sh [--with-ui] [--with-sample-cluster]
```

#### Options:
- `--with-ui`: Deploy the PostgreSQL Operator UI
- `--with-sample-cluster`: Create a sample PostgreSQL cluster

### What the Script Does

1. **Checks Prerequisites**:
   - Verifies that kubectl is installed
   - Confirms connection to a Kubernetes cluster

2. **Deploys the PostgreSQL Operator**:
   - Creates the ConfigMap with operator configuration
   - Sets up service account and RBAC resources
   - Deploys the operator
   - Creates the operator API service
   - Waits for the operator to be ready

3. **Optional UI Deployment**:
   - Asks if you want to deploy the PostgreSQL Operator UI
   - If yes, deploys the UI components
   - Provides instructions for accessing the UI

4. **Optional Sample PostgreSQL Cluster**:
   - Asks if you want to create a sample PostgreSQL cluster
   - If yes, creates a cluster using the provided manifest
   - Creates a NodePort service for external access
   - Provides instructions for checking status and connecting to the database

## Accessing the PostgreSQL Operator UI

After deploying the UI, you can access it by port-forwarding:

```bash
kubectl port-forward svc/postgres-operator-ui 8081:8081
```

Then open your browser and navigate to: http://localhost:8081

## Creating a PostgreSQL Cluster Manually

If you choose not to create a sample cluster during the script execution, you can create one later:

```bash
# Create Postgres Cluster
kubectl apply -f postgres-operator/manifests/future-db_complete-postgres-without-loadbalancer.yaml

# Create Service for Postgres Cluster for external access (just for Development) 
kubectl apply -f postgres-operator/manifests/spot-db-service-nodeport.yaml
```

## Connecting to the PostgreSQL Database

After deploying a PostgreSQL cluster, you can connect to it using:

### Connection Details for the Sample Cluster

- **Cluster Name**: cex-future-cluster
- **Database Name**: future_db
- **User**: future_user
- **Port**: 5436

### Getting the Password

```bash
kubectl get secret future-user.cex-future-cluster.credentials.postgresql.acid.zalan.do -o jsonpath='{.data.password}' | base64 -d
```

### Connection String Format

```
*********************************************************/future_db
```

Replace `PASSWORD` with the actual password retrieved from the secret.

## Troubleshooting

### Common Issues

1. **Operator pod not starting**: Check the pod logs for errors
   ```bash
   kubectl logs -l name=postgres-operator
   ```

2. **UI not connecting to the operator**: Ensure the operator service is running
   ```bash
   kubectl get svc postgres-operator
   ```

3. **PostgreSQL cluster not being created**: Check the operator logs for errors
   ```bash
   kubectl logs -l name=postgres-operator
   ```

## Undeployment Scripts

When you need to remove the PostgreSQL Operator and its components from your Kubernetes cluster, you can use the provided undeployment scripts.

### Interactive Undeployment

The `undeploy-postgres-operator.sh` script automates the removal of the PostgreSQL Operator and its components with interactive prompts.

```bash
# Make the script executable (if not already)
chmod +x undeploy-postgres-operator.sh

# Run the undeployment script
./undeploy-postgres-operator.sh
```

The script will ask you if you want to remove:
- The sample PostgreSQL cluster (if it exists)
- The PostgreSQL Operator UI (if it exists)

After that, it will remove the core PostgreSQL Operator components.

### Non-Interactive Undeployment

The `undeploy-postgres-operator-noninteractive.sh` script provides a non-interactive way to remove the PostgreSQL Operator, useful for automated undeployments or CI/CD pipelines.

```bash
# Make the script executable (if not already)
chmod +x undeploy-postgres-operator-noninteractive.sh

# Run the undeployment script with options
./undeploy-postgres-operator-noninteractive.sh [--remove-ui] [--remove-sample-cluster]
```

#### Options:
- `--remove-ui`: Remove the PostgreSQL Operator UI
- `--remove-sample-cluster`: Remove the sample PostgreSQL cluster

### What the Undeployment Script Does

1. **Checks Prerequisites**:
   - Verifies that kubectl is installed
   - Confirms connection to a Kubernetes cluster

2. **Optionally Removes the Sample PostgreSQL Cluster**:
   - Removes the NodePort service
   - Removes the PostgreSQL cluster

3. **Optionally Removes the UI**:
   - Removes all UI components

4. **Removes the PostgreSQL Operator**:
   - Removes the operator API service
   - Removes the operator deployment
   - Removes service account and RBAC resources
   - Removes the ConfigMap

5. **Notes About Persistent Data**:
   - Informs that PersistentVolumeClaims and PersistentVolumes may still exist
   - Suggests manual deletion if complete data removal is desired

## References

- [Zalando PostgreSQL Operator Documentation](https://postgres-operator.readthedocs.io/)
- [Zalando PostgreSQL Operator GitHub Repository](https://github.com/zalando/postgres-operator)
