# K3s DNS Resolution Troubleshooting Guide

## Overview

This document addresses DNS resolution issues in K3s clusters, specifically focusing on scenarios where pods cannot connect to services using service name DNS in an EC2-deployed K3s cluster, while the same functionality works correctly in a local K3s cluster.

## Verification of Local K3s Cluster

We've verified that DNS resolution works correctly on the local K3s cluster:

1. The CoreDNS pod is running properly in the kube-system namespace
2. The kube-dns service is correctly configured with IP **********
3. Pods can resolve service names to their correct IP addresses (verified using ping)

## Common Causes for DNS Resolution Issues in EC2 K3s Clusters

### 1. Network Configuration Issues

EC2 instances have specific networking requirements that might differ from local deployments:

- **VPC DNS Settings**: Ensure that DNS resolution and DNS hostnames are enabled in the VPC
- **Security Groups**: Verify that security groups allow DNS traffic (UDP/TCP port 53) between nodes
- **Network ACLs**: Check if any network ACLs are blocking DNS traffic

### 2. CoreDNS Configuration

The CoreDNS configuration might need adjustments for EC2 environments:

- **Custom Domains**: EC2 might require additional domain configurations
- **Forward Plugin**: The forward plugin might need to be configured differently for EC2

### 3. K3s Installation Options

K3s can be installed with different options that affect DNS resolution:

- **--cluster-dns**: This flag sets the IP address for the DNS service
- **--cluster-domain**: This flag sets the cluster domain
- **--flannel-backend**: Different network backends might affect DNS resolution

### 4. EC2 Instance Metadata Service

EC2 instances use the Instance Metadata Service (IMDS), which might interfere with DNS resolution:

- **IMDS Version**: Ensure you're using IMDSv2 for better security
- **Hostname Resolution**: EC2 instances might have hostname resolution issues

## Solutions

### Solution 1: Verify CoreDNS Configuration

1. Check the CoreDNS ConfigMap on the EC2 cluster:
   ```bash
   kubectl get configmap -n kube-system coredns -o yaml
   ```

2. Compare it with the local cluster's CoreDNS ConfigMap
   
3. If there are differences, update the EC2 cluster's CoreDNS ConfigMap to match the working local configuration:
   ```bash
   kubectl edit configmap -n kube-system coredns
   ```

### Solution 2: Check DNS Service and Pod Status

1. Verify that the CoreDNS pod is running:
   ```bash
   kubectl get pods -n kube-system -l k8s-app=kube-dns
   ```

2. Check the CoreDNS pod logs for errors:
   ```bash
   kubectl logs -n kube-system -l k8s-app=kube-dns
   ```

3. Verify the kube-dns service:
   ```bash
   kubectl get service -n kube-system kube-dns
   ```

### Solution 3: Test DNS Resolution from Within Pods

1. Deploy a debug pod to test DNS resolution:
   ```bash
   kubectl run dnsutils --image=gcr.io/kubernetes-e2e-test-images/dnsutils:1.3 -- sleep 3600
   ```

2. Test DNS resolution from within the pod:
   ```bash
   kubectl exec -it dnsutils -- nslookup kubernetes.default
   kubectl exec -it dnsutils -- nslookup <service-name>
   ```

### Solution 4: Check Network Policies

1. Verify if there are any network policies that might be blocking DNS traffic:
   ```bash
   kubectl get networkpolicies --all-namespaces
   ```

2. If necessary, create a network policy that explicitly allows DNS traffic:
   ```yaml
   apiVersion: networking.k8s.io/v1
   kind: NetworkPolicy
   metadata:
     name: allow-dns
     namespace: default
   spec:
     podSelector: {}
     policyTypes:
     - Egress
     egress:
     - to:
       - namespaceSelector:
           matchLabels:
             kubernetes.io/metadata.name: kube-system
       ports:
       - protocol: UDP
         port: 53
       - protocol: TCP
         port: 53
   ```

### Solution 5: Reinstall K3s with Specific DNS Options

If the above solutions don't work, consider reinstalling K3s on the EC2 instances with specific DNS options:

```bash
curl -sfL https://get.k3s.io | INSTALL_K3S_EXEC="--cluster-dns=********** --cluster-domain=cluster.local" sh -
```

## Specific EC2 Considerations

### EC2 Instance Types

Different EC2 instance types have different networking capabilities:

- **Enhanced Networking**: Ensure your instance type supports enhanced networking
- **Network Bandwidth**: Higher bandwidth instances might perform better

### EC2 Placement Groups

If your K3s cluster spans multiple EC2 instances, consider using placement groups:

- **Cluster Placement Group**: Provides high network throughput and low latency
- **Spread Placement Group**: Distributes instances across hardware to reduce correlated failures

## Conclusion

DNS resolution issues in K3s clusters deployed on EC2 can be caused by various factors. By systematically checking the CoreDNS configuration, network settings, and K3s installation options, you can identify and resolve these issues.

If the above solutions don't resolve the issue, consider reaching out to AWS support or the K3s community for more specific guidance.