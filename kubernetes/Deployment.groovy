pipeline {
    agent any

    environment {
        REGISTRY_URL = '************:5000'
        IMAGE_NAME = '************:5000/futures-core'
        IMAGE_TAG = 'latest'
        MAVEN_SETTINGS = './deploy/settings.xml'
        KUBECONFIG = '/var/jen<PERSON>_home/kubeconfig'
    }

    tools {
        maven 'maven'  // Tên Maven đã cấu hình trong Global Tool Configuration
        dockerTool 'docker'  // Tên Docker đã cấu hình trong Global Tool Configuration
    }

    stages {

        stage('Test Docker') {
            steps {
                script {
                    // Chạy lệnh Docker để kiểm tra
                    sh 'docker info'
                }
            }
        }

        stage('Test kubectl Connection') {
            steps {
                script {
                    sh 'kubectl get nodes'
                    sh 'java -version'
                    sh 'mvn -version'
                }
            }
        }

        stage('Poll SCM') {
            steps {
                checkout scmGit(branches: [[name: 'cleanarchitechture']],
                        extensions: [],
                        userRemoteConfigs: [[credentialsId: 'linhdv2', url: 'https://git.icetea-software.com/its-glyph_cex/cex_be/future-core.git']])
                script {
                    echo "================> Stage poll SCM Clone source done..."
                }
            }
        }

        stage('Package Spring Boot Jar') {
            steps {
                script {
                    echo "Packaging Spring Boot application into JAR..."
                    sh 'mvn clean package -U -s ${MAVEN_SETTINGS} -B -DskipTests'  // Chạy Maven package
                }
            }
        }


        stage('Get Git Commit Hash') {
            steps {
                script {
                    env.COMMIT_HASH = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                    echo "Commit Hash: ${env.COMMIT_HASH}"
                }
            }
        }

        stage('Build image and push to docker registry') {
            steps {
                script {
                    echo "Building Docker image for Spring Boot app..."
                    def imageTag = "${env.COMMIT_HASH}"  // Dùng commit hash làm tag
                    sh "docker build -t ${REGISTRY_URL}/futures-core:${imageTag} ."  // Tạo Docker image với commit hash
                    sh "docker push ${REGISTRY_URL}/futures-core:${imageTag}"  // Đẩy Docker image lên registry
                }
            }
        }

        stage('Deploy to Kubernetes') {
            steps {
                script {
                    try {
                        // Use withVault to fetch secrets (Existing code)
                        withVault(configuration: [vaultCredentialId: "vault",
                                                  vaultUrl         : "http://************:8200"],
                                vaultSecrets: [[path        : 'secret/datasource',
                                                secretValues: [[envVar: 'DATASOURCE_URL', vaultKey: 'DATASOURCE_URL'],
                                                               [envVar: 'DATASOURCE_USERNAME', vaultKey: 'DATASOURCE_USERNAME'],
                                                               [envVar: 'DATASOURCE_PASSWORD', vaultKey: 'DATASOURCE_PASSWORD']]],
                                               [path        : 'secret/redis',
                                                secretValues: [[envVar: 'REDIS_HOST', vaultKey: 'REDIS_HOST'],
                                                               [envVar: 'REDIS_PORT', vaultKey: 'REDIS_PORT'],
                                                               [envVar: 'REDIS_PASSWORD', vaultKey: 'REDIS_PASSWORD']]],
                                               [path        : 'secret/kafka',
                                                secretValues: [[envVar: 'KAFKA_BOOTSTRAP', vaultKey: 'KAFKA_BOOTSTRAP']]],
                                               [path        : 'secret/mongo',
                                                secretValues: [[envVar: 'SPRING_MONGODB_URI', vaultKey: 'SPRING_MONGODB_URI']]],
                                               [path        : 'secret/consul',
                                                secretValues: [[envVar: 'CONSUL_HOST', vaultKey: 'CONSUL_HOST'],
                                                               [envVar: 'CONSUL_PORT', vaultKey: 'CONSUL_PORT'],]]]) {
                            echo "Vault secrets fetched successfully."

                            //Create kubernetes secret to hold the injected values
                            def secretYaml = """
apiVersion: v1
kind: Secret
metadata:
  name: cex-futures-core-secrets
type: Opaque
stringData:
  datasource_url: "${env.DATASOURCE_URL}"
  datasource_username: "${env.DATASOURCE_USERNAME}"
  datasource_password: "${env.DATASOURCE_PASSWORD}"
  redis_host: "${env.REDIS_HOST}"
  redis_port: "${env.REDIS_PORT}"
  redis_password: "${env.REDIS_PASSWORD}"
  kafka_bootstrap: "${env.KAFKA_BOOTSTRAP}"
  spring_mongodb_uri: "${env.SPRING_MONGODB_URI}"
  consul_host: "${env.CONSUL_HOST}"
  consul_port: "${env.CONSUL_PORT}"
"""

                            // Define the Kubernetes deployment YAML with placeholders for secrets
                            def deploymentYaml = """
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cex-futures-core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cex-futures-core
  template:
    metadata:
      labels:
        app: cex-futures-core
    spec:
      containers:
        - name: cex-futures-core
          image: ************:5000/futures-core:${COMMIT_HASH}
          imagePullPolicy: Always
          ports:
            - containerPort: 6060
              protocol: TCP
          env:
            - name: DATASOURCE_URL
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: datasource_url
            - name: DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: datasource_username    
            - name: DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: datasource_password
            - name: REDIS_HOST
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: redis_host    
            - name: REDIS_PORT
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: redis_port
            - name: KAFKA_BOOTSTRAP_SERVERS
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: kafka_bootstrap
            - name: SPRING_MONGODB_URI
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: spring_mongodb_uri
            - name: CONSUL_HOST
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: consul_host
            - name: CONSUL_PORT
              valueFrom:
                secretKeyRef:
                  name: cex-futures-core-secrets
                  key: consul_port
"""

                            def serviceYaml = """
apiVersion: v1
kind: Service
metadata:
  name: cex-futures-core-service
spec:
  selector:
    app: cex-futures-core
  ports:
    - name: port-6060
      protocol: TCP
      port: 6060
      targetPort: 6060
  type: NodePort
"""

                            // Use shell commands to apply YAML files
                            sh """kubectl apply -f - <<EOF
${secretYaml}
EOF"""

                            sh """kubectl apply -f - <<EOF
${deploymentYaml}
EOF"""

                            sh """kubectl apply -f - <<EOF
${serviceYaml}
EOF"""
                        }
                    } catch (Exception e) {
                        // Handle the exception if secrets are missing from Vault
                        if (e instanceof com.datapipe.jenkins.vault.exception.VaultPluginException) {
                            echo "Failed to fetch Vault secrets: ${e.message}"
                        } else {
                            echo "An unexpected error occurred: ${e.message}"
                        }
                    }
                }
            }
        }
    }
}