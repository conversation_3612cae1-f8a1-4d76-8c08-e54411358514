apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: future-api
  labels:
    app: future-api
spec:
  serviceName: future-api
  replicas: 3
  selector:
    matchLabels:
      app: future-api
  template:
    metadata:
      labels:
        app: future-api
    spec:
      containers:
      - name: future-api
        image: future-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: SPRING_REDIS_HOST
          value: "redis-service"
        - name: SPRING_REDIS_PORT
          value: "6379"
        - name: SPRING_KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-service:9092"
        - name: SPRING_DATASOURCE_URL
          value: "**********************************************"
        - name: SPRING_DATASOURCE_USERNAME
          value: "postgres"
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
      volumes:
      - name: config-volume
        configMap:
          name: future-api-config
---
apiVersion: v1
kind: Service
metadata:
  name: future-api-service
spec:
  selector:
    app: future-api
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: future-api-config
data:
  application-production.yml: |
    spring:
      redis:
        host: redis-service
        port: 6379
      kafka:
        bootstrap-servers: kafka-service:9092
        consumer:
          group-id: future-api
      datasource:
        url: **********************************************
        username: postgres
        password: ${SPRING_DATASOURCE_PASSWORD}
    
    logging:
      level:
        root: INFO
        com.icetea.lotus: DEBUG
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: future-api-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: future-api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: future-api-service
            port:
              number: 8080
