#!/bin/bash

# PostgreSQL Operator Non-Interactive Undeployment Script
# This script removes the Zalando PostgreSQL Operator from a Kubernetes cluster without user interaction
# Usage: ./undeploy-postgres-operator-noninteractive.sh [--remove-ui] [--remove-sample-cluster]

set -e

# Parse command line arguments
REMOVE_UI=false
REMOVE_SAMPLE_CLUSTER=false

for arg in "$@"; do
  case $arg in
    --remove-ui)
      REMOVE_UI=true
      shift
      ;;
    --remove-sample-cluster)
      REMOVE_SAMPLE_CLUSTER=true
      shift
      ;;
    *)
      # Unknown option
      ;;
  esac
done

echo "=== PostgreSQL Operator Non-Interactive Undeployment Script ==="
echo ""

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "Error: kubectl is not installed or not in the PATH"
    echo "Please install kubectl and try again"
    exit 1
fi

# Check if connected to a Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    echo "Error: Not connected to a Kubernetes cluster"
    echo "Please configure kubectl to connect to your cluster and try again"
    exit 1
fi

# Remove sample cluster if requested
if [ "$REMOVE_SAMPLE_CLUSTER" = true ]; then
    echo "=== Removing Sample PostgreSQL Cluster ==="

    # Remove the NodePort service (if it exists)
    echo "Removing NodePort service..."
    kubectl delete -f postgres-operator/manifests/spot-db-service-nodeport.yaml --ignore-not-found

    # Remove the PostgreSQL cluster
    echo "Removing PostgreSQL cluster..."
    kubectl delete -f postgres-operator/manifests/future-db_complete-postgres-without-loadbalancer.yaml --ignore-not-found

    # Wait for the PostgreSQL cluster to be removed
    echo "Waiting for PostgreSQL cluster to be removed..."
    # Give it some time to start the deletion process
    sleep 5

    # Check if the cluster still exists and wait for it to be removed
    if kubectl get postgresql bitcello-future-cluster &> /dev/null; then
        echo "Waiting for PostgreSQL cluster to be fully removed (this may take a few minutes)..."
        # Try to wait for the resource to be deleted, but don't fail if the CRD is already gone
        kubectl wait --for=delete postgresql/bitcello-future-cluster --timeout=300s &> /dev/null || true
    fi

    echo "=== Sample PostgreSQL Cluster removal completed ==="
fi

# Remove UI if requested
if [ "$REMOVE_UI" = true ]; then
    echo "=== Removing PostgreSQL Operator UI ==="

    # Remove the UI components
    echo "Removing UI components..."
    kubectl delete -f postgres-operator/ui/manifests/ --ignore-not-found

    # Wait for the UI to be removed
    echo "Waiting for PostgreSQL Operator UI to be removed..."
    # Check if the deployment still exists and wait for it to be removed
    if kubectl get deployment postgres-operator-ui -n postgres-operator &> /dev/null; then
        kubectl wait --for=delete deployment/postgres-operator-ui -n postgres-operator --timeout=60s &> /dev/null || true
    fi

    echo "=== PostgreSQL Operator UI removed successfully ==="
fi

echo "=== Removing PostgreSQL Operator ==="

# Step 1: Remove the operator service
echo "Removing operator API service..."
kubectl delete -f postgres-operator/manifests/api-service.yaml --ignore-not-found

# Step 2: Remove the operator deployment
echo "Removing PostgreSQL Operator deployment..."
kubectl delete -f postgres-operator/manifests/postgres-operator.yaml --ignore-not-found

# Wait for the operator to be removed
if kubectl get deployment postgres-operator -n postgres-operator &> /dev/null; then
    echo "Waiting for PostgreSQL Operator deployment to be removed..."
    kubectl wait --for=delete deployment/postgres-operator -n postgres-operator --timeout=60s &> /dev/null || true
fi

# Step 3: Remove the service account and RBAC resources
echo "Removing service account and RBAC resources..."
kubectl delete -f postgres-operator/manifests/operator-service-account-rbac.yaml --ignore-not-found

# Step 4: Remove the ConfigMap
echo "Removing ConfigMap..."
kubectl delete -f postgres-operator/manifests/configmap.yaml --ignore-not-found

# Step 5: Remove the namespace
echo "Removing namespace..."
kubectl delete -f postgres-operator/manifests/namespace.yaml --ignore-not-found

# Wait for the namespace to be removed
if kubectl get namespace postgres-operator &> /dev/null; then
    echo "Waiting for postgres-operator namespace to be removed..."
    kubectl wait --for=delete namespace/postgres-operator --timeout=120s &> /dev/null || true
fi

echo "=== PostgreSQL Operator removed successfully ==="
echo ""
echo "Note: PersistentVolumeClaims and PersistentVolumes may still exist."
echo "If you want to completely remove all data, you may need to manually delete these resources."
echo ""
echo "=== Undeployment Complete ==="
