# K3s Containerd Snapshotter Fix

## Overview

This repository contains scripts and documentation to fix the following error in K3s clusters:

```
Failed to create pod sandbox: rpc error: code = Unknown desc = failed to start sandbox: failed to create containerd container: failed to stat parent: stat /var/lib/rancher/k3s/agent/containerd/io.containerd.snapshotter.v1.overlayfs/snapshots/1/fs: no such file or directory
```

This error occurs when the containerd snapshotter is unable to find a required directory in the overlay filesystem, preventing pods from being created.

## Files Included

1. `fix-containerd-snapshotter.sh` - Script to fix the containerd snapshotter issue
2. `test-k3s-fix.sh` - Script to test if the fix was successful
3. `k3s-containerd-snapshotter-troubleshooting.md` - Comprehensive troubleshooting guide
4. `README-k3s-containerd-fix.md` - This file

## Quick Start

To fix the containerd snapshotter issue:

1. Copy the `fix-containerd-snapshotter.sh` script to the affected K3s node
2. Make the script executable:
   ```bash
   chmod +x fix-containerd-snapshotter.sh
   ```
3. Run the script with sudo:
   ```bash
   sudo ./fix-containerd-snapshotter.sh
   ```
4. Test the fix:
   ```bash
   chmod +x test-k3s-fix.sh
   sudo ./test-k3s-fix.sh
   ```

> **IMPORTANT**: Always run the fix script **before** the test script. The test script now includes checks to verify that the containerd snapshotter directories exist and have proper permissions, and will fail with an informative error message if the fix has not been applied.

## What the Fix Does

The fix script:
- Detects whether it's running on a K3s server or agent node
- Stops the K3s service
- Creates a backup of the existing containerd directories
- Cleans up the containerd snapshotter state
- Ensures proper permissions
- Restarts the K3s service

## Detailed Documentation

For more detailed information about the issue, its causes, and additional troubleshooting steps, please refer to the comprehensive troubleshooting guide:

[K3s Containerd Snapshotter Troubleshooting Guide](k3s-containerd-snapshotter-troubleshooting.md)

## Compatibility

These scripts have been tested with:
- K3s v1.21.x and above
- Ubuntu 20.04 LTS and above
- CentOS/RHEL 7.x and above

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
