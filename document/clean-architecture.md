# Clean Architecture trong Future-Core Module

## Giới thiệu

Future-Core Module đang được chuyển đổi theo nguyên tắc Clean Architecture để cải thiện tính bảo trì, khả năng kiểm thử và khả năng mở rộng của hệ thống. Tài liệu này mô tả cấu trúc hiện tại của module, tình trạng chuyển đổi, và kế hoạch cho việc hoàn thiện chuyển đổi.

## Nguyên tắc Clean Architecture

Clean Architecture là một mô hình kiến trúc phần mềm được đề xuất bởi Robert <PERSON> (Uncle Bob). Nó dựa trên các nguyên tắc sau:

1. **Độc lập với Framework**: Kiến trúc không phụ thuộc vào sự tồn tại của một thư viện nào. <PERSON><PERSON><PERSON><PERSON> này cho phép bạn sử dụng các framework như công cụ, thay vì phải ràng buộc hệ thống vào các ràng buộc của chúng.

2. **Khả năng kiểm thử**: Logic nghiệp vụ có thể được kiểm thử mà không cần UI, Database, Web Server, hoặc bất kỳ yếu tố bên ngoài nào.

3. **Độc lập với UI**: UI có thể thay đổi dễ dàng, mà không thay đổi phần còn lại của hệ thống. Ví dụ: UI Web có thể được thay thế bằng UI Console mà không thay đổi các quy tắc nghiệp vụ.

4. **Độc lập với Database**: Bạn có thể hoán đổi Oracle hoặc SQL Server với MongoDB, BigTable, CouchDB, hoặc bất kỳ thứ gì khác. Logic nghiệp vụ của bạn không bị ràng buộc với Database.

5. **Độc lập với bất kỳ tác nhân bên ngoài nào**: Trên thực tế, logic nghiệp vụ của bạn đơn giản không biết gì về thế giới bên ngoài.

## Cấu trúc hiện tại của Future-Core Module

Future-Core Module hiện đang trong quá trình chuyển đổi sang Clean Architecture. Một số phần đã được chuyển đổi hoàn toàn, trong khi một số phần khác vẫn đang sử dụng cấu trúc cũ.

### 1. Domain Layer (Core)

Đây là lớp trung tâm của ứng dụng, chứa các entities, value objects, và domain services.

#### Đã chuyển đổi:

- **Entities**: `Order` (từ `ContractOrder`), `Position` (từ `ContractPosition`), `Trade` (từ `ContractTrade`), `FundingRate` (từ `ContractFundingRate`), `Contract` (từ `ContractCoin`), `Wallet` (từ `ContractWallet`), `Transaction` (từ `ContractTransaction`), `Fee` (từ `ContractFee`), `Insurance` (từ `ContractInsurance`), `Liquidation` (từ `ContractLiquidation`), `ClawbackPosition` (từ `ContractClawbackPosition`)
- **Value Objects**: `Money`, `OrderId`, `PositionId`, `Symbol`, `TradeId`, `FundingRateId`, `ContractId`, `WalletId`, `TransactionId`, `FeeId`, `InsuranceId`, `LiquidationId`, `ClawbackPositionId`
- **Domain Services**: `PositionService`, `FundingService`, `LiquidationService`, `OrderMatchingService`, `PriceService`, `OrderBook`, `PositionTransferService`, `SpecialOrderService`, `HybridPricingService`, `OrderMatchingEngineService` (từ `ContractCoinMatch` và `ContractCoinMatchFactory`), `OrderMatchingExtensionService` (từ `ContractCoinMatchExtension`), `ContractService` (từ `ContractCoinService`)
- **Domain Repositories**: `OrderRepository`, `PositionRepository`, `TradeRepository`, `ContractRepository`, `FundingRateRepository`

#### Chưa chuyển đổi:

- **Services cũ**: (không còn)
- **Components cũ**: (không còn)
- **Repositories cũ**: (không còn)

### 2. Application Layer

Lớp này chứa các use cases của ứng dụng, điều phối luồng dữ liệu đến và đi từ entities, và hướng dẫn các entities thực hiện logic nghiệp vụ.

#### Đã chuyển đổi:

- **Use Cases**: `PlaceOrderUseCase` (từ `ContractOrderService`), `ManagePositionUseCase` (từ `ContractPositionService`), `ManagePriceUseCase`, `ManageFundingUseCase` (từ `ContractFundingRateService`), `ManageLiquidationUseCase` (từ `ContractLiquidationService`), `ManageTransactionUseCase` (từ `ContractTransactionService`), `ManageFeeUseCase` (từ `ContractFeeService`), `ManageInsuranceUseCase` (từ `ContractInsuranceService`), `ManageClawbackPositionUseCase` (từ `ClawbackSystem`), `ManagePositionTransferUseCase` (từ `ContractPositionTransferService`), `ManageSpecialOrderUseCase` (từ `ContractSpecialOrderService`), `ManageHybridPricingUseCase` (từ `HybridPricingService`), `ManageOrderMatchingUseCase`, `ManageContractUseCase` (từ `ContractCoinService`)
- **DTOs**: `OrderDto`, `PositionDto`, `TransactionDto`, `FeeDto`, `FundingRateDto`, `InsuranceDto`, `LiquidationDto`, `ClawbackPositionDto`, `OrderBookDto`, `ContractDto`, `PlaceOrderCommand`, `ClosePositionCommand`, `AdjustLeverageCommand`, `AdjustMarginCommand`, `FundingSettlementCommand`, `FundingSettlementResult`, `LiquidationCommand`, `LiquidationResult`
- **Services**: `PlaceOrderService` (từ `ContractOrderService`), `ManagePositionService` (từ `ContractPositionService`), `ManagePriceService`, `ManageFundingService` (từ `ContractFundingRateService`), `ManageLiquidationService` (từ `ContractLiquidationService`), `ManageTransactionService` (từ `ContractTransactionService`), `ManageFeeService` (từ `ContractFeeService`), `ManageInsuranceService` (từ `ContractInsuranceService`), `ManageClawbackPositionService` (từ `ClawbackSystem`), `ManagePositionTransferService` (từ `ContractPositionTransferService`), `ManageSpecialOrderService` (từ `ContractSpecialOrderService`), `ManageHybridPricingService` (từ `HybridPricingService`), `ManageOrderMatchingService`, `ManageContractService` (từ `ContractCoinService`)
- **Ports**: `OrderPersistencePort`, `PositionPersistencePort`, `FundingRatePersistencePort`, `ContractPersistencePort`, `TradePersistencePort`, `WalletPersistencePort`, `TransactionPersistencePort`, `FeePersistencePort`, `InsurancePersistencePort`, `LiquidationPersistencePort`, `ClawbackPositionPersistencePort`

#### Chưa chuyển đổi:

- **Services cũ**: (không còn)

### 3. Infrastructure Layer

Lớp này chứa các adapter để tương tác với thế giới bên ngoài, như database, message queue, API, v.v.

#### Đã chuyển đổi:

- **Persistence Adapters**: `OrderPersistenceAdapter` (từ `ContractOrderRepository`), `PositionPersistenceAdapter` (từ `ContractPositionRepository`), `FundingRatePersistenceAdapter`, `ContractPersistenceAdapter` (từ `ContractCoinRepository`), `TradePersistenceAdapter` (từ `ContractTradeRepository`), `WalletPersistenceAdapter` (từ `ContractWalletRepository`), `TransactionPersistenceAdapter` (từ `ContractTransactionRepository`), `FeePersistenceAdapter` (từ `ContractFeeRepository`), `InsurancePersistenceAdapter` (từ `ContractInsuranceRepository`), `LiquidationPersistenceAdapter` (từ `ContractLiquidationRepository`), `ClawbackPositionPersistenceAdapter` (từ `ContractClawbackRepository`)
- **JPA/Mongo Entities**: `OrderJpaEntity` (từ `ContractOrderJpaEntity`), `PositionJpaEntity`, `TradeJpaEntity`, `FundingRateJpaEntity`, `ContractJpaEntity`, `WalletJpaEntity`, `TransactionJpaEntity`, `FeeJpaEntity`, `InsuranceJpaEntity`, `LiquidationJpaEntity`, `ClawbackPositionJpaEntity`
- **JPA/Mongo Repositories**: `OrderJpaRepository` (từ `ContractOrderJpaRepository`), `PositionJpaRepository`, `TradeJpaRepository`, `FundingRateJpaRepository`, `ContractJpaRepository`, `WalletJpaRepository`, `TransactionJpaRepository`, `FeeJpaRepository`, `InsuranceJpaRepository`, `LiquidationJpaRepository`, `ClawbackPositionJpaRepository`
- **Controllers**: `OrderController` (từ `ContractOrderController`), `PositionController` (từ `ContractPositionController`), `PriceController`, `FundingController`, `LiquidationController` (từ `ContractLiquidationController`), `TransactionController`, `FeeController`, `InsuranceController`, `ClawbackPositionController`, `ContractController` (từ `ContractCoinController`)
- **Mappers**: `OrderPersistenceMapper`, `PositionPersistenceMapper`, `TradePersistenceMapper`, `FundingRatePersistenceMapper`, `ContractPersistenceMapper`, `WalletPersistenceMapper`, `TransactionPersistenceMapper`, `TransactionDtoMapper`, `FeePersistenceMapper`, `FeeDtoMapper`, `InsurancePersistenceMapper`, `InsuranceDtoMapper`, `LiquidationPersistenceMapper`, `LiquidationDtoMapper`, `ClawbackPositionPersistenceMapper`, `ClawbackPositionDtoMapper`, `ContractDtoMapper`

#### Chưa chuyển đổi:

- **JPA Entities cũ**: (không còn)
- **JPA Repositories cũ**: (không còn)
- **Components cũ**: (không còn)

### 4. Presentation Layer

Lớp này chứa các thành phần liên quan đến giao diện người dùng, như controllers, views, v.v.

#### Đã chuyển đổi:

- **Controllers**: `OrderController` (từ `ContractOrderController`), `PositionController` (từ `ContractPositionController`), `PriceController`, `FundingController` (từ `ContractFundingRateController`), `LiquidationController`, `TransactionController`, `FeeController`, `InsuranceController`, `ClawbackPositionController`, `PositionTransferController`, `SpecialOrderController`, `HybridPricingController`, `OrderMatchingController`, `ContractController` (từ `ContractCoinController`)
- **Schedulers**: `LiquidationScheduler` (từ `ContractLiquidationScheduler`), `FundingScheduler` (từ `ContractFundingScheduler`), `SpecialOrderScheduler` (từ `ContractSpecialOrderService`), `ContractSynchronizationScheduler` (từ `ContractCoinMatchSynchronizer`)
- **Request Objects**: `PlaceOrderRequest`, `ClosePositionRequest`, `AdjustLeverageRequest`, `AdjustMarginRequest`, `ChangeMarginModeRequest`, `CalculateFeeRequest`, `FundingSettlementRequest`, `CreateInsuranceRequest`, `UpdateInsuranceRequest`, `CreateLiquidationRequest`, `UpdateInsuranceAmountRequest`, `CreateClawbackPositionRequest`, `TransferPositionRequest`, `TransferPartialPositionRequest`, `MergePositionsRequest`, `CreateTimeOrderRequest`, `CancelTimeOrderRequest`, `CreateOCOOrderRequest`, `CancelOCOOrderRequest`, `CalculateHybridIndexPriceRequest`, `UpdateMarkPriceRequest`
- **Response Objects**: `ApiResponse`

## Luồng dữ liệu

Luồng dữ liệu trong Clean Architecture đi từ bên ngoài vào trong:

1. **Request**: Người dùng gửi request đến controller.
2. **Controller**: Controller chuyển đổi request thành command và gọi use case.
3. **Use Case**: Use case xử lý command, gọi domain service và repository để thực hiện logic nghiệp vụ.
4. **Domain Service**: Domain service thực hiện logic nghiệp vụ phức tạp liên quan đến nhiều entities.
5. **Repository**: Repository lưu trữ và truy xuất entities từ database.
6. **Response**: Kết quả được trả về cho người dùng thông qua controller.

## Ví dụ

### Đặt lệnh mới

1. Người dùng gửi request đến `OrderController.placeOrder()` với thông tin lệnh.
2. Controller chuyển đổi `PlaceOrderRequest` thành `PlaceOrderCommand` và gọi `PlaceOrderUseCase.placeOrder()`.
3. `PlaceOrderService` xử lý command, tạo `Order` mới và gọi `OrderPersistencePort.save()` để lưu lệnh.
4. `OrderPersistenceAdapter` chuyển đổi `Order` thành `OrderJpaEntity` và gọi `OrderJpaRepository.save()` để lưu vào database.
5. Kết quả được trả về cho người dùng thông qua controller.

### Đóng vị thế

1. Người dùng gửi request đến `PositionController.closePosition()` với thông tin đóng vị thế.
2. Controller chuyển đổi `ClosePositionRequest` thành `ClosePositionCommand` và gọi `ManagePositionUseCase.closePosition()`.
3. `ManagePositionService` xử lý command, tìm vị thế hiện tại và gọi `PositionService.closePosition()` để đóng vị thế.
4. `PositionServiceImpl` thực hiện logic đóng vị thế và trả về vị thế đã được cập nhật.
5. `ManagePositionService` gọi `PositionPersistencePort.save()` để lưu vị thế đã được cập nhật.
6. `PositionPersistenceAdapter` chuyển đổi `Position` thành `PositionJpaEntity` và gọi `PositionJpaRepository.save()` để lưu vào database.
7. Kết quả được trả về cho người dùng thông qua controller.

### Tính toán giá đánh dấu

1. Hệ thống gọi `PriceService.calculateMarkPrice()` để tính toán giá đánh dấu mới.
2. `PriceServiceImpl` lấy giá chỉ số hiện tại và tỷ lệ tài trợ, sau đó tính toán giá đánh dấu.
3. Giá đánh dấu mới được cập nhật vào cache và lưu vào lịch sử.
4. Giá đánh dấu mới được sử dụng để tính toán lợi nhuận không thực hiện của các vị thế.

## Kế hoạch chuyển đổi

Để hoàn thiện việc chuyển đổi sang Clean Architecture, cần thực hiện các bước sau:

### 1. Chuyển đổi các entity cũ

1. **Tạo domain entities mới**:
   - Tạo các domain entities mới trong package `com.icetea.lotus.core.domain.entity` cho các entity còn lại.
   - Đảm bảo các domain entities không phụ thuộc vào bất kỳ framework nào (JPA, Spring, v.v.).

2. **Tạo value objects**:
   - Tạo các value objects trong package `com.icetea.lotus.core.domain.valueobject` cho các thuộc tính có ý nghĩa nghiệp vụ.

3. **Tạo JPA entities**:
   - Tạo các JPA entities trong package `com.icetea.lotus.infrastructure.persistence.entity` để ánh xạ với các bảng trong database.

### 2. Chuyển đổi các repository cũ

1. **Tạo domain repository interfaces**:
   - Tạo các domain repository interfaces trong package `com.icetea.lotus.core.domain.repository` cho các repository còn lại.
   - Đảm bảo các domain repository interfaces không phụ thuộc vào bất kỳ framework nào.

2. **Tạo persistence ports**:
   - Tạo các persistence ports trong package `com.icetea.lotus.application.port.output` để định nghĩa cách application layer tương tác với infrastructure layer.

3. **Tạo persistence adapters**:
   - Tạo các persistence adapters trong package `com.icetea.lotus.infrastructure.persistence.adapter` để triển khai các persistence ports.
   - Sử dụng các JPA repositories để truy cập database.

### 3. Chuyển đổi các service cũ

1. **Tạo domain services**:
   - Tạo các domain services trong package `com.icetea.lotus.core.domain.service` cho các service còn lại.
   - Đảm bảo các domain services chỉ phụ thuộc vào các domain entities và domain repositories.

2. **Tạo use cases**:
   - Tạo các use case interfaces trong package `com.icetea.lotus.application.port.input` để định nghĩa các hành động mà người dùng có thể thực hiện.

3. **Tạo application services**:
   - Tạo các application services trong package `com.icetea.lotus.application.service` để triển khai các use cases.
   - Sử dụng các domain services và persistence ports để thực hiện logic nghiệp vụ.

### 4. Chuyển đổi các component khác

1. **Tạo các adapter khác**:
   - Tạo các adapter khác trong package `com.icetea.lotus.infrastructure` để tương tác với các hệ thống bên ngoài (Kafka, Redis, v.v.).

2. **Tạo các controller**:
   - Tạo các controller trong package `com.icetea.lotus.infrastructure.api.controller` để xử lý các request từ người dùng.
   - Sử dụng các use cases để thực hiện logic nghiệp vụ.

## Lợi ích của Clean Architecture

1. **Tách biệt các mối quan tâm**: Mỗi lớp có trách nhiệm riêng biệt, giúp code dễ hiểu và bảo trì hơn.
2. **Dễ dàng kiểm thử**: Domain logic có thể được kiểm thử mà không cần database, UI, hoặc bất kỳ yếu tố bên ngoài nào.
3. **Dễ dàng thay đổi**: Thay đổi framework, database, hoặc UI không ảnh hưởng đến logic nghiệp vụ.
4. **Dễ dàng mở rộng**: Thêm tính năng mới không ảnh hưởng đến các tính năng hiện có.

## Test

Các test case được tổ chức theo cấu trúc clean architecture:

1. **Domain Layer Tests**:
   - Test cho domain entities, value objects và domain services
   - Ví dụ: `OrderMatchingEngineServiceTest`, `IndexPriceServiceTest`, `MarkPriceServiceTest`

2. **Application Layer Tests**:
   - Test cho use cases và application services
   - Ví dụ: `ManageOrderMatchingServiceTest`, `ManagePriceServiceTest`

3. **Infrastructure Layer Tests**:
   - Test cho persistence adapters và controllers
   - Ví dụ: `OrderMatchingControllerTest`, `OrderBookMapperTest`

Các test case sử dụng JUnit 5 và Mockito để mock các dependencies.

## Tối ưu hóa hiệu suất

Để tối ưu hóa hiệu suất của hệ thống, các kỹ thuật sau đã được áp dụng:

1. **Caching**: Sử dụng cache để lưu trữ các giá trị thường xuyên truy cập, giúp giảm số lần truy vấn cơ sở dữ liệu và tính toán.
   - Ví dụ: `OrderMatchingEngineServiceImpl` sử dụng cache cho giá đánh dấu, giá chỉ số và giá giao dịch cuối cùng.

2. **Xử lý đồng thời**: Sử dụng thread pool để xử lý các tác vụ đồng thời, giúp tăng hiệu suất của hệ thống.
   - Ví dụ: `OrderMatchingEngineServiceImpl` sử dụng thread pool để khởi tạo các matching engine mới.

3. **Scheduled tasks**: Sử dụng scheduled tasks để thực hiện các tác vụ định kỳ, giúp giảm tải cho hệ thống.
   - Ví dụ: `OrderMatchingEngineServiceImpl` sử dụng scheduled tasks để đồng bộ hóa matching engine với hợp đồng, kiểm tra lệnh chờ và thanh lý.

4. **Concurrent collections**: Sử dụng các collection hỗ trợ xử lý đồng thời, giúp tăng hiệu suất và đảm bảo tính nhất quán của dữ liệu.
   - Ví dụ: `OrderMatchingEngineServiceImpl` sử dụng `ConcurrentHashMap` để lưu trữ các matching engine và cache.

5. **Lazy initialization**: Khởi tạo các đối tượng chỉ khi cần thiết, giúp giảm tải cho hệ thống.
   - Ví dụ: `OrderMatchingEngineServiceImpl` chỉ khởi tạo matching engine khi cần thiết.

## Hoàn thành chuyển đổi

Việc chuyển đổi sang Clean Architecture đã hoàn thành. Tất cả các component cũ đã được chuyển đổi sang các component mới tuân thủ nguyên tắc của Clean Architecture. Các component cũ đã được xóa bỏ khỏi codebase, bao gồm:

1. **ContractOrder**: Đã được chuyển đổi hoàn toàn sang `Order`
2. **ContractOrderJpaEntity**: Đã được chuyển đổi hoàn toàn sang `OrderJpaEntity`
3. **ContractOrderJpaRepository**: Đã được chuyển đổi hoàn toàn sang `OrderJpaRepository`
4. **ContractOrderProcessorComponent**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng
5. **ContractOrderService**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng
6. **HybridPricingService**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng
7. **ContractConditionalOrderService**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng

8. **ContractCoinRepository**: Đã được chuyển đổi hoàn toàn sang các persistence adapters tương ứng
9. **ContractOrderDetail**: Đã được chuyển đổi hoàn toàn sang `OrderDetail` (domain entity) và `OrderDetailJpaEntity` (infrastructure entity)
10. **ContractOrderDetailService**: Đã được chuyển đổi hoàn toàn sang `OrderDetailService` (domain service) và `ManageOrderDetailService` (application service)
11. **ContractOrderDetailRepository**: Đã được chuyển đổi hoàn toàn sang `OrderDetailRepository` (domain repository) và `OrderDetailPersistenceAdapter` (infrastructure adapter)
12. **ContractPostOnlyService**: Đã được chuyển đổi hoàn toàn sang `PostOnlyService` (domain service)
13. **ContractPositionOrderService**: Đã được chuyển đổi hoàn toàn sang `PositionOrderService` (domain service)
14. **ContractMarketDataService**: Đã được chuyển đổi hoàn toàn sang `MarketDataService` (domain service)
15. **CircuitBreakerSystem**: Đã được chuyển đổi hoàn toàn sang `CircuitBreakerService` (domain service)

Các component cũ đã được chuyển đổi thành công và xóa bỏ khỏi codebase, bao gồm:

1. **ContractCoinMatch**: Đã được chuyển đổi thành `OrderMatchingEngineService`
2. **ContractCoinMatchExtension**: Đã được chuyển đổi thành `OrderMatchingExtensionService`
3. **ContractCoinMatchFactory**: Đã được tích hợp vào `OrderMatchingEngineServiceImpl`
4. **ContractMergeOrder**: Đã được tích hợp vào `OrderBook`
5. **ContractTradePlate**: Đã được tích hợp vào `OrderBook`
6. **ImpliedMatchingEngine**: Đã được tích hợp vào `OrderMatchingEngineServiceImpl`
7. **MatchingAlgorithm**: Đã được chuyển đổi thành `MatchingAlgorithm` trong package `com.icetea.lotus.core.domain.entity`
8. **ContractLiquidationScheduler**: Đã được chuyển đổi thành `LiquidationScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`
9. **ContractFundingScheduler**: Đã được chuyển đổi thành `FundingScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`
10. **ContractSpecialOrderService**: Đã được chuyển đổi thành `SpecialOrderScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`
11. **ContractCoinMatchSynchronizer**: Đã được chuyển đổi thành `ContractSynchronizationScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`
12. **ContractCoinService**: Đã được chuyển đổi thành `ContractService` (domain service) và `ManageContractService` (application service)
13. **ContractOrderService**: Đã được chuyển đổi thành `OrderService` (domain service) và `PlaceOrderService` (application service)
14. **ContractPositionService**: Đã được chuyển đổi thành `PositionService` (domain service) và `ManagePositionService` (application service)
15. **ContractTradeService**: Đã được chuyển đổi thành `TradeService` (domain service) và `ManageTradeService` (application service)
16. **ContractWalletService**: Đã được chuyển đổi thành `WalletService` (domain service) và `ManageWalletService` (application service)
17. **ContractFeeService**: Đã được chuyển đổi thành `FeeService` (domain service) và `ManageFeeService` (application service)
18. **ContractFundingService**: Đã được chuyển đổi thành `FundingService` (domain service) và `ManageFundingService` (application service)
19. **ContractInsuranceFundService**: Đã được chuyển đổi thành `InsuranceService` (domain service) và `ManageInsuranceService` (application service)
20. **ContractLiquidationService**: Đã được chuyển đổi thành `LiquidationService` (domain service) và `ManageLiquidationService` (application service)
21. **ContractOrderDetailService**: Đã được chuyển đổi thành `OrderDetailService` (domain service)
22. **ContractPostOnlyService**: Đã được chuyển đổi thành `PostOnlyService` (domain service)
23. **ContractPositionOrderService**: Đã được chuyển đổi thành `PositionOrderService` (domain service)
24. **ContractMarketDataService**: Đã được chuyển đổi thành `MarketDataService` (domain service)
25. **CircuitBreakerSystem**: Đã được chuyển đổi thành `CircuitBreakerService` (domain service)

## Kết luận

Clean Architecture giúp tạo ra một hệ thống linh hoạt, dễ bảo trì và dễ kiểm thử. Nó tách biệt logic nghiệp vụ khỏi các yếu tố bên ngoài, giúp hệ thống dễ dàng thích ứng với các thay đổi trong tương lai. Future-Core Module đã hoàn thành quá trình chuyển đổi sang Clean Architecture, và các kỹ thuật tối ưu hóa hiệu suất đã được áp dụng để cải thiện hiệu suất của hệ thống.

Hệ thống hiện tại đã được tổ chức theo các nguyên tắc của Clean Architecture, với các lớp rõ ràng và các ranh giới được xác định rõ ràng. Điều này giúp hệ thống dễ dàng thích ứng với các thay đổi trong tương lai, đồng thời giảm thiểu sự phụ thuộc vào các framework và thư viện bên ngoài.
