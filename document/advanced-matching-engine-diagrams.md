# Biểu đồ chi tiết cho Matching Engine tiên tiến

## 1. <PERSON>ến trúc tổng thể

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[API Gateway]
    B -->|Load Balancing| C[Order Router]
    C -->|Phân <PERSON>h<PERSON>i lệnh| D[Shard Manager]
    D -->|<PERSON><PERSON><PERSON> tuyến lệnh| E[Symbol Shards]
    E -->|Xử lý lệnh| F[Lock-Free Matching Engine]
    F -->|Cập nhật| G[Distributed Order Book]
    F -->|Tạo giao dịch| H[Trade Processor]
    H -->|Thông báo| I[Kafka]
    I -->|Cập nhật| J[Client]
    I -->|Cập nhật| K[Risk Management]
    I -->|Cập nhật| L[Settlement]
```

## 2. C<PERSON> chế Lock-Free

```mermaid
sequenceDiagram
    participant Client
    participant OrderRouter
    participant MatchingEngine
    participant OrderBook
    
    Client->>OrderRouter: Đặt lệnh
    OrderRouter->>MatchingEngine: <PERSON><PERSON><PERSON><PERSON> l<PERSON>
    
    activate MatchingEngine
    MatchingEngine->>OrderBook: L<PERSON>y snapshot hiện tại
    OrderBook-->>MatchingEngine: Trả về snapshot
    
    MatchingEngine->>MatchingEngine: Tạo bản sao snapshot
    MatchingEngine->>MatchingEngine: Khớp lệnh trên bản sao
    
    MatchingEngine->>OrderBook: compareAndSet(oldSnapshot, newSnapshot)
    
    alt CAS thành công
        OrderBook-->>MatchingEngine: true
        MatchingEngine-->>OrderRouter: Trả về kết quả
    else CAS thất bại
        OrderBook-->>MatchingEngine: false
        MatchingEngine->>OrderBook: Lấy snapshot mới
        OrderBook-->>MatchingEngine: Trả về snapshot mới
        MatchingEngine->>MatchingEngine: Thử lại khớp lệnh
    end
    
    deactivate MatchingEngine
    
    OrderRouter-->>Client: Trả về kết quả
```

## 3. Cơ chế Load Balancing

### 3.1. Consistent Hashing

```mermaid
graph TD
    A[Symbol] -->|Hash| B[Hash Ring]
    B -->|Lookup| C[Node Selection]
    C -->|Route to| D[Matching Engine Node]
    
    subgraph Hash Ring
        N1[Node 1]
        N2[Node 2]
        N3[Node 3]
        N4[Node 4]
        
        N1 -->|Virtual Node| V1[Virtual Node 1-1]
        N1 -->|Virtual Node| V2[Virtual Node 1-2]
        N2 -->|Virtual Node| V3[Virtual Node 2-1]
        N2 -->|Virtual Node| V4[Virtual Node 2-2]
        N3 -->|Virtual Node| V5[Virtual Node 3-1]
        N3 -->|Virtual Node| V6[Virtual Node 3-2]
        N4 -->|Virtual Node| V7[Virtual Node 4-1]
        N4 -->|Virtual Node| V8[Virtual Node 4-2]
    end
```

### 3.2. Power of Two Choices

```mermaid
sequenceDiagram
    participant Client
    participant LoadBalancer
    participant Node1
    participant Node2
    participant Node3
    participant Node4
    
    Client->>LoadBalancer: Đặt lệnh
    
    activate LoadBalancer
    LoadBalancer->>LoadBalancer: Chọn ngẫu nhiên 2 node (Node1, Node3)
    LoadBalancer->>Node1: Kiểm tra tải
    Node1-->>LoadBalancer: Tải = 10
    LoadBalancer->>Node3: Kiểm tra tải
    Node3-->>LoadBalancer: Tải = 5
    
    LoadBalancer->>Node3: Chọn node có tải thấp hơn
    LoadBalancer->>Node3: Tăng tải
    LoadBalancer-->>Client: Trả về Node3
    deactivate LoadBalancer
    
    Client->>Node3: Gửi lệnh
    Node3-->>Client: Trả về kết quả
    Node3->>LoadBalancer: Giảm tải
```

## 4. Cơ chế Sharding

### 4.1. Symbol-Based Sharding

```mermaid
graph TD
    A[Symbols] -->|Hash| B[Shard Distribution]
    
    subgraph Shard 1
        S1[BTC-USDT]
        S2[ETH-USDT]
    end
    
    subgraph Shard 2
        S3[BNB-USDT]
        S4[SOL-USDT]
    end
    
    subgraph Shard 3
        S5[ADA-USDT]
        S6[DOT-USDT]
    end
    
    subgraph Shard 4
        S7[XRP-USDT]
        S8[DOGE-USDT]
    end
    
    B -->|Route| Shard 1
    B -->|Route| Shard 2
    B -->|Route| Shard 3
    B -->|Route| Shard 4
```

### 4.2. Price Range Sharding

```mermaid
graph TD
    A[Order Book] -->|Segment by Price Range| B[Price Segments]
    
    subgraph "Price Range 0-1000"
        PR1[Price Level 100]
        PR2[Price Level 500]
        PR3[Price Level 800]
    end
    
    subgraph "Price Range 1000-2000"
        PR4[Price Level 1200]
        PR5[Price Level 1500]
        PR6[Price Level 1800]
    end
    
    subgraph "Price Range 2000-3000"
        PR7[Price Level 2100]
        PR8[Price Level 2400]
        PR9[Price Level 2700]
    end
    
    B -->|Access| C[Order at Price 1500]
    C -->|Lookup| PR5
```

## 5. Xử lý trường hợp xấu nhất của ConcurrentSkipListMap

### 5.1. Phân đoạn theo khoảng giá

```mermaid
graph TD
    A[ConcurrentSkipListMap] -->|Segment| B[Multiple Smaller Maps]
    
    subgraph "Price Range 0-1000"
        M1[ConcurrentSkipListMap 1]
    end
    
    subgraph "Price Range 1000-2000"
        M2[ConcurrentSkipListMap 2]
    end
    
    subgraph "Price Range 2000-3000"
        M3[ConcurrentSkipListMap 3]
    end
    
    subgraph "Price Range 3000-4000"
        M4[ConcurrentSkipListMap 4]
    end
    
    B -->|Reduce Size| C[Avoid Worst Case]
```

### 5.2. Tái cấu trúc định kỳ

```mermaid
sequenceDiagram
    participant Scheduler
    participant OrderBook
    participant TempMap
    
    Scheduler->>OrderBook: Trigger restructure
    
    activate OrderBook
    OrderBook->>TempMap: Create new map
    OrderBook->>OrderBook: Copy data to new map
    OrderBook->>TempMap: Remove empty price levels
    OrderBook->>OrderBook: Clear old map
    OrderBook->>OrderBook: Replace with new map
    deactivate OrderBook
    
    Scheduler->>Scheduler: Schedule next restructure
```

## 6. Luồng xử lý lệnh

```mermaid
sequenceDiagram
    participant Client
    participant API as API Gateway
    participant Router as Order Router
    participant Shard as Symbol Shard
    participant Engine as Lock-Free Matching Engine
    participant Book as Distributed Order Book
    participant Kafka
    
    Client->>API: Đặt lệnh
    API->>Router: Chuyển lệnh
    
    Router->>Shard: Định tuyến lệnh theo symbol
    Shard->>Engine: Chọn node và gửi lệnh
    
    activate Engine
    Engine->>Book: Lấy snapshot hiện tại
    Book-->>Engine: Trả về snapshot
    
    Engine->>Engine: Tạo bản sao snapshot
    Engine->>Engine: Khớp lệnh trên bản sao
    
    Engine->>Book: compareAndSet(oldSnapshot, newSnapshot)
    
    alt CAS thành công
        Book-->>Engine: true
        Engine->>Kafka: Gửi thông báo giao dịch
        Engine-->>Shard: Trả về kết quả
    else CAS thất bại
        Book-->>Engine: false
        Engine->>Book: Lấy snapshot mới
        Book-->>Engine: Trả về snapshot mới
        Engine->>Engine: Thử lại khớp lệnh
    end
    
    deactivate Engine
    
    Shard-->>Router: Trả về kết quả
    Router-->>API: Trả về kết quả
    API-->>Client: Trả về kết quả
    
    Kafka-->>Client: Thông báo giao dịch
```

## 7. Mô hình dữ liệu

```mermaid
classDiagram
    class OrderBookSnapshot {
        -ImmutableNavigableMap~Money, ImmutableList~Order~~ buyOrders
        -ImmutableNavigableMap~Money, ImmutableList~Order~~ sellOrders
        +copy(): OrderBookSnapshot
        +withAddedBuyOrder(Money, Order): OrderBookSnapshot
        +withAddedSellOrder(Money, Order): OrderBookSnapshot
    }
    
    class OrderBookSegment {
        -PriceRange range
        -NavigableMap~Money, List~Order~~ orders
        +addOrder(Order): void
        +getOrdersAtPrice(Money): List~Order~
        +isEmpty(): boolean
        +size(): int
    }
    
    class PriceRange {
        -Money lowerBound
        -Money upperBound
        +contains(Money): boolean
        +forPrice(Money): PriceRange
    }
    
    class DistributedOrderBook {
        -Map~PriceRange, OrderBookSegment~ segments
        +addOrder(Order): void
        +getOrdersAtPrice(Money): List~Order~
    }
    
    class LockFreeMatchingEngine {
        -Symbol symbol
        -AtomicReference~OrderBookSnapshot~ orderBookRef
        +processOrder(Order): List~Trade~
        -matchOrder(Order, OrderBookSnapshot): OrderBookResult
    }
    
    OrderBookSnapshot --> "2" ImmutableNavigableMap
    DistributedOrderBook --> "*" OrderBookSegment
    OrderBookSegment --> "1" PriceRange
    OrderBookSegment --> "1" NavigableMap
    LockFreeMatchingEngine --> "1" AtomicReference
```

## 8. Cơ chế mở rộng động

```mermaid
sequenceDiagram
    participant Monitor as Load Monitor
    participant Manager as Shard Manager
    participant Shards as Symbol Shards
    participant Router as Order Router
    
    Monitor->>Monitor: Kiểm tra tải
    
    alt Tải cao
        Monitor->>Manager: Thông báo quá tải
        Manager->>Manager: Tăng số lượng shard
        Manager->>Shards: Tạo shard mới
        Manager->>Manager: Phân phối lại symbols
        Manager->>Router: Cập nhật bảng định tuyến
    else Tải thấp
        Monitor->>Manager: Thông báo tải thấp
        Manager->>Manager: Giảm số lượng shard
        Manager->>Shards: Gộp các shard
        Manager->>Manager: Phân phối lại symbols
        Manager->>Router: Cập nhật bảng định tuyến
    end
```
