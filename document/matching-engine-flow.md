# Luồng hoạt động của Matching Engine trong Future-Core

## 1. Tổng quan về Matching Engine

Matching Engine là thành phần trung tâm của hệ thống giao dịch hợp đồng tương lai, chịu trách nhiệm khớp các lệnh mua và bán để tạo ra các giao dịch. Matching Engine trong Future-Core được thiết kế để hỗ trợ nhiều thuật toán khớp lệnh khác nhau và xử lý các loại lệnh đặc biệt như lệnh dừng lỗ, lệ<PERSON> chốt lời, và lệnh thanh lý.

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[Future-API]
    B -->|Gửi lệnh| C[Future-Core]
    C -->|Xử lý lệnh| D[Matching Engine]
    D -->|Tạo giao dịch| E[Trade]
    D -->|Cập nhật| F[Order Book]
    D -->|Kiểm tra| G[Trigger Orders]
    D -->|Kiểm tra| H[Liquidation]
    E -->|Thông báo| I[Kafka]
    I -->|Cập nhật| J[Client]
    I -->|Cập nhật| K[Risk Management]
    I -->|Cập nhật| L[Settlement]
```

## 2. Các thành phần chính

### 2.1. OrderMatchingEngineService

`OrderMatchingEngineService` là domain service chính cho matching engine, thay thế cho các thành phần cũ như `ContractCoinMatch` và `ContractCoinMatchFactory`. Service này cung cấp các chức năng như đặt lệnh, hủy lệnh, cập nhật giá, và kiểm tra lệnh chờ.

```mermaid
classDiagram
    class OrderMatchingEngineService {
        +placeOrder(Order order): List~Trade~
        +cancelOrder(Order order): boolean
        +cancelOrder(OrderId orderId, Symbol symbol): boolean
        +cancelAllOrders(Long memberId, Symbol symbol): List~Order~
        +pauseTrading(Symbol symbol): void
        +resumeTrading(Symbol symbol): void
        +updateMarkPrice(Symbol symbol, Money markPrice): void
        +updateIndexPrice(Symbol symbol, Money indexPrice): void
        +getMarkPrice(Symbol symbol): Money
        +getIndexPrice(Symbol symbol): Money
        +getLastPrice(Symbol symbol): Money
        +checkTriggerOrders(Symbol symbol): void
        +checkLiquidations(Symbol symbol): void
        +liquidatePosition(Position position): List~Trade~
        +synchronizeWithContracts(): void
        +initialize(Symbol symbol): void
        +shutdown(Symbol symbol): void
        +getOrderBook(Symbol symbol): OrderBook
        +setMatchingAlgorithm(Symbol symbol, MatchingAlgorithm algorithm): void
        +getMatchingAlgorithm(Symbol symbol): MatchingAlgorithm
        +createLiquidationOrder(Position position, Money liquidationPrice): List~Trade~
        +createADLOrder(Position position, Money volume): List~Trade~
        +addPositionToCheck(Position position): void
        +removePositionToCheck(PositionId positionId): void
    }
```

### 2.2. MatchingEngine

`MatchingEngine` là lớp nội bộ của `OrderMatchingEngineServiceImpl` chịu trách nhiệm khớp lệnh theo các thuật toán khác nhau.

```mermaid
classDiagram
    class MatchingEngine {
        -Symbol symbol
        -Contract contract
        -boolean tradingHalt
        -Money markPrice
        -Money indexPrice
        -Money lastPrice
        -MatchingAlgorithm matchingAlgorithm
        -Map~OrderId, Order~ orders
        -Map~OrderDirection, Map~Money, List~Order~~~ ordersByPrice
        -List~Order~ triggerOrders
        -Map~Long, Position~ positionsToCheck
        +matchOrder(Order order): List~Trade~
        -matchOrderFIFO(Order order): List~Trade~
        -matchOrderProRata(Order order): List~Trade~
        -matchOrderHybrid(Order order): List~Trade~
        +cancelOrder(Order order): boolean
        +cancelOrder(OrderId orderId): boolean
        +cancelAllOrders(Long memberId): List~Order~
        +addOrder(Order order): void
        +removeOrder(OrderId orderId): void
        +getOrderBook(): OrderBook
        +isTradingHalt(): boolean
        +setTradingHalt(boolean tradingHalt): void
        +getMarkPrice(): Money
        +setMarkPrice(Money markPrice): void
        +getIndexPrice(): Money
        +setIndexPrice(Money indexPrice): void
        +getLastPrice(): Money
        +setLastPrice(Money lastPrice): void
        +getMatchingAlgorithm(): MatchingAlgorithm
        +setMatchingAlgorithm(MatchingAlgorithm matchingAlgorithm): void
        +addTriggerOrder(Order order): void
        +removeTriggerOrder(Order order): void
        +getTriggerOrders(): List~Order~
        +checkTriggerOrders(): void
        +addPositionToCheck(Position position): void
        +removePositionToCheck(PositionId positionId): void
        +getPositionsToCheck(): List~Position~
        +checkLiquidations(): void
    }
```

### 2.3. OptimizedMatchingEngine

`OptimizedMatchingEngine` là phiên bản tối ưu hóa của `MatchingEngine`, được thiết kế để xử lý khối lượng lệnh lớn với hiệu suất cao hơn.

```mermaid
classDiagram
    class OptimizedMatchingEngine {
        -Symbol symbol
        -Contract contract
        -boolean tradingEnabled
        -Money markPrice
        -Money indexPrice
        -Money lastPrice
        -MatchingAlgorithm matchingAlgorithm
        -Map~String, MutableOrder~ orders
        -NavigableMap~Money, List~MutableOrder~~ buyOrders
        -NavigableMap~Money, List~MutableOrder~~ sellOrders
        -List~MutableOrder~ triggerOrders
        -Map~String, Position~ positionsToCheck
        -ReentrantReadWriteLock lock
        +matchOrder(Order order): List~Trade~
        -matchOrderFIFO(Order order): List~Trade~
        -matchOrderProRata(Order order): List~Trade~
        -matchOrderHybrid(Order order): List~Trade~
        -matchWithOppositeOrders(MutableOrder mutableOrder, BigDecimal remainingVolume, List~Trade~ trades): BigDecimal
        -matchOrdersAtPrice(MutableOrder mutableOrder, List~MutableOrder~ ordersAtPrice, Money price, BigDecimal remainingVolume, List~Trade~ trades): BigDecimal
        -matchOrdersProRata(MutableOrder mutableOrder, List~MutableOrder~ ordersAtPrice, Money price, BigDecimal remainingVolume, List~Trade~ trades): BigDecimal
        -matchOrdersHybrid(MutableOrder mutableOrder, List~MutableOrder~ ordersAtPrice, Map~String, BigDecimal~ orderToRemainingVolumeMap, Money price, BigDecimal remainingVolume, List~Trade~ trades): BigDecimal
        -updateOrderStatus(MutableOrder mutableOrder, BigDecimal remainingVolume): void
        -updateLastPrice(List~Trade~ trades): void
        -isPriceAcceptable(MutableOrder mutableOrder, Money price): boolean
        +cancelOrder(Order order): boolean
        +cancelOrder(OrderId orderId): boolean
        +cancelAllOrders(Long memberId): List~Order~
        +addOrder(Order order): void
        +removeOrder(OrderId orderId): void
        +getOrderBook(): OrderBook
        +isTradingEnabled(): boolean
        +setTradingEnabled(boolean tradingEnabled): void
        +getMarkPrice(): Money
        +setMarkPrice(Money markPrice): void
        +getIndexPrice(): Money
        +setIndexPrice(Money indexPrice): void
        +getLastPrice(): Money
        +setLastPrice(Money lastPrice): void
        +getMatchingAlgorithm(): MatchingAlgorithm
        +setMatchingAlgorithm(MatchingAlgorithm matchingAlgorithm): void
        +addTriggerOrder(Order order): void
        +removeTriggerOrder(Order order): void
        +getTriggerOrders(): List~Order~
        +checkTriggerOrders(): void
        +addPositionToCheck(Position position): void
        +removePositionToCheck(PositionId positionId): void
        +getPositionsToCheck(): List~Position~
        +checkLiquidations(): void
    }
```

### 2.4. OrderBook

`OrderBook` là domain entity đại diện cho sổ lệnh, chứa danh sách các lệnh mua và bán. OrderBook không lưu trữ các lệnh riêng lẻ mà chỉ lưu trữ thông tin tổng hợp về mỗi mức giá.

```mermaid
classDiagram
    class OrderBook {
        -Symbol symbol
        -List~OrderBookEntry~ bids
        -List~OrderBookEntry~ asks
        -Money lastPrice
        -Money markPrice
        -Money indexPrice
        +getBestPrice(OrderDirection direction): Money
        +getBestBid(): Money
        +getBestAsk(): Money
        +getSpread(): Money
        +getMidPrice(): Money
        +getDepth(int depth): OrderBook
    }

    class OrderBookEntry {
        -Money price
        -Money volume
        -int orderCount
    }

    OrderBook *-- OrderBookEntry
```

Trong khi đó, cấu trúc dữ liệu thực tế của sổ lệnh trong matching engine phức tạp hơn nhiều:

```mermaid
classDiagram
    class OptimizedMatchingEngine {
        -NavigableMap~Money, List~MutableOrder~~ buyOrders
        -NavigableMap~Money, List~MutableOrder~~ sellOrders
        +getOrderBook(): OrderBook
    }

    class NavigableMap~K,V~ {
        <<interface>>
        +firstEntry(): Entry~K,V~
        +lastEntry(): Entry~K,V~
        +higherEntry(K key): Entry~K,V~
        +lowerEntry(K key): Entry~K,V~
    }

    class ConcurrentSkipListMap~K,V~ {
        +put(K key, V value): V
        +remove(K key): V
        +get(K key): V
    }

    class ArrayList~E~ {
        +add(E e): boolean
        +remove(E e): boolean
        +get(int index): E
    }

    OptimizedMatchingEngine --> NavigableMap : uses
    NavigableMap <|-- ConcurrentSkipListMap : implements
    ConcurrentSkipListMap --> ArrayList : contains
```

## 3. Luồng xử lý lệnh

### 3.1. Đặt lệnh mới

```mermaid
sequenceDiagram
    participant Client
    participant API as Future-API
    participant Service as OrderMatchingEngineService
    participant Engine as MatchingEngine
    participant DB as Database

    Client->>API: Đặt lệnh mới
    API->>Service: placeOrder(order)
    Service->>DB: Lưu lệnh vào cơ sở dữ liệu
    Service->>Engine: Lấy matching engine cho symbol
    Service->>Engine: matchOrder(order)

    alt Thuật toán FIFO
        Engine->>Engine: matchOrderFIFO(order)
    else Thuật toán Pro-Rata
        Engine->>Engine: matchOrderProRata(order)
    else Thuật toán Hybrid
        Engine->>Engine: matchOrderHybrid(order)
    end

    Engine-->>Service: Trả về danh sách giao dịch
    Service->>DB: Lưu các giao dịch vào cơ sở dữ liệu
    Service-->>API: Trả về danh sách giao dịch
    API-->>Client: Trả về kết quả đặt lệnh
```

### 3.2. Khớp lệnh theo thuật toán FIFO

```mermaid
flowchart TD
    A[Bắt đầu] --> B{Lệnh hợp lệ?}
    B -- Không --> C[Trả về danh sách trống]
    B -- Có --> D[Tạo danh sách giao dịch]
    D --> E[Tính toán khối lượng còn lại]
    E --> F{Lệnh là lệnh mua?}
    F -- Có --> G[Lấy danh sách lệnh bán]
    F -- Không --> H[Lấy danh sách lệnh mua]
    G --> I[Sắp xếp lệnh bán theo giá tăng dần]
    H --> J[Sắp xếp lệnh mua theo giá giảm dần]
    I --> K{Còn lệnh để khớp?}
    J --> K
    K -- Không --> L[Cập nhật trạng thái lệnh]
    K -- Có --> M{Kiểm tra điều kiện giá}
    M -- Không thỏa mãn --> L
    M -- Thỏa mãn --> N[Tính toán khối lượng khớp]
    N --> O[Tạo giao dịch mới]
    O --> P[Thêm giao dịch vào danh sách]
    P --> Q[Cập nhật khối lượng còn lại]
    Q --> K
    L --> R[Cập nhật giá giao dịch cuối cùng]
    R --> S[Kết thúc]
```

### 3.3. Khớp lệnh theo thuật toán Pro-Rata

```mermaid
flowchart TD
    A[Bắt đầu] --> B{Lệnh hợp lệ?}
    B -- Không --> C[Trả về danh sách trống]
    B -- Có --> D[Tạo danh sách giao dịch]
    D --> E[Tính toán khối lượng còn lại]
    E --> F{Lệnh là lệnh mua?}
    F -- Có --> G[Lấy danh sách lệnh bán]
    F -- Không --> H[Lấy danh sách lệnh mua]
    G --> I[Sắp xếp lệnh bán theo giá tăng dần]
    H --> J[Sắp xếp lệnh mua theo giá giảm dần]
    I --> K[Tính tổng khối lượng ở mức giá tốt nhất]
    J --> K
    K --> L{Còn lệnh để khớp?}
    L -- Không --> M[Cập nhật trạng thái lệnh]
    L -- Có --> N{Kiểm tra điều kiện giá}
    N -- Không thỏa mãn --> M
    N -- Thỏa mãn --> O[Tính toán tỷ lệ khối lượng cho mỗi lệnh]
    O --> P[Tính toán khối lượng khớp cho mỗi lệnh]
    P --> Q[Tạo giao dịch mới cho mỗi lệnh]
    Q --> R[Thêm giao dịch vào danh sách]
    R --> S[Cập nhật khối lượng còn lại]
    S --> L
    M --> T[Cập nhật giá giao dịch cuối cùng]
    T --> U[Kết thúc]
```

### 3.4. Khớp lệnh theo thuật toán Hybrid

```mermaid
flowchart TD
    A[Bắt đầu] --> B{Lệnh hợp lệ?}
    B -- Không --> C[Trả về danh sách trống]
    B -- Có --> D[Tạo danh sách giao dịch]
    D --> E[Tính toán khối lượng còn lại]
    E --> F{Lệnh là lệnh mua?}
    F -- Có --> G[Lấy danh sách lệnh bán]
    F -- Không --> H[Lấy danh sách lệnh mua]
    G --> I[Sắp xếp lệnh bán theo giá tăng dần]
    H --> J[Sắp xếp lệnh mua theo giá giảm dần]
    I --> K[Tính toán khối lượng FIFO và Pro-Rata]
    J --> K
    K --> L[Khớp lệnh theo FIFO cho phần đầu tiên]
    L --> M[Khớp lệnh theo Pro-Rata cho phần còn lại]
    M --> N[Cập nhật trạng thái lệnh]
    N --> O[Cập nhật giá giao dịch cuối cùng]
    O --> P[Kết thúc]
```

## 4. Các loại lệnh và trạng thái

### 4.1. OrderType

```mermaid
classDiagram
    class OrderType {
        <<enumeration>>
        LIMIT
        MARKET
        STOP
        STOP_LIMIT
        STOP_MARKET
        TAKE_PROFIT_MARKET
        TAKE_PROFIT_LIMIT
        POST_ONLY
        FILL_OR_KILL
        IMMEDIATE_OR_CANCEL
        TIME
        LIQUIDATION
        ADL
        +isMarketOrder(): boolean
        +isLimitOrder(): boolean
        +isStopOrder(): boolean
        +isSpecialOrder(): boolean
    }
```

### 4.2. OrderDirection

```mermaid
classDiagram
    class OrderDirection {
        <<enumeration>>
        BUY
        SELL
        +opposite(): OrderDirection
        +isBuy(): boolean
        +isSell(): boolean
    }
```

### 4.3. OrderStatus

```mermaid
classDiagram
    class OrderStatus {
        <<enumeration>>
        NEW
        PARTIALLY_FILLED
        FILLED
        CANCELED
        REJECTED
        TRIGGERED
        PENDING
        EXPIRED
        TRADING
    }
```

### 4.4. MatchingAlgorithm

```mermaid
classDiagram
    class MatchingAlgorithm {
        <<enumeration>>
        FIFO
        PRO_RATA
        HYBRID
    }
```

## 5. Xử lý lệnh đặc biệt

### 5.1. Lệnh chờ (Trigger Orders)

```mermaid
sequenceDiagram
    participant Service as OrderMatchingEngineService
    participant Engine as MatchingEngine
    participant Special as SpecialOrderService

    Service->>Engine: checkTriggerOrders(symbol)
    Engine->>Engine: Lấy danh sách lệnh chờ
    Engine->>Engine: Lấy giá hiện tại (markPrice, indexPrice, lastPrice)

    loop Cho mỗi lệnh chờ
        Engine->>Special: checkTriggerOrder(order, markPrice, indexPrice, lastPrice)
        Special->>Special: Kiểm tra điều kiện kích hoạt

        alt Điều kiện kích hoạt thỏa mãn
            Special->>Special: convertTriggerOrder(order)
            Special->>Engine: Đặt lệnh mới
            Engine->>Engine: matchOrder(newOrder)
            Engine->>Engine: Xóa lệnh chờ
        end
    end
```

### 5.2. Thanh lý vị thế (Liquidation)

```mermaid
sequenceDiagram
    participant Service as OrderMatchingEngineService
    participant Engine as MatchingEngine
    participant Position as PositionService

    Service->>Engine: checkLiquidations(symbol)
    Engine->>Engine: Lấy danh sách vị thế cần kiểm tra
    Engine->>Engine: Lấy giá hiện tại (markPrice)

    loop Cho mỗi vị thế
        Engine->>Position: needsLiquidation(position, markPrice, contract)

        alt Cần thanh lý
            Engine->>Service: createLiquidationOrder(position, liquidationPrice)
            Service->>Engine: matchOrder(liquidationOrder)
        end
    end
```

## 6. Tối ưu hóa hiệu suất

### 6.1. Cấu trúc dữ liệu của Order Book

#### 6.1.1. Trong OptimizedMatchingEngine

OptimizedMatchingEngine sử dụng `ConcurrentSkipListMap` (một triển khai của `NavigableMap`) để lưu trữ sổ lệnh:

```java
// Sổ lệnh
private final NavigableMap<Money, List<MutableOrder>> buyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
private final NavigableMap<Money, List<MutableOrder>> sellOrders = new ConcurrentSkipListMap<>();
```

**Ưu điểm của ConcurrentSkipListMap:**

1. **Thread-safe**: `ConcurrentSkipListMap` là thread-safe, cho phép nhiều luồng đọc và ghi đồng thời mà không cần khóa bổ sung.
2. **Hiệu suất cao**: Cung cấp hiệu suất O(log n) cho các thao tác thêm, xóa và tìm kiếm.
3. **Sắp xếp tự động**: Tự động sắp xếp các khóa, giúp dễ dàng truy cập đến mức giá tốt nhất.
4. **Hỗ trợ các thao tác dải**: Cung cấp các phương thức như `firstEntry()`, `lastEntry()`, `headMap()`, `tailMap()` để truy cập các phần của map.

**Cách sử dụng:**

- **Lệnh mua (Buy Orders)**: Sử dụng `Comparator.reverseOrder()` để sắp xếp theo giá giảm dần, giúp lệnh mua có giá cao nhất được ưu tiên.
- **Lệnh bán (Sell Orders)**: Sắp xếp theo giá tăng dần (mặc định), giúp lệnh bán có giá thấp nhất được ưu tiên.

Mỗi mức giá chứa một danh sách các lệnh (`List<MutableOrder>`), được sắp xếp theo thời gian đặt lệnh (FIFO) hoặc theo khối lượng (Pro-Rata) tùy thuộc vào thuật toán khớp lệnh.

#### 6.1.2. Trong MatchingEngine (non-optimized)

MatchingEngine sử dụng cấu trúc dữ liệu phức tạp hơn:

```java
// Sổ lệnh
private final Map<OrderId, Order> orders = new ConcurrentHashMap<>();
private final Map<OrderDirection, Map<Money, List<Order>>> ordersByPrice = new EnumMap<>(OrderDirection.class);
```

**Cấu trúc này bao gồm:**

1. **Map lưu trữ tất cả các lệnh theo ID**: `Map<OrderId, Order>` cho phép truy cập nhanh chóng đến một lệnh cụ thể theo ID.
2. **Map lưu trữ các lệnh theo hướng và giá**: `Map<OrderDirection, Map<Money, List<Order>>>` tổ chức các lệnh theo hướng (mua/bán) và sau đó theo giá.

#### 6.1.3. Cấu trúc OrderBook

OrderBook là một entity đại diện cho sổ lệnh, được trả về cho client:

```java
@Getter
@AllArgsConstructor
@Builder
public class OrderBook {
    private final Symbol symbol;
    private final List<OrderBookEntry> bids;
    private final List<OrderBookEntry> asks;
    private final Money lastPrice;
    private final Money markPrice;
    private final Money indexPrice;

    @Getter
    @AllArgsConstructor
    @Builder
    public static class OrderBookEntry {
        private final Money price;
        private final Money volume;
        private final int orderCount;
    }
}
```

OrderBook chỉ chứa thông tin tổng hợp về mỗi mức giá (giá, tổng khối lượng, số lượng lệnh) mà không chứa thông tin chi tiết về từng lệnh riêng lẻ.

#### 6.1.4. Các cấu trúc dữ liệu hỗ trợ khác

- **ReentrantReadWriteLock**: Đảm bảo thread-safety khi nhiều luồng truy cập vào matching engine.
- **Map<String, MutableOrder>**: Lưu trữ các lệnh chờ (stop orders) theo ID.
- **ConcurrentHashMap**: Sử dụng cho các map cần thread-safe.

### 6.2. Thuật toán khớp lệnh

Matching Engine hỗ trợ ba thuật toán khớp lệnh:

1. **FIFO (First In First Out)**: Lệnh vào trước được xử lý trước.
2. **Pro-Rata**: Khối lượng khớp lệnh được phân bổ theo tỷ lệ khối lượng của các lệnh đối ứng.
3. **Hybrid**: Kết hợp FIFO và Pro-Rata, một phần khối lượng được khớp theo FIFO và phần còn lại được khớp theo Pro-Rata.

## 7. Kết luận

Matching Engine trong Future-Core là một thành phần quan trọng của hệ thống giao dịch hợp đồng tương lai, cung cấp các chức năng khớp lệnh hiệu quả và linh hoạt. Với việc hỗ trợ nhiều thuật toán khớp lệnh và xử lý các loại lệnh đặc biệt, Matching Engine đáp ứng được các yêu cầu phức tạp của thị trường hợp đồng tương lai.
