# Cập nhật Matching Engine

## 1. Tổng quan

Tài liệu này mô tả việc cập nhật Matching Engine trong module future-core để phù hợp với diagram trong tài liệu thiết kế. Thay vì sử dụng nhiều loại matching engine kh<PERSON><PERSON> nhau, chúng ta đã mở rộng `OptimizedMatchingEngine` để kết hợp các tính năng tốt nhất của các loại matching engine hiện có. Chúng ta cũng đã loại bỏ `UnifiedMatchingEngine` vì nó không còn cần thiết.

## 2. Lý do cập nhật

Trước đây, `OrderMatchingEngineServiceImpl` hỗ trợ 3 loại matching engine:
1. DistributedLockingMatchingEngine
2. LockFreeMatchingEngine
3. OptimizedMatchingEngine

Việc này không phù hợp với diagram trong tài liệu thi<PERSON><PERSON> kế, theo đó chỉ có một Matching Engine duy nhất. <PERSON><PERSON><PERSON><PERSON> ra, việc có nhiều loại matching engine cũng gây ra một số vấn đề:
- Phức tạp trong việc triển khai và bảo trì
- Khó hiểu đối với người mới
- Khó đảm bảo tất cả các loại matching engine đều có cùng chức năng và hành vi

## 3. Giải pháp

### 3.1. Mở rộng OptimizedMatchingEngine

Thay vì tạo một matching engine mới, chúng ta đã mở rộng `OptimizedMatchingEngine` để kết hợp các tính năng tốt nhất của cả 3 loại matching engine hiện có:
- Sử dụng Red-Black Tree để lưu trữ order book (từ OptimizedMatchingEngine)
- Sử dụng cơ chế lock-free để tránh nghẽn cổ chai (từ LockFreeMatchingEngine)
- Sử dụng distributed lock để đảm bảo tính nhất quán trong môi trường phân tán (từ DistributedLockingMatchingEngine)

### 3.2. Cập nhật OptimizedMatchingEngine

Chúng ta đã cập nhật `OptimizedMatchingEngine` với các thay đổi sau:
- Thêm các thuộc tính cần thiết cho distributed locking và sharding
- Thêm phương thức `processOrder()` để xử lý lệnh với distributed lock
- Cập nhật các phương thức khác để sử dụng distributed lock
- Thêm các phương thức hỗ trợ cho distributed locking và sharding

### 3.3. Cập nhật OrderMatchingEngineServiceImpl

Chúng ta đã cập nhật `OrderMatchingEngineServiceImpl` để chỉ sử dụng `OptimizedMatchingEngine`:
- Thay thế các tham chiếu đến 3 loại matching engine cũ bằng tham chiếu đến `OptimizedMatchingEngine`
- Đơn giản hóa các phương thức để chỉ gọi các phương thức tương ứng trong `OptimizedMatchingEngine`
- Loại bỏ các đoạn code không cần thiết liên quan đến việc chọn loại matching engine

## 4. Luồng xử lý mới

Luồng xử lý mới phù hợp với diagram trong tài liệu thiết kế:

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[Future-API]
    B -->|Gửi lệnh| C[Future-Core]
    C -->|Xử lý lệnh| D[Matching Engine]
    D -->|Tạo giao dịch| E[Trade]
    D -->|Cập nhật| F[Order Book]
    D -->|Kiểm tra| G[Trigger Orders]
    D -->|Kiểm tra| H[Liquidation]
    E -->|Thông báo| I[Kafka]
    I -->|Cập nhật| J[Client]
    I -->|Cập nhật| K[Risk Management]
    I -->|Cập nhật| L[Settlement]
```

### 4.1. Đặt lệnh mới

```java
@Override
public List<Trade> placeOrder(Order order) {
    if (order == null || order.getSymbol() == null) {
        log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
        return Collections.emptyList();
    }

    Symbol symbol = order.getSymbol();

    // Lưu lệnh vào cơ sở dữ liệu
    Order savedOrder = orderRepository.save(order);

    try {
        // Xử lý lệnh với OptimizedMatchingEngine
        List<Trade> trades = optimizedMatchingEngine.processOrder(savedOrder);

        // Lưu các giao dịch vào cơ sở dữ liệu
        if (trades != null && !trades.isEmpty()) {
            tradeRepository.saveAll(trades);
        }

        return trades != null ? trades : Collections.emptyList();
    } catch (Exception e) {
        log.error(LogMessages.OrderMatching.ERROR_PLACE_ORDER(), order.getId(), symbol, e);
        return Collections.emptyList();
    }
}
```

### 4.2. Kiểm tra lệnh chờ

```java
@Override
public void checkTriggerOrders(Symbol symbol) {
    if (symbol == null) {
        log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
        return;
    }

    try {
        // Kiểm tra lệnh chờ trên Unified Matching Engine
        unifiedMatchingEngine.checkTriggerOrders(symbol);
        log.debug(LogMessages.OrderMatching.DEBUG_TRIGGER_ORDERS_CHECKED(), symbol);
    } catch (Exception e) {
        log.error(LogMessages.OrderMatching.ERROR_CHECK_TRIGGER_ORDERS(), symbol, e);
    }
}
```

### 4.3. Kiểm tra thanh lý

```java
@Override
public void checkLiquidations(Symbol symbol) {
    if (symbol == null) {
        log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
        return;
    }

    try {
        // Kiểm tra thanh lý trên Unified Matching Engine
        unifiedMatchingEngine.checkLiquidations(symbol);
        log.debug(LogMessages.OrderMatching.DEBUG_LIQUIDATIONS_CHECKED(), symbol);
    } catch (Exception e) {
        log.error(LogMessages.OrderMatching.ERROR_CHECK_LIQUIDATIONS(), symbol, e);
    }
}
```

## 5. Lợi ích

Việc cập nhật này mang lại một số lợi ích:
- Phù hợp với diagram trong tài liệu thiết kế
- Đơn giản hóa code, dễ hiểu và bảo trì hơn
- Giảm thiểu khả năng xảy ra lỗi do sự không nhất quán giữa các loại matching engine
- Dễ dàng mở rộng và cải tiến trong tương lai
- Tận dụng code hiện có của OptimizedMatchingEngine, giảm thiểu trùng lặp code

## 6. Loại bỏ UnifiedMatchingEngine

Chúng ta đã loại bỏ `UnifiedMatchingEngine` vì nó không còn được sử dụng trong codebase. Thay vào đó, chúng ta đã mở rộng `OptimizedMatchingEngine` để kết hợp các tính năng tốt nhất của cả 3 loại matching engine hiện có. Điều này giúp giảm thiểu sự phức tạp và trùng lặp trong codebase.

## 7. Kết luận

Việc cập nhật Matching Engine trong module future-core đã được hoàn thành thành công. Chúng ta đã mở rộng `OptimizedMatchingEngine` để kết hợp các tính năng tốt nhất của cả 3 loại matching engine hiện có, phù hợp với diagram trong tài liệu thiết kế. Chúng ta cũng đã loại bỏ `UnifiedMatchingEngine` vì nó không còn cần thiết. Điều này giúp đơn giản hóa code, dễ hiểu và bảo trì hơn, đồng thời giảm thiểu khả năng xảy ra lỗi.
