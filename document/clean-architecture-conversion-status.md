# Tình trạng chuyển đổi sang Clean Architecture

## Tóm tắt

Tài liệu này mô tả tình trạng hiện tại của quá trình chuyển đổi sang Clean Architecture trong future-core module. Nó liệt kê các thành phần đã được chuyển đổi và các thành phần còn lại cần được chuyển đổi.

## <PERSON><PERSON><PERSON> thành phần đã được chuyển đổi

### 1. Domain Layer (Core)

- **Entities**: `Order` (từ `ContractOrder`), `Position` (từ `ContractPosition`), `Trade` (từ `ContractTrade`), `FundingRate` (từ `ContractFundingRate`), `Contract` (từ `ContractCoin`), `Wallet` (từ `ContractWallet`), `Transaction` (từ `ContractTransaction`), `Fee` (từ `ContractFee`), `Insurance` (từ `ContractInsurance`), `Liquidation` (từ `ContractLiquidation`), `ClawbackPosition` (từ `ContractClawbackPosition`), `OrderReport` (từ `ContractOrderReport`)
- **Value Objects**: `Money`, `OrderId`, `PositionId`, `Symbol`, `TradeId`, `FundingRateId`, `ContractId`, `WalletId`, `TransactionId`, `FeeId`, `InsuranceId`, `LiquidationId`, `ClawbackPositionId`, `OrderReportId`
- **Domain Services**: `PositionService`, `FundingService`, `LiquidationService`, `OrderMatchingService`, `PriceService`, `OrderBook`, `PositionTransferService`, `SpecialOrderService`, `HybridPricingService`, `OrderMatchingEngineService` (từ `ContractCoinMatch` và `ContractCoinMatchFactory`), `OrderMatchingExtensionService` (từ `ContractCoinMatchExtension`), `ContractService` (từ `ContractCoinService`), `PriceManagementService` (hợp nhất từ `PriceService`, `IndexPriceService`, `MarkPriceService`)
- **Domain Repositories**: `OrderRepository`, `PositionRepository`, `TradeRepository`, `ContractRepository`, `FundingRateRepository`

### 2. Application Layer

- **Use Cases**: `PlaceOrderUseCase` (từ `ContractOrderService`), `ManagePositionUseCase` (từ `ContractPositionService`), `ManagePriceUseCase`, `ManageFundingUseCase` (từ `ContractFundingRateService`), `ManageLiquidationUseCase` (từ `ContractLiquidationService`), `ManageTransactionUseCase` (từ `ContractTransactionService`), `ManageFeeUseCase` (từ `ContractFeeService`), `ManageInsuranceUseCase` (từ `ContractInsuranceService`), `ManageClawbackPositionUseCase` (từ `ClawbackSystem`), `ManageOrderReportUseCase` (từ `ContractOrderReportService`), `ManagePositionTransferUseCase` (từ `ContractPositionTransferService`), `ManageSpecialOrderUseCase` (từ `ContractSpecialOrderService`), `ManageHybridPricingUseCase` (từ `HybridPricingService`), `ManageOrderMatchingUseCase`, `ManageContractUseCase` (từ `ContractCoinService`)
- **DTOs**: `OrderDto`, `PositionDto`, `TransactionDto`, `FeeDto`, `FundingRateDto`, `InsuranceDto`, `LiquidationDto`, `ClawbackPositionDto`, `OrderReportDto`, `OrderBookDto`, `ContractDto`, `PlaceOrderCommand`, `ClosePositionCommand`, `AdjustLeverageCommand`, `AdjustMarginCommand`, `FundingSettlementCommand`, `FundingSettlementResult`, `LiquidationCommand`, `LiquidationResult`
- **Services**: `PlaceOrderService` (từ `ContractOrderService`), `ManagePositionService` (từ `ContractPositionService`), `ManagePriceService`, `ManageFundingService` (từ `ContractFundingRateService`), `ManageLiquidationService` (từ `ContractLiquidationService`), `ManageTransactionService` (từ `ContractTransactionService`), `ManageFeeService` (từ `ContractFeeService`), `ManageInsuranceService` (từ `ContractInsuranceService`), `ManageClawbackPositionService` (từ `ClawbackSystem`), `ManageOrderReportService` (từ `ContractOrderReportService`), `ManagePositionTransferService` (từ `ContractPositionTransferService`), `ManageSpecialOrderService` (từ `ContractSpecialOrderService`), `ManageHybridPricingService` (từ `HybridPricingService`), `ManageOrderMatchingService`, `ManageContractService` (từ `ContractCoinService`)
- **Ports**: `OrderPersistencePort`, `PositionPersistencePort`, `FundingRatePersistencePort`, `ContractPersistencePort`, `TradePersistencePort`, `WalletPersistencePort`, `TransactionPersistencePort`, `FeePersistencePort`, `InsurancePersistencePort`, `LiquidationPersistencePort`, `ClawbackPositionPersistencePort`, `OrderReportPersistencePort`

### 3. Infrastructure Layer

- **Persistence Adapters**: `OrderPersistenceAdapter` (từ `ContractOrderRepository`), `PositionPersistenceAdapter` (từ `ContractPositionRepository`), `FundingRatePersistenceAdapter`, `ContractPersistenceAdapter` (từ `ContractCoinRepository`), `TradePersistenceAdapter` (từ `ContractTradeRepository`), `WalletPersistenceAdapter` (từ `ContractWalletRepository`), `TransactionPersistenceAdapter` (từ `ContractTransactionRepository`), `FeePersistenceAdapter` (từ `ContractFeeRepository`), `InsurancePersistenceAdapter` (từ `ContractInsuranceRepository`), `LiquidationPersistenceAdapter` (từ `ContractLiquidationRepository`), `ClawbackPositionPersistenceAdapter` (từ `ContractClawbackRepository`), `OrderReportPersistenceAdapter` (từ `ContractOrderReportRepository`)
- **JPA/Mongo Entities**: `OrderJpaEntity` (từ `ContractOrderJpaEntity`), `PositionJpaEntity`, `TradeJpaEntity`, `FundingRateJpaEntity`, `ContractJpaEntity`, `WalletJpaEntity`, `TransactionJpaEntity`, `FeeJpaEntity`, `InsuranceJpaEntity`, `LiquidationJpaEntity`, `ClawbackPositionJpaEntity`, `OrderReportMongoEntity`
- **JPA/Mongo Repositories**: `OrderJpaRepository` (từ `ContractOrderJpaRepository`), `PositionJpaRepository`, `TradeJpaRepository`, `FundingRateJpaRepository`, `ContractJpaRepository`, `WalletJpaRepository`, `TransactionJpaRepository`, `FeeJpaRepository`, `InsuranceJpaRepository`, `LiquidationJpaRepository`, `ClawbackPositionJpaRepository`, `OrderReportMongoRepository`
- **Controllers**: `OrderController` (từ `ContractOrderController`), `PositionController` (từ `ContractPositionController`), `PriceController`, `FundingController`, `LiquidationController` (từ `ContractLiquidationController`), `TransactionController`, `FeeController`, `InsuranceController`, `ClawbackPositionController`, `OrderReportController`, `ContractController` (từ `ContractCoinController`)
- **Mappers**: `OrderPersistenceMapper`, `PositionPersistenceMapper`, `TradePersistenceMapper`, `FundingRatePersistenceMapper`, `ContractPersistenceMapper`, `WalletPersistenceMapper`, `TransactionPersistenceMapper`, `TransactionDtoMapper`, `FeePersistenceMapper`, `FeeDtoMapper`, `InsurancePersistenceMapper`, `InsuranceDtoMapper`, `LiquidationPersistenceMapper`, `LiquidationDtoMapper`, `ClawbackPositionPersistenceMapper`, `ClawbackPositionDtoMapper`, `OrderReportPersistenceMapper`, `OrderReportDtoMapper`, `ContractDtoMapper`

### 4. Presentation Layer

- **Controllers**: `OrderController` (từ `ContractOrderController`), `PositionController` (từ `ContractPositionController`), `PriceController`, `FundingController` (từ `ContractFundingRateController`), `LiquidationController`, `TransactionController`, `FeeController`, `InsuranceController`, `ClawbackPositionController`, `OrderReportController`, `PositionTransferController`, `SpecialOrderController`, `HybridPricingController`, `OrderMatchingController`, `ContractController` (từ `ContractCoinController`)
- **Schedulers**: `LiquidationScheduler` (từ `ContractLiquidationScheduler`), `FundingScheduler` (từ `ContractFundingScheduler`), `SpecialOrderScheduler` (từ `ContractSpecialOrderService`), `ContractSynchronizationScheduler` (từ `ContractCoinMatchSynchronizer`)
- **Request Objects**: `PlaceOrderRequest`, `ClosePositionRequest`, `AdjustLeverageRequest`, `AdjustMarginRequest`, `ChangeMarginModeRequest`, `CalculateFeeRequest`, `FundingSettlementRequest`, `CreateInsuranceRequest`, `UpdateInsuranceRequest`, `CreateLiquidationRequest`, `UpdateInsuranceAmountRequest`, `CreateClawbackPositionRequest`, `TransferPositionRequest`, `TransferPartialPositionRequest`, `MergePositionsRequest`, `CreateTimeOrderRequest`, `CancelTimeOrderRequest`, `CreateOCOOrderRequest`, `CancelOCOOrderRequest`, `CalculateHybridIndexPriceRequest`, `UpdateMarkPriceRequest`
- **Response Objects**: `ApiResponse`

## Các thành phần đã được chuyển đổi và xóa bỏ

Tất cả các thành phần cũ đã được chuyển đổi sang các thành phần mới tuân thủ nguyên tắc của Clean Architecture và đã được xóa khỏi codebase. Dưới đây là danh sách các thành phần chính đã được chuyển đổi:

### Entities
1. **ContractOrder**: Đã được chuyển đổi hoàn toàn sang `Order`
2. **ContractPosition**: Đã được chuyển đổi hoàn toàn sang `Position`
3. **ContractTrade**: Đã được chuyển đổi hoàn toàn sang `Trade`
4. **ContractFundingRate**: Đã được chuyển đổi hoàn toàn sang `FundingRate`
5. **ContractCoin**: Đã được chuyển đổi hoàn toàn sang `Contract`
6. **ContractWallet**: Đã được chuyển đổi hoàn toàn sang `Wallet`
7. **ContractTransaction**: Đã được chuyển đổi hoàn toàn sang `Transaction`
8. **ContractFee**: Đã được chuyển đổi hoàn toàn sang `Fee`
9. **ContractInsuranceFund**: Đã được chuyển đổi hoàn toàn sang `Insurance`
10. **ContractCircuitBreaker**: Đã được chuyển đổi hoàn toàn sang `CircuitBreaker`
11. **ContractClawback**: Đã được chuyển đổi hoàn toàn sang `Clawback`
12. **ContractClawbackPosition**: Đã được chuyển đổi hoàn toàn sang `ClawbackPosition`
13. **ContractOrderDetail**: Đã được chuyển đổi hoàn toàn sang `OrderDetail`
14. **ContractOrderReport**: Đã được chuyển đổi hoàn toàn sang `OrderReport`

### Services
1. **ContractOrderService**: Đã được chuyển đổi hoàn toàn sang `OrderService` (domain service) và `PlaceOrderService` (application service)
2. **ContractPositionService**: Đã được chuyển đổi hoàn toàn sang `PositionService` (domain service) và `ManagePositionService` (application service)
3. **ContractTradeService**: Đã được chuyển đổi hoàn toàn sang `TradeService` (domain service) và `ManageTradeService` (application service)
4. **ContractWalletService**: Đã được chuyển đổi hoàn toàn sang `WalletService` (domain service) và `ManageWalletService` (application service)
5. **ContractFeeService**: Đã được chuyển đổi hoàn toàn sang `FeeService` (domain service) và `ManageFeeService` (application service)
6. **ContractFundingService**: Đã được chuyển đổi hoàn toàn sang `FundingService` (domain service) và `ManageFundingService` (application service)
7. **ContractInsuranceFundService**: Đã được chuyển đổi hoàn toàn sang `InsuranceService` (domain service) và `ManageInsuranceService` (application service)
8. **ContractLiquidationService**: Đã được chuyển đổi hoàn toàn sang `LiquidationService` (domain service) và `ManageLiquidationService` (application service)
9. **ContractOrderDetailService**: Đã được chuyển đổi hoàn toàn sang `OrderDetailService` (domain service) và `ManageOrderDetailService` (application service)
10. **ContractPostOnlyService**: Đã được chuyển đổi hoàn toàn sang `PostOnlyService` (domain service)
11. **ContractPositionOrderService**: Đã được chuyển đổi hoàn toàn sang `PositionOrderService` (domain service)
12. **ContractMarketDataService**: Đã được chuyển đổi hoàn toàn sang `MarketDataService` (domain service)
13. **CircuitBreakerSystem**: Đã được chuyển đổi hoàn toàn sang `CircuitBreakerService` (domain service)
14. **HybridPricingService**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng
15. **ContractConditionalOrderService**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng
16. **ContractOrderReportService**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng
17. **ContractCoinService**: Đã được chuyển đổi thành `ContractService` (domain service) và `ManageContractService` (application service)
18. **ContractSpecialOrderService**: Đã được chuyển đổi thành `SpecialOrderScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`

### Repositories
1. **ContractOrderRepository**: Đã được chuyển đổi hoàn toàn sang `OrderRepository` (domain repository) và `OrderPersistenceAdapter` (infrastructure adapter)
2. **ContractPositionRepository**: Đã được chuyển đổi hoàn toàn sang `PositionRepository` (domain repository) và `PositionPersistenceAdapter` (infrastructure adapter)
3. **ContractTradeRepository**: Đã được chuyển đổi hoàn toàn sang `TradeRepository` (domain repository) và `TradePersistenceAdapter` (infrastructure adapter)
4. **ContractCoinRepository**: Đã được chuyển đổi hoàn toàn sang `ContractRepository` (domain repository) và `ContractPersistenceAdapter` (infrastructure adapter)
5. **ContractWalletRepository**: Đã được chuyển đổi hoàn toàn sang `WalletRepository` (domain repository) và `WalletPersistenceAdapter` (infrastructure adapter)
6. **ContractTransactionRepository**: Đã được chuyển đổi hoàn toàn sang `TransactionRepository` (domain repository) và `TransactionPersistenceAdapter` (infrastructure adapter)
7. **ContractFeeRepository**: Đã được chuyển đổi hoàn toàn sang `FeeRepository` (domain repository) và `FeePersistenceAdapter` (infrastructure adapter)
8. **ContractInsuranceFundRepository**: Đã được chuyển đổi hoàn toàn sang `InsuranceRepository` (domain repository) và `InsurancePersistenceAdapter` (infrastructure adapter)
9. **ContractCircuitBreakerRepository**: Đã được chuyển đổi hoàn toàn sang `CircuitBreakerRepository` (domain repository) và `CircuitBreakerPersistenceAdapter` (infrastructure adapter)
10. **ContractClawbackRepository**: Đã được chuyển đổi hoàn toàn sang `ClawbackRepository` (domain repository) và `ClawbackPersistenceAdapter` (infrastructure adapter)
11. **ContractOrderDetailRepository**: Đã được chuyển đổi hoàn toàn sang `OrderDetailRepository` (domain repository) và `OrderDetailPersistenceAdapter` (infrastructure adapter)
12. **ContractOrderReportRepository**: Đã được chuyển đổi hoàn toàn sang `OrderReportRepository` (domain repository) và `OrderReportPersistenceAdapter` (infrastructure adapter)
13. **ContractFundingRateRepository**: Đã được chuyển đổi hoàn toàn sang `FundingRateRepository` (domain repository) và `FundingRatePersistenceAdapter` (infrastructure adapter)

### JPA Entities
1. **ContractOrderJpaEntity**: Đã được chuyển đổi hoàn toàn sang `OrderJpaEntity`
2. **ContractPositionJpaEntity**: Đã được chuyển đổi hoàn toàn sang `PositionJpaEntity`
3. **ContractTradeJpaEntity**: Đã được chuyển đổi hoàn toàn sang `TradeJpaEntity`
4. **ContractCoinJpaEntity**: Đã được chuyển đổi hoàn toàn sang `ContractJpaEntity`
5. **ContractWalletJpaEntity**: Đã được chuyển đổi hoàn toàn sang `WalletJpaEntity`
6. **ContractTransactionJpaEntity**: Đã được chuyển đổi hoàn toàn sang `TransactionJpaEntity`
7. **ContractFeeJpaEntity**: Đã được chuyển đổi hoàn toàn sang `FeeJpaEntity`
8. **ContractInsuranceFundJpaEntity**: Đã được chuyển đổi hoàn toàn sang `InsuranceJpaEntity`
9. **ContractCircuitBreakerJpaEntity**: Đã được chuyển đổi hoàn toàn sang `CircuitBreakerJpaEntity`
10. **ContractClawbackJpaEntity**: Đã được chuyển đổi hoàn toàn sang `ClawbackJpaEntity`
11. **ContractOrderDetailJpaEntity**: Đã được chuyển đổi hoàn toàn sang `OrderDetailJpaEntity`
12. **ContractFundingRateJpaEntity**: Đã được chuyển đổi hoàn toàn sang `FundingRateJpaEntity`

### Các thành phần khác
1. **ContractOrderProcessorComponent**: Đã được chuyển đổi hoàn toàn sang các domain services và application services tương ứng
2. **ContractCoinMatch**: Đã được chuyển đổi thành `OrderMatchingEngineService`
3. **ContractCoinMatchExtension**: Đã được chuyển đổi thành `OrderMatchingExtensionService`
4. **ContractCoinMatchFactory**: Đã được tích hợp vào `OrderMatchingEngineServiceImpl`
5. **ContractMergeOrder**: Đã được tích hợp vào `OrderBook`
6. **ContractTradePlate**: Đã được tích hợp vào `OrderBook`
7. **ImpliedMatchingEngine**: Đã được tích hợp vào `OrderMatchingEngineServiceImpl`
8. **MatchingAlgorithm**: Đã được chuyển đổi thành `MatchingAlgorithm` trong package `com.icetea.lotus.core.domain.entity`
9. **ContractLiquidationScheduler**: Đã được chuyển đổi thành `LiquidationScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`
10. **ContractFundingScheduler**: Đã được chuyển đổi thành `FundingScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`
11. **ContractCoinMatchSynchronizer**: Đã được chuyển đổi thành `ContractSynchronizationScheduler` trong package `com.icetea.lotus.infrastructure.scheduler`



## Kế hoạch hoàn thiện

Quá trình chuyển đổi sang Clean Architecture đã hoàn thành. Tất cả các thành phần cũ đã được chuyển đổi sang các thành phần mới tuân thủ nguyên tắc của Clean Architecture. Các thành phần cũ đã được xóa khỏi codebase.

### Các thành phần đã được chuyển đổi và xóa bỏ

1. **Entities**: Tất cả các entity cũ đã được chuyển đổi sang domain entities mới.
2. **Services**: Tất cả các service cũ đã được chuyển đổi sang domain services và application services mới.
3. **Repositories**: Tất cả các repository cũ đã được chuyển đổi sang domain repositories và persistence adapters mới.
4. **Components**: Tất cả các component cũ đã được chuyển đổi sang các thành phần tương ứng trong clean architecture.
5. **Enums**: Tất cả các enum cũ đã được chuyển đổi sang các enum mới trong domain layer.
6. **Configs**: Tất cả các config cũ đã được chuyển đổi sang các config mới trong infrastructure layer.
7. **Cache**: Tất cả các cache cũ đã được chuyển đổi sang các cache mới trong infrastructure layer.
8. **Consumers**: Tất cả các consumer cũ đã được chuyển đổi sang các consumer mới trong infrastructure layer.
9. **Market**: Tất cả các market service cũ đã được chuyển đổi sang các service mới trong infrastructure layer.
10. **Processor**: Tất cả các processor cũ đã được chuyển đổi sang các service mới trong domain và application layer.

### Kết quả chuyển đổi

Quá trình chuyển đổi sang Clean Architecture đã hoàn thành 100%. Tất cả các thành phần cũ đã được chuyển đổi sang các thành phần mới tuân thủ nguyên tắc của Clean Architecture và đã được xóa khỏi codebase.

Cấu trúc thư mục hiện tại của module future-core:
- **application**: Chứa các use cases và services của application layer
- **core**: Chứa các entities, repositories và services của domain layer
- **infrastructure**: Chứa các adapters, configurations và implementations của infrastructure layer
- **FuturesCoreApplication.java**: File chính của ứng dụng

Tất cả các thư mục cũ đã được xóa bỏ:
- dao
- entity
- service
- processor
- cache
- config
- consumer
- market
- util

### Các bước tiếp theo

Để duy trì và cải thiện kiến trúc, cần thực hiện các bước sau:

1. **Kiểm thử**:
   - Viết các unit test cho các domain services và application services.
   - Viết các integration test cho các persistence adapters và controllers.
   - Viết các end-to-end test cho các use cases.

2. **Tối ưu hóa hiệu suất**:
   - Áp dụng các kỹ thuật tối ưu hóa hiệu suất như caching, lazy loading, và batch processing.
   - Sử dụng các công cụ profiling để xác định các điểm nghẽn hiệu suất.

3. **Cải thiện khả năng bảo trì**:
   - Áp dụng các nguyên tắc SOLID và các mẫu thiết kế phù hợp.
   - Sử dụng các công cụ phân tích mã nguồn để xác định các vấn đề về chất lượng mã.
   - Giảm thiểu trùng lặp chức năng giữa các service.

4. **Cập nhật tài liệu**:
   - Cập nhật tài liệu để phản ánh chính xác kiến trúc hiện tại.
   - Tạo các tài liệu hướng dẫn cho việc phát triển các tính năng mới tuân thủ Clean Architecture.

5. **Xử lý trùng lặp chức năng**:
   - Xóa các phương thức `@Scheduled` trong application service và chỉ giữ chúng trong scheduler.
   - Hợp nhất các service có chức năng tương tự như đã làm với `PriceManagementService`.
   - Phân định rõ trách nhiệm giữa application service và domain service.

## Kết luận

Quá trình chuyển đổi sang Clean Architecture đã hoàn thành. Tất cả các thành phần cũ đã được chuyển đổi sang các thành phần mới tuân thủ nguyên tắc của Clean Architecture. Các thành phần cũ đã được xóa khỏi codebase.

Việc hoàn thành quá trình chuyển đổi đã giúp cải thiện tính bảo trì, khả năng kiểm thử và khả năng mở rộng của hệ thống, đồng thời giảm thiểu sự nhầm lẫn và không nhất quán trong codebase.

Hệ thống hiện tại đã được tổ chức theo các nguyên tắc của Clean Architecture, với các lớp rõ ràng và các ranh giới được xác định rõ ràng. Điều này giúp hệ thống dễ dàng thích ứng với các thay đổi trong tương lai, đồng thời giảm thiểu sự phụ thuộc vào các framework và thư viện bên ngoài.
