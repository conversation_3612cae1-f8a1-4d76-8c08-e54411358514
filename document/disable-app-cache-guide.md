# Hướng dẫn tắt cache ở ứng dụng nhưng vẫn lưu dữ liệu vào Redis

Tài liệu này mô tả cách tắt cache ở phía ứng dụng nhưng vẫn giữ lại khả năng lưu trữ dữ liệu vào Redis.

## Giới thiệu

Trong một số trường hợp, bạn có thể muốn tắt cơ chế cache tự động của ứng dụng (thông qua các annotation như `@Cacheable`, `@CachePut`, `@CacheEvict`) nhưng vẫn muốn giữ lại khả năng lưu trữ dữ liệu vào Redis một cách thủ công. Giải pháp này giúp bạn đạt được mục tiêu đó.

## Các file đã tạo

1. **DisableAppCacheConfig.java**: <PERSON><PERSON><PERSON> hình để vô hiệu hóa các annotation cache nhưng vẫn giữ lại kết nối với Redis.
2. **RedisDataService.java**: Service cung cấp các phương thức để thao tác với Redis một cách thủ công.

## Cách hoạt động

1. **Vô hiệu hóa các annotation cache**:
   - Sử dụng AOP để chặn các phương thức có annotation `@Cacheable`, `@CachePut`, `@CacheEvict`.
   - Các phương thức này sẽ được thực thi trực tiếp mà không sử dụng cache.

2. **Giữ lại kết nối với Redis**:
   - Vẫn cấu hình `RedisTemplate` để có thể thao tác với Redis.
   - Không sử dụng `CacheManager` để quản lý cache.

3. **Cung cấp phương thức thao tác thủ công**:
   - `RedisDataService` cung cấp các phương thức để lưu trữ, truy vấn và xóa dữ liệu từ Redis.

## Cách sử dụng

### 1. Thay thế các annotation cache bằng gọi trực tiếp RedisDataService

Thay vì sử dụng các annotation cache, bạn có thể sử dụng `RedisDataService` để thao tác với Redis một cách thủ công.

**Trước đây**:
```java
@Cacheable(value = "orders", key = "#orderId")
public Order getOrder(String orderId) {
    return orderRepository.findById(orderId).orElse(null);
}
```

**Bây giờ**:
```java
public Order getOrder(String orderId) {
    // Truy vấn trực tiếp từ cơ sở dữ liệu
    Order order = orderRepository.findById(orderId).orElse(null);
    
    // Lưu vào Redis một cách thủ công (nếu cần)
    if (order != null) {
        redisDataService.set("orders:" + orderId, order, 30, TimeUnit.MINUTES);
    }
    
    return order;
}
```

### 2. Sử dụng RedisDataService trong các service

```java
@Service
public class OrderService {
    private final OrderRepository orderRepository;
    private final RedisDataService redisDataService;
    
    @Autowired
    public OrderService(OrderRepository orderRepository, RedisDataService redisDataService) {
        this.orderRepository = orderRepository;
        this.redisDataService = redisDataService;
    }
    
    public Order getOrder(String orderId) {
        // Thử lấy từ Redis trước
        String key = "orders:" + orderId;
        Order order = (Order) redisDataService.get(key);
        
        if (order == null) {
            // Nếu không có trong Redis, lấy từ cơ sở dữ liệu
            order = orderRepository.findById(orderId).orElse(null);
            
            // Lưu vào Redis nếu tìm thấy
            if (order != null) {
                redisDataService.set(key, order, 30, TimeUnit.MINUTES);
            }
        }
        
        return order;
    }
    
    public Order saveOrder(Order order) {
        // Lưu vào cơ sở dữ liệu
        Order savedOrder = orderRepository.save(order);
        
        // Lưu vào Redis
        redisDataService.set("orders:" + savedOrder.getId(), savedOrder, 30, TimeUnit.MINUTES);
        
        return savedOrder;
    }
    
    public void deleteOrder(String orderId) {
        // Xóa từ cơ sở dữ liệu
        orderRepository.deleteById(orderId);
        
        // Xóa từ Redis
        redisDataService.delete("orders:" + orderId);
    }
}
```

### 3. Các phương thức có sẵn trong RedisDataService

- **Thao tác với giá trị đơn giản**:
  - `set(String key, Object value)`: Lưu giá trị vào Redis
  - `set(String key, Object value, long timeout, TimeUnit unit)`: Lưu giá trị vào Redis với thời gian hết hạn
  - `get(String key)`: Lấy giá trị từ Redis
  - `delete(String key)`: Xóa giá trị từ Redis
  - `hasKey(String key)`: Kiểm tra xem khóa có tồn tại trong Redis không
  - `expire(String key, long timeout, TimeUnit unit)`: Thiết lập thời gian hết hạn cho khóa

- **Thao tác với hash**:
  - `hSet(String key, String hashKey, Object value)`: Lưu giá trị vào hash
  - `hGet(String key, String hashKey)`: Lấy giá trị từ hash
  - `hGetAll(String key)`: Lấy tất cả các cặp khóa-giá trị từ hash

## Lưu ý

1. **Hiệu suất**:
   - Việc thao tác thủ công với Redis có thể làm giảm hiệu suất so với sử dụng các annotation cache.
   - Bạn cần cẩn thận để tránh các vấn đề như N+1 query.

2. **Nhất quán dữ liệu**:
   - Khi thao tác thủ công, bạn cần đảm bảo dữ liệu trong Redis và cơ sở dữ liệu luôn nhất quán.
   - Đảm bảo xóa cache khi dữ liệu thay đổi.

3. **Khôi phục cache tự động**:
   - Khi vấn đề đã được khắc phục, bạn có thể khôi phục lại cơ chế cache tự động bằng cách xóa các file cấu hình đã tạo.

## Kết luận

Giải pháp này giúp bạn tắt cơ chế cache tự động của ứng dụng nhưng vẫn giữ lại khả năng lưu trữ dữ liệu vào Redis một cách thủ công. Điều này có thể hữu ích trong các trường hợp bạn muốn kiểm soát chặt chẽ hơn việc lưu trữ và truy xuất dữ liệu từ Redis.
