# Cập nhật PriceController

## Giới thiệu

Tài liệu này mô tả việc chuyển các chức năng liên quan đến giá từ `TradingController` sang `PriceController` để tăng tính nhất quán và dễ bảo trì.

## Các thay đổi đã thực hiện

1. **C<PERSON><PERSON> nhật `PriceController`**:
   - <PERSON><PERSON> thêm các endpoint mới từ `TradingController` vào `PriceController`
   - Đ<PERSON> thêm các phương thức fallback cho các endpoint mới
   - Đã thêm các annotation `@CircuitBreaker`, `@RateLimiter`, và `@Retry` cho các endpoint mới

2. **Cập nhật `TradingController`**:
   - Đã loại bỏ các endpoint liên quan đến giá
   - <PERSON><PERSON> loại bỏ các import không cần thiết

## Các endpoint đã chuyển

Các endpoint sau đã được chuyển từ `TradingController` sang `PriceController`:

| Endpoint cũ | Endpoint mới | Mô tả |
|-------------|--------------|-------|
| `/api/v1/trading/matching/update-mark-price` | `/api/v1/prices/update-mark-price` | Cập nhật giá đánh dấu |
| `/api/v1/trading/matching/mark-price/{symbol}` | `/api/v1/prices/mark-price/{symbol}` | Lấy giá đánh dấu |
| `/api/v1/trading/matching/index-price/{symbol}` | `/api/v1/prices/index-price/{symbol}` | Lấy giá chỉ số |
| `/api/v1/trading/matching/last-price/{symbol}` | `/api/v1/prices/last-price/{symbol}` | Lấy giá giao dịch cuối cùng |

## Các endpoint hiện có trong PriceController

### API quản lý giá

- `GET /api/v1/prices/index/{symbol}`: Lấy giá chỉ số hiện tại
- `GET /api/v1/prices/index/history/{symbol}`: Lấy lịch sử giá chỉ số
- `GET /api/v1/prices/mark/{symbol}`: Lấy giá đánh dấu hiện tại
- `GET /api/v1/prices/mark/history/{symbol}`: Lấy lịch sử giá đánh dấu
- `POST /api/v1/prices/index`: Cập nhật giá chỉ số
- `POST /api/v1/prices/mark`: Cập nhật giá đánh dấu
- `POST /api/v1/prices/calculate-index`: Tính toán giá chỉ số
- `GET /api/v1/prices/mark-price/{symbol}`: Lấy giá đánh dấu (từ matching engine)
- `GET /api/v1/prices/index-price/{symbol}`: Lấy giá chỉ số (từ matching engine)
- `GET /api/v1/prices/last-price/{symbol}`: Lấy giá giao dịch cuối cùng
- `POST /api/v1/prices/update-mark-price`: Cập nhật giá đánh dấu (từ matching engine)

## Lợi ích

1. **Tính nhất quán**: Tất cả các endpoint liên quan đến giá đều được tập trung trong một controller.
2. **Dễ bảo trì**: Dễ dàng tìm và cập nhật các endpoint liên quan đến giá.
3. **Tránh trùng lặp**: Không còn nhiều endpoint khác nhau cho cùng một chức năng.
4. **Rõ ràng hơn**: API trở nên rõ ràng hơn với các endpoint được tổ chức theo chức năng.

## Kết luận

Việc chuyển các chức năng liên quan đến giá từ `TradingController` sang `PriceController` giúp tăng tính nhất quán và dễ bảo trì của hệ thống. Tất cả các endpoint liên quan đến giá đều được tập trung trong một controller duy nhất, giúp dễ dàng quản lý và bảo trì.
