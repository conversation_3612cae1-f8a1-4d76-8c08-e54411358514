# Hướng dẫn cấu hình Matching Engine

Tài liệu này mô tả cách cấu hình và sử dụng các loại matching engine khác nhau trong hệ thống.

## Giới thiệu

Hệ thống hỗ trợ 3 loại matching engine khá<PERSON> nhau, có thể được sử dụng đồng thời:

1. **OPTIMIZED**: Matching Engine tối ưu hóa (lớp `OptimizedMatchingEngine`)
2. **LOCK_FREE**: Matching Engine không khóa (lớp `LockFreeMatchingEngine`)
3. **DISTRIBUTED**: Matching Engine phân tán (lớp `DistributedLockingMatchingEngine`)

Mỗi loại matching engine có những ưu điểm và nhược điểm riêng, và chúng có thể được kết hợp để tận dụng điểm mạnh của từng loại.

## C<PERSON><PERSON> hình

Cấu hình matching engine được định nghĩa trong file `application-matching-engine.yml`. File này được bao gồm trong `application.yml` thông qua cấu hình `spring.profiles.include: matching-engine`.

### Cấu trúc cấu hình

```yaml
lotus:
  matching-engine:
    # Cấu hình kích hoạt các loại matching engine
    # Có thể kích hoạt nhiều loại cùng lúc
    use-distributed: true
    use-lock-free: true
    use-optimized: true

    # Cấu hình cho distributed matching engine
    distributed:
      # Thời gian timeout cho lock (giây)
      lock-timeout-seconds: 5
      # Thời gian giữ lock (giây)
      lock-lease-time-seconds: 30
      # Kích thước phân đoạn cho distributed order book
      segment-size: "100"
      # Có sử dụng distributed order book không
      use-distributed-order-book: true

    # Cấu hình cho optimized matching engine
    optimized:
      # Kích thước cache cho order book
      order-book-cache-size: 1000
      # Thời gian hết hạn cache (giây)
      cache-expiration-seconds: 60

    # Cấu hình cho lock-free matching engine
    lock-free:
      # Số lần thử lại tối đa khi CAS thất bại
      max-retries: 10
      # Thời gian chờ giữa các lần thử lại (mili giây)
      retry-delay-millis: 10
```

### Các loại Matching Engine

#### 1. OPTIMIZED

Matching Engine tối ưu hóa sử dụng các cấu trúc dữ liệu và thuật toán được tối ưu hóa để cải thiện hiệu suất.

**Ưu điểm**:
- Hiệu suất cao
- Hỗ trợ cache
- Tối ưu hóa cho các trường hợp sử dụng phổ biến

**Nhược điểm**:
- Không hỗ trợ phân tán

**Trường hợp sử dụng**:
- Tối ưu hóa hiệu suất trong một instance
- Cải thiện thời gian phản hồi cho các lệnh giao dịch

#### 2. LOCK_FREE

Matching Engine không khóa sử dụng các cấu trúc dữ liệu không khóa và cơ chế CAS (Compare-And-Swap) để đảm bảo tính nhất quán mà không cần sử dụng khóa.

**Ưu điểm**:
- Hiệu suất rất cao
- Không bị block bởi các khóa
- Khả năng mở rộng tốt trong một instance

**Nhược điểm**:
- Phức tạp hơn so với các loại khác
- Khó debug hơn

**Trường hợp sử dụng**:
- Xử lý đồng thời nhiều lệnh giao dịch
- Giảm thiểu thời gian chờ do khóa

#### 3. DISTRIBUTED

Matching Engine phân tán sử dụng Redis để đảm bảo chỉ có một instance xử lý một symbol tại một thời điểm, cho phép phân tán khối lượng công việc giữa nhiều instance.

**Ưu điểm**:
- Hỗ trợ phân tán
- Khả năng mở rộng theo chiều ngang
- Khả năng chịu lỗi tốt

**Nhược điểm**:
- Phụ thuộc vào Redis
- Có thể có độ trễ do giao tiếp mạng

**Trường hợp sử dụng**:
- Phân tán khối lượng công việc giữa nhiều instance
- Đảm bảo tính nhất quán trong môi trường phân tán

## Cách kết hợp các Matching Engine

Hệ thống cho phép kết hợp các loại matching engine để tận dụng điểm mạnh của từng loại:

1. **DISTRIBUTED + LOCK_FREE**: Kết hợp này cho phép phân tán khối lượng công việc giữa nhiều instance (DISTRIBUTED) và xử lý đồng thời nhiều lệnh giao dịch trong một instance (LOCK_FREE).
2. **DISTRIBUTED + OPTIMIZED**: Kết hợp này cho phép phân tán khối lượng công việc giữa nhiều instance (DISTRIBUTED) và tối ưu hóa hiệu suất trong một instance (OPTIMIZED).
3. **DISTRIBUTED + LOCK_FREE + OPTIMIZED**: Kết hợp đầy đủ cả ba loại matching engine để đạt được hiệu suất tối đa và khả năng mở rộng tốt nhất.

### Bảng so sánh

| Tính năng | OPTIMIZED | LOCK_FREE | DISTRIBUTED |
|-----------|-----------|-----------|-------------|
| Hiệu suất | Cao | Rất cao | Cao |
| Phân tán | Không | Không | Có |
| Phức tạp | Trung bình | Cao | Cao |
| Phụ thuộc | Không | Không | Redis |
| Khả năng mở rộng | Thấp | Trung bình | Cao |

## Cách cấu hình

Để kích hoạt hoặc vô hiệu hóa các loại matching engine, chỉ cần thay đổi giá trị của các thuộc tính `use-distributed`, `use-lock-free` và `use-optimized` trong file `application-matching-engine.yml`:

```yaml
lotus:
  matching-engine:
    use-distributed: true   # Kích hoạt hoặc vô hiệu hóa Distributed Matching Engine
    use-lock-free: true     # Kích hoạt hoặc vô hiệu hóa Lock-Free Matching Engine
    use-optimized: true     # Kích hoạt hoặc vô hiệu hóa Optimized Matching Engine
```

Sau đó, cấu hình các tham số cụ thể cho từng loại matching engine.

## Luồng xử lý

Khi kích hoạt nhiều loại matching engine cùng lúc, hệ thống sẽ xử lý các lệnh giao dịch theo thứ tự ưu tiên sau:

1. **DISTRIBUTED**: Nếu được kích hoạt, hệ thống sẽ sử dụng Distributed Matching Engine để xử lý lệnh giao dịch. Distributed Matching Engine sẽ đảm bảo chỉ có một instance xử lý một symbol tại một thời điểm.
2. **LOCK_FREE**: Nếu Distributed Matching Engine không được kích hoạt, hệ thống sẽ sử dụng Lock-Free Matching Engine để xử lý lệnh giao dịch. Lock-Free Matching Engine sẽ xử lý đồng thời nhiều lệnh giao dịch trong một instance.
3. **OPTIMIZED**: Nếu cả Distributed Matching Engine và Lock-Free Matching Engine đều không được kích hoạt, hệ thống sẽ sử dụng Optimized Matching Engine để xử lý lệnh giao dịch.

Các thao tác khác như cập nhật giá đánh dấu, cập nhật giá chỉ số, tạm dừng giao dịch, v.v. sẽ được thực hiện trên tất cả các matching engine được kích hoạt.

## Khắc phục sự cố

### Vấn đề với DISTRIBUTED

Nếu gặp vấn đề với DISTRIBUTED, hãy kiểm tra:

1. Redis có đang chạy không
2. Cấu hình Redis trong `application.yml` có chính xác không
3. Thời gian timeout và lease time có phù hợp không

### Vấn đề với LOCK_FREE

Nếu gặp vấn đề với LOCK_FREE, hãy kiểm tra:

1. Số lần thử lại có đủ không
2. Thời gian chờ giữa các lần thử lại có phù hợp không

### Vấn đề với OPTIMIZED

Nếu gặp vấn đề với OPTIMIZED, hãy kiểm tra:

1. Kích thước cache có phù hợp không
2. Thời gian hết hạn cache có phù hợp không

## Kết luận

Việc kết hợp các loại matching engine là một cách hiệu quả để tận dụng điểm mạnh của từng loại và đạt được hiệu suất tối đa cùng với khả năng mở rộng tốt. Hãy cân nhắc kỹ các yếu tố trước khi quyết định cấu hình phù hợp cho hệ thống của bạn.
