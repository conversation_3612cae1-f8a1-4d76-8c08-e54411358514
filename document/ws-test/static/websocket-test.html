<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Order Book Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container {
            max-width: 1200px;
            margin-top: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .table-container {
            max-height: 300px;
            overflow-y: auto;
        }
        .asks-table tbody tr {
            background-color: rgba(255, 200, 200, 0.3);
        }
        .bids-table tbody tr {
            background-color: rgba(200, 255, 200, 0.3);
        }
        .connection-status {
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        .connected {
            color: green;
        }
        .disconnected {
            color: red;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-indicator.connected {
            background-color: green;
            box-shadow: 0 0 5px green;
        }
        .status-indicator.disconnected {
            background-color: red;
            box-shadow: 0 0 5px red;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">WebSocket Order Book Test</h1>

        <div class="alert alert-info mb-4">
            <h5>Issue Resolution</h5>
            <p>This page has been updated to fix the connection issue with the WebSocket endpoint. The original error was:</p>
            <pre class="bg-light p-2">Error: Whoops! Lost connection to https://lotus-api.dev-glyph.click/future/contrract-ws</pre>
            <p>The issue was a typo in the URL: <code>contrract-ws</code> (with three 'r's) instead of <code>contract-ws</code> (with two 'r's).</p>
            <p>This page now:</p>
            <ul>
                <li>Uses the correct URL format by default</li>
                <li>Automatically detects and fixes the typo if entered incorrectly</li>
                <li>Provides clear error messages and troubleshooting suggestions</li>
                <li>Includes environment presets for easy switching between environments</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>Connection Status: <span id="connection-status" class="connection-status disconnected"><span class="status-indicator disconnected"></span>Disconnected</span></h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="serverUrl" class="form-label">Server URL:</label>
                        <div class="input-group mb-2">
                            <input type="text" id="serverUrl" class="form-control" value="https://lotus-api.dev-glyph.click/future/contract-ws">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">Presets</button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#" onclick="setServerUrl('http://localhost:8080/contract-ws'); return false;">Local Development</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setServerUrl('https://lotus-api.dev-glyph.click/future/contract-ws'); return false;">Dev Environment</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setServerUrl('https://lotus-api.staging-glyph.click/future/contract-ws'); return false;">Staging Environment</a></li>
                                <li><a class="dropdown-item" href="#" onclick="setServerUrl('https://lotus-api.glyph.click/future/contract-ws'); return false;">Production Environment</a></li>
                            </ul>
                        </div>
                        <small class="form-text text-muted">Note: Make sure to use the correct path. The original error had a typo: "contrract-ws" instead of "contract-ws".</small>
                    </div>
                    <div class="col-md-6">
                        <label for="symbol" class="form-label">Symbol:</label>
                        <input type="text" id="symbol" class="form-control" value="BTC-USDT">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button id="connect" class="btn btn-primary me-2">Connect</button>
                        <button id="disconnect" class="btn btn-danger me-2" disabled>Disconnect</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5>Subscriptions</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="orderbook-subscription" disabled>
                            <label class="form-check-label" for="orderbook-subscription">
                                /topic/market/orderbook/<span class="symbol-placeholder">BTC-USDT</span>
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="trade-plate-subscription" disabled>
                            <label class="form-check-label" for="trade-plate-subscription">
                                /topic/market/trade-plate/<span class="symbol-placeholder">BTC-USDT</span>
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="trade-depth-subscription" disabled>
                            <label class="form-check-label" for="trade-depth-subscription">
                                /topic/market/trade-depth/<span class="symbol-placeholder">BTC-USDT</span>
                            </label>
                        </div>
                        <div class="form-check form-check-inline mt-3">
                            <input class="form-check-input" type="checkbox" id="custom-topic-subscription" disabled>
                            <label class="form-check-label" for="custom-topic-subscription">
                                Custom Topic:
                            </label>
                            <input type="text" id="custom-topic" class="form-control ms-2" placeholder="Enter custom topic" style="width: 300px;">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Order Book</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6>Symbol: <span id="ob-symbol">-</span></h6>
                            </div>
                            <div class="col-md-6">
                                <h6>Last Price: <span id="ob-last-price">-</span></h6>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6>Mark Price: <span id="ob-mark-price">-</span></h6>
                            </div>
                            <div class="col-md-6">
                                <h6>Index Price: <span id="ob-index-price">-</span></h6>
                            </div>
                        </div>

                        <h6>Asks (Sell Orders)</h6>
                        <div class="table-container">
                            <table class="table table-sm asks-table">
                                <thead>
                                    <tr>
                                        <th>Price</th>
                                        <th>Volume</th>
                                        <th>Order Count</th>
                                    </tr>
                                </thead>
                                <tbody id="asks-table-body">
                                    <!-- Asks data will be inserted here -->
                                </tbody>
                            </table>
                        </div>

                        <h6 class="mt-3">Bids (Buy Orders)</h6>
                        <div class="table-container">
                            <table class="table table-sm bids-table">
                                <thead>
                                    <tr>
                                        <th>Price</th>
                                        <th>Volume</th>
                                        <th>Order Count</th>
                                    </tr>
                                </thead>
                                <tbody id="bids-table-body">
                                    <!-- Bids data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Raw Messages</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="auto-scroll" checked>
                            <label class="form-check-label" for="auto-scroll">
                                Auto-scroll to latest messages
                            </label>
                        </div>
                        <button id="clear-messages" class="btn btn-secondary btn-sm mb-3">Clear Messages</button>
                        <pre id="messages"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap, SockJS and STOMP client libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>

    <script>
        let stompClient = null;
        let subscriptions = {
            orderbook: null,
            tradePlate: null,
            tradeDepth: null,
            customTopic: null
        };

        // Function to set server URL from presets
        function setServerUrl(url) {
            document.getElementById('serverUrl').value = url;
            addMessage('Server URL set to: ' + url);
        }

        // DOM elements
        const connectButton = document.getElementById('connect');
        const disconnectButton = document.getElementById('disconnect');
        const connectionStatus = document.getElementById('connection-status');
        const serverUrlInput = document.getElementById('serverUrl');
        const symbolInput = document.getElementById('symbol');
        const messagesContainer = document.getElementById('messages');
        const clearMessagesButton = document.getElementById('clear-messages');
        const autoScrollCheckbox = document.getElementById('auto-scroll');

        // Subscription checkboxes
        const orderbookSubscription = document.getElementById('orderbook-subscription');
        const tradePlateSubscription = document.getElementById('trade-plate-subscription');
        const tradeDepthSubscription = document.getElementById('trade-depth-subscription');
        const customTopicSubscription = document.getElementById('custom-topic-subscription');
        const customTopicInput = document.getElementById('custom-topic');

        // Update symbol placeholders when symbol input changes
        symbolInput.addEventListener('input', function() {
            document.querySelectorAll('.symbol-placeholder').forEach(el => {
                el.textContent = this.value;
            });
        });

        // Connect to WebSocket
        connectButton.addEventListener('click', function() {
            let serverUrl = serverUrlInput.value;

            // Auto-fix common typos in the URL
            if (serverUrl.includes('contrract-ws')) {
                const correctedUrl = serverUrl.replace('contrract-ws', 'contract-ws');
                addMessage('Auto-correcting URL typo: "contrract-ws" → "contract-ws"');
                addMessage('Original URL: ' + serverUrl);
                addMessage('Corrected URL: ' + correctedUrl);
                serverUrl = correctedUrl;
                serverUrlInput.value = correctedUrl;
            }

            // Create SockJS and STOMP client
            const socket = new SockJS(serverUrl);
            stompClient = Stomp.over(socket);

            // Disable debug logs
            stompClient.debug = null;

            // Connect to the WebSocket server
            stompClient.connect({}, function(frame) {
                // Update UI
                connectionStatus.innerHTML = '<span class="status-indicator connected"></span>Connected';
                connectionStatus.classList.remove('disconnected');
                connectionStatus.classList.add('connected');
                connectButton.disabled = true;
                disconnectButton.disabled = false;

                // Enable subscription checkboxes
                orderbookSubscription.disabled = false;
                tradePlateSubscription.disabled = false;
                tradeDepthSubscription.disabled = false;
                customTopicSubscription.disabled = false;

                // Log connection
                addMessage('Connected to WebSocket server');
                addMessage('Connected to: ' + serverUrl);
                addMessage('STOMP version: ' + stompClient.version);
                addMessage('STOMP transaction ID: ' + frame.headers['heart-beat']);
            }, function(error) {
                // Handle connection error
                connectionStatus.innerHTML = '<span class="status-indicator disconnected"></span>Error: ' + error;
                connectionStatus.classList.remove('connected');
                connectionStatus.classList.add('disconnected');

                // Log detailed error information
                addMessage('Error connecting to WebSocket server: ' + error);

                // Provide troubleshooting suggestions
                addMessage('Troubleshooting suggestions:');
                addMessage('1. Check if the URL is correct. Make sure there are no typos (e.g., "contrract-ws" vs "contract-ws")');
                addMessage('2. Verify that the server is running and accessible');
                addMessage('3. Check if there are any CORS issues (try using the same domain)');
                addMessage('4. Ensure the WebSocket endpoint is properly configured on the server');

                // Check for common URL issues
                if (serverUrl.includes('contrract-ws')) {
                    addMessage('WARNING: Your URL contains "contrract-ws" which appears to be a typo. Try changing it to "contract-ws".');
                    addMessage('This matches the error in the issue description: "Error: Whoops! Lost connection to https://lotus-api.dev-glyph.click/future/contrract-ws"');
                    addMessage('The correct URL should be: ' + serverUrl.replace('contrract-ws', 'contract-ws'));
                }
            });
        });

        // Disconnect from WebSocket
        disconnectButton.addEventListener('click', function() {
            if (stompClient !== null) {
                // Unsubscribe from all topics
                unsubscribeFromAll();

                // Disconnect from the server
                stompClient.disconnect(function() {
                    // Update UI
                    connectionStatus.innerHTML = '<span class="status-indicator disconnected"></span>Disconnected';
                    connectionStatus.classList.remove('connected');
                    connectionStatus.classList.add('disconnected');
                    connectButton.disabled = false;
                    disconnectButton.disabled = true;

                    // Disable subscription checkboxes
                    orderbookSubscription.disabled = true;
                    tradePlateSubscription.disabled = true;
                    tradeDepthSubscription.disabled = true;
                    customTopicSubscription.disabled = true;

                    // Reset checkboxes
                    orderbookSubscription.checked = false;
                    tradePlateSubscription.checked = false;
                    tradeDepthSubscription.checked = false;
                    customTopicSubscription.checked = false;

                    // Log disconnection
                    addMessage('Disconnected from WebSocket server');
                });
            }
        });

        // Subscribe to Order Book topic
        orderbookSubscription.addEventListener('change', function() {
            const symbol = symbolInput.value;
            if (this.checked) {
                subscribeToOrderBook(symbol);
            } else {
                unsubscribeFromOrderBook();
            }
        });

        // Subscribe to Trade Plate topic
        tradePlateSubscription.addEventListener('change', function() {
            const symbol = symbolInput.value;
            if (this.checked) {
                subscribeToTradePlate(symbol);
            } else {
                unsubscribeFromTradePlate();
            }
        });

        // Subscribe to Trade Depth topic
        tradeDepthSubscription.addEventListener('change', function() {
            const symbol = symbolInput.value;
            if (this.checked) {
                subscribeToTradeDepth(symbol);
            } else {
                unsubscribeFromTradeDepth();
            }
        });

        // Subscribe to Custom Topic
        customTopicSubscription.addEventListener('change', function() {
            if (this.checked) {
                const topic = customTopicInput.value.trim();
                if (topic) {
                    subscribeToCustomTopic(topic);
                } else {
                    addMessage('Error: Please enter a custom topic');
                    this.checked = false;
                }
            } else {
                unsubscribeFromCustomTopic();
            }
        });

        // Clear messages
        clearMessagesButton.addEventListener('click', function() {
            messagesContainer.innerHTML = '';
        });

        // Subscribe to Order Book topic
        function subscribeToOrderBook(symbol) {
            if (stompClient !== null) {
                const topic = `/topic/market/orderbook/${symbol}`;
                subscriptions.orderbook = stompClient.subscribe(topic, function(message) {
                    // Parse message body
                    const orderBook = JSON.parse(message.body);

                    // Update Order Book UI
                    updateOrderBookUI(orderBook);

                    // Log message
                    addMessage(`Received message from ${topic}:`);
                    addMessage(JSON.stringify(orderBook, null, 2));
                });
                addMessage(`Subscribed to ${topic}`);
            }
        }

        // Subscribe to Trade Plate topic
        function subscribeToTradePlate(symbol) {
            if (stompClient !== null) {
                const topic = `/topic/market/trade-plate/${symbol}`;
                subscriptions.tradePlate = stompClient.subscribe(topic, function(message) {
                    // Parse message body
                    const orderBook = JSON.parse(message.body);

                    // Log message
                    addMessage(`Received message from ${topic}:`);
                    addMessage(JSON.stringify(orderBook, null, 2));
                });
                addMessage(`Subscribed to ${topic}`);
            }
        }

        // Subscribe to Trade Depth topic
        function subscribeToTradeDepth(symbol) {
            if (stompClient !== null) {
                const topic = `/topic/market/trade-depth/${symbol}`;
                subscriptions.tradeDepth = stompClient.subscribe(topic, function(message) {
                    // Parse message body
                    const orderBook = JSON.parse(message.body);

                    // Log message
                    addMessage(`Received message from ${topic}:`);
                    addMessage(JSON.stringify(orderBook, null, 2));
                });
                addMessage(`Subscribed to ${topic}`);
            }
        }

        // Unsubscribe from Order Book topic
        function unsubscribeFromOrderBook() {
            if (subscriptions.orderbook !== null) {
                subscriptions.orderbook.unsubscribe();
                subscriptions.orderbook = null;
                addMessage('Unsubscribed from Order Book topic');
            }
        }

        // Unsubscribe from Trade Plate topic
        function unsubscribeFromTradePlate() {
            if (subscriptions.tradePlate !== null) {
                subscriptions.tradePlate.unsubscribe();
                subscriptions.tradePlate = null;
                addMessage('Unsubscribed from Trade Plate topic');
            }
        }

        // Unsubscribe from Trade Depth topic
        function unsubscribeFromTradeDepth() {
            if (subscriptions.tradeDepth !== null) {
                subscriptions.tradeDepth.unsubscribe();
                subscriptions.tradeDepth = null;
                addMessage('Unsubscribed from Trade Depth topic');
            }
        }

        // Subscribe to Custom Topic
        function subscribeToCustomTopic(topic) {
            if (stompClient !== null) {
                subscriptions.customTopic = stompClient.subscribe(topic, function(message) {
                    // Parse message body
                    try {
                        const data = JSON.parse(message.body);
                        // Log message
                        addMessage(`Received message from ${topic}:`);
                        addMessage(JSON.stringify(data, null, 2));
                    } catch (e) {
                        // Handle non-JSON messages
                        addMessage(`Received message from ${topic}:`);
                        addMessage(message.body);
                    }
                });
                addMessage(`Subscribed to custom topic: ${topic}`);
            }
        }

        // Unsubscribe from Custom Topic
        function unsubscribeFromCustomTopic() {
            if (subscriptions.customTopic !== null) {
                subscriptions.customTopic.unsubscribe();
                subscriptions.customTopic = null;
                addMessage('Unsubscribed from custom topic');
            }
        }

        // Unsubscribe from all topics
        function unsubscribeFromAll() {
            unsubscribeFromOrderBook();
            unsubscribeFromTradePlate();
            unsubscribeFromTradeDepth();
            unsubscribeFromCustomTopic();
        }

        // Update Order Book UI
        function updateOrderBookUI(orderBook) {
            // Update symbol and prices
            document.getElementById('ob-symbol').textContent = orderBook.symbol || '-';
            document.getElementById('ob-last-price').textContent = orderBook.lastPrice || '-';
            document.getElementById('ob-mark-price').textContent = orderBook.markPrice || '-';
            document.getElementById('ob-index-price').textContent = orderBook.indexPrice || '-';

            // Update asks table
            const asksTableBody = document.getElementById('asks-table-body');
            asksTableBody.innerHTML = '';
            if (orderBook.asks && orderBook.asks.length > 0) {
                // Sort asks by price (ascending)
                orderBook.asks.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));

                // Add rows to table
                orderBook.asks.forEach(ask => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${ask.price}</td>
                        <td>${ask.volume}</td>
                        <td>${ask.orderCount}</td>
                    `;
                    asksTableBody.appendChild(row);
                });
            }

            // Update bids table
            const bidsTableBody = document.getElementById('bids-table-body');
            bidsTableBody.innerHTML = '';
            if (orderBook.bids && orderBook.bids.length > 0) {
                // Sort bids by price (descending)
                orderBook.bids.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));

                // Add rows to table
                orderBook.bids.forEach(bid => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${bid.price}</td>
                        <td>${bid.volume}</td>
                        <td>${bid.orderCount}</td>
                    `;
                    bidsTableBody.appendChild(row);
                });
            }
        }

        // Add message to messages container
        function addMessage(message) {
            const messageElement = document.createElement('div');
            messageElement.textContent = message;
            messagesContainer.appendChild(messageElement);

            // Auto-scroll to bottom if enabled
            if (autoScrollCheckbox.checked) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        // Initialize symbol placeholders
        document.querySelectorAll('.symbol-placeholder').forEach(el => {
            el.textContent = symbolInput.value;
        });
    </script>
</body>
</html>
