// This file is used by SockJS for iframe-based transport
(function() {
  var cookie = '', 
      cookies = document.cookie.split(';');
  
  for (var i = 0; i < cookies.length; i++) {
    var p = cookies[i].indexOf('=');
    if (p >= 0) {
      var key = cookies[i].substring(0, p).trim();
      if (key === 'JSESSIONID' || key.indexOf('XSRF') >= 0) {
        cookie += key + '=' + cookies[i].substring(p + 1).trim() + '; ';
      }
    }
  }
  
  document.cookie = cookie;
  
  // Notify parent window that iframe is ready
  if (window.parent && window.parent.postMessage) {
    window.parent.postMessage('s', '*');
  }
})();