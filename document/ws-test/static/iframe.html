<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <script>
    document.domain = document.domain;
    
    // This is used by SockJS for iframe-based transport
    _sockjs_onload = function() {
      var iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = 'javascript:var d=document,h=d.getElementsByTagName(\'head\')[0],c=d.createElement(\'script\');c.type=\'text/javascript\';c.src=\'../iframe.js\';h.appendChild(c);';
      document.body.appendChild(iframe);
    };
  </script>
</head>
<body>
  <h2>Don't panic!</h2>
  <p>This is a SockJS hidden iframe. It's used for cross domain iframe based transport.</p>
</body>
</html>