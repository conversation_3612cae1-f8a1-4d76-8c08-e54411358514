# Domain Entities trong Future-Core Module

## Giới thiệu

Tài liệu này mô tả các domain entities trong Future-Core Module. Domain entities là các đối tượng nghiệp vụ chính trong hệ thống, và chúng được triển khai trong domain layer của clean architecture.

## Tình trạng chuyển đổi

Future-Core Module đang trong quá trình chuyển đổi sang clean architecture. Một số domain entities đã được chuyển đổi hoàn toàn, trong khi một số khác vẫn đang sử dụng cấu trúc cũ.

### Đã chuyển đổi

Các domain entities sau đã được chuyển đổi sang clean architecture:

- `Order`: Đã được chuyển đổi thành domain entity trong package `com.icetea.lotus.core.domain.entity`.
- `Position`: Đã đượ<PERSON> chuyển đổi thành domain entity trong package `com.icetea.lotus.core.domain.entity`.
- `Trade`: Đã được chuyển đổi thành domain entity trong package `com.icetea.lotus.core.domain.entity`.
- `FundingRate`: Đã được chuyển đổi thành domain entity trong package `com.icetea.lotus.core.domain.entity`.
- `Contract`: Đã được chuyển đổi thành domain entity trong package `com.icetea.lotus.core.domain.entity`.

### Chưa chuyển đổi

Các domain entities sau vẫn chưa được chuyển đổi sang clean architecture:

- `ContractOrder`: Vẫn đang sử dụng cấu trúc cũ trong package `com.icetea.lotus.entity`.
- `ContractPosition`: Vẫn đang sử dụng cấu trúc cũ trong package `com.icetea.lotus.entity`.
- `ContractTrade`: Vẫn đang sử dụng cấu trúc cũ trong package `com.icetea.lotus.entity`.
- `ContractCoin`: Vẫn đang sử dụng cấu trúc cũ trong package `com.icetea.lotus.entity`.

## Order

Order là đối tượng đại diện cho lệnh giao dịch trong hệ thống. Nó chứa thông tin về lệnh, bao gồm ID, thành viên, symbol, hướng, loại, giá, khối lượng, v.v.

### Thuộc tính

- **orderId**: ID của lệnh.
- **memberId**: ID của thành viên.
- **symbol**: Symbol của hợp đồng.
- **direction**: Hướng của lệnh (BUY, SELL).
- **type**: Loại lệnh (LIMIT, MARKET, STOP, STOP_LIMIT, TAKE_PROFIT, TAKE_PROFIT_LIMIT).
- **price**: Giá của lệnh.
- **triggerPrice**: Giá kích hoạt (cho lệnh STOP, STOP_LIMIT, TAKE_PROFIT, TAKE_PROFIT_LIMIT).
- **volume**: Khối lượng của lệnh.
- **dealVolume**: Khối lượng đã khớp.
- **dealMoney**: Số tiền đã khớp.
- **fee**: Phí giao dịch.
- **status**: Trạng thái của lệnh (NEW, PARTIALLY_FILLED, FILLED, CANCELED, REJECTED, EXPIRED).
- **createTime**: Thời gian tạo lệnh.
- **completeTime**: Thời gian hoàn thành lệnh.
- **timeInForce**: Thời gian hiệu lực của lệnh (GTC, IOC, FOK, GTX).
- **expireTime**: Thời gian hết hạn của lệnh.
- **leverage**: Đòn bẩy.
- **reduceOnly**: Chỉ giảm vị thế.
- **callbackRate**: Tỷ lệ callback (cho lệnh TRAILING_STOP).
- **activationPrice**: Giá kích hoạt (cho lệnh TRAILING_STOP).
- **postOnly**: Chỉ đặt lệnh.
- **cancelReason**: Lý do hủy lệnh.

### Phương thức

- **isCompleted()**: Kiểm tra xem lệnh đã hoàn thành chưa.
- **isCancellable()**: Kiểm tra xem lệnh có thể hủy không.
- **isTriggered(BigDecimal currentPrice)**: Kiểm tra xem lệnh có được kích hoạt không.
- **calculateValue()**: Tính toán giá trị của lệnh.
- **calculateRemainingVolume()**: Tính toán khối lượng còn lại của lệnh.
- **calculateFillPercentage()**: Tính toán phần trăm khớp lệnh.

## Position

Position là đối tượng đại diện cho vị thế trong hệ thống. Nó chứa thông tin về vị thế, bao gồm ID, thành viên, symbol, hướng, khối lượng, giá mở, v.v.

### Thuộc tính

- **id**: ID của vị thế.
- **memberId**: ID của thành viên.
- **symbol**: Symbol của hợp đồng.
- **direction**: Hướng của vị thế (LONG, SHORT).
- **volume**: Khối lượng của vị thế.
- **openPrice**: Giá mở vị thế.
- **closePrice**: Giá đóng vị thế.
- **liquidationPrice**: Giá thanh lý.
- **maintenanceMargin**: Ký quỹ bảo trì.
- **margin**: Ký quỹ.
- **profit**: Lợi nhuận.
- **marginMode**: Chế độ ký quỹ (ISOLATED, CROSS).
- **leverage**: Đòn bẩy.
- **createTime**: Thời gian tạo vị thế.
- **updateTime**: Thời gian cập nhật vị thế.
- **remark**: Ghi chú.

### Phương thức

- **calculateValue()**: Tính toán giá trị vị thế.
- **calculateUnrealizedProfit(Money currentPrice)**: Tính toán lợi nhuận không thực hiện.
- **calculateProfitRatio(Money currentPrice)**: Tính toán tỷ lệ lợi nhuận.
- **needsLiquidation(Money currentPrice, BigDecimal maintenanceMarginRate)**: Kiểm tra xem vị thế có cần thanh lý không.
- **calculateLiquidationPrice(BigDecimal maintenanceMarginRate)**: Tính toán giá thanh lý.

## Trade

Trade là đối tượng đại diện cho giao dịch trong hệ thống. Nó chứa thông tin về giao dịch, bao gồm ID, symbol, lệnh mua, lệnh bán, thành viên mua, thành viên bán, giá, khối lượng, v.v.

### Thuộc tính

- **id**: ID của giao dịch.
- **symbol**: Symbol của hợp đồng.
- **buyOrderId**: ID của lệnh mua.
- **sellOrderId**: ID của lệnh bán.
- **buyMemberId**: ID của thành viên mua.
- **sellMemberId**: ID của thành viên bán.
- **price**: Giá của giao dịch.
- **volume**: Khối lượng của giao dịch.
- **buyFee**: Phí giao dịch của thành viên mua.
- **sellFee**: Phí giao dịch của thành viên bán.
- **tradeTime**: Thời gian giao dịch.
- **buyOrderType**: Loại lệnh mua.
- **sellOrderType**: Loại lệnh bán.

### Phương thức

- **calculateValue()**: Tính toán giá trị giao dịch.
- **involveMember(Long memberId)**: Kiểm tra xem giao dịch có liên quan đến một thành viên không.
- **involveOrder(OrderId orderId)**: Kiểm tra xem giao dịch có liên quan đến một lệnh không.
- **getFeeForMember(Long memberId)**: Lấy phí giao dịch cho một thành viên.
- **getDirectionForMember(Long memberId)**: Lấy hướng giao dịch cho một thành viên.

## FundingRate

FundingRate là đối tượng đại diện cho tỷ lệ tài trợ trong hệ thống. Nó chứa thông tin về tỷ lệ tài trợ, bao gồm ID, symbol, tỷ lệ, thời gian, v.v.

### Thuộc tính

- **id**: ID của tỷ lệ tài trợ.
- **symbol**: Symbol của hợp đồng.
- **rate**: Tỷ lệ tài trợ.
- **time**: Thời gian tỷ lệ tài trợ.
- **nextTime**: Thời gian tỷ lệ tài trợ tiếp theo.

### Phương thức

- **isPositive()**: Kiểm tra xem tỷ lệ tài trợ có dương không.
- **isNegative()**: Kiểm tra xem tỷ lệ tài trợ có âm không.
- **calculateFundingAmount(BigDecimal positionValue)**: Tính toán số tiền tài trợ.
- **isNextFundingTime(LocalDateTime currentTime)**: Kiểm tra xem đã đến thời gian tài trợ tiếp theo chưa.

## Contract

Contract là đối tượng đại diện cho hợp đồng trong hệ thống. Nó chứa thông tin về hợp đồng, bao gồm ID, symbol, tên, baseSymbol, quoteSymbol, v.v.

### Thuộc tính

- **id**: ID của hợp đồng.
- **symbol**: Symbol của hợp đồng.
- **name**: Tên của hợp đồng.
- **baseSymbol**: Symbol cơ sở.
- **quoteSymbol**: Symbol báo giá.
- **multiplier**: Hệ số nhân.
- **minVolume**: Khối lượng tối thiểu.
- **maxVolume**: Khối lượng tối đa.
- **pricePrecision**: Độ chính xác của giá.
- **volumePrecision**: Độ chính xác của khối lượng.
- **maintenanceMarginRate**: Tỷ lệ ký quỹ bảo trì.
- **initialMarginRate**: Tỷ lệ ký quỹ ban đầu.
- **leverage**: Đòn bẩy.
- **fundingInterval**: Khoảng thời gian tài trợ (giờ).
- **marginMode**: Chế độ ký quỹ mặc định.
- **enabled**: Trạng thái kích hoạt.
- **createTime**: Thời gian tạo hợp đồng.
- **updateTime**: Thời gian cập nhật hợp đồng.

### Phương thức

- **isEnabled()**: Kiểm tra xem hợp đồng có được kích hoạt không.
- **isValidVolume(BigDecimal volume)**: Kiểm tra xem khối lượng có hợp lệ không.
- **roundPrice(BigDecimal price)**: Làm tròn giá theo độ chính xác của hợp đồng.
- **roundVolume(BigDecimal volume)**: Làm tròn khối lượng theo độ chính xác của hợp đồng.
- **calculateNotionalValue(BigDecimal price, BigDecimal volume)**: Tính toán giá trị danh nghĩa của hợp đồng.
- **calculateInitialMargin(BigDecimal price, BigDecimal volume, BigDecimal leverage)**: Tính toán ký quỹ ban đầu.
- **calculateMaintenanceMargin(BigDecimal price, BigDecimal volume)**: Tính toán ký quỹ bảo trì.

## OrderBook

OrderBook là đối tượng đại diện cho sổ lệnh trong hệ thống. Nó chứa thông tin về các lệnh mua và bán cho một symbol.

### Phương thức

- **addOrder(Order order)**: Thêm lệnh vào sổ lệnh.
- **removeOrder(String orderId)**: Xóa lệnh khỏi sổ lệnh.
- **updateOrder(Order order)**: Cập nhật lệnh trong sổ lệnh.
- **getOrder(String orderId)**: Lấy lệnh từ sổ lệnh.
- **getAllOrders()**: Lấy tất cả các lệnh trong sổ lệnh.
- **getBuyOrders()**: Lấy tất cả các lệnh mua trong sổ lệnh.
- **getSellOrders()**: Lấy tất cả các lệnh bán trong sổ lệnh.
- **getOrdersByDirection(OrderDirection direction)**: Lấy tất cả các lệnh theo hướng.
- **getOrdersByMemberId(Long memberId)**: Lấy tất cả các lệnh của một thành viên.
- **getBestBidPrice()**: Lấy giá mua tốt nhất.
- **getBestAskPrice()**: Lấy giá bán tốt nhất.
- **getVolumeAtPrice(BigDecimal price, OrderDirection direction)**: Lấy khối lượng tại một giá.
- **getOrderCountAtPrice(BigDecimal price, OrderDirection direction)**: Lấy số lượng lệnh tại một giá.
- **getOrdersAtPrice(BigDecimal price, OrderDirection direction)**: Lấy tất cả các lệnh tại một giá.
- **getPriceLevels(OrderDirection direction)**: Lấy tất cả các mức giá.
- **getPriceLevels(OrderDirection direction, int depth)**: Lấy tất cả các mức giá và khối lượng.
- **getTotalVolume(OrderDirection direction)**: Lấy tổng khối lượng của tất cả các lệnh.
- **getTotalValue(OrderDirection direction)**: Lấy tổng giá trị của tất cả các lệnh.
- **clear()**: Xóa tất cả các lệnh.

## IndexPrice

IndexPrice là đối tượng đại diện cho giá chỉ số trong hệ thống. Nó chứa thông tin về giá chỉ số, bao gồm symbol, giá, thời gian, v.v.

### Thuộc tính

- **symbol**: Symbol của hợp đồng.
- **price**: Giá chỉ số.
- **time**: Thời gian cập nhật.

### Phương thức

- **calculateIndexPrice(Map<String, BigDecimal> prices)**: Tính toán giá chỉ số từ các giá từ các sàn giao dịch.
- **isValid()**: Kiểm tra xem giá chỉ số có hợp lệ không.

## MarkPrice

MarkPrice là đối tượng đại diện cho giá đánh dấu trong hệ thống. Nó chứa thông tin về giá đánh dấu, bao gồm symbol, giá, thời gian, v.v.

### Thuộc tính

- **symbol**: Symbol của hợp đồng.
- **price**: Giá đánh dấu.
- **time**: Thời gian cập nhật.

### Phương thức

- **calculateMarkPrice(BigDecimal indexPrice, BigDecimal fundingRate)**: Tính toán giá đánh dấu từ giá chỉ số và tỷ lệ tài trợ.
- **isValid()**: Kiểm tra xem giá đánh dấu có hợp lệ không.

## Value Objects

Value Objects là các đối tượng không có ID, được xác định bởi các thuộc tính của nó. Chúng được sử dụng để đại diện cho các khái niệm trong domain.

### Money

Money là value object đại diện cho tiền tệ trong hệ thống.

#### Thuộc tính

- **value**: Giá trị của tiền tệ.

#### Phương thức

- **add(Money money)**: Cộng hai giá trị tiền tệ.
- **subtract(Money money)**: Trừ hai giá trị tiền tệ.
- **multiply(BigDecimal multiplier)**: Nhân giá trị tiền tệ với một số.
- **divide(BigDecimal divisor)**: Chia giá trị tiền tệ cho một số.
- **isLessThan(Money money)**: Kiểm tra xem giá trị tiền tệ có nhỏ hơn một giá trị khác không.
- **isGreaterThan(Money money)**: Kiểm tra xem giá trị tiền tệ có lớn hơn một giá trị khác không.
- **isZero()**: Kiểm tra xem giá trị tiền tệ có bằng 0 không.

### OrderId

OrderId là value object đại diện cho ID của lệnh trong hệ thống.

#### Thuộc tính

- **value**: Giá trị của ID.

#### Phương thức

- **of(String value)**: Tạo OrderId từ một chuỗi.
- **toString()**: Chuyển đổi OrderId thành chuỗi.

### PositionId

PositionId là value object đại diện cho ID của vị thế trong hệ thống.

#### Thuộc tính

- **value**: Giá trị của ID.

#### Phương thức

- **of(Long value)**: Tạo PositionId từ một số.
- **toString()**: Chuyển đổi PositionId thành chuỗi.

### TradeId

TradeId là value object đại diện cho ID của giao dịch trong hệ thống.

#### Thuộc tính

- **value**: Giá trị của ID.

#### Phương thức

- **of(Long value)**: Tạo TradeId từ một số.
- **toString()**: Chuyển đổi TradeId thành chuỗi.

### FundingRateId

FundingRateId là value object đại diện cho ID của tỷ lệ tài trợ trong hệ thống.

#### Thuộc tính

- **value**: Giá trị của ID.

#### Phương thức

- **of(Long value)**: Tạo FundingRateId từ một số.
- **toString()**: Chuyển đổi FundingRateId thành chuỗi.

### ContractId

ContractId là value object đại diện cho ID của hợp đồng trong hệ thống.

#### Thuộc tính

- **value**: Giá trị của ID.

#### Phương thức

- **of(Long value)**: Tạo ContractId từ một số.
- **toString()**: Chuyển đổi ContractId thành chuỗi.

### Symbol

Symbol là value object đại diện cho symbol của hợp đồng trong hệ thống.

#### Thuộc tính

- **value**: Giá trị của symbol.

#### Phương thức

- **of(String value)**: Tạo Symbol từ một chuỗi.
- **toString()**: Chuyển đổi Symbol thành chuỗi.

## Enums

Enums là các kiểu liệt kê được sử dụng để đại diện cho các giá trị cố định trong domain.

### OrderDirection

OrderDirection là enum đại diện cho hướng của lệnh.

#### Giá trị

- **BUY**: Lệnh mua.
- **SELL**: Lệnh bán.

### OrderType

OrderType là enum đại diện cho loại lệnh.

#### Giá trị

- **LIMIT**: Lệnh giới hạn.
- **MARKET**: Lệnh thị trường.
- **STOP**: Lệnh dừng.
- **STOP_LIMIT**: Lệnh dừng giới hạn.
- **TAKE_PROFIT**: Lệnh chốt lời.
- **TAKE_PROFIT_LIMIT**: Lệnh chốt lời giới hạn.
- **TRAILING_STOP**: Lệnh dừng theo dõi.

### OrderStatus

OrderStatus là enum đại diện cho trạng thái của lệnh.

#### Giá trị

- **NEW**: Lệnh mới.
- **PARTIALLY_FILLED**: Lệnh đã khớp một phần.
- **FILLED**: Lệnh đã khớp hoàn toàn.
- **CANCELED**: Lệnh đã hủy.
- **REJECTED**: Lệnh bị từ chối.
- **EXPIRED**: Lệnh đã hết hạn.

### TimeInForce

TimeInForce là enum đại diện cho thời gian hiệu lực của lệnh.

#### Giá trị

- **GTC**: Good Till Cancel - Lệnh có hiệu lực cho đến khi bị hủy.
- **IOC**: Immediate Or Cancel - Lệnh phải được thực hiện ngay lập tức, nếu không thì bị hủy.
- **FOK**: Fill Or Kill - Lệnh phải được thực hiện toàn bộ ngay lập tức, nếu không thì bị hủy.
- **GTX**: Good Till Crossing - Lệnh có hiệu lực cho đến khi nó trở thành lệnh taker.

### PositionDirection

PositionDirection là enum đại diện cho hướng của vị thế.

#### Giá trị

- **LONG**: Vị thế mua.
- **SHORT**: Vị thế bán.

### MarginMode

MarginMode là enum đại diện cho chế độ ký quỹ.

#### Giá trị

- **ISOLATED**: Ký quỹ cô lập.
- **CROSS**: Ký quỹ chéo.

### MatchingAlgorithm

MatchingAlgorithm là enum đại diện cho thuật toán khớp lệnh.

#### Giá trị

- **FIFO**: First In First Out - Lệnh vào trước được khớp trước.
- **PRO_RATA**: Pro Rata - Lệnh được khớp theo tỷ lệ.
- **IMPLIED**: Implied - Lệnh được khớp theo giá ngầm định.
