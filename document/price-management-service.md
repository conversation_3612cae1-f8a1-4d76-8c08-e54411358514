# PriceManagementService - Tài liệu kỹ thuật

## Giớ<PERSON> thiệu

`PriceManagementService` là một domain service trong module future-core, đư<PERSON><PERSON> thiết kế để quản lý tất cả các chức năng liên quan đến giá trong hệ thống giao dịch hợp đồng tương lai. Service này hợp nhất các chức năng từ các service riêng lẻ trước đây (`PriceService`, `IndexPriceService`, `MarkPriceService` và `PricingService`) để tạo ra một interface thống nhất và dễ sử dụng.

## Cấu trúc

`PriceManagementService` được thiết kế theo mẫu Facade, cung cấp một interface đơn giản cho các service khác để tương tác với các chức năng liên quan đến giá. Service này được chia thành ba nhóm chức năng chính:

1. **Quản lý giá chỉ số (Index Price)**
2. **Quản lý giá đánh dấu (Mark Price)**
3. **Quản lý tỷ lệ tài trợ (Funding Rate)**

### Sơ đồ cấu trúc

```
PriceManagementService (Interface)
├── PriceManagementServiceImpl (Implementation)
│   ├── IndexPriceService (Dependency)
│   ├── MarkPriceService (Dependency)
│   └── FundingRateService (Dependency)
```

## Chức năng

### 1. Quản lý giá chỉ số (Index Price)

Giá chỉ số là giá tham chiếu được tính toán từ các sàn giao dịch khác nhau. `PriceManagementService` cung cấp các phương thức sau để quản lý giá chỉ số:

- `getCurrentIndexPrice(Symbol symbol)`: Lấy giá chỉ số hiện tại cho một symbol.
- `updateIndexPrice(Symbol symbol, Money price, LocalDateTime time)`: Cập nhật giá chỉ số cho một symbol.
- `calculateIndexPrice(Symbol symbol, Map<String, BigDecimal> prices, Contract contract)`: Tính toán giá chỉ số cho một symbol dựa trên giá từ các sàn giao dịch khác nhau.
- `getIndexPriceHistory(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime, int interval)`: Lấy lịch sử giá chỉ số cho một symbol.

### 2. Quản lý giá đánh dấu (Mark Price)

Giá đánh dấu là giá được sử dụng để tính toán lợi nhuận không thực hiện và xác định khi nào cần thanh lý vị thế. `PriceManagementService` cung cấp các phương thức sau để quản lý giá đánh dấu:

- `getCurrentMarkPrice(Symbol symbol)`: Lấy giá đánh dấu hiện tại cho một symbol.
- `updateMarkPrice(Symbol symbol, Money price, LocalDateTime time)`: Cập nhật giá đánh dấu cho một symbol.
- `calculateMarkPrice(Symbol symbol, Money indexPrice, Contract contract, BigDecimal fundingRate)`: Tính toán giá đánh dấu cho một symbol dựa trên giá chỉ số và tỷ lệ tài trợ.
- `getMarkPriceHistory(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime, int interval)`: Lấy lịch sử giá đánh dấu cho một symbol.

### 3. Quản lý tỷ lệ tài trợ (Funding Rate)

Tỷ lệ tài trợ là cơ chế được sử dụng để đảm bảo giá hợp đồng tương lai gần với giá chỉ số. `PriceManagementService` cung cấp các phương thức sau để quản lý tỷ lệ tài trợ:

- `calculateFundingRate(Symbol symbol, Money indexPrice, Money markPrice, Contract contract)`: Tính toán tỷ lệ tài trợ mới dựa trên giá chỉ số và giá đánh dấu.
- `getCurrentFundingRate(Symbol symbol)`: Lấy tỷ lệ tài trợ hiện tại cho một symbol.
- `getNextFundingTime(Symbol symbol)`: Lấy thời gian tài trợ tiếp theo cho một symbol.

## Mẫu thiết kế áp dụng

`PriceManagementService` áp dụng các mẫu thiết kế sau:

1. **Facade Pattern**: `PriceManagementService` cung cấp một interface đơn giản cho các service khác để tương tác với các chức năng liên quan đến giá, ẩn đi sự phức tạp của các service bên dưới.

2. **Dependency Injection**: `PriceManagementServiceImpl` sử dụng dependency injection để nhận các service cần thiết (`IndexPriceService`, `MarkPriceService`, `FundingRateService`).

3. **Retry Pattern**: Các phương thức trong `PriceManagementServiceImpl` sử dụng annotation `@Retryable` để tự động thử lại khi gặp lỗi liên quan đến cơ sở dữ liệu hoặc giao dịch.

4. **Template Method Pattern**: Các phương thức trong `PriceManagementServiceImpl` sử dụng một mẫu chung: kiểm tra đầu vào, gọi service tương ứng, xử lý lỗi.

## Xử lý lỗi

`PriceManagementService` sử dụng các cơ chế sau để xử lý lỗi:

1. **Kiểm tra đầu vào**: Mỗi phương thức đều kiểm tra các tham số đầu vào và ném `ValidationException` nếu có lỗi.

2. **Retry**: Các phương thức sử dụng annotation `@Retryable` để tự động thử lại khi gặp lỗi liên quan đến cơ sở dữ liệu hoặc giao dịch.

3. **Logging**: Mỗi phương thức đều ghi log debug trước khi thực hiện và log error nếu có lỗi.

4. **Exception propagation**: Các lỗi được bắt và ném lại để các service gọi có thể xử lý.

## Ví dụ sử dụng

### Lấy giá chỉ số hiện tại

```java
@Service
@RequiredArgsConstructor
public class SomeService {
    private final PriceManagementService priceManagementService;
    
    public void someMethod() {
        Symbol symbol = Symbol.of("BTC-USDT");
        Money indexPrice = priceManagementService.getCurrentIndexPrice(symbol);
        // Sử dụng indexPrice
    }
}
```

### Tính toán giá đánh dấu

```java
@Service
@RequiredArgsConstructor
public class SomeService {
    private final PriceManagementService priceManagementService;
    private final ContractRepository contractRepository;
    
    public void someMethod() {
        Symbol symbol = Symbol.of("BTC-USDT");
        Money indexPrice = priceManagementService.getCurrentIndexPrice(symbol);
        BigDecimal fundingRate = priceManagementService.getCurrentFundingRate(symbol);
        Contract contract = contractRepository.findBySymbol(symbol).orElseThrow();
        
        Money markPrice = priceManagementService.calculateMarkPrice(symbol, indexPrice, contract, fundingRate);
        // Sử dụng markPrice
    }
}
```

## Cải tiến trong tương lai

Để cải thiện `PriceManagementService` trong tương lai, có thể xem xét các cải tiến sau:

1. **Áp dụng mẫu Strategy**: Cho phép sử dụng các thuật toán khác nhau để tính toán giá chỉ số và giá đánh dấu.

2. **Áp dụng mẫu Observer**: Thông báo cho các service khác khi giá thay đổi.

3. **Áp dụng mẫu Command**: Cho phép hoàn tác các thay đổi giá.

4. **Áp dụng mẫu Decorator**: Thêm các tính năng mới cho `PriceManagementService` mà không cần thay đổi code hiện tại.

5. **Áp dụng mẫu Chain of Responsibility**: Cho phép xử lý các yêu cầu liên quan đến giá theo một chuỗi các handler.

## Kết luận

`PriceManagementService` là một domain service quan trọng trong module future-core, cung cấp các chức năng liên quan đến giá cho hệ thống giao dịch hợp đồng tương lai. Service này được thiết kế theo các nguyên tắc của clean architecture và áp dụng các mẫu thiết kế phù hợp để giảm thiểu độ phức tạp và tăng tính bảo trì.
