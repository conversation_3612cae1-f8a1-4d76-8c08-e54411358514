# Kiến trúc Matching Engine tiên tiến

## 1. Tổng quan kiến trúc

Kiến trúc Matching Engine tiên tiến được thiết kế để giải quyết các vấn đề hiệu suất và khả năng mở rộng của hệ thống giao dịch hợp đồng tương lai, đặc biệt là trường hợp xấu nhất khi ConcurrentSkipListMap biến thành single linked list.

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[API Gateway]
    B -->|Load Balancing| C[Order Router]
    C -->|Phân phối lệnh| D[Shard Manager]
    D -->|Định tuyến lệnh| E[Symbol Shards]
    E -->|Xử lý lệnh| F[Lock-Free Matching Engine]
    F -->|Cập nhật| G[Distributed Order Book]
    F -->|Tạo giao dịch| H[Trade Processor]
    H -->|Thông báo| I[Kafka]
    I -->|C<PERSON><PERSON> nhật| J[Client]
    I -->|C<PERSON><PERSON> nh<PERSON>t| K[Risk Management]
    I -->|Cập nhật| L[Settlement]
```

## 2. Các thành phần chính

### 2.1. Order Router

Order Router là thành phần chịu trách nhiệm phân phối lệnh đến các shard phù hợp dựa trên symbol. Nó sử dụng thuật toán cân bằng tải để đảm bảo các shard không bị quá tải.

```java
public class OrderRouter {
    private final ShardManager shardManager;
    private final LoadBalancer loadBalancer;
    
    public CompletableFuture<List<Trade>> routeOrder(Order order) {
        // Xác định shard dựa trên symbol
        SymbolShard shard = shardManager.getShardForSymbol(order.getSymbol());
        
        // Cân bằng tải giữa các node trong shard
        MatchingEngineNode node = loadBalancer.selectNode(shard);
        
        // Gửi lệnh đến node
        return node.processOrder(order);
    }
}
```

### 2.2. Shard Manager

Shard Manager quản lý các shard theo symbol, mỗi shard chứa một hoặc nhiều cặp giao dịch. Việc sharding giúp phân tán tải và giảm thiểu tác động của trường hợp xấu nhất.

```java
public class ShardManager {
    private final Map<String, SymbolShard> shardsBySymbol = new ConcurrentHashMap<>();
    
    public SymbolShard getShardForSymbol(Symbol symbol) {
        return shardsBySymbol.computeIfAbsent(symbol.getValue(), this::createShard);
    }
    
    private SymbolShard createShard(String symbolStr) {
        // Tạo shard mới cho symbol
        SymbolShard shard = new SymbolShard(symbolStr);
        
        // Khởi tạo các node trong shard
        for (int i = 0; i < NODES_PER_SHARD; i++) {
            shard.addNode(new MatchingEngineNode(symbolStr, i));
        }
        
        return shard;
    }
}
```

### 2.3. Symbol Shard

Symbol Shard là một nhóm các node xử lý cùng một symbol. Mỗi shard có thể chứa nhiều node để đảm bảo khả năng chịu lỗi và cân bằng tải.

```java
public class SymbolShard {
    private final String symbol;
    private final List<MatchingEngineNode> nodes = new ArrayList<>();
    
    public SymbolShard(String symbol) {
        this.symbol = symbol;
    }
    
    public void addNode(MatchingEngineNode node) {
        nodes.add(node);
    }
    
    public List<MatchingEngineNode> getNodes() {
        return Collections.unmodifiableList(nodes);
    }
}
```

### 2.4. Lock-Free Matching Engine

Lock-Free Matching Engine sử dụng các cấu trúc dữ liệu không khóa để tránh tình trạng nghẽn cổ chai khi nhiều thread cùng truy cập.

```java
public class LockFreeMatchingEngine {
    private final Symbol symbol;
    
    // Sử dụng cấu trúc dữ liệu lock-free
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());
    
    public List<Trade> processOrder(Order order) {
        // Xử lý lệnh theo cơ chế lock-free
        while (true) {
            OrderBookSnapshot current = orderBookRef.get();
            OrderBookResult result = matchOrder(order, current);
            
            if (orderBookRef.compareAndSet(current, result.getNewSnapshot())) {
                return result.getTrades();
            }
            
            // Nếu CAS thất bại, thử lại
        }
    }
    
    private OrderBookResult matchOrder(Order order, OrderBookSnapshot snapshot) {
        // Tạo bản sao của snapshot
        OrderBookSnapshot newSnapshot = snapshot.copy();
        List<Trade> trades = new ArrayList<>();
        
        // Thực hiện khớp lệnh trên bản sao
        if (order.getDirection() == OrderDirection.BUY) {
            matchBuyOrder(order, newSnapshot, trades);
        } else {
            matchSellOrder(order, newSnapshot, trades);
        }
        
        return new OrderBookResult(newSnapshot, trades);
    }
}
```

### 2.5. Distributed Order Book

Distributed Order Book là sổ lệnh phân tán, được thiết kế để xử lý trường hợp xấu nhất của ConcurrentSkipListMap.

```java
public class DistributedOrderBook {
    // Phân chia sổ lệnh thành các phân đoạn theo khoảng giá
    private final Map<PriceRange, OrderBookSegment> segments = new ConcurrentHashMap<>();
    
    public void addOrder(Order order) {
        // Xác định phân đoạn dựa trên giá
        PriceRange range = PriceRange.forPrice(order.getPrice());
        
        // Thêm lệnh vào phân đoạn
        segments.computeIfAbsent(range, r -> new OrderBookSegment(r))
                .addOrder(order);
    }
    
    public List<Order> getOrdersAtPrice(Money price) {
        // Xác định phân đoạn dựa trên giá
        PriceRange range = PriceRange.forPrice(price);
        
        // Lấy lệnh từ phân đoạn
        OrderBookSegment segment = segments.get(range);
        return segment != null ? segment.getOrdersAtPrice(price) : Collections.emptyList();
    }
}
```

### 2.6. Order Book Segment

Order Book Segment là một phân đoạn của sổ lệnh, chứa các lệnh trong một khoảng giá nhất định.

```java
public class OrderBookSegment {
    private final PriceRange range;
    
    // Sử dụng cấu trúc dữ liệu hiệu quả cho phân đoạn
    private final NavigableMap<Money, List<Order>> orders = new ConcurrentSkipListMap<>();
    
    public OrderBookSegment(PriceRange range) {
        this.range = range;
    }
    
    public void addOrder(Order order) {
        orders.computeIfAbsent(order.getPrice(), p -> new CopyOnWriteArrayList<>())
              .add(order);
    }
    
    public List<Order> getOrdersAtPrice(Money price) {
        return orders.getOrDefault(price, Collections.emptyList());
    }
    
    public boolean isEmpty() {
        return orders.isEmpty();
    }
    
    public int size() {
        return orders.size();
    }
}
```

## 3. Cơ chế Lock-Free

### 3.1. Atomic Reference và Compare-And-Swap (CAS)

Matching Engine sử dụng AtomicReference và CAS để cập nhật sổ lệnh mà không cần khóa.

```java
private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

public List<Trade> processOrder(Order order) {
    while (true) {
        OrderBookSnapshot current = orderBookRef.get();
        OrderBookResult result = matchOrder(order, current);
        
        if (orderBookRef.compareAndSet(current, result.getNewSnapshot())) {
            return result.getTrades();
        }
        
        // Nếu CAS thất bại, thử lại
    }
}
```

### 3.2. Immutable Data Structures

Sử dụng cấu trúc dữ liệu bất biến để tránh race condition.

```java
public class OrderBookSnapshot {
    private final ImmutableNavigableMap<Money, ImmutableList<Order>> buyOrders;
    private final ImmutableNavigableMap<Money, ImmutableList<Order>> sellOrders;
    
    public OrderBookSnapshot copy() {
        return new OrderBookSnapshot(buyOrders, sellOrders);
    }
    
    public OrderBookSnapshot withAddedBuyOrder(Money price, Order order) {
        ImmutableNavigableMap<Money, ImmutableList<Order>> newBuyOrders = buyOrders.put(
            price,
            buyOrders.getOrDefault(price, ImmutableList.of()).add(order)
        );
        
        return new OrderBookSnapshot(newBuyOrders, sellOrders);
    }
    
    public OrderBookSnapshot withAddedSellOrder(Money price, Order order) {
        ImmutableNavigableMap<Money, ImmutableList<Order>> newSellOrders = sellOrders.put(
            price,
            sellOrders.getOrDefault(price, ImmutableList.of()).add(order)
        );
        
        return new OrderBookSnapshot(buyOrders, newSellOrders);
    }
}
```

### 3.3. Lock-Free Algorithms

Sử dụng thuật toán không khóa để xử lý các thao tác phức tạp.

```java
public class LockFreeQueue<T> {
    private final AtomicReference<Node<T>> head;
    private final AtomicReference<Node<T>> tail;
    
    public LockFreeQueue() {
        Node<T> dummy = new Node<>(null);
        head = new AtomicReference<>(dummy);
        tail = new AtomicReference<>(dummy);
    }
    
    public void enqueue(T item) {
        Node<T> newNode = new Node<>(item);
        
        while (true) {
            Node<T> currentTail = tail.get();
            Node<T> tailNext = currentTail.next.get();
            
            if (currentTail == tail.get()) {
                if (tailNext != null) {
                    // Tail is falling behind, help advance it
                    tail.compareAndSet(currentTail, tailNext);
                } else {
                    // Try to link new node at the end of the list
                    if (currentTail.next.compareAndSet(null, newNode)) {
                        // Enqueue is done, try to advance tail
                        tail.compareAndSet(currentTail, newNode);
                        return;
                    }
                }
            }
        }
    }
    
    private static class Node<T> {
        final T item;
        final AtomicReference<Node<T>> next;
        
        Node(T item) {
            this.item = item;
            this.next = new AtomicReference<>(null);
        }
    }
}
```

## 4. Cơ chế Load Balancing

### 4.1. Consistent Hashing

Sử dụng Consistent Hashing để phân phối lệnh đến các node.

```java
public class ConsistentHashLoadBalancer implements LoadBalancer {
    private final int virtualNodesPerNode = 100;
    private final NavigableMap<Integer, MatchingEngineNode> ring = new TreeMap<>();
    
    public void addNode(MatchingEngineNode node) {
        for (int i = 0; i < virtualNodesPerNode; i++) {
            int hash = hash(node.getId() + "-" + i);
            ring.put(hash, node);
        }
    }
    
    public void removeNode(MatchingEngineNode node) {
        for (int i = 0; i < virtualNodesPerNode; i++) {
            int hash = hash(node.getId() + "-" + i);
            ring.remove(hash);
        }
    }
    
    @Override
    public MatchingEngineNode selectNode(SymbolShard shard) {
        if (ring.isEmpty()) {
            throw new IllegalStateException("No nodes available");
        }
        
        int hash = hash(shard.getSymbol());
        if (!ring.containsKey(hash)) {
            SortedMap<Integer, MatchingEngineNode> tailMap = ring.tailMap(hash);
            hash = tailMap.isEmpty() ? ring.firstKey() : tailMap.firstKey();
        }
        
        return ring.get(hash);
    }
    
    private int hash(String key) {
        return key.hashCode() & 0x7FFFFFFF;
    }
}
```

### 4.2. Least Connections

Sử dụng thuật toán Least Connections để chọn node có ít kết nối nhất.

```java
public class LeastConnectionsLoadBalancer implements LoadBalancer {
    private final Map<MatchingEngineNode, AtomicInteger> connectionCounts = new ConcurrentHashMap<>();
    
    @Override
    public MatchingEngineNode selectNode(SymbolShard shard) {
        List<MatchingEngineNode> nodes = shard.getNodes();
        
        if (nodes.isEmpty()) {
            throw new IllegalStateException("No nodes available");
        }
        
        // Chọn node có ít kết nối nhất
        return nodes.stream()
                   .min(Comparator.comparingInt(node -> connectionCounts.computeIfAbsent(node, n -> new AtomicInteger()).get()))
                   .orElseThrow();
    }
    
    public void incrementConnections(MatchingEngineNode node) {
        connectionCounts.computeIfAbsent(node, n -> new AtomicInteger()).incrementAndGet();
    }
    
    public void decrementConnections(MatchingEngineNode node) {
        AtomicInteger count = connectionCounts.get(node);
        if (count != null) {
            count.decrementAndGet();
        }
    }
}
```

### 4.3. Power of Two Choices

Sử dụng thuật toán Power of Two Choices để cân bằng tải.

```java
public class PowerOfTwoChoicesLoadBalancer implements LoadBalancer {
    private final Random random = new Random();
    private final Map<MatchingEngineNode, AtomicInteger> loadCounts = new ConcurrentHashMap<>();
    
    @Override
    public MatchingEngineNode selectNode(SymbolShard shard) {
        List<MatchingEngineNode> nodes = shard.getNodes();
        
        if (nodes.isEmpty()) {
            throw new IllegalStateException("No nodes available");
        }
        
        if (nodes.size() == 1) {
            return nodes.get(0);
        }
        
        // Chọn ngẫu nhiên 2 node
        MatchingEngineNode node1 = nodes.get(random.nextInt(nodes.size()));
        MatchingEngineNode node2 = nodes.get(random.nextInt(nodes.size()));
        
        // Chọn node có tải thấp hơn
        int load1 = loadCounts.computeIfAbsent(node1, n -> new AtomicInteger()).get();
        int load2 = loadCounts.computeIfAbsent(node2, n -> new AtomicInteger()).get();
        
        MatchingEngineNode selectedNode = load1 <= load2 ? node1 : node2;
        loadCounts.get(selectedNode).incrementAndGet();
        
        return selectedNode;
    }
    
    public void decrementLoad(MatchingEngineNode node) {
        AtomicInteger load = loadCounts.get(node);
        if (load != null) {
            load.decrementAndGet();
        }
    }
}
```

## 5. Cơ chế Sharding

### 5.1. Symbol-Based Sharding

Phân chia dữ liệu theo symbol để mỗi shard chỉ xử lý một tập hợp các symbol.

```java
public class SymbolShardManager {
    private final int numShards;
    private final Map<Integer, SymbolShard> shards = new ConcurrentHashMap<>();
    
    public SymbolShardManager(int numShards) {
        this.numShards = numShards;
        
        // Khởi tạo các shard
        for (int i = 0; i < numShards; i++) {
            shards.put(i, new SymbolShard(i));
        }
    }
    
    public SymbolShard getShardForSymbol(Symbol symbol) {
        // Tính toán shard ID dựa trên symbol
        int shardId = Math.abs(symbol.getValue().hashCode() % numShards);
        return shards.get(shardId);
    }
}
```

### 5.2. Price Range Sharding

Phân chia sổ lệnh theo khoảng giá để tránh trường hợp xấu nhất của ConcurrentSkipListMap.

```java
public class PriceRangeSharding {
    private final BigDecimal rangeSize;
    private final Map<PriceRange, OrderBookSegment> segments = new ConcurrentHashMap<>();
    
    public PriceRangeSharding(BigDecimal rangeSize) {
        this.rangeSize = rangeSize;
    }
    
    public OrderBookSegment getSegmentForPrice(Money price) {
        PriceRange range = calculatePriceRange(price);
        return segments.computeIfAbsent(range, r -> new OrderBookSegment(r));
    }
    
    private PriceRange calculatePriceRange(Money price) {
        BigDecimal value = price.getValue();
        BigDecimal lowerBound = value.divideToIntegralValue(rangeSize).multiply(rangeSize);
        BigDecimal upperBound = lowerBound.add(rangeSize);
        return new PriceRange(Money.of(lowerBound), Money.of(upperBound));
    }
}
```

### 5.3. Dynamic Sharding

Điều chỉnh số lượng shard động dựa trên tải.

```java
public class DynamicShardManager {
    private final AtomicInteger numShards = new AtomicInteger(INITIAL_SHARDS);
    private final ConcurrentHashMap<Integer, SymbolShard> shards = new ConcurrentHashMap<>();
    private final LoadMonitor loadMonitor = new LoadMonitor();
    
    public DynamicShardManager() {
        // Khởi tạo các shard ban đầu
        for (int i = 0; i < INITIAL_SHARDS; i++) {
            shards.put(i, new SymbolShard(i));
        }
        
        // Bắt đầu giám sát tải
        startLoadMonitoring();
    }
    
    private void startLoadMonitoring() {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleAtFixedRate(this::adjustShards, 1, 1, TimeUnit.MINUTES);
    }
    
    private void adjustShards() {
        if (loadMonitor.isOverloaded()) {
            // Tăng số lượng shard
            int currentShards = numShards.get();
            int newShards = currentShards * 2;
            
            // Tạo các shard mới
            for (int i = currentShards; i < newShards; i++) {
                shards.put(i, new SymbolShard(i));
            }
            
            numShards.set(newShards);
            
            // Phân phối lại các symbol
            redistributeSymbols();
        } else if (loadMonitor.isUnderloaded()) {
            // Giảm số lượng shard
            int currentShards = numShards.get();
            int newShards = Math.max(INITIAL_SHARDS, currentShards / 2);
            
            if (newShards < currentShards) {
                // Xóa các shard không cần thiết
                for (int i = newShards; i < currentShards; i++) {
                    shards.remove(i);
                }
                
                numShards.set(newShards);
                
                // Phân phối lại các symbol
                redistributeSymbols();
            }
        }
    }
    
    private void redistributeSymbols() {
        // Phân phối lại các symbol giữa các shard
    }
}
```

## 6. Xử lý trường hợp xấu nhất của ConcurrentSkipListMap

### 6.1. Phân đoạn theo khoảng giá

```java
public class SegmentedOrderBook {
    private final BigDecimal segmentSize;
    private final ConcurrentHashMap<PriceRange, ConcurrentSkipListMap<Money, List<Order>>> segments = new ConcurrentHashMap<>();
    
    public SegmentedOrderBook(BigDecimal segmentSize) {
        this.segmentSize = segmentSize;
    }
    
    public void addOrder(Order order) {
        PriceRange range = calculatePriceRange(order.getPrice());
        ConcurrentSkipListMap<Money, List<Order>> segment = segments.computeIfAbsent(range, r -> new ConcurrentSkipListMap<>());
        
        segment.computeIfAbsent(order.getPrice(), p -> new CopyOnWriteArrayList<>()).add(order);
    }
    
    public List<Order> getOrdersAtPrice(Money price) {
        PriceRange range = calculatePriceRange(price);
        ConcurrentSkipListMap<Money, List<Order>> segment = segments.get(range);
        
        if (segment == null) {
            return Collections.emptyList();
        }
        
        List<Order> orders = segment.get(price);
        return orders != null ? orders : Collections.emptyList();
    }
    
    private PriceRange calculatePriceRange(Money price) {
        BigDecimal value = price.getValue();
        BigDecimal lowerBound = value.divideToIntegralValue(segmentSize).multiply(segmentSize);
        BigDecimal upperBound = lowerBound.add(segmentSize);
        return new PriceRange(Money.of(lowerBound), Money.of(upperBound));
    }
}
```

### 6.2. Giới hạn kích thước ConcurrentSkipListMap

```java
public class SizeLimitedOrderBook {
    private static final int MAX_PRICE_LEVELS = 1000;
    private final NavigableMap<Money, List<Order>> buyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
    private final NavigableMap<Money, List<Order>> sellOrders = new ConcurrentSkipListMap<>();
    
    public void addBuyOrder(Order order) {
        addOrder(buyOrders, order);
    }
    
    public void addSellOrder(Order order) {
        addOrder(sellOrders, order);
    }
    
    private void addOrder(NavigableMap<Money, List<Order>> orders, Order order) {
        // Kiểm tra số lượng mức giá
        if (orders.size() >= MAX_PRICE_LEVELS) {
            // Xóa mức giá xa nhất
            if (orders == buyOrders) {
                // Đối với lệnh mua, xóa mức giá thấp nhất
                orders.pollLastEntry();
            } else {
                // Đối với lệnh bán, xóa mức giá cao nhất
                orders.pollLastEntry();
            }
        }
        
        // Thêm lệnh vào sổ lệnh
        orders.computeIfAbsent(order.getPrice(), p -> new CopyOnWriteArrayList<>()).add(order);
    }
}
```

### 6.3. Tái cấu trúc định kỳ

```java
public class PeriodicRestructuringOrderBook {
    private final NavigableMap<Money, List<Order>> buyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
    private final NavigableMap<Money, List<Order>> sellOrders = new ConcurrentSkipListMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    public PeriodicRestructuringOrderBook() {
        // Lên lịch tái cấu trúc định kỳ
        scheduler.scheduleAtFixedRate(this::restructure, 1, 1, TimeUnit.HOURS);
    }
    
    private void restructure() {
        restructureOrderBook(buyOrders);
        restructureOrderBook(sellOrders);
    }
    
    private void restructureOrderBook(NavigableMap<Money, List<Order>> orders) {
        // Tạo map mới
        NavigableMap<Money, List<Order>> newOrders = new ConcurrentSkipListMap<>(orders.comparator());
        
        // Sao chép dữ liệu từ map cũ sang map mới
        newOrders.putAll(orders);
        
        // Xóa các mức giá không có lệnh
        newOrders.entrySet().removeIf(entry -> entry.getValue().isEmpty());
        
        // Thay thế map cũ bằng map mới
        orders.clear();
        orders.putAll(newOrders);
    }
}
```

## 7. Kết luận

Kiến trúc Matching Engine tiên tiến sử dụng các cơ chế lock-free, load balancing và sharding để giải quyết các vấn đề hiệu suất và khả năng mở rộng của hệ thống giao dịch hợp đồng tương lai. Đặc biệt, nó xử lý hiệu quả trường hợp xấu nhất của ConcurrentSkipListMap bằng cách phân đoạn sổ lệnh, giới hạn kích thước và tái cấu trúc định kỳ.

Các cơ chế này giúp hệ thống đạt được:
- **Hiệu suất cao**: Xử lý hàng triệu lệnh mỗi giây
- **Khả năng mở rộng**: Dễ dàng thêm node mới khi tải tăng
- **Độ tin cậy**: Duy trì hoạt động ngay cả khi một số node gặp sự cố
- **Tính nhất quán**: Đảm bảo tính nhất quán của dữ liệu trong môi trường phân tán
