# Tài liệu API Future Core

Tài liệu này mô tả các API endpoint có sẵn trong module Future Core. Các API này được sử dụng để tương tác với hệ thống giao dịch hợp đồng tương lai.

## Cấu trúc chung của Response

Tất cả các API đều trả về một cấu trúc response chung như sau:

```json
{
  "code": 200,
  "message": "Success",
  "data": { ... },
  "timestamp": "2023-05-01T12:00:00"
}
```

Trong đó:
- `code`: Mã trạng thái HTTP
- `message`: Thông báo
- `data`: Dữ liệu trả về (tùy thuộc vào API)
- `timestamp`: Thời gian phản hồi

## Các tham số quan trọng

### Lo<PERSON><PERSON> l<PERSON> (OrderType)

| Gi<PERSON> trị | <PERSON><PERSON> tả |
|---------|-------|
| `LIMIT` | Lệnh giới hạn - đặt lệnh với giá cụ thể |
| `MARKET` | Lệnh thị trường - khớp ngay lập tức theo giá thị trường hiện tại |
| `STOP_LOSS` | Lệnh dừng lỗ - kích hoạt khi giá chạm mức kích hoạt, sau đó đặt lệnh thị trường |
| `STOP_LOSS_LIMIT` | Lệnh dừng lỗ giới hạn - kích hoạt khi giá chạm mức kích hoạt, sau đó đặt lệnh giới hạn |
| `TAKE_PROFIT` | Lệnh chốt lời - kích hoạt khi giá chạm mức kích hoạt, sau đó đặt lệnh thị trường |
| `TAKE_PROFIT_LIMIT` | Lệnh chốt lời giới hạn - kích hoạt khi giá chạm mức kích hoạt, sau đó đặt lệnh giới hạn |
| `POST_ONLY` | Lệnh chỉ được tạo - không được khớp ngay, chỉ được thêm vào sổ lệnh |
| `FILL_OR_KILL` | Lệnh FOK - phải được khớp toàn bộ ngay lập tức hoặc bị hủy |
| `IMMEDIATE_OR_CANCEL` | Lệnh IOC - phải được khớp một phần ngay lập tức, phần còn lại bị hủy |
| `TRAILING_STOP` | Lệnh trailing stop - tự động điều chỉnh giá kích hoạt theo biến động thị trường |

### Loại kích hoạt (TriggerType)

| Giá trị | Mô tả |
|---------|-------|
| `LAST_PRICE` | Kích hoạt dựa trên giá giao dịch gần nhất |
| `MARK_PRICE` | Kích hoạt dựa trên giá đánh dấu |
| `INDEX_PRICE` | Kích hoạt dựa trên giá chỉ số |

### Hướng lệnh (OrderDirection)

| Giá trị | Mô tả |
|---------|-------|
| `BUY` | Lệnh mua |
| `SELL` | Lệnh bán |

### Thời gian hiệu lực (TimeInForce)

| Giá trị | Mô tả |
|---------|-------|
| `GTC` | Good Till Cancel - lệnh có hiệu lực cho đến khi bị hủy |
| `IOC` | Immediate Or Cancel - lệnh phải được khớp một phần ngay lập tức, phần còn lại bị hủy |
| `FOK` | Fill Or Kill - lệnh phải được khớp toàn bộ ngay lập tức hoặc bị hủy |
| `GTX` | Good Till Crossing - lệnh có hiệu lực cho đến khi trở thành lệnh taker |

## Hướng dẫn sử dụng các loại lệnh

### Lệnh thị trường (Market Order)

Lệnh thị trường là lệnh được khớp ngay lập tức theo giá thị trường hiện tại. Khi đặt lệnh thị trường, bạn không cần chỉ định giá, hệ thống sẽ tự động khớp lệnh với các lệnh hiện có trong sổ lệnh theo giá tốt nhất có sẵn.

**Ưu điểm**:
- Khớp lệnh ngay lập tức
- Đảm bảo lệnh được thực hiện

**Nhược điểm**:
- Không kiểm soát được giá khớp lệnh
- Có thể bị trượt giá (slippage) khi thị trường biến động mạnh

**Khi nào sử dụng**:
- Khi cần khớp lệnh ngay lập tức
- Khi ưu tiên tốc độ thực hiện hơn giá khớp lệnh
- Khi cần đóng vị thế nhanh chóng để cắt lỗ hoặc chốt lời

### Lệnh Stop Loss (SL)

Lệnh Stop Loss là lệnh được sử dụng để giới hạn thua lỗ. Lệnh này sẽ được kích hoạt khi giá thị trường chạm mức giá kích hoạt (trigger price) và sau đó sẽ đặt một lệnh thị trường để đóng vị thế.

**Cách hoạt động**:
- Đối với vị thế LONG (mua): Lệnh SL sẽ là lệnh SELL và được kích hoạt khi giá giảm xuống dưới mức giá kích hoạt
- Đối với vị thế SHORT (bán): Lệnh SL sẽ là lệnh BUY và được kích hoạt khi giá tăng lên trên mức giá kích hoạt

**Lưu ý**:
- Nên sử dụng với `reduceOnly: true` để đảm bảo chỉ đóng vị thế hiện có
- Có thể bị trượt giá khi thị trường biến động mạnh
- Nên sử dụng `maxSlippage` để giới hạn mức trượt giá tối đa

### Lệnh Take Profit (TP)

Lệnh Take Profit là lệnh được sử dụng để chốt lời. Lệnh này sẽ được kích hoạt khi giá thị trường chạm mức giá kích hoạt (trigger price) và sau đó sẽ đặt một lệnh thị trường để đóng vị thế.

**Cách hoạt động**:
- Đối với vị thế LONG (mua): Lệnh TP sẽ là lệnh SELL và được kích hoạt khi giá tăng lên trên mức giá kích hoạt
- Đối với vị thế SHORT (bán): Lệnh TP sẽ là lệnh BUY và được kích hoạt khi giá giảm xuống dưới mức giá kích hoạt

**Lưu ý**:
- Nên sử dụng với `reduceOnly: true` để đảm bảo chỉ đóng vị thế hiện có
- Có thể bị trượt giá khi thị trường biến động mạnh
- Nên sử dụng `maxSlippage` để giới hạn mức trượt giá tối đa

### Sự khác biệt giữa lệnh SL/TP thường và lệnh SL/TP Limit

- **Lệnh SL/TP thường**: Khi được kích hoạt, sẽ đặt một lệnh thị trường (market order) để đóng vị thế ngay lập tức theo giá thị trường hiện tại.
- **Lệnh SL/TP Limit**: Khi được kích hoạt, sẽ đặt một lệnh giới hạn (limit order) với giá đã chỉ định trước. Lệnh này có thể không được khớp ngay lập tức nếu giá thị trường không đạt đến mức giá đã chỉ định.

**Khi nào sử dụng lệnh SL/TP Limit**:
- Khi muốn kiểm soát giá đóng vị thế
- Khi muốn tránh trượt giá trong thị trường biến động mạnh
- Khi sẵn sàng chấp nhận rủi ro lệnh không được khớp nếu giá thị trường không đạt đến mức giá đã chỉ định

## Danh sách API

### 1. Quản lý Hợp đồng (Contract)

#### 1.1. Lấy danh sách tất cả hợp đồng

- **URL**: `/api/v1/contracts`
- **Method**: GET
- **Description**: Lấy danh sách tất cả hợp đồng
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "baseSymbol": "BTC",
        "quoteSymbol": "USDT",
        "enable": 1,
        "sort": 1,
        "fee": 0.0004,
        "leverageMin": 1,
        "leverageMax": 125,
        "minVolume": 0.001,
        "maxVolume": 100,
        "priceScale": 1,
        "volumeScale": 3,
        "marginMode": "CROSSED",
        "maintenanceMarginRate": 0.005,
        "initialMarginRate": 0.01
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 1.2. Lấy danh sách hợp đồng đã kích hoạt

- **URL**: `/api/v1/contracts/enabled`
- **Method**: GET
- **Description**: Lấy danh sách hợp đồng đã kích hoạt
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "baseSymbol": "BTC",
        "quoteSymbol": "USDT",
        "enable": 1,
        "sort": 1,
        "fee": 0.0004,
        "leverageMin": 1,
        "leverageMax": 125,
        "minVolume": 0.001,
        "maxVolume": 100,
        "priceScale": 1,
        "volumeScale": 3,
        "marginMode": "CROSSED",
        "maintenanceMarginRate": 0.005,
        "initialMarginRate": 0.01
      },
      {
        "id": 2,
        "symbol": "ETHUSDT",
        "baseSymbol": "ETH",
        "quoteSymbol": "USDT",
        "enable": 1,
        "sort": 2,
        "fee": 0.0004,
        "leverageMin": 1,
        "leverageMax": 100,
        "minVolume": 0.01,
        "maxVolume": 1000,
        "priceScale": 2,
        "volumeScale": 2,
        "marginMode": "CROSSED",
        "maintenanceMarginRate": 0.005,
        "initialMarginRate": 0.01
      }
    ],
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 1.3. Lấy thông tin hợp đồng theo ID

- **URL**: `/api/v1/contracts/{id}`
- **Method**: GET
- **Path Parameters**:
  - `id`: ID của hợp đồng
- **Description**: Lấy thông tin hợp đồng theo ID
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "symbol": "BTCUSDT",
      "baseSymbol": "BTC",
      "quoteSymbol": "USDT",
      "enable": 1,
      "sort": 1,
      "fee": 0.0004,
      "leverageMin": 1,
      "leverageMax": 125,
      "minVolume": 0.001,
      "maxVolume": 100,
      "priceScale": 1,
      "volumeScale": 3,
      "marginMode": "CROSSED",
      "maintenanceMarginRate": 0.005,
      "initialMarginRate": 0.01
    },
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 1.4. Lấy thông tin hợp đồng theo symbol

- **URL**: `/api/v1/contracts/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy thông tin hợp đồng theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "symbol": "BTCUSDT",
      "baseSymbol": "BTC",
      "quoteSymbol": "USDT",
      "enable": 1,
      "sort": 1,
      "fee": 0.0004,
      "leverageMin": 1,
      "leverageMax": 125,
      "minVolume": 0.001,
      "maxVolume": 100,
      "priceScale": 1,
      "volumeScale": 3,
      "marginMode": "CROSSED",
      "maintenanceMarginRate": 0.005,
      "initialMarginRate": 0.01
    },
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 1.5. Tạo hợp đồng mới

- **URL**: `/api/v1/contracts`
- **Method**: POST
- **Description**: Tạo hợp đồng mới
- **Request Body**:
  ```json
  {
    "symbol": "ETHUSDT",
    "baseSymbol": "ETH",
    "quoteSymbol": "USDT",
    "enable": 1,
    "sort": 2,
    "fee": 0.0004,
    "leverageMin": 1,
    "leverageMax": 100,
    "minVolume": 0.01,
    "maxVolume": 1000,
    "priceScale": 2,
    "volumeScale": 2,
    "marginMode": "CROSSED",
    "maintenanceMarginRate": 0.005,
    "initialMarginRate": 0.01
  }
  ```
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 3,
      "symbol": "ETHUSDT",
      "baseSymbol": "ETH",
      "quoteSymbol": "USDT",
      "enable": 1,
      "sort": 2,
      "fee": 0.0004,
      "leverageMin": 1,
      "leverageMax": 100,
      "minVolume": 0.01,
      "maxVolume": 1000,
      "priceScale": 2,
      "volumeScale": 2,
      "marginMode": "CROSSED",
      "maintenanceMarginRate": 0.005,
      "initialMarginRate": 0.01
    },
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 1.6. Cập nhật hợp đồng

- **URL**: `/api/v1/contracts/{id}`
- **Method**: PUT
- **Path Parameters**:
  - `id`: ID của hợp đồng
- **Description**: Cập nhật hợp đồng
- **Request Body**:
  ```json
  {
    "symbol": "ETHUSDT",
    "baseSymbol": "ETH",
    "quoteSymbol": "USDT",
    "enable": 1,
    "sort": 3,
    "fee": 0.0005,
    "leverageMin": 1,
    "leverageMax": 75,
    "minVolume": 0.05,
    "maxVolume": 500,
    "priceScale": 2,
    "volumeScale": 2,
    "marginMode": "CROSSED",
    "maintenanceMarginRate": 0.01,
    "initialMarginRate": 0.02
  }
  ```
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 2,
      "symbol": "ETHUSDT",
      "baseSymbol": "ETH",
      "quoteSymbol": "USDT",
      "enable": 1,
      "sort": 3,
      "fee": 0.0005,
      "leverageMin": 1,
      "leverageMax": 75,
      "minVolume": 0.05,
      "maxVolume": 500,
      "priceScale": 2,
      "volumeScale": 2,
      "marginMode": "CROSSED",
      "maintenanceMarginRate": 0.01,
      "initialMarginRate": 0.02
    },
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 1.7. Xóa hợp đồng

- **URL**: `/api/v1/contracts/{id}`
- **Method**: DELETE
- **Path Parameters**:
  - `id`: ID của hợp đồng
- **Description**: Xóa hợp đồng
- **Response**: HTTP 204 No Content

### 2. Quản lý Giao dịch (Trading)

#### 2.1. Đặt lệnh mới

- **URL**: `/api/v1/trading/orders`
- **Method**: POST
- **Description**: Đặt lệnh mới
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "BUY",
    "type": "LIMIT",
    "price": 50000.00,
    "triggerPrice": null,
    "volume": 0.1,
    "timeInForce": "GTC",
    "expireTime": null,
    "leverage": 10,
    "reduceOnly": false,
    "callbackRate": null,
    "postOnly": false,
    "maxSlippage": null,
    "fillOrKill": false,
    "immediateOrCancel": false
  }
  ```
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "orderId": "ORD123456789",
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "BUY",
      "type": "LIMIT",
      "price": 50000.00,
      "volume": 0.1,
      "status": "NEW",
      "createTime": "2023-05-01T12:00:00"
    },
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 2.1.1. Đặt lệnh thị trường (Market Order)

- **URL**: `/api/v1/trading/orders`
- **Method**: POST
- **Description**: Đặt lệnh thị trường (khớp ngay lập tức theo giá thị trường)
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "BUY",
    "type": "MARKET",
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": false
  }
  ```
- **Lưu ý**:
  - Không cần cung cấp `price` vì giá sẽ được xác định bởi thị trường tại thời điểm khớp lệnh
  - Lệnh thị trường sẽ được khớp ngay lập tức với các lệnh hiện có trong sổ lệnh
  - Nên sử dụng `maxSlippage` để giới hạn mức trượt giá tối đa
- **Response**: Tương tự như đặt lệnh thông thường

#### 2.1.2. Đặt lệnh Stop Loss (SL)

- **URL**: `/api/v1/trading/orders`
- **Method**: POST
- **Description**: Đặt lệnh dừng lỗ (kích hoạt khi giá đạt đến mức kích hoạt)
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "SELL",
    "type": "STOP_LOSS",
    "triggerPrice": 48000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": true
  }
  ```
- **Lưu ý**:
  - `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
  - Khi giá thị trường chạm mức `triggerPrice`, lệnh sẽ được kích hoạt và chuyển thành lệnh thị trường
  - Thường được sử dụng với `reduceOnly: true` để đảm bảo chỉ đóng vị thế hiện có
- **Response**: Tương tự như đặt lệnh thông thường

#### 2.1.3. Đặt lệnh Stop Loss Limit (SL Limit)

- **URL**: `/api/v1/trading/orders`
- **Method**: POST
- **Description**: Đặt lệnh dừng lỗ giới hạn (kích hoạt khi giá đạt đến mức kích hoạt, sau đó đặt lệnh giới hạn)
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "SELL",
    "type": "STOP_LOSS_LIMIT",
    "price": 47900.00,
    "triggerPrice": 48000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": true
  }
  ```
- **Lưu ý**:
  - `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
  - `price` là giá đặt lệnh sau khi lệnh được kích hoạt (bắt buộc)
  - Khi giá thị trường chạm mức `triggerPrice`, lệnh sẽ được kích hoạt và chuyển thành lệnh giới hạn với giá `price`
- **Response**: Tương tự như đặt lệnh thông thường

#### 2.1.4. Đặt lệnh Take Profit (TP)

- **URL**: `/api/v1/trading/orders`
- **Method**: POST
- **Description**: Đặt lệnh chốt lời (kích hoạt khi giá đạt đến mức kích hoạt)
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "SELL",
    "type": "TAKE_PROFIT",
    "triggerPrice": 52000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": true
  }
  ```
- **Lưu ý**:
  - `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
  - Khi giá thị trường chạm mức `triggerPrice`, lệnh sẽ được kích hoạt và chuyển thành lệnh thị trường
  - Thường được sử dụng với `reduceOnly: true` để đảm bảo chỉ đóng vị thế hiện có
  - Đối với lệnh BUY, lệnh được kích hoạt khi giá <= triggerPrice
  - Đối với lệnh SELL, lệnh được kích hoạt khi giá >= triggerPrice
- **Response**: Tương tự như đặt lệnh thông thường

#### 2.1.5. Đặt lệnh Take Profit Limit (TP Limit)

- **URL**: `/api/v1/trading/orders`
- **Method**: POST
- **Description**: Đặt lệnh chốt lời giới hạn (kích hoạt khi giá đạt đến mức kích hoạt, sau đó đặt lệnh giới hạn)
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "SELL",
    "type": "TAKE_PROFIT_LIMIT",
    "price": 52100.00,
    "triggerPrice": 52000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": true
  }
  ```
- **Lưu ý**:
  - `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
  - `price` là giá đặt lệnh sau khi lệnh được kích hoạt (bắt buộc)
  - Khi giá thị trường chạm mức `triggerPrice`, lệnh sẽ được kích hoạt và chuyển thành lệnh giới hạn với giá `price`
  - Đối với lệnh BUY, lệnh được kích hoạt khi giá <= triggerPrice
  - Đối với lệnh SELL, lệnh được kích hoạt khi giá >= triggerPrice
- **Response**: Tương tự như đặt lệnh thông thường

#### 2.2. Hủy lệnh

- **URL**: `/api/v1/trading/orders/{orderId}`
- **Method**: DELETE
- **Path Parameters**:
  - `orderId`: ID của lệnh
- **Query Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Description**: Hủy lệnh
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "orderId": "ORD123456789",
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "BUY",
      "type": "LIMIT",
      "price": 50000.00,
      "volume": 0.1,
      "status": "CANCELED",
      "createTime": "2023-05-01T12:00:00",
      "canceledTime": "2023-05-01T12:05:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 2.3. Hủy tất cả các lệnh của một thành viên

- **URL**: `/api/v1/trading/orders/member/{memberId}`
- **Method**: DELETE
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Description**: Hủy tất cả các lệnh của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": 5,
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 2.4. Hủy tất cả các lệnh của một thành viên cho một symbol

- **URL**: `/api/v1/trading/orders/member/{memberId}/symbol/{symbol}`
- **Method**: DELETE
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Description**: Hủy tất cả các lệnh của một thành viên cho một symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "orderId": "ORD123456789",
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "BUY",
        "type": "LIMIT",
        "price": 50000.00,
        "volume": 0.1,
        "status": "CANCELED",
        "createTime": "2023-05-01T12:00:00",
        "canceledTime": "2023-05-01T12:05:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 2.5. Lấy thông tin lệnh

- **URL**: `/api/v1/trading/orders/{orderId}`
- **Method**: GET
- **Path Parameters**:
  - `orderId`: ID của lệnh
- **Description**: Lấy thông tin lệnh
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "orderId": "ORD123456789",
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "BUY",
      "type": "LIMIT",
      "price": 50000.00,
      "volume": 0.1,
      "filledVolume": 0.0,
      "turnover": 0.0,
      "fee": 0.0,
      "status": "NEW",
      "createTime": "2023-05-01T12:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 2.6. Lấy danh sách lệnh theo memberId và symbol

- **URL**: `/api/v1/trading/orders/member/{memberId}/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `status`: Trạng thái lệnh (optional)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy danh sách lệnh theo memberId và symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "orderId": "ORD123456789",
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "BUY",
        "type": "LIMIT",
        "price": 50000.00,
        "volume": 0.1,
        "filledVolume": 0.0,
        "turnover": 0.0,
        "fee": 0.0,
        "status": "NEW",
        "createTime": "2023-05-01T12:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 2.7. Lấy danh sách lệnh đang hoạt động theo memberId và symbol

- **URL**: `/api/v1/trading/orders/member/{memberId}/symbol/{symbol}/active`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy danh sách lệnh đang hoạt động theo memberId và symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "orderId": "ORD123456789",
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "BUY",
        "type": "LIMIT",
        "price": 50000.00,
        "volume": 0.1,
        "filledVolume": 0.0,
        "turnover": 0.0,
        "fee": 0.0,
        "status": "NEW",
        "createTime": "2023-05-01T12:00:00"
      },
      {
        "orderId": "ORD987654321",
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "SELL",
        "type": "LIMIT",
        "price": 51000.00,
        "volume": 0.2,
        "filledVolume": 0.1,
        "turnover": 5100.0,
        "fee": 2.04,
        "status": "PARTIALLY_FILLED",
        "createTime": "2023-05-01T11:30:00"
      }
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 3. Quản lý Vị thế (Position)

#### 3.1. Lấy danh sách vị thế của một thành viên

- **URL**: `/api/v1/positions/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Description**: Lấy danh sách vị thế của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "LONG",
        "volume": 1.0,
        "openPrice": 50000.0,
        "liquidationPrice": 45000.0,
        "margin": 5000.0,
        "profit": 0.0,
        "marginMode": "CROSSED",
        "leverage": 10.0,
        "status": "OPEN",
        "createTime": "2023-05-01T12:00:00",
        "updateTime": "2023-05-01T12:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 3.2. Lấy danh sách vị thế của một thành viên theo symbol

- **URL**: `/api/v1/positions/member/{memberId}/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy danh sách vị thế của một thành viên theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "LONG",
        "volume": 1.0,
        "openPrice": 50000.0,
        "liquidationPrice": 45000.0,
        "margin": 5000.0,
        "profit": 0.0,
        "marginMode": "CROSSED",
        "leverage": 10.0,
        "status": "OPEN",
        "createTime": "2023-05-01T12:00:00",
        "updateTime": "2023-05-01T12:00:00"
      },
      {
        "id": 2,
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "SHORT",
        "volume": 0.5,
        "openPrice": 51000.0,
        "liquidationPrice": 56000.0,
        "margin": 2550.0,
        "profit": 0.0,
        "marginMode": "CROSSED",
        "leverage": 10.0,
        "status": "OPEN",
        "createTime": "2023-05-01T11:00:00",
        "updateTime": "2023-05-01T11:00:00"
      }
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 3.3. Lấy thông tin vị thế theo ID

- **URL**: `/api/v1/positions/{id}`
- **Method**: GET
- **Path Parameters**:
  - `id`: ID của vị thế
- **Description**: Lấy thông tin vị thế theo ID
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "LONG",
      "volume": 1.0,
      "openPrice": 50000.0,
      "liquidationPrice": 45000.0,
      "margin": 5000.0,
      "profit": 0.0,
      "marginMode": "CROSSED",
      "leverage": 10.0,
      "status": "OPEN",
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T12:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 3.4. Điều chỉnh đòn bẩy

- **URL**: `/api/v1/positions/{id}/leverage`
- **Method**: PUT
- **Path Parameters**:
  - `id`: ID của vị thế
- **Request Body**:
  ```json
  {
    "leverage": 20.0
  }
  ```
- **Description**: Điều chỉnh đòn bẩy của vị thế
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "LONG",
      "volume": 1.0,
      "openPrice": 50000.0,
      "liquidationPrice": 40000.0,
      "margin": 2500.0,
      "profit": 0.0,
      "marginMode": "CROSSED",
      "leverage": 20.0,
      "status": "OPEN",
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T12:10:00"
    },
    "timestamp": "2023-05-01T12:10:00"
  }
  ```

#### 3.5. Đóng vị thế

- **URL**: `/api/v1/positions/{id}/close`
- **Method**: POST
- **Path Parameters**:
  - `id`: ID của vị thế
- **Request Body**:
  ```json
  {
    "closeType": "MARKET",
    "price": null
  }
  ```
- **Description**: Đóng vị thế bằng lệnh thị trường
- **Lưu ý**:
  - `closeType` có thể là `MARKET` (đóng bằng lệnh thị trường) hoặc `LIMIT` (đóng bằng lệnh giới hạn)
  - Khi `closeType` là `MARKET`, không cần cung cấp `price`
  - Khi `closeType` là `LIMIT`, cần cung cấp `price` là giá đóng vị thế
  - Mặc định, nếu không cung cấp `closeType`, hệ thống sẽ sử dụng `MARKET`
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "LONG",
      "volume": 1.0,
      "openPrice": 50000.0,
      "closePrice": 51000.0,
      "liquidationPrice": 40000.0,
      "margin": 2500.0,
      "profit": 1000.0,
      "marginMode": "CROSSED",
      "leverage": 20.0,
      "status": "CLOSED",
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T13:00:00",
      "closeTime": "2023-05-01T13:00:00"
    },
    "timestamp": "2023-05-01T13:00:00"
  }
  ```

#### 3.5.1. Đóng vị thế bằng lệnh giới hạn

- **URL**: `/api/v1/positions/{id}/close`
- **Method**: POST
- **Path Parameters**:
  - `id`: ID của vị thế
- **Request Body**:
  ```json
  {
    "closeType": "LIMIT",
    "price": 51000.0
  }
  ```
- **Description**: Đóng vị thế bằng lệnh giới hạn với giá chỉ định
- **Lưu ý**:
  - Khi `closeType` là `LIMIT`, cần cung cấp `price` là giá đóng vị thế
  - Lệnh giới hạn sẽ được đặt vào sổ lệnh và chỉ được khớp khi giá thị trường đạt đến mức giá đã chỉ định
- **Response**: Tương tự như đóng vị thế bằng lệnh thị trường

#### 3.6. Đóng vị thế một phần

- **URL**: `/api/v1/positions/{id}/close-partial`
- **Method**: POST
- **Path Parameters**:
  - `id`: ID của vị thế
- **Request Body**:
  ```json
  {
    "volume": 0.5,
    "closeType": "MARKET",
    "price": null
  }
  ```
- **Description**: Đóng một phần vị thế
- **Lưu ý**:
  - `volume` là khối lượng cần đóng, phải nhỏ hơn khối lượng hiện tại của vị thế
  - `closeType` có thể là `MARKET` (đóng bằng lệnh thị trường) hoặc `LIMIT` (đóng bằng lệnh giới hạn)
  - Khi `closeType` là `MARKET`, không cần cung cấp `price`
  - Khi `closeType` là `LIMIT`, cần cung cấp `price` là giá đóng vị thế
  - Mặc định, nếu không cung cấp `closeType`, hệ thống sẽ sử dụng `MARKET`
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "LONG",
      "volume": 0.5,
      "openPrice": 50000.0,
      "liquidationPrice": 45000.0,
      "margin": 2500.0,
      "profit": 0.0,
      "marginMode": "CROSSED",
      "leverage": 10.0,
      "status": "OPEN",
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T13:00:00"
    },
    "timestamp": "2023-05-01T13:00:00"
  }
  ```

#### 3.7. Đặt lệnh Take Profit (TP) cho vị thế

- **URL**: `/api/v1/positions/{id}/take-profit`
- **Method**: POST
- **Path Parameters**:
  - `id`: ID của vị thế
- **Request Body**:
  ```json
  {
    "triggerPrice": 52000.0,
    "type": "MARKET",
    "price": null,
    "volume": 1.0
  }
  ```
- **Description**: Đặt lệnh chốt lời (Take Profit) cho vị thế
- **Lưu ý**:
  - `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
  - `type` có thể là `MARKET` (lệnh thị trường) hoặc `LIMIT` (lệnh giới hạn)
  - Khi `type` là `LIMIT`, cần cung cấp `price` là giá đặt lệnh sau khi lệnh được kích hoạt
  - `volume` là khối lượng cần đóng, mặc định là toàn bộ vị thế
  - Đối với vị thế LONG, lệnh TP sẽ được kích hoạt khi giá >= triggerPrice
  - Đối với vị thế SHORT, lệnh TP sẽ được kích hoạt khi giá <= triggerPrice
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "orderId": "TP123456789",
      "positionId": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "SELL",
      "type": "TAKE_PROFIT_MARKET",
      "triggerPrice": 52000.0,
      "price": null,
      "volume": 1.0,
      "status": "NEW",
      "createTime": "2023-05-01T13:00:00"
    },
    "timestamp": "2023-05-01T13:00:00"
  }
  ```

#### 3.8. Đặt lệnh Stop Loss (SL) cho vị thế

- **URL**: `/api/v1/positions/{id}/stop-loss`
- **Method**: POST
- **Path Parameters**:
  - `id`: ID của vị thế
- **Request Body**:
  ```json
  {
    "triggerPrice": 48000.0,
    "type": "MARKET",
    "price": null,
    "volume": 1.0
  }
  ```
- **Description**: Đặt lệnh dừng lỗ (Stop Loss) cho vị thế
- **Lưu ý**:
  - `triggerPrice` là giá kích hoạt lệnh (bắt buộc)
  - `type` có thể là `MARKET` (lệnh thị trường) hoặc `LIMIT` (lệnh giới hạn)
  - Khi `type` là `LIMIT`, cần cung cấp `price` là giá đặt lệnh sau khi lệnh được kích hoạt
  - `volume` là khối lượng cần đóng, mặc định là toàn bộ vị thế
  - Đối với vị thế LONG, lệnh SL sẽ được kích hoạt khi giá <= triggerPrice
  - Đối với vị thế SHORT, lệnh SL sẽ được kích hoạt khi giá >= triggerPrice
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "orderId": "SL123456789",
      "positionId": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "SELL",
      "type": "STOP_LOSS_MARKET",
      "triggerPrice": 48000.0,
      "price": null,
      "volume": 1.0,
      "status": "NEW",
      "createTime": "2023-05-01T13:00:00"
    },
    "timestamp": "2023-05-01T13:00:00"
  }
  ```

#### 3.9. Hủy lệnh TP/SL của vị thế

- **URL**: `/api/v1/positions/{id}/cancel-tp-sl`
- **Method**: POST
- **Path Parameters**:
  - `id`: ID của vị thế
- **Request Body**:
  ```json
  {
    "orderType": "TAKE_PROFIT"
  }
  ```
- **Description**: Hủy lệnh TP hoặc SL của vị thế
- **Lưu ý**:
  - `orderType` có thể là `TAKE_PROFIT` (hủy lệnh TP), `STOP_LOSS` (hủy lệnh SL) hoặc `ALL` (hủy cả hai)
  - Mặc định, nếu không cung cấp `orderType`, hệ thống sẽ hủy cả hai loại lệnh
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": true,
    "timestamp": "2023-05-01T13:00:00"
  }
  ```

### 4. Quản lý Ví (Wallet)

#### 4.1. Lấy thông tin ví của một thành viên

- **URL**: `/api/v1/wallets/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Description**: Lấy thông tin ví của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "memberId": 123,
        "coin": "USDT",
        "balance": 100000.0,
        "frozenBalance": 0.0,
        "availableBalance": 100000.0,
        "unrealizedPnl": 0.0,
        "realizedPnl": 0.0,
        "usedMargin": 0.0,
        "totalFee": 0.0,
        "totalFundingFee": 0.0,
        "isLocked": false,
        "createTime": "2023-05-01T12:00:00",
        "updateTime": "2023-05-01T12:00:00"
      }
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 4.2. Lấy thông tin ví của một thành viên theo coin

- **URL**: `/api/v1/wallets/member/{memberId}/coin/{coin}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `coin`: Loại coin
- **Description**: Lấy thông tin ví của một thành viên theo coin
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "coin": "USDT",
      "balance": 100000.0,
      "frozenBalance": 0.0,
      "availableBalance": 100000.0,
      "unrealizedPnl": 0.0,
      "realizedPnl": 0.0,
      "usedMargin": 0.0,
      "totalFee": 0.0,
      "totalFundingFee": 0.0,
      "isLocked": false,
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T12:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 4.3. Nạp tiền vào ví

- **URL**: `/api/v1/wallets/deposit`
- **Method**: POST
- **Description**: Nạp tiền vào ví
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "coin": "USDT",
    "amount": 10000.0
  }
  ```
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "coin": "USDT",
      "balance": 110000.0,
      "frozenBalance": 0.0,
      "availableBalance": 110000.0,
      "unrealizedPnl": 0.0,
      "realizedPnl": 0.0,
      "usedMargin": 0.0,
      "totalFee": 0.0,
      "totalFundingFee": 0.0,
      "isLocked": false,
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T12:30:00"
    },
    "timestamp": "2023-05-01T12:30:00"
  }
  ```

#### 4.4. Rút tiền từ ví

- **URL**: `/api/v1/wallets/withdraw`
- **Method**: POST
- **Description**: Rút tiền từ ví
- **Request Body**:
  ```json
  {
    "memberId": 123,
    "coin": "USDT",
    "amount": 5000.0
  }
  ```
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "coin": "USDT",
      "balance": 95000.0,
      "frozenBalance": 0.0,
      "availableBalance": 95000.0,
      "unrealizedPnl": 0.0,
      "realizedPnl": 0.0,
      "usedMargin": 0.0,
      "totalFee": 0.0,
      "totalFundingFee": 0.0,
      "isLocked": false,
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T12:45:00"
    },
    "timestamp": "2023-05-01T12:45:00"
  }
  ```

### 5. Quản lý Giao dịch (Transaction)

#### 5.1. Lấy danh sách giao dịch của một thành viên

- **URL**: `/api/v1/transactions/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Query Parameters**:
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy danh sách giao dịch của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "memberId": 123,
        "amount": -5000.0,
        "coin": "USDT",
        "referenceId": "ORD123456789",
        "createTime": "2023-05-01T12:00:00",
        "type": "OPEN_POSITION",
        "symbol": "BTCUSDT",
        "orderId": "ORD123456789",
        "leverage": 10,
        "marginMode": "CROSSED",
        "realizedPnl": 0.0
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 5.2. Lấy danh sách giao dịch của một thành viên theo symbol

- **URL**: `/api/v1/transactions/member/{memberId}/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy danh sách giao dịch của một thành viên theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "memberId": 123,
        "amount": -5000.0,
        "coin": "USDT",
        "referenceId": "ORD123456789",
        "createTime": "2023-05-01T12:00:00",
        "type": "OPEN_POSITION",
        "symbol": "BTCUSDT",
        "orderId": "ORD123456789",
        "leverage": 10,
        "marginMode": "CROSSED",
        "realizedPnl": 0.0
      },
      {
        "id": 3,
        "memberId": 123,
        "amount": 1000.0,
        "coin": "USDT",
        "referenceId": "POS123456789",
        "createTime": "2023-05-01T13:00:00",
        "type": "CLOSE_POSITION",
        "symbol": "BTCUSDT",
        "orderId": "ORD987654321",
        "leverage": 10,
        "marginMode": "CROSSED",
        "realizedPnl": 1000.0
      }
    ],
    "timestamp": "2023-05-01T13:05:00"
  }
  ```

### 6. Quản lý Giá (Price)

#### 6.1. Lấy giá hiện tại của một symbol

- **URL**: `/api/v1/prices/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy giá hiện tại của một symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "symbol": "BTCUSDT",
      "lastPrice": 50000.0,
      "markPrice": 50010.0,
      "indexPrice": 50005.0,
      "fundingRate": 0.0001,
      "nextFundingTime": "2023-05-01T16:00:00",
      "timestamp": "2023-05-01T12:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 6.2. Lấy giá của tất cả các symbol

- **URL**: `/api/v1/prices`
- **Method**: GET
- **Description**: Lấy giá của tất cả các symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "symbol": "BTCUSDT",
        "lastPrice": 50000.0,
        "markPrice": 50010.0,
        "indexPrice": 50005.0,
        "fundingRate": 0.0001,
        "nextFundingTime": "2023-05-01T16:00:00",
        "timestamp": "2023-05-01T12:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 7. Quản lý Funding Rate

#### 7.1. Lấy funding rate hiện tại của một symbol

- **URL**: `/api/v1/funding/rates/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy funding rate hiện tại của một symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "symbol": "BTCUSDT",
      "rate": 0.0001,
      "markPrice": 50010.0,
      "indexPrice": 50005.0,
      "time": "2023-05-01T08:00:00",
      "nextTime": "2023-05-01T16:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 7.2. Lấy lịch sử funding rate của một symbol

- **URL**: `/api/v1/funding/rates/{symbol}/history`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `limit`: Số lượng kết quả tối đa (default: 100)
- **Description**: Lấy lịch sử funding rate của một symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "symbol": "BTCUSDT",
        "rate": 0.0001,
        "markPrice": 50010.0,
        "indexPrice": 50005.0,
        "time": "2023-05-01T08:00:00",
        "nextTime": "2023-05-01T16:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 7.3. Lấy funding payment của một thành viên

- **URL**: `/api/v1/funding/payments/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Query Parameters**:
  - `symbol`: Symbol của hợp đồng (optional)
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy funding payment của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "positionId": 1,
        "memberId": 123,
        "symbol": "BTCUSDT",
        "amount": 5.0,
        "rate": 0.0001,
        "time": "2023-05-01T08:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 8. Quản lý Phí (Fee)

#### 8.1. Lấy danh sách phí của một thành viên

- **URL**: `/api/v1/fees/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Query Parameters**:
  - `symbol`: Symbol của hợp đồng (optional)
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy danh sách phí của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "contractId": 1,
        "memberId": 123,
        "symbol": "BTCUSDT",
        "orderId": "ORD123456789",
        "direction": "BUY",
        "volume": 1.0,
        "price": 50000.0,
        "turnover": 50000.0,
        "fee": 20.0,
        "maker": false,
        "createTime": "2023-05-01T12:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 8.2. Lấy tổng phí của một thành viên

- **URL**: `/api/v1/fees/member/{memberId}/total`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Query Parameters**:
  - `symbol`: Symbol của hợp đồng (optional)
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
- **Description**: Lấy tổng phí của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "memberId": 123,
      "totalFee": 100.0,
      "makerFee": 30.0,
      "takerFee": 70.0,
      "startTime": "2023-05-01T00:00:00",
      "endTime": "2023-05-01T23:59:59"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 9. Quản lý Thanh lý (Liquidation)

#### 9.1. Lấy danh sách thanh lý của một thành viên

- **URL**: `/api/v1/liquidations/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Query Parameters**:
  - `symbol`: Symbol của hợp đồng (optional)
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy danh sách thanh lý của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "contractId": 1,
        "symbol": "BTCUSDT",
        "memberId": 123,
        "positionId": 1,
        "liquidationOrderId": "LIQ123456789",
        "liquidationPrice": 45000.0,
        "liquidationVolume": 1.0,
        "realizedPnl": -5000.0,
        "insuranceAmount": 1000.0,
        "type": "FULL",
        "createTime": "2023-05-01T12:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 9.2. Lấy danh sách thanh lý theo symbol

- **URL**: `/api/v1/liquidations/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy danh sách thanh lý theo symbol
- **Response**: Tương tự như API 9.1, nhưng chỉ trả về các thanh lý của symbol được chỉ định

### 10. Quản lý Bảo hiểm (Insurance)

#### 10.1. Lấy thông tin quỹ bảo hiểm theo symbol

- **URL**: `/api/v1/insurance/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy thông tin quỹ bảo hiểm theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "contractId": 1,
      "symbol": "BTCUSDT",
      "balance": 1000000.0,
      "frozenBalance": 0.0,
      "availableBalance": 1000000.0,
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T12:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 10.2. Lấy lịch sử quỹ bảo hiểm theo symbol

- **URL**: `/api/v1/insurance/symbol/{symbol}/history`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy lịch sử quỹ bảo hiểm theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "contractId": 1,
        "symbol": "BTCUSDT",
        "amount": 1000.0,
        "balance": 1001000.0,
        "type": "DEPOSIT",
        "createTime": "2023-05-01T00:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 11. Quản lý Đòn bẩy (Leverage)

#### 11.1. Lấy cài đặt đòn bẩy của một thành viên

- **URL**: `/api/v1/leverage/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Description**: Lấy cài đặt đòn bẩy của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "memberId": 123,
        "symbol": "BTCUSDT",
        "leverage": 10.0,
        "createTime": "2023-05-01T00:00:00",
        "updateTime": "2023-05-01T00:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 11.2. Lấy cài đặt đòn bẩy của một thành viên theo symbol

- **URL**: `/api/v1/leverage/member/{memberId}/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy cài đặt đòn bẩy của một thành viên theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "leverage": 10.0,
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T00:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 11.3. Cập nhật cài đặt đòn bẩy

- **URL**: `/api/v1/leverage/member/{memberId}/symbol/{symbol}`
- **Method**: PUT
- **Path Parameters**:
  - `memberId`: ID của thành viên
  - `symbol`: Symbol của hợp đồng
- **Request Body**:
  ```json
  {
    "leverage": 20.0
  }
  ```
- **Description**: Cập nhật cài đặt đòn bẩy
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "symbol": "BTCUSDT",
      "leverage": 20.0,
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T12:30:00"
    },
    "timestamp": "2023-05-01T12:30:00"
  }
  ```

### 12. Quản lý Chế độ Vị thế (Position Mode)

#### 12.1. Lấy chế độ vị thế của một thành viên

- **URL**: `/api/v1/position-mode/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Description**: Lấy chế độ vị thế của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "positionMode": "HEDGE",
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T00:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 12.2. Cập nhật chế độ vị thế

- **URL**: `/api/v1/position-mode/member/{memberId}`
- **Method**: PUT
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Request Body**:
  ```json
  {
    "positionMode": "ONE_WAY"
  }
  ```
- **Description**: Cập nhật chế độ vị thế
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "memberId": 123,
      "positionMode": "ONE_WAY",
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T12:45:00"
    },
    "timestamp": "2023-05-01T12:45:00"
  }
  ```

### 13. Quản lý Cấu hình Giá (Price Configuration)

#### 13.1. Lấy cấu hình giá theo symbol

- **URL**: `/api/v1/price-configuration/symbol/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy cấu hình giá theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "symbol": "BTCUSDT",
      "indexPriceMethod": "WEIGHTED_AVERAGE",
      "markPriceMethod": "WEIGHTED_AVERAGE",
      "customIndexPriceFormula": null,
      "customMarkPriceFormula": null,
      "parameters": "{\"exchanges\": [\"binance\", \"okex\", \"huobi\"], \"weights\": [0.5, 0.3, 0.2]}",
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T00:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 13.2. Cập nhật cấu hình giá

- **URL**: `/api/v1/price-configuration/symbol/{symbol}`
- **Method**: PUT
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Request Body**:
  ```json
  {
    "indexPriceMethod": "WEIGHTED_AVERAGE",
    "markPriceMethod": "WEIGHTED_AVERAGE",
    "customIndexPriceFormula": null,
    "customMarkPriceFormula": null,
    "parameters": "{\"exchanges\": [\"binance\", \"okex\", \"huobi\"], \"weights\": [0.6, 0.3, 0.1]}"
  }
  ```
- **Description**: Cập nhật cấu hình giá
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "symbol": "BTCUSDT",
      "indexPriceMethod": "WEIGHTED_AVERAGE",
      "markPriceMethod": "WEIGHTED_AVERAGE",
      "customIndexPriceFormula": null,
      "customMarkPriceFormula": null,
      "parameters": "{\"exchanges\": [\"binance\", \"okex\", \"huobi\"], \"weights\": [0.6, 0.3, 0.1]}",
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T13:00:00"
    },
    "timestamp": "2023-05-01T13:00:00"
  }
  ```

### 14. Quản lý Orderbook

#### 14.1. Lấy orderbook theo symbol

- **URL**: `/api/v1/orderbook/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `limit`: Số lượng mức giá tối đa (default: 20)
- **Description**: Lấy orderbook theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "symbol": "BTCUSDT",
      "bids": [
        [50000.0, 1.5],
        [49990.0, 2.0],
        [49980.0, 3.0],
        // ...
      ],
      "asks": [
        [50010.0, 1.0],
        [50020.0, 2.5],
        [50030.0, 3.5],
        // ...
      ],
      "timestamp": "2023-05-01T12:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 15. Quản lý K-line

#### 15.1. Lấy K-line theo symbol

- **URL**: `/api/v1/klines/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `interval`: Khoảng thời gian (1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w, 1M)
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `limit`: Số lượng kết quả tối đa (default: 500)
- **Description**: Lấy K-line theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "symbol": "BTCUSDT",
        "interval": "1h",
        "openTime": 1619856000000,
        "closeTime": 1619859599999,
        "open": 50000.0,
        "high": 50100.0,
        "low": 49900.0,
        "close": 50050.0,
        "volume": 100.0,
        "turnover": 5000000.0,
        "trades": 1000
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 16. Quản lý Giao dịch (Trade)

#### 16.1. Lấy danh sách giao dịch theo symbol

- **URL**: `/api/v1/trades/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `limit`: Số lượng kết quả tối đa (default: 500)
- **Description**: Lấy danh sách giao dịch theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "price": 50000.0,
        "volume": 1.0,
        "time": "2023-05-01T12:00:00",
        "direction": "BUY"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 16.2. Lấy danh sách giao dịch của một thành viên

- **URL**: `/api/v1/trades/member/{memberId}`
- **Method**: GET
- **Path Parameters**:
  - `memberId`: ID của thành viên
- **Query Parameters**:
  - `symbol`: Symbol của hợp đồng (optional)
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy danh sách giao dịch của một thành viên
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "buyOrderId": "ORD123456789",
        "sellOrderId": "ORD987654321",
        "buyMemberId": 123,
        "sellMemberId": 456,
        "price": 50000.0,
        "volume": 1.0,
        "buyFee": 20.0,
        "sellFee": 20.0,
        "buyTurnover": 50000.0,
        "sellTurnover": 50000.0,
        "time": "2023-05-01T12:00:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

### 17. Quản lý Circuit Breaker

#### 17.1. Lấy trạng thái circuit breaker theo symbol

- **URL**: `/api/v1/circuit-breaker/{symbol}`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Description**: Lấy trạng thái circuit breaker theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1,
      "symbol": "BTCUSDT",
      "status": "NORMAL",
      "reason": null,
      "triggerPrice": 0.0,
      "createTime": "2023-05-01T00:00:00",
      "updateTime": "2023-05-01T00:00:00"
    },
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

#### 17.2. Lấy lịch sử circuit breaker theo symbol

- **URL**: `/api/v1/circuit-breaker/{symbol}/history`
- **Method**: GET
- **Path Parameters**:
  - `symbol`: Symbol của hợp đồng
- **Query Parameters**:
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
  - `page`: Số trang (default: 0)
  - `size`: Kích thước trang (default: 10)
- **Description**: Lấy lịch sử circuit breaker theo symbol
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "status": "TRIGGERED",
        "reason": "Price dropped more than 10% in 5 minutes",
        "triggerPrice": 45000.0,
        "createTime": "2023-05-01T10:00:00",
        "updateTime": "2023-05-01T10:00:00"
      },
      {
        "id": 2,
        "symbol": "BTCUSDT",
        "status": "RESOLVED",
        "reason": "Market stabilized",
        "triggerPrice": 47000.0,
        "createTime": "2023-05-01T10:30:00",
        "updateTime": "2023-05-01T10:30:00"
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:05:00"
  }
  ```

## Mã lỗi

Dưới đây là danh sách các mã lỗi có thể được trả về từ API:

| Mã lỗi | Mô tả |
|--------|-------|
| 200 | Thành công |
| 400 | Yêu cầu không hợp lệ |
| 401 | Không được xác thực |
| 403 | Không có quyền truy cập |
| 404 | Không tìm thấy tài nguyên |
| 409 | Xung đột |
| 429 | Quá nhiều yêu cầu |
| 500 | Lỗi máy chủ nội bộ |

## WebSocket API

Future Core cung cấp các kênh WebSocket để nhận dữ liệu theo thời gian thực. WebSocket giúp giảm tải cho server và cung cấp dữ liệu nhanh hơn so với REST API.

### 1. Kết nối WebSocket

- **URL**: `/contract-ws`
- **Protocol**: STOMP over SockJS
- **Description**: Endpoint để kết nối WebSocket

### 2. Kênh K-line

#### 2.1. Lấy K-line mới nhất

- **Destination**: `/app/kline/latest/{symbol}/{period}`
- **Method**: SEND
- **Path Variables**:
  - `symbol`: Symbol của hợp đồng (ví dụ: "BTCUSDT")
  - `period`: Khoảng thời gian (ví dụ: "1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1M")
- **Subscription**: `/topic/market/kline/{symbol}`
- **Description**: Lấy K-line mới nhất cho một symbol và khoảng thời gian cụ thể
- **Response**:
  ```json
  {
    "symbol": "BTCUSDT",
    "interval": "1h",
    "openTime": 1619856000000,
    "closeTime": 1619859599999,
    "open": 50000.0,
    "high": 50100.0,
    "low": 49900.0,
    "close": 50050.0,
    "volume": 100.0,
    "turnover": 5000000.0,
    "trades": 1000
  }
  ```

#### 2.2. Lấy danh sách K-line

- **Destination**: `/app/kline/list/{symbol}/{period}/{limit}`
- **Method**: SEND
- **Path Variables**:
  - `symbol`: Symbol của hợp đồng (ví dụ: "BTCUSDT")
  - `period`: Khoảng thời gian (ví dụ: "1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1M")
  - `limit`: Số lượng kết quả tối đa
- **Subscription**: `/topic/market/kline/{symbol}`
- **Description**: Lấy danh sách K-line cho một symbol và khoảng thời gian cụ thể
- **Response**:
  ```json
  [
    {
      "symbol": "BTCUSDT",
      "interval": "1h",
      "openTime": 1619856000000,
      "closeTime": 1619859599999,
      "open": 50000.0,
      "high": 50100.0,
      "low": 49900.0,
      "close": 50050.0,
      "volume": 100.0,
      "turnover": 5000000.0,
      "trades": 1000
    },
    // ...
  ]
  ```

#### 2.3. Lấy danh sách K-line theo khoảng thời gian

- **Destination**: `/app/kline/range/{symbol}/{period}/{startTime}/{endTime}`
- **Method**: SEND
- **Path Variables**:
  - `symbol`: Symbol của hợp đồng (ví dụ: "BTCUSDT")
  - `period`: Khoảng thời gian (ví dụ: "1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w", "1M")
  - `startTime`: Thời gian bắt đầu (timestamp)
  - `endTime`: Thời gian kết thúc (timestamp)
- **Subscription**: `/topic/market/kline/{symbol}`
- **Description**: Lấy danh sách K-line trong một khoảng thời gian cụ thể
- **Response**:
  ```json
  [
    {
      "symbol": "BTCUSDT",
      "interval": "1h",
      "openTime": 1619856000000,
      "closeTime": 1619859599999,
      "open": 50000.0,
      "high": 50100.0,
      "low": 49900.0,
      "close": 50050.0,
      "volume": 100.0,
      "turnover": 5000000.0,
      "trades": 1000
    },
    // ...
  ]
  ```

### 3. Kênh Orderbook

#### 3.1. Lấy orderbook

- **Destination**: `/app/orderbook/{symbol}`
- **Method**: SEND
- **Path Variables**:
  - `symbol`: Symbol của hợp đồng (ví dụ: "BTCUSDT")
- **Subscription**: `/topic/market/orderbook/{symbol}`
- **Description**: Lấy orderbook cho một symbol cụ thể
- **Response**:
  ```json
  {
    "symbol": "BTCUSDT",
    "bids": [
      [50000.0, 1.5],
      [49990.0, 2.0],
      [49980.0, 3.0],
      // ...
    ],
    "asks": [
      [50010.0, 1.0],
      [50020.0, 2.5],
      [50030.0, 3.5],
      // ...
    ],
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

#### 3.2. Lấy orderbook theo độ sâu

- **Destination**: `/app/orderbook/{symbol}/depth/{depth}`
- **Method**: SEND
- **Path Variables**:
  - `symbol`: Symbol của hợp đồng (ví dụ: "BTCUSDT")
  - `depth`: Độ sâu của orderbook
- **Subscription**: `/topic/market/orderbook/{symbol}`
- **Description**: Lấy orderbook với độ sâu cụ thể cho một symbol
- **Response**:
  ```json
  {
    "symbol": "BTCUSDT",
    "bids": [
      [50000.0, 1.5],
      [49990.0, 2.0],
      [49980.0, 3.0],
      // ...
    ],
    "asks": [
      [50010.0, 1.0],
      [50020.0, 2.5],
      [50030.0, 3.5],
      // ...
    ],
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

### 4. Kênh Giá

#### 4.1. Đăng ký nhận cập nhật giá

- **Subscription**: `/topic/market/price/{symbol}`
- **Description**: Đăng ký nhận cập nhật giá cho một symbol cụ thể
- **Response**:
  ```json
  {
    "symbol": "BTCUSDT",
    "lastPrice": 50000.0,
    "markPrice": 50010.0,
    "indexPrice": 50005.0,
    "fundingRate": 0.0001,
    "nextFundingTime": "2023-05-01T16:00:00",
    "timestamp": "2023-05-01T12:00:00"
  }
  ```

### 5. Kênh Giao dịch

#### 5.1. Đăng ký nhận cập nhật giao dịch

- **Subscription**: `/topic/market/trade/{symbol}`
- **Description**: Đăng ký nhận cập nhật giao dịch cho một symbol cụ thể
- **Response**:
  ```json
  {
    "id": 1,
    "symbol": "BTCUSDT",
    "price": 50000.0,
    "volume": 1.0,
    "time": "2023-05-01T12:00:00",
    "direction": "BUY"
  }
  ```

### 6. Kênh Vị thế

#### 6.1. Đăng ký nhận cập nhật vị thế

- **Subscription**: `/topic/user/{memberId}/position`
- **Description**: Đăng ký nhận cập nhật vị thế cho một thành viên cụ thể
- **Response**:
  ```json
  {
    "id": 1,
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "LONG",
    "volume": 1.0,
    "openPrice": 50000.0,
    "liquidationPrice": 45000.0,
    "margin": 5000.0,
    "profit": 0.0,
    "marginMode": "CROSSED",
    "leverage": 10.0,
    "status": "OPEN",
    "createTime": "2023-05-01T12:00:00",
    "updateTime": "2023-05-01T12:00:00"
  }
  ```

### 7. Kênh Lệnh

#### 7.1. Đăng ký nhận cập nhật lệnh

- **Subscription**: `/topic/user/{memberId}/order`
- **Description**: Đăng ký nhận cập nhật lệnh cho một thành viên cụ thể
- **Response**:
  ```json
  {
    "orderId": "ORD123456789",
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "BUY",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "filledVolume": 0.0,
    "turnover": 0.0,
    "fee": 0.0,
    "status": "NEW",
    "createTime": "2023-05-01T12:00:00"
  }
  ```

### 8. Kênh Ví

#### 8.1. Đăng ký nhận cập nhật ví

- **Subscription**: `/topic/user/{memberId}/wallet`
- **Description**: Đăng ký nhận cập nhật ví cho một thành viên cụ thể
- **Response**:
  ```json
  {
    "id": 1,
    "memberId": 123,
    "coin": "USDT",
    "balance": 100000.0,
    "frozenBalance": 0.0,
    "availableBalance": 100000.0,
    "unrealizedPnl": 0.0,
    "realizedPnl": 0.0,
    "usedMargin": 0.0,
    "totalFee": 0.0,
    "totalFundingFee": 0.0,
    "isLocked": false,
    "createTime": "2023-05-01T12:00:00",
    "updateTime": "2023-05-01T12:00:00"
  }
  ```

### 9. WebSocket Client cho Future-API

Future-API cũng cung cấp một WebSocket client để kết nối với module market và lấy giá spot theo thời gian thực:

- **URL**: `/contract/ws/{symbol}/{memberId}`
- **Protocol**: Native WebSocket
- **Path Variables**:
  - `symbol`: Symbol của hợp đồng (ví dụ: "BTCUSDT")
  - `memberId`: ID của thành viên
- **Description**: Endpoint WebSocket cho client kết nối trực tiếp

### 10. Ví dụ sử dụng WebSocket

#### 10.1. Kết nối WebSocket sử dụng JavaScript

```javascript
// Kết nối đến WebSocket server
const socket = new SockJS('https://api.example.com/contract-ws');
const stompClient = Stomp.over(socket);

// Kết nối
stompClient.connect({}, function(frame) {
  console.log('Connected: ' + frame);

  // Đăng ký nhận cập nhật orderbook
  stompClient.subscribe('/topic/market/orderbook/BTCUSDT', function(response) {
    const orderbook = JSON.parse(response.body);
    console.log('Orderbook update:', orderbook);
    // Xử lý dữ liệu orderbook
  });

  // Đăng ký nhận cập nhật K-line
  stompClient.subscribe('/topic/market/kline/BTCUSDT', function(response) {
    const kline = JSON.parse(response.body);
    console.log('K-line update:', kline);
    // Xử lý dữ liệu K-line
  });

  // Gửi yêu cầu lấy K-line
  stompClient.send('/app/kline/latest/BTCUSDT/1h', {}, {});
});

// Ngắt kết nối
function disconnect() {
  if (stompClient !== null) {
    stompClient.disconnect();
  }
  console.log("Disconnected");
}
```

#### 10.2. Kết nối WebSocket sử dụng Java

```java
// Cấu hình WebSocket client
WebSocketClient webSocketClient = new StandardWebSocketClient();
WebSocketStompClient stompClient = new WebSocketStompClient(webSocketClient);
stompClient.setMessageConverter(new MappingJackson2MessageConverter());

// Kết nối
StompSessionHandler sessionHandler = new MyStompSessionHandler();
stompClient.connect("https://api.example.com/contract-ws", sessionHandler);

// Lớp xử lý session
class MyStompSessionHandler extends StompSessionHandlerAdapter {
    @Override
    public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
        System.out.println("Connected!");

        // Đăng ký nhận cập nhật orderbook
        session.subscribe("/topic/market/orderbook/BTCUSDT", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return OrderBookDto.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                OrderBookDto orderbook = (OrderBookDto) payload;
                System.out.println("Orderbook update: " + orderbook);
                // Xử lý dữ liệu orderbook
            }
        });

        // Gửi yêu cầu lấy orderbook
        session.send("/app/orderbook/BTCUSDT", null);
    }
}
```

## Kết luận

Tài liệu này mô tả các API endpoint và WebSocket có sẵn trong module Future Core. Các API và WebSocket này được sử dụng để tương tác với hệ thống giao dịch hợp đồng tương lai.

Nếu bạn có bất kỳ câu hỏi hoặc gặp vấn đề nào, vui lòng liên hệ với đội phát triển.