# Persistence Adapters trong Future-Core Module

## Giới thiệu

Tài liệu này mô tả chi tiết các persistence adapters trong Future-Core Module. Persistence adapters là các thành phần trong infrastructure layer của clean architecture, chịu trách nhiệm triển khai các port để tương tác với database.

## Tình trạng chuyển đổi

Future-Core Module đang trong quá trình chuyển đổi sang clean architecture. Một số persistence adapters đã được chuyển đổi hoàn toàn, trong khi một số khác vẫn đang sử dụng cấu trúc cũ.

### Đã chuyển đổi

Các persistence adapters sau đã được chuyển đổi sang clean architecture:

- `OrderPersistenceAdapter`: Đã được chuyển đổi thành persistence adapter trong package `com.icetea.lotus.infrastructure.persistence.adapter`.
- `PositionPersistenceAdapter`: Đã được chuyển đổi thành persistence adapter trong package `com.icetea.lotus.infrastructure.persistence.adapter`.
- `TradePersistenceAdapter`: Đã được chuyển đổi thành persistence adapter trong package `com.icetea.lotus.infrastructure.persistence.adapter`.
- `FundingRatePersistenceAdapter`: Đã được chuyển đổi thành persistence adapter trong package `com.icetea.lotus.infrastructure.persistence.adapter`.
- `ContractPersistenceAdapter`: Đã được chuyển đổi thành persistence adapter trong package `com.icetea.lotus.infrastructure.persistence.adapter`.

### Chưa chuyển đổi

Các persistence adapters sau vẫn chưa được chuyển đổi sang clean architecture:

- Các repository trong package `com.icetea.lotus.dao` chưa được chuyển đổi sang persistence adapters.

## Persistence Adapters

### 1. OrderPersistenceAdapter

`OrderPersistenceAdapter` là persistence adapter chịu trách nhiệm triển khai `OrderPersistencePort` để tương tác với database cho entity `Order`.

#### Implementation

```java
@Component
@RequiredArgsConstructor
public class OrderPersistenceAdapter implements OrderPersistencePort {
    
    private final ContractOrderJpaRepository contractOrderJpaRepository;
    private final OrderPersistenceMapper orderPersistenceMapper;
    
    @Override
    public Optional<Order> findByOrderId(OrderId orderId) {
        ContractOrderJpaEntity entity = contractOrderJpaRepository.findByOrderId(orderId.getValue());
        return Optional.ofNullable(entity).map(orderPersistenceMapper::entityToDomain);
    }
    
    @Override
    public List<Order> findAllByMemberId(Long memberId) {
        List<ContractOrderJpaEntity> entities = contractOrderJpaRepository.findAllByMemberId(memberId);
        return entities.stream()
                .map(orderPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Order> findAllByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        List<ContractOrderJpaEntity> entities = contractOrderJpaRepository.findAllByMemberIdAndSymbol(memberId, symbol.getValue());
        return entities.stream()
                .map(orderPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Order> findAllBySymbolAndStatus(Symbol symbol, OrderStatus status) {
        List<ContractOrderJpaEntity> entities = contractOrderJpaRepository.findAllBySymbolAndStatus(symbol.getValue(), status);
        return entities.stream()
                .map(orderPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public Order save(Order order) {
        ContractOrderJpaEntity entity = orderPersistenceMapper.domainToEntity(order);
        entity = contractOrderJpaRepository.save(entity);
        return orderPersistenceMapper.entityToDomain(entity);
    }
    
    @Override
    public void deleteByOrderId(OrderId orderId) {
        contractOrderJpaRepository.deleteByOrderId(orderId.getValue());
    }
}
```

### 2. PositionPersistenceAdapter

`PositionPersistenceAdapter` là persistence adapter chịu trách nhiệm triển khai `PositionPersistencePort` để tương tác với database cho entity `Position`.

#### Implementation

```java
@Component
@RequiredArgsConstructor
public class PositionPersistenceAdapter implements PositionPersistencePort {
    
    private final PositionJpaRepository positionJpaRepository;
    private final PositionPersistenceMapper positionPersistenceMapper;
    
    @Override
    public Optional<Position> findById(PositionId id) {
        return positionJpaRepository.findById(id.getValue())
                .map(positionPersistenceMapper::entityToDomain);
    }
    
    @Override
    public Optional<Position> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        return positionJpaRepository.findByMemberIdAndSymbol(memberId, symbol.getValue())
                .map(positionPersistenceMapper::entityToDomain);
    }
    
    @Override
    public List<Position> findAllByMemberId(Long memberId) {
        return positionJpaRepository.findAllByMemberId(memberId).stream()
                .map(positionPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Position> findAll() {
        return positionJpaRepository.findAll().stream()
                .map(positionPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public Position save(Position position) {
        PositionJpaEntity entity = positionPersistenceMapper.domainToEntity(position);
        entity = positionJpaRepository.save(entity);
        return positionPersistenceMapper.entityToDomain(entity);
    }
    
    @Override
    public void deleteById(PositionId id) {
        positionJpaRepository.deleteById(id.getValue());
    }
}
```

### 3. TradePersistenceAdapter

`TradePersistenceAdapter` là persistence adapter chịu trách nhiệm triển khai `TradePersistencePort` để tương tác với database cho entity `Trade`.

#### Implementation

```java
@Component
@RequiredArgsConstructor
public class TradePersistenceAdapter implements TradePersistencePort {
    
    private final TradeJpaRepository tradeJpaRepository;
    private final TradePersistenceMapper tradePersistenceMapper;
    
    @Override
    public Optional<Trade> findById(TradeId id) {
        return tradeJpaRepository.findById(id.getValue())
                .map(tradePersistenceMapper::entityToDomain);
    }
    
    @Override
    public List<Trade> findAllByMemberId(Long memberId) {
        return tradeJpaRepository.findAllByBuyMemberIdOrSellMemberId(memberId, memberId).stream()
                .map(tradePersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Trade> findAllByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        return tradeJpaRepository.findAllByBuyMemberIdOrSellMemberIdAndSymbol(memberId, memberId, symbol.getValue()).stream()
                .map(tradePersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Trade> findAllBySymbol(Symbol symbol) {
        return tradeJpaRepository.findAllBySymbol(symbol.getValue()).stream()
                .map(tradePersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public Trade save(Trade trade) {
        TradeJpaEntity entity = tradePersistenceMapper.domainToEntity(trade);
        entity = tradeJpaRepository.save(entity);
        return tradePersistenceMapper.entityToDomain(entity);
    }
}
```

### 4. FundingRatePersistenceAdapter

`FundingRatePersistenceAdapter` là persistence adapter chịu trách nhiệm triển khai `FundingRatePersistencePort` để tương tác với database cho entity `FundingRate`.

#### Implementation

```java
@Component
@RequiredArgsConstructor
public class FundingRatePersistenceAdapter implements FundingRatePersistencePort {
    
    private final FundingRateJpaRepository fundingRateJpaRepository;
    private final FundingRatePersistenceMapper fundingRatePersistenceMapper;
    
    @Override
    public Optional<FundingRate> findById(FundingRateId id) {
        return fundingRateJpaRepository.findById(id.getValue())
                .map(fundingRatePersistenceMapper::entityToDomain);
    }
    
    @Override
    public Optional<FundingRate> findLatestBySymbol(Symbol symbol) {
        return fundingRateJpaRepository.findTopBySymbolOrderByTimeDesc(symbol.getValue())
                .map(fundingRatePersistenceMapper::entityToDomain);
    }
    
    @Override
    public List<FundingRate> findAllBySymbol(Symbol symbol, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "time"));
        return fundingRateJpaRepository.findAllBySymbol(symbol.getValue(), pageable).stream()
                .map(fundingRatePersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<FundingRate> findAllBySymbolAndTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        return fundingRateJpaRepository.findAllBySymbolAndTimeBetween(symbol.getValue(), startTime, endTime).stream()
                .map(fundingRatePersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public FundingRate save(FundingRate fundingRate) {
        FundingRateJpaEntity entity = fundingRatePersistenceMapper.domainToEntity(fundingRate);
        entity = fundingRateJpaRepository.save(entity);
        return fundingRatePersistenceMapper.entityToDomain(entity);
    }
}
```

### 5. ContractPersistenceAdapter

`ContractPersistenceAdapter` là persistence adapter chịu trách nhiệm triển khai `ContractPersistencePort` để tương tác với database cho entity `Contract`.

#### Implementation

```java
@Component
@RequiredArgsConstructor
public class ContractPersistenceAdapter implements ContractPersistencePort {
    
    private final ContractJpaRepository contractJpaRepository;
    private final ContractPersistenceMapper contractPersistenceMapper;
    
    @Override
    public Optional<Contract> findById(ContractId id) {
        return contractJpaRepository.findById(id.getValue())
                .map(contractPersistenceMapper::entityToDomain);
    }
    
    @Override
    public Optional<Contract> findBySymbol(Symbol symbol) {
        return contractJpaRepository.findBySymbol(symbol.getValue())
                .map(contractPersistenceMapper::entityToDomain);
    }
    
    @Override
    public List<Contract> findAll() {
        return contractJpaRepository.findAll().stream()
                .map(contractPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public Contract save(Contract contract) {
        ContractJpaEntity entity = contractPersistenceMapper.domainToEntity(contract);
        entity = contractJpaRepository.save(entity);
        return contractPersistenceMapper.entityToDomain(entity);
    }
    
    @Override
    public void deleteById(ContractId id) {
        contractJpaRepository.deleteById(id.getValue());
    }
}
```

## Persistence Mappers

### 1. OrderPersistenceMapper

`OrderPersistenceMapper` là mapper chịu trách nhiệm chuyển đổi giữa domain entity `Order` và JPA entity `ContractOrderJpaEntity`.

#### Implementation

```java
@Component
public class OrderPersistenceMapper {
    
    public Order entityToDomain(ContractOrderJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return Order.builder()
                .orderId(new OrderId(entity.getOrderId()))
                .memberId(entity.getMemberId())
                .symbol(Symbol.of(entity.getSymbol()))
                .direction(entity.getDirection())
                .type(entity.getType())
                .price(entity.getPrice() != null ? Money.of(entity.getPrice()) : null)
                .triggerPrice(entity.getTriggerPrice() != null ? Money.of(entity.getTriggerPrice()) : null)
                .volume(entity.getVolume())
                .filledVolume(entity.getFilledVolume())
                .status(entity.getStatus())
                .timeInForce(entity.getTimeInForce())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .remark(entity.getRemark())
                .build();
    }
    
    public ContractOrderJpaEntity domainToEntity(Order domain) {
        if (domain == null) {
            return null;
        }
        
        ContractOrderJpaEntity entity = new ContractOrderJpaEntity();
        entity.setOrderId(domain.getOrderId().getValue());
        entity.setMemberId(domain.getMemberId());
        entity.setSymbol(domain.getSymbol().getValue());
        entity.setDirection(domain.getDirection());
        entity.setType(domain.getType());
        entity.setPrice(domain.getPrice() != null ? domain.getPrice().getValue() : null);
        entity.setTriggerPrice(domain.getTriggerPrice() != null ? domain.getTriggerPrice().getValue() : null);
        entity.setVolume(domain.getVolume());
        entity.setFilledVolume(domain.getFilledVolume());
        entity.setStatus(domain.getStatus());
        entity.setTimeInForce(domain.getTimeInForce());
        entity.setCreateTime(domain.getCreateTime());
        entity.setUpdateTime(domain.getUpdateTime());
        entity.setRemark(domain.getRemark());
        
        return entity;
    }
}
```

### 2. PositionPersistenceMapper

`PositionPersistenceMapper` là mapper chịu trách nhiệm chuyển đổi giữa domain entity `Position` và JPA entity `PositionJpaEntity`.

#### Implementation

```java
@Component
public class PositionPersistenceMapper {
    
    public Position entityToDomain(PositionJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return Position.builder()
                .id(new PositionId(entity.getId()))
                .memberId(entity.getMemberId())
                .symbol(Symbol.of(entity.getSymbol()))
                .direction(entity.getDirection())
                .volume(entity.getVolume())
                .openPrice(Money.of(entity.getOpenPrice()))
                .closePrice(entity.getClosePrice() != null ? Money.of(entity.getClosePrice()) : null)
                .liquidationPrice(Money.of(entity.getLiquidationPrice()))
                .maintenanceMargin(Money.of(entity.getMaintenanceMargin()))
                .margin(Money.of(entity.getMargin()))
                .profit(Money.of(entity.getProfit()))
                .marginMode(entity.getMarginMode())
                .leverage(entity.getLeverage())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .remark(entity.getRemark())
                .build();
    }
    
    public PositionJpaEntity domainToEntity(Position domain) {
        if (domain == null) {
            return null;
        }
        
        PositionJpaEntity entity = new PositionJpaEntity();
        entity.setId(domain.getId().getValue());
        entity.setMemberId(domain.getMemberId());
        entity.setSymbol(domain.getSymbol().getValue());
        entity.setDirection(domain.getDirection());
        entity.setVolume(domain.getVolume());
        entity.setOpenPrice(domain.getOpenPrice().getValue());
        entity.setClosePrice(domain.getClosePrice() != null ? domain.getClosePrice().getValue() : null);
        entity.setLiquidationPrice(domain.getLiquidationPrice().getValue());
        entity.setMaintenanceMargin(domain.getMaintenanceMargin().getValue());
        entity.setMargin(domain.getMargin().getValue());
        entity.setProfit(domain.getProfit().getValue());
        entity.setMarginMode(domain.getMarginMode());
        entity.setLeverage(domain.getLeverage());
        entity.setCreateTime(domain.getCreateTime());
        entity.setUpdateTime(domain.getUpdateTime());
        entity.setRemark(domain.getRemark());
        
        return entity;
    }
}
```

## JPA Repositories

### 1. ContractOrderJpaRepository

`ContractOrderJpaRepository` là JPA repository chịu trách nhiệm truy cập database cho entity `ContractOrderJpaEntity`.

#### Interface

```java
@Repository
public interface ContractOrderJpaRepository extends JpaRepository<ContractOrderJpaEntity, String> {
    
    ContractOrderJpaEntity findByOrderId(String orderId);
    
    List<ContractOrderJpaEntity> findAllByMemberId(Long memberId);
    
    List<ContractOrderJpaEntity> findAllByMemberIdAndSymbol(Long memberId, String symbol);
    
    List<ContractOrderJpaEntity> findAllBySymbolAndStatus(String symbol, OrderStatus status);
    
    void deleteByOrderId(String orderId);
}
```

### 2. PositionJpaRepository

`PositionJpaRepository` là JPA repository chịu trách nhiệm truy cập database cho entity `PositionJpaEntity`.

#### Interface

```java
@Repository
public interface PositionJpaRepository extends JpaRepository<PositionJpaEntity, String> {
    
    Optional<PositionJpaEntity> findByMemberIdAndSymbol(Long memberId, String symbol);
    
    List<PositionJpaEntity> findAllByMemberId(Long memberId);
}
```

### 3. TradeJpaRepository

`TradeJpaRepository` là JPA repository chịu trách nhiệm truy cập database cho entity `TradeJpaEntity`.

#### Interface

```java
@Repository
public interface TradeJpaRepository extends JpaRepository<TradeJpaEntity, String> {
    
    List<TradeJpaEntity> findAllByBuyMemberIdOrSellMemberId(Long buyMemberId, Long sellMemberId);
    
    List<TradeJpaEntity> findAllByBuyMemberIdOrSellMemberIdAndSymbol(Long buyMemberId, Long sellMemberId, String symbol);
    
    List<TradeJpaEntity> findAllBySymbol(String symbol);
}
```

### 4. FundingRateJpaRepository

`FundingRateJpaRepository` là JPA repository chịu trách nhiệm truy cập database cho entity `FundingRateJpaEntity`.

#### Interface

```java
@Repository
public interface FundingRateJpaRepository extends JpaRepository<FundingRateJpaEntity, String> {
    
    Optional<FundingRateJpaEntity> findTopBySymbolOrderByTimeDesc(String symbol);
    
    List<FundingRateJpaEntity> findAllBySymbol(String symbol, Pageable pageable);
    
    List<FundingRateJpaEntity> findAllBySymbolAndTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);
}
```

### 5. ContractJpaRepository

`ContractJpaRepository` là JPA repository chịu trách nhiệm truy cập database cho entity `ContractJpaEntity`.

#### Interface

```java
@Repository
public interface ContractJpaRepository extends JpaRepository<ContractJpaEntity, String> {
    
    Optional<ContractJpaEntity> findBySymbol(String symbol);
}
```

## JPA Entities

### 1. ContractOrderJpaEntity

`ContractOrderJpaEntity` là JPA entity đại diện cho bảng `contract_order` trong database.

#### Implementation

```java
@Entity
@Table(name = "contract_order")
@Getter
@Setter
public class ContractOrderJpaEntity {
    
    @Id
    @Column(name = "order_id")
    private String orderId;
    
    @Column(name = "member_id")
    private Long memberId;
    
    @Column(name = "symbol")
    private String symbol;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "direction")
    private OrderDirection direction;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private OrderType type;
    
    @Column(name = "price")
    private BigDecimal price;
    
    @Column(name = "trigger_price")
    private BigDecimal triggerPrice;
    
    @Column(name = "volume")
    private BigDecimal volume;
    
    @Column(name = "filled_volume")
    private BigDecimal filledVolume;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private OrderStatus status;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "time_in_force")
    private TimeInForce timeInForce;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    @Column(name = "remark")
    private String remark;
}
```

### 2. PositionJpaEntity

`PositionJpaEntity` là JPA entity đại diện cho bảng `position` trong database.

#### Implementation

```java
@Entity
@Table(name = "position")
@Getter
@Setter
public class PositionJpaEntity {
    
    @Id
    @Column(name = "id")
    private String id;
    
    @Column(name = "member_id")
    private Long memberId;
    
    @Column(name = "symbol")
    private String symbol;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "direction")
    private PositionDirection direction;
    
    @Column(name = "volume")
    private BigDecimal volume;
    
    @Column(name = "open_price")
    private BigDecimal openPrice;
    
    @Column(name = "close_price")
    private BigDecimal closePrice;
    
    @Column(name = "liquidation_price")
    private BigDecimal liquidationPrice;
    
    @Column(name = "maintenance_margin")
    private BigDecimal maintenanceMargin;
    
    @Column(name = "margin")
    private BigDecimal margin;
    
    @Column(name = "profit")
    private BigDecimal profit;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "margin_mode")
    private MarginMode marginMode;
    
    @Column(name = "leverage")
    private BigDecimal leverage;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    @Column(name = "remark")
    private String remark;
}
```

## Kết luận

Persistence adapters trong Future-Core Module được thiết kế để triển khai các port trong application layer, giúp tương tác với database. Chúng được triển khai trong infrastructure layer của clean architecture, và sử dụng các JPA repositories để truy cập database. Việc chuyển đổi các persistence adapters sang clean architecture giúp tạo ra một mô hình persistence rõ ràng, dễ hiểu và dễ bảo trì.
