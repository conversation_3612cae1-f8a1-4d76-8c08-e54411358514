# Thiết kế Matching Engine phân tán

## 1. Tổng quan

Tài liệu này mô tả thiết kế chi tiết của Matching Engine phân tán, cho phép hệ thống giao dịch hợp đồng tương lai chạy trên nhiều instance trong môi trường Kubernetes.

> **Lưu ý**: Future-core là thư viện được Future-api sử dụng, không phải là service riêng biệt.

## 2. Kiến trúc hiện tại

Hiện tại, Matching Engine được thiết kế để chạy trên một instance duy nhất:

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[Future-API]
    B -->|Sử dụng| C[Future-Core Library]
    C -->|Xử lý lệnh| D[Matching Engine]
    D -->|Tạo giao dịch| E[Trade]
    D -->|Cập nhật| F[Order Book]
    D -->|Kiểm tra| G[Trigger Orders]
    D -->|Kiểm tra| H[Liquidation]
    E -->|Thông báo| I[Kafka]
    I -->|Cập nhật| J[Client]
    I -->|Cập nhật| K[Risk Management]
    I -->|Cập nhật| L[Settlement]
```

Matching Engine sử dụng các cấu trúc dữ liệu nội bộ:

```java
// Map lưu trữ các matching engine theo symbol
private final Map<String, MatchingEngine> matchingEngines = new ConcurrentHashMap<>();

// Lock cho việc đồng bộ hóa
private final ReadWriteLock lock = new ReentrantReadWriteLock();
```

## 3. Kiến trúc phân tán mới

Kiến trúc phân tán mới sẽ sử dụng các kỹ thuật sau:

1. **Sharding theo Symbol**: Mỗi instance chỉ xử lý một tập hợp các symbol
2. **Distributed Locking**: Đảm bảo chỉ có một instance xử lý một symbol tại một thời điểm
3. **Event Sourcing**: Đồng bộ hóa trạng thái giữa các instance
4. **Distributed Cache**: Cải thiện hiệu suất và giảm tải cho cơ sở dữ liệu

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[API Gateway]
    B -->|Load Balancing| C[Future-API Pods]
    C -->|Sử dụng| D[Future-Core Library]
    D -->|Sharding| E[Symbol Sharding Manager]
    E -->|Lấy lock| F[Redis Cluster]
    D -->|Xử lý lệnh| G[Lock-Free Matching Engine]
    G -->|Event| H[Kafka Cluster]
    G -->|Cache| I[Redis Cache]
    G -->|Lưu trữ| J[PostgreSQL]
    H -->|Đồng bộ| G
```

## 4. Sharding theo Symbol

### 4.1. Redis-based Sharding

Sử dụng Redis để quản lý việc phân phối các symbol giữa các instance:

```java
@Component
public class SymbolShardingManager {
    private final RedissonClient redissonClient;
    private final String podName;

    public SymbolShardingManager(RedissonClient redissonClient,
                                @Value("${HOSTNAME}") String podName) {
        this.redissonClient = redissonClient;
        this.podName = podName;
    }

    public boolean isSymbolOwnedByThisPod(String symbol) {
        // Sử dụng Redis để lưu trữ mapping giữa symbol và pod
        RMap<String, String> symbolToPodMap = redissonClient.getMap("symbol-to-pod-map");

        // Nếu symbol chưa được gán cho pod nào, thử gán cho pod hiện tại
        return symbolToPodMap.putIfAbsent(symbol, podName) == null ||
               symbolToPodMap.get(symbol).equals(podName);
    }

    public void rebalanceSymbols(List<String> allPods, List<String> allSymbols) {
        // Phân phối lại các symbol giữa các pod
        RMap<String, String> symbolToPodMap = redissonClient.getMap("symbol-to-pod-map");

        // Xóa các pod không còn tồn tại
        symbolToPodMap.entrySet().removeIf(entry -> !allPods.contains(entry.getValue()));

        // Phân phối các symbol chưa được gán
        for (String symbol : allSymbols) {
            if (!symbolToPodMap.containsKey(symbol)) {
                // Chọn pod có ít symbol nhất
                Map<String, Integer> podSymbolCount = new HashMap<>();
                for (String pod : allPods) {
                    podSymbolCount.put(pod, 0);
                }

                for (String assignedPod : symbolToPodMap.values()) {
                    podSymbolCount.put(assignedPod, podSymbolCount.getOrDefault(assignedPod, 0) + 1);
                }

                String podWithMinSymbols = allPods.get(0);
                for (String pod : allPods) {
                    if (podSymbolCount.get(pod) < podSymbolCount.get(podWithMinSymbols)) {
                        podWithMinSymbols = pod;
                    }
                }

                symbolToPodMap.put(symbol, podWithMinSymbols);
            }
        }
    }
}
```

### 4.2. Triển khai trong OrderController

```java
@RestController
@RequestMapping("/api/orders")
public class OrderController {
    private final DistributedLockingMatchingEngine matchingEngine;
    private final SymbolShardingManager shardingManager;
    private final KafkaTemplate<String, OrderCommand> kafkaTemplate;

    @Autowired
    public OrderController(DistributedLockingMatchingEngine matchingEngine,
                          SymbolShardingManager shardingManager,
                          KafkaTemplate<String, OrderCommand> kafkaTemplate) {
        this.matchingEngine = matchingEngine;
        this.shardingManager = shardingManager;
        this.kafkaTemplate = kafkaTemplate;
    }

    @PostMapping
    public ResponseEntity<OrderResponse> placeOrder(@RequestBody OrderRequest request) {
        try {
            // Chuyển đổi từ request sang domain model
            Order order = convertToOrder(request);

            // Kiểm tra xem symbol có được gán cho pod này không
            if (!shardingManager.isSymbolOwnedByThisPod(order.getSymbol().getValue())) {
                // Gửi lệnh đến Kafka để instance phù hợp xử lý
                OrderCommand command = new OrderCommand(
                    UUID.randomUUID().toString(),
                    OrderCommandType.PLACE_ORDER,
                    order,
                    LocalDateTime.now()
                );
                kafkaTemplate.send("order-commands", order.getSymbol().getValue(), command);

                // Trả về response tạm thời
                return ResponseEntity.accepted().body(new OrderResponse(order.getId(), "Order forwarded to appropriate instance"));
            }

            // Xử lý lệnh
            List<Trade> trades = matchingEngine.placeOrder(order);

            // Chuyển đổi kết quả và trả về
            OrderResponse response = convertToResponse(order, trades);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Lỗi khi đặt lệnh: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(new OrderResponse(null, e.getMessage()));
        }
    }
}
```

## 5. Distributed Locking với Redis

### 5.1. Cấu hình Redisson

```java
@Configuration
public class RedissonConfig {
    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
              .setAddress("redis://" + redisHost + ":" + redisPort)
              .setPassword(redisPassword)
              .setConnectionMinimumIdleSize(5)
              .setConnectionPoolSize(20)
              .setSubscriptionConnectionMinimumIdleSize(5)
              .setSubscriptionConnectionPoolSize(20)
              .setRetryAttempts(3)
              .setRetryInterval(1000)
              .setTimeout(3000);

        return Redisson.create(config);
    }
}
```

### 5.2. Triển khai Distributed Locking

```java
@Service
public class DistributedLockingMatchingEngine {
    private final RedissonClient redissonClient;
    private final OrderMatchingEngineService orderMatchingEngineService;
    private final SymbolShardingManager shardingManager;

    public List<Trade> placeOrder(Order order) {
        String symbol = order.getSymbol().getValue();

        // Kiểm tra xem symbol có được gán cho pod này không
        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
            throw new SymbolNotOwnedByThisPodException(
                "Symbol " + symbol + " không được gán cho pod này");
        }

        String lockKey = "order_matching:" + symbol;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // Lấy lock với timeout
            if (lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                try {
                    // Xử lý lệnh
                    return orderMatchingEngineService.placeOrder(order);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new RuntimeException("Không thể lấy lock cho symbol: " + symbol);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Bị gián đoạn khi chờ lock", e);
        }
    }

    public boolean cancelOrder(Order order) {
        String lockKey = "order_matching:" + order.getSymbol().getValue();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // Lấy lock với timeout
            if (lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                try {
                    // Xử lý hủy lệnh
                    return orderMatchingEngineService.cancelOrder(order);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new RuntimeException("Không thể lấy lock cho symbol: " + order.getSymbol().getValue());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Bị gián đoạn khi chờ lock", e);
        }
    }

    // Các phương thức khác tương tự
}
```

## 6. Event Sourcing với Kafka

### 6.1. Định nghĩa Event và Command

```java
@Data
@AllArgsConstructor
public class OrderEvent {
    private String id;
    private OrderEventType type;
    private Order order;
    private List<Trade> trades;
    private LocalDateTime timestamp;

    public enum OrderEventType {
        ORDER_PLACED,
        ORDER_CANCELLED,
        ORDER_UPDATED,
        ORDER_MATCHED,
        POSITION_LIQUIDATED
    }
}

@Data
@AllArgsConstructor
public class OrderCommand {
    private String id;
    private OrderCommandType type;
    private Order order;
    private LocalDateTime timestamp;

    public enum OrderCommandType {
        PLACE_ORDER,
        CANCEL_ORDER,
        UPDATE_ORDER,
        LIQUIDATE_POSITION
    }
}
```

### 6.2. Triển khai Event Producer

```java
@Service
public class OrderEventProducer {
    private final KafkaTemplate<String, OrderEvent> kafkaTemplate;

    public void publishOrderEvent(OrderEvent event) {
        kafkaTemplate.send("order-events", event.getOrder().getSymbol().getValue(), event);
    }
}
```

### 6.3. Triển khai Command Consumer

```java
@Service
public class OrderCommandConsumer {
    private final OrderMatchingEngineService orderMatchingEngineService;
    private final OrderEventProducer orderEventProducer;
    private final SymbolShardingManager shardingManager;

    @KafkaListener(topics = "order-commands")
    public void handleOrderCommand(OrderCommand command) {
        String symbol = command.getOrder().getSymbol().getValue();

        // Chỉ xử lý command nếu symbol được gán cho instance này
        if (shardingManager.isSymbolOwnedByThisPod(symbol)) {
            switch (command.getType()) {
                case PLACE_ORDER:
                    List<Trade> trades = orderMatchingEngineService.placeOrder(command.getOrder());

                    // Publish event
                    OrderEvent event = new OrderEvent(
                        UUID.randomUUID().toString(),
                        OrderEvent.OrderEventType.ORDER_PLACED,
                        command.getOrder(),
                        trades,
                        LocalDateTime.now()
                    );
                    orderEventProducer.publishOrderEvent(event);
                    break;

                case CANCEL_ORDER:
                    boolean cancelled = orderMatchingEngineService.cancelOrder(command.getOrder());

                    // Publish event
                    OrderEvent cancelEvent = new OrderEvent(
                        UUID.randomUUID().toString(),
                        OrderEvent.OrderEventType.ORDER_CANCELLED,
                        command.getOrder(),
                        Collections.emptyList(),
                        LocalDateTime.now()
                    );
                    orderEventProducer.publishOrderEvent(cancelEvent);
                    break;

                // Xử lý các loại command khác
            }
        }
    }
}
```

### 6.4. Triển khai Event Consumer

```java
@Service
public class OrderEventConsumer {
    private final OrderRepository orderRepository;
    private final TradeRepository tradeRepository;
    private final PositionService positionService;

    @KafkaListener(topics = "order-events")
    public void handleOrderEvent(OrderEvent event) {
        switch (event.getType()) {
            case ORDER_PLACED:
                // Cập nhật trạng thái order
                orderRepository.save(event.getOrder());

                // Lưu các giao dịch
                if (!event.getTrades().isEmpty()) {
                    tradeRepository.saveAll(event.getTrades());

                    // Cập nhật vị thế
                    for (Trade trade : event.getTrades()) {
                        positionService.updatePosition(trade);
                    }
                }
                break;

            case ORDER_CANCELLED:
                // Cập nhật trạng thái order
                orderRepository.save(event.getOrder());
                break;

            // Xử lý các loại event khác
        }
    }
}
```

## 7. Distributed Cache với Redis

### 7.1. Cấu hình Redis Cache

```java
@Configuration
@EnableCaching
public class RedisCacheConfig {
    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisHost);
        config.setPort(redisPort);
        config.setPassword(redisPassword);

        return new LettuceConnectionFactory(config);
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));

        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        cacheConfigurations.put("orders", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        cacheConfigurations.put("positions", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        cacheConfigurations.put("prices", defaultConfig.entryTtl(Duration.ofSeconds(30)));

        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
```

### 7.2. Sử dụng Cache trong Service

```java
@Service
public class CachedOrderService {
    private final OrderRepository orderRepository;

    @Cacheable(value = "orders", key = "#orderId")
    public Order getOrder(String orderId) {
        return orderRepository.findById(orderId).orElse(null);
    }

    @CachePut(value = "orders", key = "#order.id")
    public Order saveOrder(Order order) {
        return orderRepository.save(order);
    }

    @CacheEvict(value = "orders", key = "#orderId")
    public void deleteOrder(String orderId) {
        orderRepository.deleteById(orderId);
    }
}
```

## 8. Xử lý trường hợp xấu nhất của ConcurrentSkipListMap

### 8.1. Phân đoạn Order Book

```java
public class SegmentedOrderBook {
    private final BigDecimal segmentSize;
    private final ConcurrentHashMap<PriceRange, ConcurrentSkipListMap<Money, List<Order>>> segments = new ConcurrentHashMap<>();

    public SegmentedOrderBook(BigDecimal segmentSize) {
        this.segmentSize = segmentSize;
    }

    public void addOrder(Order order) {
        PriceRange range = calculatePriceRange(order.getPrice());
        ConcurrentSkipListMap<Money, List<Order>> segment = segments.computeIfAbsent(range, r -> new ConcurrentSkipListMap<>());

        segment.computeIfAbsent(order.getPrice(), p -> new CopyOnWriteArrayList<>()).add(order);
    }

    public List<Order> getOrdersAtPrice(Money price) {
        PriceRange range = calculatePriceRange(price);
        ConcurrentSkipListMap<Money, List<Order>> segment = segments.get(range);

        if (segment == null) {
            return Collections.emptyList();
        }

        List<Order> orders = segment.get(price);
        return orders != null ? orders : Collections.emptyList();
    }

    private PriceRange calculatePriceRange(Money price) {
        BigDecimal value = price.getValue();
        BigDecimal lowerBound = value.divideToIntegralValue(segmentSize).multiply(segmentSize);
        BigDecimal upperBound = lowerBound.add(segmentSize);
        return new PriceRange(Money.of(lowerBound), Money.of(upperBound));
    }
}
```

### 8.2. Giới hạn kích thước Order Book

```java
public class SizeLimitedOrderBook {
    private static final int MAX_PRICE_LEVELS = 1000;
    private final NavigableMap<Money, List<Order>> buyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
    private final NavigableMap<Money, List<Order>> sellOrders = new ConcurrentSkipListMap<>();

    public void addBuyOrder(Order order) {
        addOrder(buyOrders, order);
    }

    public void addSellOrder(Order order) {
        addOrder(sellOrders, order);
    }

    private void addOrder(NavigableMap<Money, List<Order>> orders, Order order) {
        // Kiểm tra số lượng mức giá
        if (orders.size() >= MAX_PRICE_LEVELS) {
            // Xóa mức giá xa nhất
            if (orders == buyOrders) {
                // Đối với lệnh mua, xóa mức giá thấp nhất
                orders.pollLastEntry();
            } else {
                // Đối với lệnh bán, xóa mức giá cao nhất
                orders.pollLastEntry();
            }
        }

        // Thêm lệnh vào sổ lệnh
        orders.computeIfAbsent(order.getPrice(), p -> new CopyOnWriteArrayList<>()).add(order);
    }
}
```

### 8.3. Tái cấu trúc định kỳ

```java
@Service
public class PeriodicRestructuringService {
    private final OrderMatchingEngineService orderMatchingEngineService;

    @Scheduled(fixedRate = 3600000) // 1 giờ
    public void restructureOrderBooks() {
        // Lấy danh sách các symbol
        List<Symbol> symbols = orderMatchingEngineService.getAllSymbols();

        for (Symbol symbol : symbols) {
            try {
                // Tái cấu trúc sổ lệnh cho mỗi symbol
                orderMatchingEngineService.restructureOrderBook(symbol);
            } catch (Exception e) {
                log.error("Lỗi khi tái cấu trúc sổ lệnh cho symbol {}: {}", symbol.getValue(), e.getMessage());
            }
        }
    }
}
```

## 9. Xử lý lỗi và khả năng chịu lỗi

### 9.1. Circuit Breaker

```java
@Service
public class CircuitBreakerOrderService {
    private final OrderMatchingEngineService orderMatchingEngineService;
    private final CircuitBreakerFactory circuitBreakerFactory;

    public List<Trade> placeOrder(Order order) {
        CircuitBreaker circuitBreaker = circuitBreakerFactory.create("placeOrder");

        return circuitBreaker.run(
            () -> orderMatchingEngineService.placeOrder(order),
            throwable -> handlePlaceOrderFailure(order, throwable)
        );
    }

    private List<Trade> handlePlaceOrderFailure(Order order, Throwable throwable) {
        log.error("Lỗi khi đặt lệnh: {}", throwable.getMessage());

        // Lưu lệnh với trạng thái lỗi
        order.setStatus(OrderStatus.REJECTED);
        order.setRejectionReason("Lỗi hệ thống: " + throwable.getMessage());

        // Thông báo cho client

        return Collections.emptyList();
    }
}
```

### 9.2. Retry với Exponential Backoff

```java
@Service
public class RetryableOrderService {
    private final OrderMatchingEngineService orderMatchingEngineService;

    @Retryable(
        value = {DataAccessException.class, TransactionException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Trade> placeOrder(Order order) {
        return orderMatchingEngineService.placeOrder(order);
    }

    @Recover
    public List<Trade> recoverPlaceOrder(Exception e, Order order) {
        log.error("Lỗi sau khi thử lại đặt lệnh: {}", e.getMessage());

        // Lưu lệnh với trạng thái lỗi
        order.setStatus(OrderStatus.REJECTED);
        order.setRejectionReason("Lỗi hệ thống sau khi thử lại: " + e.getMessage());

        // Thông báo cho client

        return Collections.emptyList();
    }
}
```

### 9.3. Bulkhead

```java
@Service
public class BulkheadOrderService {
    private final OrderMatchingEngineService orderMatchingEngineService;
    private final Bulkhead bulkhead;

    public BulkheadOrderService(OrderMatchingEngineService orderMatchingEngineService, BulkheadRegistry bulkheadRegistry) {
        this.orderMatchingEngineService = orderMatchingEngineService;
        this.bulkhead = bulkheadRegistry.bulkhead("orderService");
    }

    public List<Trade> placeOrder(Order order) {
        return Bulkhead.decorateSupplier(bulkhead, () -> orderMatchingEngineService.placeOrder(order)).get();
    }
}
```

## 10. Giám sát và Metrics

### 10.1. Prometheus Metrics

```java
@Configuration
public class MetricsConfig {
    @Bean
    public MeterRegistry meterRegistry() {
        return new SimpleMeterRegistry();
    }
}

@Service
public class MetricsService {
    private final MeterRegistry meterRegistry;

    public MetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public void recordOrderPlaced(Order order) {
        meterRegistry.counter("orders.placed", "symbol", order.getSymbol().getValue()).increment();
    }

    public void recordOrderMatched(Order order, List<Trade> trades) {
        meterRegistry.counter("orders.matched", "symbol", order.getSymbol().getValue()).increment();
        meterRegistry.counter("trades.created", "symbol", order.getSymbol().getValue()).increment(trades.size());
    }

    public void recordOrderCancelled(Order order) {
        meterRegistry.counter("orders.cancelled", "symbol", order.getSymbol().getValue()).increment();
    }

    public void recordMatchingTime(Order order, long timeInMillis) {
        meterRegistry.timer("matching.time", "symbol", order.getSymbol().getValue()).record(timeInMillis, TimeUnit.MILLISECONDS);
    }
}
```

### 10.2. Health Indicators

```java
@Component
public class MatchingEngineHealthIndicator implements HealthIndicator {
    private final OrderMatchingEngineService orderMatchingEngineService;

    @Override
    public Health health() {
        try {
            // Kiểm tra trạng thái của matching engine
            boolean isHealthy = orderMatchingEngineService.isHealthy();

            if (isHealthy) {
                return Health.up()
                        .withDetail("status", "Matching engine is running")
                        .build();
            } else {
                return Health.down()
                        .withDetail("status", "Matching engine is not running")
                        .build();
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("status", "Matching engine check failed")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}
```

## 11. Kết luận

Thiết kế Matching Engine phân tán cho phép hệ thống giao dịch hợp đồng tương lai chạy trên nhiều instance trong môi trường Kubernetes, đảm bảo tính nhất quán và hiệu suất cao. Bằng cách sử dụng sharding, distributed locking, event sourcing, và distributed cache, chúng ta có thể xây dựng một hệ thống có khả năng mở rộng và chịu lỗi cao.

Các bước tiếp theo:

1. Triển khai các thay đổi code theo thiết kế này
2. Thực hiện kiểm thử đơn vị và tích hợp
3. Thực hiện kiểm thử hiệu suất và tính nhất quán
4. Triển khai dần dần trong môi trường production
