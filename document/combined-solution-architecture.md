# Kiến trúc tổng thể khi kết hợp cả 3 giải pháp

## 1. Tổng quan

Tài liệu này mô tả kiến trúc tổng thể của hệ thống giao dịch hợp đồng tương lai khi kết hợp cả 3 giải pháp:
1. **Triển khai nhiều instance trong Kubernetes**
2. **Matching Engine phân tán**
3. **Lock-Free Matching Engine**

Kiến trúc này tận dụng ưu điểm của cả 3 giải pháp để xây dựng một hệ thống có hiệu suất cao, t<PERSON>h nh<PERSON>t quán tốt, và khả năng mở rộng linh hoạt.

> **Lưu ý**: Future-core là thư viện được Future-api sử dụng, không phải là service riêng biệt.

## 2. Kiến trúc tổng thể

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[API Gateway]
    B -->|Load Balancing| C[Future-API Pods]
    C -->|Sử dụng| D[Future-Core Library]
    D -->|Sharding| E[Distributed Lock Manager]
    E -->|Lấy lock| F[Redis Cluster]
    D -->|Xử lý lệnh| G[Lock-Free Matching Engine]
    G -->|Event| H[Kafka Cluster]
    G -->|Cache| I[Redis Cache]
    G -->|Lưu trữ| J[PostgreSQL]
    H -->|Đồng bộ| G
    K[Prometheus] -->|Giám sát| C
    L[Grafana] -->|Dashboard| K
    M[ELK Stack] -->|Log| C
```

## 3. Các thành phần chính

### 3.1. Kubernetes Infrastructure

#### 3.1.1. API Gateway
- **Chức năng**: Điểm vào duy nhất cho tất cả các request từ client
- **Công nghệ**: Nginx Ingress Controller
- **Tính năng**: Load balancing, SSL termination, routing

#### 3.1.2. Future-API Pods
- **Chức năng**: Xử lý các request từ client và chứa Future-Core Library
- **Công nghệ**: Spring Boot, containerized trong Docker
- **Tính năng**: Xác thực, phân quyền, validation, matching engine

#### 3.1.3. Future-Core Library
- **Chức năng**: Thư viện chứa logic nghiệp vụ, bao gồm Lock-Free Matching Engine
- **Công nghệ**: Java library, được nhúng trong Future-API
- **Tính năng**: Matching engine, order book, position management

#### 3.1.4. Deployment Configuration
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: future-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: future-api
  template:
    metadata:
      labels:
        app: future-api
    spec:
      containers:
      - name: future-api
        image: future-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: KUBERNETES_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: KUBERNETES_POD_COUNT
          value: "3"
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: SPRING_REDIS_HOST
          value: "redis-service"
        - name: SPRING_REDIS_PORT
          value: "6379"
        - name: SPRING_KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-service:9092"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 5
```

### 3.2. Distributed Matching Engine

#### 3.2.1. Symbol Sharding Manager
- **Chức năng**: Quản lý việc phân phối các symbol giữa các Future-API Pod
- **Công nghệ**: Spring Component, Redis
- **Tính năng**: Sharding, load balancing, rebalancing

#### 3.2.2. Redis-based Sharding
```java
@Component
public class SymbolShardingManager {
    private final RedissonClient redissonClient;
    private final String podName;

    public SymbolShardingManager(RedissonClient redissonClient,
                                @Value("${HOSTNAME}") String podName) {
        this.redissonClient = redissonClient;
        this.podName = podName;
    }

    public boolean isSymbolOwnedByThisPod(String symbol) {
        // Sử dụng Redis để lưu trữ mapping giữa symbol và pod
        RMap<String, String> symbolToPodMap = redissonClient.getMap("symbol-to-pod-map");

        // Nếu symbol chưa được gán cho pod nào, thử gán cho pod hiện tại
        return symbolToPodMap.putIfAbsent(symbol, podName) == null ||
               symbolToPodMap.get(symbol).equals(podName);
    }

    public void rebalanceSymbols(List<String> allPods, List<String> allSymbols) {
        // Phân phối lại các symbol giữa các pod
        RMap<String, String> symbolToPodMap = redissonClient.getMap("symbol-to-pod-map");

        // Xóa các pod không còn tồn tại
        symbolToPodMap.entrySet().removeIf(entry -> !allPods.contains(entry.getValue()));

        // Phân phối các symbol chưa được gán
        for (String symbol : allSymbols) {
            if (!symbolToPodMap.containsKey(symbol)) {
                // Chọn pod có ít symbol nhất
                Map<String, Integer> podSymbolCount = new HashMap<>();
                for (String pod : allPods) {
                    podSymbolCount.put(pod, 0);
                }

                for (String assignedPod : symbolToPodMap.values()) {
                    podSymbolCount.put(assignedPod, podSymbolCount.getOrDefault(assignedPod, 0) + 1);
                }

                String podWithMinSymbols = allPods.get(0);
                for (String pod : allPods) {
                    if (podSymbolCount.get(pod) < podSymbolCount.get(podWithMinSymbols)) {
                        podWithMinSymbols = pod;
                    }
                }

                symbolToPodMap.put(symbol, podWithMinSymbols);
            }
        }
    }
}
```

#### 3.2.3. Distributed Locking
```java
@Service
public class DistributedLockingMatchingEngine {
    private final RedissonClient redissonClient;
    private final LockFreeMatchingEngine lockFreeMatchingEngine;
    private final SymbolShardingManager shardingManager;

    public List<Trade> placeOrder(Order order) {
        String symbol = order.getSymbol().getValue();

        // Kiểm tra xem symbol có được gán cho pod này không
        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
            throw new SymbolNotOwnedByThisPodException(
                "Symbol " + symbol + " không được gán cho pod này");
        }

        String lockKey = "order_matching:" + symbol;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // Lấy lock với timeout
            if (lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                try {
                    // Xử lý lệnh bằng Lock-Free Matching Engine
                    return lockFreeMatchingEngine.placeOrder(order);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new RuntimeException("Không thể lấy lock cho symbol: " + symbol);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Bị gián đoạn khi chờ lock", e);
        }
    }
}
```

#### 3.2.4. Event Sourcing
```java
@Service
public class OrderEventProducer {
    private final KafkaTemplate<String, OrderEvent> kafkaTemplate;

    public void publishOrderEvent(OrderEvent event) {
        kafkaTemplate.send("order-events", event.getOrder().getSymbol().getValue(), event);
    }
}

@Service
public class OrderEventConsumer {
    private final LockFreeMatchingEngine lockFreeMatchingEngine;
    private final SymbolShardingManager shardingManager;

    @KafkaListener(topics = "order-events")
    public void handleOrderEvent(OrderEvent event) {
        String symbol = event.getOrder().getSymbol().getValue();

        // Chỉ xử lý event nếu symbol được gán cho instance này
        if (shardingManager.isSymbolOwnedByThisPod(symbol)) {
            switch (event.getType()) {
                case ORDER_PLACED:
                    // Cập nhật trạng thái order book
                    lockFreeMatchingEngine.updateOrderBook(event.getOrder(), event.getTrades());
                    break;
                case ORDER_CANCELLED:
                    // Cập nhật trạng thái order book
                    lockFreeMatchingEngine.cancelOrder(event.getOrder());
                    break;
                // Xử lý các loại event khác
            }
        }
    }
}
```

### 3.3. Lock-Free Matching Engine

#### 3.3.1. Lock-Free Data Structures
```java
public class LockFreeMatchingEngine {
    // Atomic reference đến snapshot hiện tại của sổ lệnh
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    // Cấu trúc dữ liệu bất biến cho sổ lệnh
    public static class OrderBookSnapshot {
        private final ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> buyOrders;
        private final ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> sellOrders;

        public OrderBookSnapshot() {
            this.buyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
            this.sellOrders = new ConcurrentSkipListMap<>();
        }

        public OrderBookSnapshot(ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> buyOrders,
                                ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> sellOrders) {
            this.buyOrders = buyOrders;
            this.sellOrders = sellOrders;
        }

        public OrderBookSnapshot copy() {
            ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> newBuyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
            ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> newSellOrders = new ConcurrentSkipListMap<>();

            // Deep copy
            for (Map.Entry<Money, CopyOnWriteArrayList<Order>> entry : buyOrders.entrySet()) {
                newBuyOrders.put(entry.getKey(), new CopyOnWriteArrayList<>(entry.getValue()));
            }

            for (Map.Entry<Money, CopyOnWriteArrayList<Order>> entry : sellOrders.entrySet()) {
                newSellOrders.put(entry.getKey(), new CopyOnWriteArrayList<>(entry.getValue()));
            }

            return new OrderBookSnapshot(newBuyOrders, newSellOrders);
        }
    }
}
```

#### 3.3.2. Lock-Free Matching Algorithm
```java
public List<Trade> placeOrder(Order order) {
    while (true) {
        // Lấy snapshot hiện tại
        OrderBookSnapshot currentSnapshot = orderBookRef.get();

        // Tạo bản sao để thực hiện khớp lệnh
        OrderBookResult result = matchOrder(order, currentSnapshot);

        // Cập nhật snapshot bằng CAS
        if (orderBookRef.compareAndSet(currentSnapshot, result.getNewSnapshot())) {
            // Nếu CAS thành công, trả về kết quả
            return result.getTrades();
        }

        // Nếu CAS thất bại, thử lại
    }
}

private OrderBookResult matchOrder(Order order, OrderBookSnapshot snapshot) {
    // Tạo bản sao của snapshot
    OrderBookSnapshot newSnapshot = snapshot.copy();
    List<Trade> trades = new ArrayList<>();

    // Thực hiện khớp lệnh trên bản sao
    if (order.getDirection() == OrderDirection.BUY) {
        matchBuyOrder(order, newSnapshot, trades);
    } else {
        matchSellOrder(order, newSnapshot, trades);
    }

    return new OrderBookResult(newSnapshot, trades);
}
```

#### 3.3.3. Segmented Order Book
```java
public class SegmentedOrderBook {
    private final BigDecimal segmentSize;
    private final ConcurrentHashMap<PriceRange, ConcurrentSkipListMap<Money, List<Order>>> segments = new ConcurrentHashMap<>();

    public SegmentedOrderBook(BigDecimal segmentSize) {
        this.segmentSize = segmentSize;
    }

    public void addOrder(Order order) {
        PriceRange range = calculatePriceRange(order.getPrice());
        ConcurrentSkipListMap<Money, List<Order>> segment = segments.computeIfAbsent(range, r -> new ConcurrentSkipListMap<>());

        segment.computeIfAbsent(order.getPrice(), p -> new CopyOnWriteArrayList<>()).add(order);
    }

    public List<Order> getOrdersAtPrice(Money price) {
        PriceRange range = calculatePriceRange(price);
        ConcurrentSkipListMap<Money, List<Order>> segment = segments.get(range);

        if (segment == null) {
            return Collections.emptyList();
        }

        List<Order> orders = segment.get(price);
        return orders != null ? orders : Collections.emptyList();
    }

    private PriceRange calculatePriceRange(Money price) {
        BigDecimal value = price.getValue();
        BigDecimal lowerBound = value.divideToIntegralValue(segmentSize).multiply(segmentSize);
        BigDecimal upperBound = lowerBound.add(segmentSize);
        return new PriceRange(Money.of(lowerBound), Money.of(upperBound));
    }
}
```

### 3.4. Supporting Infrastructure

#### 3.4.1. Redis Cluster
- **Chức năng**: Cung cấp distributed locking và caching
- **Công nghệ**: Redis Cluster
- **Tính năng**: High availability, sharding, replication

#### 3.4.2. Kafka Cluster
- **Chức năng**: Cung cấp event sourcing và message streaming
- **Công nghệ**: Apache Kafka
- **Tính năng**: High throughput, fault tolerance, scalability

#### 3.4.3. PostgreSQL
- **Chức năng**: Lưu trữ dữ liệu giao dịch
- **Công nghệ**: PostgreSQL
- **Tính năng**: ACID compliance, reliability, extensibility

#### 3.4.4. Monitoring Stack
- **Chức năng**: Giám sát hệ thống
- **Công nghệ**: Prometheus, Grafana, ELK Stack
- **Tính năng**: Metrics collection, visualization, alerting, log aggregation

## 4. Luồng xử lý lệnh

```mermaid
sequenceDiagram
    participant Client
    participant API as Future-API
    participant ShardingMgr as Symbol Sharding Manager
    participant Engine as Lock-Free Matching Engine
    participant Redis
    participant Kafka
    participant DB as PostgreSQL

    Client->>API: Đặt lệnh
    API->>ShardingMgr: Kiểm tra symbol
    ShardingMgr->>Redis: Kiểm tra mapping symbol-pod
    Redis-->>ShardingMgr: Trả về mapping

    alt Symbol được gán cho pod hiện tại
        ShardingMgr-->>API: Symbol thuộc pod này
        API->>Engine: Xử lý lệnh
        Engine->>Redis: Lấy distributed lock
        Redis-->>Engine: Trả về lock

        Engine->>Engine: Thực hiện khớp lệnh lock-free
        Engine->>DB: Lưu lệnh và giao dịch
        Engine->>Kafka: Publish event
        Engine-->>API: Trả về kết quả
    else Symbol không được gán cho pod hiện tại
        ShardingMgr-->>API: Symbol không thuộc pod này
        API->>Kafka: Publish command
        Kafka-->>API: Xác nhận

        Note over API,Kafka: Pod phù hợp sẽ xử lý command
    end

    API-->>Client: Trả về kết quả

    Kafka->>API: Consume event
    API->>Engine: Cập nhật trạng thái
```

## 5. Cơ chế xử lý trường hợp xấu nhất

### 5.1. Phân đoạn Order Book
- Chia Order Book thành các phân đoạn theo khoảng giá
- Mỗi phân đoạn sử dụng một ConcurrentSkipListMap riêng
- Giảm kích thước của mỗi ConcurrentSkipListMap, tránh thoái hóa thành single linked list

### 5.2. Giới hạn kích thước Order Book
- Giới hạn số lượng mức giá trong mỗi Order Book
- Xóa các mức giá xa nhất khi vượt quá giới hạn
- Đảm bảo hiệu suất ổn định

### 5.3. Tái cấu trúc định kỳ
- Tái cấu trúc Order Book định kỳ
- Tạo mới các ConcurrentSkipListMap
- Loại bỏ các mức giá không có lệnh

## 6. Cơ chế xử lý lỗi và khả năng chịu lỗi

### 6.1. Circuit Breaker
```java
@Service
public class CircuitBreakerOrderService {
    private final DistributedLockingMatchingEngine matchingEngine;
    private final CircuitBreakerFactory circuitBreakerFactory;

    public List<Trade> placeOrder(Order order) {
        CircuitBreaker circuitBreaker = circuitBreakerFactory.create("placeOrder");

        return circuitBreaker.run(
            () -> matchingEngine.placeOrder(order),
            throwable -> handlePlaceOrderFailure(order, throwable)
        );
    }

    private List<Trade> handlePlaceOrderFailure(Order order, Throwable throwable) {
        log.error("Lỗi khi đặt lệnh: {}", throwable.getMessage());

        // Lưu lệnh với trạng thái lỗi
        order.setStatus(OrderStatus.REJECTED);
        order.setRejectionReason("Lỗi hệ thống: " + throwable.getMessage());

        // Thông báo cho client

        return Collections.emptyList();
    }
}
```

### 6.2. Retry với Exponential Backoff
```java
@Service
public class RetryableOrderService {
    private final DistributedLockingMatchingEngine matchingEngine;

    @Retryable(
        value = {DataAccessException.class, TransactionException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Trade> placeOrder(Order order) {
        return matchingEngine.placeOrder(order);
    }

    @Recover
    public List<Trade> recoverPlaceOrder(Exception e, Order order) {
        log.error("Lỗi sau khi thử lại đặt lệnh: {}", e.getMessage());

        // Lưu lệnh với trạng thái lỗi
        order.setStatus(OrderStatus.REJECTED);
        order.setRejectionReason("Lỗi hệ thống sau khi thử lại: " + e.getMessage());

        // Thông báo cho client

        return Collections.emptyList();
    }
}
```

### 6.3. Bulkhead
```java
@Service
public class BulkheadOrderService {
    private final DistributedLockingMatchingEngine matchingEngine;
    private final Bulkhead bulkhead;

    public BulkheadOrderService(DistributedLockingMatchingEngine matchingEngine, BulkheadRegistry bulkheadRegistry) {
        this.matchingEngine = matchingEngine;
        this.bulkhead = bulkheadRegistry.bulkhead("orderService");
    }

    public List<Trade> placeOrder(Order order) {
        return Bulkhead.decorateSupplier(bulkhead, () -> matchingEngine.placeOrder(order)).get();
    }
}
```

## 7. Giám sát và Metrics

### 7.1. Prometheus Metrics
```java
@Service
public class MetricsService {
    private final MeterRegistry meterRegistry;

    public MetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    public void recordOrderPlaced(Order order) {
        meterRegistry.counter("orders.placed", "symbol", order.getSymbol().getValue()).increment();
    }

    public void recordOrderMatched(Order order, List<Trade> trades) {
        meterRegistry.counter("orders.matched", "symbol", order.getSymbol().getValue()).increment();
        meterRegistry.counter("trades.created", "symbol", order.getSymbol().getValue()).increment(trades.size());
    }

    public void recordOrderCancelled(Order order) {
        meterRegistry.counter("orders.cancelled", "symbol", order.getSymbol().getValue()).increment();
    }

    public void recordMatchingTime(Order order, long timeInMillis) {
        meterRegistry.timer("matching.time", "symbol", order.getSymbol().getValue()).record(timeInMillis, TimeUnit.MILLISECONDS);
    }
}
```

### 7.2. Grafana Dashboard
- **Order Metrics**: Số lượng lệnh đặt, khớp, hủy theo thời gian
- **Trade Metrics**: Số lượng giao dịch, khối lượng giao dịch theo thời gian
- **Performance Metrics**: Thời gian xử lý lệnh, độ trễ
- **System Metrics**: CPU, memory, network, disk usage

### 7.3. ELK Stack
- **Logstash**: Thu thập và xử lý log
- **Elasticsearch**: Lưu trữ và tìm kiếm log
- **Kibana**: Visualize và analyze log

## 8. Kết luận

Kiến trúc tổng thể khi kết hợp cả 3 giải pháp (Triển khai nhiều instance trong Kubernetes, Matching Engine phân tán, và Lock-Free Matching Engine) cung cấp một giải pháp toàn diện cho hệ thống giao dịch hợp đồng tương lai. Kiến trúc này tận dụng ưu điểm của cả 3 giải pháp để xây dựng một hệ thống có hiệu suất cao, tính nhất quán tốt, và khả năng mở rộng linh hoạt.

Với mô hình "Future-core là thư viện được Future-api sử dụng", chúng ta tận dụng được các ưu điểm sau:
- **Giảm độ trễ giao tiếp**: Không cần gọi API giữa Future-api và Future-core
- **Đơn giản hóa triển khai**: Chỉ cần triển khai một loại service (Future-api)
- **Chia sẻ tài nguyên**: Future-api và Future-core chia sẻ cùng JVM và tài nguyên

Bằng cách sử dụng Kubernetes làm nền tảng, chúng ta có thể dễ dàng quản lý và mở rộng hệ thống. Matching Engine phân tán với sharding và distributed locking giúp đảm bảo tính nhất quán giữa các instance. Lock-Free Matching Engine giúp tối ưu hiệu suất trong mỗi instance.

Kiến trúc này cũng cung cấp các cơ chế xử lý lỗi và khả năng chịu lỗi toàn diện, cùng với hệ thống giám sát và metrics để đảm bảo hệ thống hoạt động ổn định và hiệu quả.

## 9. Tích hợp với OrderController

Dựa trên cấu trúc hiện tại, chúng ta có thể tích hợp giải pháp mới vào OrderController như sau:

```java
@RestController
@RequestMapping("/api/orders")
public class OrderController {
    private final DistributedLockingMatchingEngine matchingEngine;
    private final SymbolShardingManager shardingManager;

    @Autowired
    public OrderController(DistributedLockingMatchingEngine matchingEngine,
                          SymbolShardingManager shardingManager) {
        this.matchingEngine = matchingEngine;
        this.shardingManager = shardingManager;
    }

    @PostMapping
    public ResponseEntity<OrderResponse> placeOrder(@RequestBody OrderRequest request) {
        try {
            // Chuyển đổi từ request sang domain model
            Order order = convertToOrder(request);

            // Kiểm tra xem symbol có được gán cho pod này không
            if (!shardingManager.isSymbolOwnedByThisPod(order.getSymbol().getValue())) {
                // Chuyển hướng request đến pod phù hợp
                return redirectToAppropriateInstance(order);
            }

            // Xử lý lệnh
            List<Trade> trades = matchingEngine.placeOrder(order);

            // Chuyển đổi kết quả và trả về
            OrderResponse response = convertToResponse(order, trades);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Lỗi khi đặt lệnh: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(new OrderResponse(null, e.getMessage()));
        }
    }

    private ResponseEntity<OrderResponse> redirectToAppropriateInstance(Order order) {
        // Triển khai logic chuyển hướng request đến pod phù hợp
        // Có thể sử dụng Kafka để gửi command đến pod phù hợp

        // Đây chỉ là ví dụ
        return ResponseEntity.status(HttpStatus.TEMPORARY_REDIRECT)
                            .header("Location", "/api/orders/redirect")
                            .build();
    }

    // Các phương thức hỗ trợ khác
}
```
