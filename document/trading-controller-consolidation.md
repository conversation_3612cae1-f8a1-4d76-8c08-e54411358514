# Tài liệu về việc gộp OrderController và OrderMatchingController

## Giới thiệu

Tài liệu này mô tả việc gộp hai controller liên quan đến giao dịch trong module future-core: `OrderController` và `OrderMatchingController` thành một controller duy nhất là `TradingController`. Việc gộp này nhằm mục đích tạo ra một API thống nhất cho tất cả các hoạt động liên quan đến giao dịch.

## Lý do gộp

Trướ<PERSON> đâ<PERSON>, hệ thống có hai controller riêng biệt xử lý các khía cạnh khác nhau của quá trình giao dịch:

1. **OrderController**: Tập trung vào quản lý lệnh từ góc độ người dùng (t<PERSON><PERSON>, h<PERSON><PERSON> l<PERSON>, t<PERSON><PERSON> vấn lệnh).
2. **OrderMatchingController**: Tập trung vào quá trình khớp lệnh và các hoạt động liên quan đến matching engine.

Việc tách biệt này gây ra một số vấn đề:
- Trùng lặp chức năng giữa hai controller (ví dụ: cả hai đều có phương thức để đặt lệnh và hủy lệnh)
- Khó khăn trong việc hiểu và sử dụng API vì phải biết nên gọi controller nào cho mỗi tác vụ
- Phức tạp hóa việc bảo trì và mở rộng hệ thống

## Giải pháp

Chúng tôi đã tạo một controller mới là `TradingController` để thay thế cả hai controller cũ. Controller mới này:

1. Kết hợp tất cả các chức năng từ cả hai controller cũ
2. Tổ chức các endpoint theo cấu trúc rõ ràng hơn
3. Giữ nguyên logic nghiệp vụ bằng cách sử dụng cùng các use case hiện có

## Cấu trúc API mới

API mới được tổ chức thành hai nhóm chính:

### 1. API quản lý lệnh (từ OrderController)

Các endpoint này tập trung vào quản lý lệnh từ góc độ người dùng:

- `POST /api/v1/trading/orders`: Đặt lệnh mới
- `DELETE /api/v1/trading/orders/{orderId}`: Hủy lệnh
- `DELETE /api/v1/trading/orders/member/{memberId}`: Hủy tất cả lệnh của một thành viên
- `DELETE /api/v1/trading/orders/member/{memberId}/symbol/{symbol}`: Hủy tất cả lệnh của một thành viên cho một symbol
- `GET /api/v1/trading/orders/{orderId}`: Lấy thông tin lệnh
- `GET /api/v1/trading/orders/member/{memberId}/symbol/{symbol}`: Lấy danh sách lệnh theo memberId và symbol
- `GET /api/v1/trading/orders/member/{memberId}/symbol/{symbol}/active`: Lấy danh sách lệnh đang hoạt động

### 2. API khớp lệnh (từ OrderMatchingController)

Các endpoint này tập trung vào quá trình khớp lệnh và các hoạt động liên quan đến matching engine:

- `POST /api/v1/trading/matching/place-order`: Đặt lệnh mới (khớp lệnh)
- `POST /api/v1/trading/matching/cancel-order/{orderId}`: Hủy lệnh (khớp lệnh)
- `POST /api/v1/trading/matching/cancel-all-orders/{memberId}`: Hủy tất cả lệnh của một thành viên (khớp lệnh)
- `GET /api/v1/trading/matching/order-book/{symbol}`: Lấy sổ lệnh
- `GET /api/v1/trading/matching/order-book/{symbol}/depth/{depth}`: Lấy sổ lệnh theo độ sâu
- `GET /api/v1/trading/matching/order-book/{symbol}/direction/{direction}`: Lấy sổ lệnh theo hướng
- `GET /api/v1/trading/matching/order-book/{symbol}/direction/{direction}/depth/{depth}`: Lấy sổ lệnh theo hướng và độ sâu
- `POST /api/v1/trading/matching/update-mark-price`: Cập nhật giá đánh dấu
- `GET /api/v1/trading/matching/mark-price/{symbol}`: Lấy giá đánh dấu
- `GET /api/v1/trading/matching/index-price/{symbol}`: Lấy giá chỉ số
- `GET /api/v1/trading/matching/last-price/{symbol}`: Lấy giá giao dịch cuối cùng
- `POST /api/v1/trading/matching/pause-trading/{symbol}`: Tạm dừng giao dịch
- `POST /api/v1/trading/matching/resume-trading/{symbol}`: Tiếp tục giao dịch
- `POST /api/v1/trading/matching/check-trigger-orders/{symbol}`: Kiểm tra lệnh chờ
- `POST /api/v1/trading/matching/check-liquidations/{symbol}`: Kiểm tra thanh lý
- `POST /api/v1/trading/matching/liquidate-position`: Thanh lý vị thế
- `POST /api/v1/trading/matching/synchronize-with-contracts`: Đồng bộ hóa matching engine với hợp đồng

## Sự khác biệt giữa hai nhóm API

Mặc dù cả hai nhóm API đều liên quan đến lệnh giao dịch, nhưng chúng có những mục đích và đối tượng sử dụng khác nhau:

1. **API quản lý lệnh**: Được thiết kế cho người dùng cuối (traders) để quản lý lệnh của họ. Các API này thực hiện đầy đủ các bước xác thực, kiểm tra ví, và xử lý lỗi thân thiện với người dùng.

2. **API khớp lệnh**: Được thiết kế cho các thành phần nội bộ của hệ thống (như các dịch vụ khác hoặc các tác vụ định kỳ) để tương tác trực tiếp với matching engine. Các API này tập trung vào hiệu suất và không thực hiện tất cả các bước xác thực như nhóm API quản lý lệnh.

## Cách sử dụng

### Đặt lệnh mới

Có hai cách để đặt lệnh mới:

1. **Qua API quản lý lệnh**:
```http
POST /api/v1/trading/orders
Content-Type: application/json

{
  "memberId": 123,
  "symbol": "BTC-USDT",
  "direction": "BUY",
  "type": "LIMIT",
  "price": 50000,
  "volume": 0.1,
  "leverage": 10,
  "reduceOnly": false,
  "timeInForce": "GTC"
}
```

2. **Qua API khớp lệnh**:
```http
POST /api/v1/trading/matching/place-order
Content-Type: application/json

{
  "orderId": "ORD123456",
  "memberId": 123,
  "symbol": "BTC-USDT",
  "direction": "BUY",
  "type": "LIMIT",
  "price": 50000,
  "volume": 0.1,
  "leverage": 10,
  "reduceOnly": false,
  "timeInForce": "GTC"
}
```

### Hủy lệnh

Tương tự, có hai cách để hủy lệnh:

1. **Qua API quản lý lệnh**:
```http
DELETE /api/v1/trading/orders/ORD123456?memberId=123&symbol=BTC-USDT
```

2. **Qua API khớp lệnh**:
```http
POST /api/v1/trading/matching/cancel-order/ORD123456?symbol=BTC-USDT
```

## Lưu ý quan trọng

1. **Không nên sử dụng cả hai API cho cùng một tác vụ**: Điều này có thể dẫn đến hành vi không mong muốn. Hãy chọn API phù hợp với mục đích của bạn.

2. **API quản lý lệnh dành cho người dùng cuối**: Các API này thực hiện đầy đủ các bước xác thực và kiểm tra.

3. **API khớp lệnh dành cho hệ thống nội bộ**: Các API này giả định rằng dữ liệu đầu vào đã được xác thực.

## Kết luận

Việc gộp hai controller thành một controller duy nhất giúp tạo ra một API thống nhất và dễ hiểu hơn cho tất cả các hoạt động liên quan đến giao dịch. Điều này cũng giúp giảm thiểu sự trùng lặp và làm cho hệ thống dễ bảo trì hơn.

Tuy nhiên, chúng tôi vẫn giữ nguyên sự phân biệt giữa các API quản lý lệnh và API khớp lệnh để đảm bảo rằng mỗi nhóm API có thể phục vụ tốt nhất cho mục đích cụ thể của nó.
