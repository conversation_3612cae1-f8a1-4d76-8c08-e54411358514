# API Tra Cứu Lịch Sử Giao Dịch

Tài liệu này mô tả các API để tra cứu lịch sử giao dịch trong hệ thống, tư<PERSON><PERSON> tự như giao diện của Binance.

## 1. Positions - Vị thế đang mở

### 1.1. <PERSON><PERSON><PERSON> tất cả vị thế của một thành viên

- **URL**: `/api/v1/positions/{memberId}`
- **Method**: GET
- **Controller**: `PositionController`
- **Description**: L<PERSON>y tất cả các vị thế đang mở của một thành viên

**Path Parameters**:
- `memberId`: ID của thành viên

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "POS123456",
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "LONG",
      "leverage": 10,
      "volume": 1.0,
      "openPrice": 50000.0,
      "markPrice": 51000.0,
      "liquidationPrice": 45000.0,
      "margin": 5000.0,
      "marginMode": "CROSSED",
      "unrealizedPnl": 1000.0,
      "realizedPnl": 0.0,
      "status": "OPEN",
      "createTime": "2023-05-01T12:00:00"
    }
  ],
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/positions/123" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 1.2. Lấy vị thế theo memberId và symbol

- **URL**: `/api/v1/positions/{memberId}/{symbol}`
- **Method**: GET
- **Controller**: `PositionController`
- **Description**: Lấy thông tin vị thế của một thành viên theo symbol

**Path Parameters**:
- `memberId`: ID của thành viên
- `symbol`: Symbol của hợp đồng

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "POS123456",
    "memberId": 123,
    "symbol": "BTCUSDT",
    "direction": "LONG",
    "leverage": 10,
    "volume": 1.0,
    "openPrice": 50000.0,
    "markPrice": 51000.0,
    "liquidationPrice": 45000.0,
    "margin": 5000.0,
    "marginMode": "CROSSED",
    "unrealizedPnl": 1000.0,
    "realizedPnl": 0.0,
    "status": "OPEN",
    "createTime": "2023-05-01T12:00:00"
  },
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/positions/123/BTCUSDT" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 2. Open Orders - Lệnh đang chờ khớp

### 2.1. Lấy danh sách lệnh đang hoạt động

- **URL**: `/api/v1/trading/orders/member/{memberId}/symbol/{symbol}/active`
- **Method**: GET
- **Controller**: `TradingController`
- **Description**: Lấy danh sách lệnh đang hoạt động của một thành viên theo symbol

**Path Parameters**:
- `memberId`: ID của thành viên
- `symbol`: Symbol của hợp đồng

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "orderId": "ORD123456",
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "BUY",
      "type": "LIMIT",
      "price": 49000.0,
      "volume": 1.0,
      "filledVolume": 0.0,
      "status": "NEW",
      "createTime": "2023-05-01T12:00:00"
    }
  ],
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/trading/orders/member/123/symbol/BTCUSDT/active" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 3. Order History - Lịch sử lệnh

### 3.1. Lấy danh sách lệnh theo memberId và symbol

- **URL**: `/api/v1/trading/orders/member/{memberId}/symbol/{symbol}`
- **Method**: GET
- **Controller**: `TradingController`
- **Description**: Lấy danh sách lệnh của một thành viên theo symbol

**Path Parameters**:
- `memberId`: ID của thành viên
- `symbol`: Symbol của hợp đồng

**Query Parameters**:
- `status`: Trạng thái lệnh (optional)
- `page`: Số trang (default: 0)
- `size`: Kích thước trang (default: 10)

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "orderId": "ORD123456",
      "memberId": 123,
      "symbol": "BTCUSDT",
      "direction": "BUY",
      "type": "LIMIT",
      "price": 49000.0,
      "volume": 1.0,
      "filledVolume": 1.0,
      "status": "FILLED",
      "createTime": "2023-05-01T12:00:00",
      "updateTime": "2023-05-01T12:01:00"
    }
  ],
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/trading/orders/member/123/symbol/BTCUSDT?status=FILLED&page=0&size=10" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 4. Trade History - Lịch sử giao dịch

### 4.1. Lấy danh sách giao dịch theo symbol và memberId

- **URL**: `/api/v1/trades/symbol/{symbol}/member/{memberId}`
- **Method**: GET
- **Controller**: `TradeController`
- **Description**: Lấy danh sách giao dịch của một thành viên theo symbol

**Path Parameters**:
- `symbol`: Symbol của hợp đồng
- `memberId`: ID của thành viên

**Query Parameters**:
- `page`: Số trang (default: 0)
- `size`: Kích thước trang (default: 10)

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "TRD123456",
      "symbol": "BTCUSDT",
      "buyOrderId": "ORD123456",
      "sellOrderId": "ORD654321",
      "buyMemberId": 123,
      "sellMemberId": 456,
      "price": 50000.0,
      "volume": 1.0,
      "buyFee": 20.0,
      "sellFee": 20.0,
      "buyTurnover": 50000.0,
      "sellTurnover": 50000.0,
      "tradeTime": "2023-05-01T12:00:00"
    }
  ],
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/trades/symbol/BTCUSDT/member/123?page=0&size=10" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 5. Transaction History - Lịch sử giao dịch tài chính

### 5.1. Lấy danh sách giao dịch theo memberId

- **URL**: `/api/v1/transactions/member/{memberId}`
- **Method**: GET
- **Controller**: `TransactionController`
- **Description**: Lấy danh sách giao dịch tài chính của một thành viên

**Path Parameters**:
- `memberId`: ID của thành viên

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "TXN123456",
      "memberId": 123,
      "amount": -5000.0,
      "coin": "USDT",
      "referenceId": "ORD123456",
      "createTime": "2023-05-01T12:00:00",
      "type": "OPEN_POSITION",
      "symbol": "BTCUSDT",
      "orderId": "ORD123456",
      "leverage": 10,
      "marginMode": "CROSSED",
      "realizedPnl": 0.0
    }
  ],
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/transactions/member/123" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 5.2. Lấy danh sách giao dịch theo memberId và symbol

- **URL**: `/api/v1/transactions/member/{memberId}/symbol/{symbol}`
- **Method**: GET
- **Controller**: `TransactionController`
- **Description**: Lấy danh sách giao dịch tài chính của một thành viên theo symbol

**Path Parameters**:
- `memberId`: ID của thành viên
- `symbol`: Symbol của hợp đồng

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "TXN123456",
      "memberId": 123,
      "amount": -5000.0,
      "coin": "USDT",
      "referenceId": "ORD123456",
      "createTime": "2023-05-01T12:00:00",
      "type": "OPEN_POSITION",
      "symbol": "BTCUSDT",
      "orderId": "ORD123456",
      "leverage": 10,
      "marginMode": "CROSSED",
      "realizedPnl": 0.0
    }
  ],
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/transactions/member/123/symbol/BTCUSDT" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 6. Position History - Lịch sử vị thế

### 6.1. Lấy danh sách vị thế đã đóng

- **URL**: `/api/v1/positions/closed`
- **Method**: GET
- **Controller**: `ContractPositionController`
- **Description**: Lấy danh sách vị thế đã đóng của một thành viên

**Query Parameters**:
- `memberId`: ID của thành viên
- `pageNo`: Số trang (default: 0)
- `pageSize`: Kích thước trang (default: 10)

**Response Example**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "content": [
      {
        "id": "POS123456",
        "memberId": 123,
        "symbol": "BTCUSDT",
        "direction": "LONG",
        "leverage": 10,
        "volume": 1.0,
        "openPrice": 50000.0,
        "closePrice": 51000.0,
        "margin": 5000.0,
        "marginMode": "CROSSED",
        "realizedPnl": 1000.0,
        "status": "CLOSED",
        "createTime": "2023-05-01T12:00:00",
        "closeTime": "2023-05-02T12:00:00"
      }
    ],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "sorted": true,
        "unsorted": false,
        "empty": false
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "totalElements": 1,
    "totalPages": 1,
    "last": true,
    "size": 10,
    "number": 0,
    "sort": {
      "sorted": true,
      "unsorted": false,
      "empty": false
    },
    "numberOfElements": 1,
    "first": true,
    "empty": false
  },
  "timestamp": "2023-05-01T12:05:00"
}
```

**Curl Example**:
```bash
curl -X GET "http://localhost:8080/api/v1/positions/closed?memberId=123&pageNo=0&pageSize=10" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## Hướng dẫn sử dụng Postman để test API

### 1. Cài đặt Collection

1. Mở Postman
2. Chọn "Import" > "Raw text"
3. Sao chép và dán đoạn JSON sau:

```json
{
  "info": {
    "name": "Future Core - Trade History APIs",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "1. Positions",
      "item": [
        {
          "name": "1.1. Lấy tất cả vị thế của một thành viên",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/positions/{{memberId}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "positions", "{{memberId}}"]
            }
          }
        },
        {
          "name": "1.2. Lấy vị thế theo memberId và symbol",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/positions/{{memberId}}/{{symbol}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "positions", "{{memberId}}", "{{symbol}}"]
            }
          }
        }
      ]
    },
    {
      "name": "2. Open Orders",
      "item": [
        {
          "name": "2.1. Lấy danh sách lệnh đang hoạt động",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/trading/orders/member/{{memberId}}/symbol/{{symbol}}/active",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "trading", "orders", "member", "{{memberId}}", "symbol", "{{symbol}}", "active"]
            }
          }
        }
      ]
    },
    {
      "name": "3. Order History",
      "item": [
        {
          "name": "3.1. Lấy danh sách lệnh theo memberId và symbol",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/trading/orders/member/{{memberId}}/symbol/{{symbol}}?status={{status}}&page={{page}}&size={{size}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "trading", "orders", "member", "{{memberId}}", "symbol", "{{symbol}}"],
              "query": [
                {
                  "key": "status",
                  "value": "{{status}}"
                },
                {
                  "key": "page",
                  "value": "{{page}}"
                },
                {
                  "key": "size",
                  "value": "{{size}}"
                }
              ]
            }
          }
        }
      ]
    },
    {
      "name": "4. Trade History",
      "item": [
        {
          "name": "4.1. Lấy danh sách giao dịch theo symbol và memberId",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/trades/symbol/{{symbol}}/member/{{memberId}}?page={{page}}&size={{size}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "trades", "symbol", "{{symbol}}", "member", "{{memberId}}"],
              "query": [
                {
                  "key": "page",
                  "value": "{{page}}"
                },
                {
                  "key": "size",
                  "value": "{{size}}"
                }
              ]
            }
          }
        }
      ]
    },
    {
      "name": "5. Transaction History",
      "item": [
        {
          "name": "5.1. Lấy danh sách giao dịch theo memberId",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/transactions/member/{{memberId}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "transactions", "member", "{{memberId}}"]
            }
          }
        },
        {
          "name": "5.2. Lấy danh sách giao dịch theo memberId và symbol",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/transactions/member/{{memberId}}/symbol/{{symbol}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "transactions", "member", "{{memberId}}", "symbol", "{{symbol}}"]
            }
          }
        }
      ]
    },
    {
      "name": "6. Position History",
      "item": [
        {
          "name": "6.1. Lấy danh sách vị thế đã đóng",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{token}}"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/v1/positions/closed?memberId={{memberId}}&pageNo={{pageNo}}&pageSize={{pageSize}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "v1", "positions", "closed"],
              "query": [
                {
                  "key": "memberId",
                  "value": "{{memberId}}"
                },
                {
                  "key": "pageNo",
                  "value": "{{pageNo}}"
                },
                {
                  "key": "pageSize",
                  "value": "{{pageSize}}"
                }
              ]
            }
          }
        }
      ]
    }
  ],
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8080"
    },
    {
      "key": "token",
      "value": "YOUR_TOKEN"
    },
    {
      "key": "memberId",
      "value": "123"
    },
    {
      "key": "symbol",
      "value": "BTCUSDT"
    },
    {
      "key": "status",
      "value": "FILLED"
    },
    {
      "key": "page",
      "value": "0"
    },
    {
      "key": "size",
      "value": "10"
    },
    {
      "key": "pageNo",
      "value": "0"
    },
    {
      "key": "pageSize",
      "value": "10"
    }
  ]
}
```

4. Nhấn "Import"

### 2. Cấu hình biến môi trường

1. Tạo môi trường mới trong Postman
2. Thêm các biến sau:
   - `baseUrl`: URL cơ sở của API (ví dụ: http://localhost:8080)
   - `token`: Token xác thực
   - `memberId`: ID của thành viên cần truy vấn
   - `symbol`: Symbol của hợp đồng (ví dụ: BTCUSDT)
   - Các biến khác như trong collection

### 3. Thực hiện test

1. Chọn môi trường đã tạo
2. Mở collection và chọn request cần test
3. Nhấn "Send" để gửi request
4. Kiểm tra kết quả trả về

## Tổng kết

Tài liệu này đã mô tả đầy đủ các API để tra cứu lịch sử giao dịch trong hệ thống, tương ứng với các tab trong giao diện của Binance:

1. **Positions**: API để lấy vị thế đang mở
2. **Open Orders**: API để lấy lệnh đang chờ khớp
3. **Order History**: API để lấy lịch sử lệnh
4. **Trade History**: API để lấy lịch sử giao dịch đã khớp
5. **Transaction History**: API để lấy lịch sử giao dịch tài chính
6. **Position History**: API để lấy lịch sử vị thế đã đóng

Các API này đều được triển khai trong các controller khác nhau của module future-core và cung cấp khả năng lọc theo nhiều tiêu chí như memberId, symbol, thời gian, và loại giao dịch.
