# Thiết kế Lock-Free Matching Engine

## 1. Tổng quan

Tài liệu này mô tả thiết kế chi tiết của Lock-Free Matching Engine, một cải tiến quan trọng cho hệ thống giao dịch hợp đồng tương lai để đạt được hiệu suất cao hơn và giảm thiểu tình trạng nghẽn cổ chai khi nhiều thread cùng truy cập.

> **Lưu ý**: Future-core là thư viện được Future-api sử dụng, không phải là service riêng biệt.

## 2. Vấn đề hiện tại

Matching Engine hiện tại sử dụng các cơ chế locking truyền thống:

```java
// Lock cho việc đồng bộ hóa
private final ReadWriteLock lock = new ReentrantReadWriteLock();

// Sổ lệnh
private final Map<OrderId, Order> orders = new ConcurrentHashMap<>();
private final Map<OrderDirection, Map<Money, List<Order>>> ordersByPrice = new EnumMap<>(OrderDirection.class);
```

C<PERSON>c vấn đề với cách tiếp cận này:

1. **Nghẽn cổ chai**: Khi nhiều thread cùng truy cập, lock có thể trở thành điểm nghẽn cổ chai
2. **Độ trễ cao**: Thread phải chờ đợi để lấy lock, dẫn đến độ trễ cao
3. **Deadlock**: Có thể xảy ra deadlock nếu không cẩn thận trong việc lấy và giải phóng lock

## 3. Giải pháp Lock-Free

### 3.1. Nguyên tắc cơ bản

Lock-Free Matching Engine sử dụng các nguyên tắc sau:

1. **Atomic Operations**: Sử dụng các atomic operations thay vì lock
2. **Compare-And-Swap (CAS)**: Sử dụng CAS để cập nhật trạng thái một cách an toàn
3. **Immutable Data Structures**: Sử dụng cấu trúc dữ liệu bất biến để tránh race condition
4. **Copy-On-Write**: Tạo bản sao của dữ liệu trước khi thay đổi

### 3.2. Cấu trúc dữ liệu

```java
public class LockFreeMatchingEngine {
    // Atomic reference đến snapshot hiện tại của sổ lệnh
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    // Cấu trúc dữ liệu bất biến cho sổ lệnh
    public static class OrderBookSnapshot {
        private final ImmutableNavigableMap<Money, ImmutableList<Order>> buyOrders;
        private final ImmutableNavigableMap<Money, ImmutableList<Order>> sellOrders;

        public OrderBookSnapshot() {
            this.buyOrders = ImmutableNavigableMap.of();
            this.sellOrders = ImmutableNavigableMap.of();
        }

        public OrderBookSnapshot(ImmutableNavigableMap<Money, ImmutableList<Order>> buyOrders,
                                ImmutableNavigableMap<Money, ImmutableList<Order>> sellOrders) {
            this.buyOrders = buyOrders;
            this.sellOrders = sellOrders;
        }

        public OrderBookSnapshot copy() {
            return new OrderBookSnapshot(buyOrders, sellOrders);
        }

        // Các phương thức để tạo snapshot mới với các thay đổi
    }
}
```

### 3.3. Thuật toán khớp lệnh

```java
public List<Trade> placeOrder(Order order) {
    while (true) {
        // Lấy snapshot hiện tại
        OrderBookSnapshot currentSnapshot = orderBookRef.get();

        // Tạo bản sao để thực hiện khớp lệnh
        OrderBookResult result = matchOrder(order, currentSnapshot);

        // Cập nhật snapshot bằng CAS
        if (orderBookRef.compareAndSet(currentSnapshot, result.getNewSnapshot())) {
            // Nếu CAS thành công, trả về kết quả
            return result.getTrades();
        }

        // Nếu CAS thất bại, thử lại
    }
}

private OrderBookResult matchOrder(Order order, OrderBookSnapshot snapshot) {
    // Tạo bản sao của snapshot
    OrderBookSnapshot newSnapshot = snapshot.copy();
    List<Trade> trades = new ArrayList<>();

    // Thực hiện khớp lệnh trên bản sao
    if (order.getDirection() == OrderDirection.BUY) {
        matchBuyOrder(order, newSnapshot, trades);
    } else {
        matchSellOrder(order, newSnapshot, trades);
    }

    return new OrderBookResult(newSnapshot, trades);
}
```

## 4. Cấu trúc dữ liệu Lock-Free

### 4.1. Immutable NavigableMap

```java
public class ImmutableNavigableMap<K, V> {
    private final NavigableMap<K, V> map;

    private ImmutableNavigableMap(NavigableMap<K, V> map) {
        this.map = Collections.unmodifiableNavigableMap(new TreeMap<>(map));
    }

    public static <K, V> ImmutableNavigableMap<K, V> of() {
        return new ImmutableNavigableMap<>(new TreeMap<>());
    }

    public static <K, V> ImmutableNavigableMap<K, V> of(K key, V value) {
        TreeMap<K, V> map = new TreeMap<>();
        map.put(key, value);
        return new ImmutableNavigableMap<>(map);
    }

    public ImmutableNavigableMap<K, V> put(K key, V value) {
        TreeMap<K, V> newMap = new TreeMap<>(map);
        newMap.put(key, value);
        return new ImmutableNavigableMap<>(newMap);
    }

    public ImmutableNavigableMap<K, V> remove(K key) {
        TreeMap<K, V> newMap = new TreeMap<>(map);
        newMap.remove(key);
        return new ImmutableNavigableMap<>(newMap);
    }

    public V get(K key) {
        return map.get(key);
    }

    public Map.Entry<K, V> firstEntry() {
        return map.firstEntry();
    }

    public boolean isEmpty() {
        return map.isEmpty();
    }

    // Các phương thức khác
}
```

### 4.2. Immutable List

```java
public class ImmutableList<E> {
    private final List<E> list;

    private ImmutableList(List<E> list) {
        this.list = Collections.unmodifiableList(new ArrayList<>(list));
    }

    public static <E> ImmutableList<E> of() {
        return new ImmutableList<>(new ArrayList<>());
    }

    public static <E> ImmutableList<E> of(E element) {
        ArrayList<E> list = new ArrayList<>();
        list.add(element);
        return new ImmutableList<>(list);
    }

    public ImmutableList<E> add(E element) {
        ArrayList<E> newList = new ArrayList<>(list);
        newList.add(element);
        return new ImmutableList<>(newList);
    }

    public ImmutableList<E> remove(E element) {
        ArrayList<E> newList = new ArrayList<>(list);
        newList.remove(element);
        return new ImmutableList<>(newList);
    }

    public E get(int index) {
        return list.get(index);
    }

    public int size() {
        return list.size();
    }

    public boolean isEmpty() {
        return list.isEmpty();
    }

    // Các phương thức khác
}
```

## 5. Thuật toán khớp lệnh Lock-Free

### 5.1. Khớp lệnh mua

```java
private void matchBuyOrder(Order buyOrder, OrderBookSnapshot snapshot, List<Trade> trades) {
    // Lấy danh sách lệnh bán
    ImmutableNavigableMap<Money, ImmutableList<Order>> sellOrders = snapshot.getSellOrders();

    // Khối lượng còn lại cần khớp
    BigDecimal remainingVolume = buyOrder.getVolume();

    // Khớp lệnh với các lệnh bán
    while (remainingVolume.compareTo(BigDecimal.ZERO) > 0 && !sellOrders.isEmpty()) {
        // Lấy mức giá tốt nhất (thấp nhất) của lệnh bán
        Map.Entry<Money, ImmutableList<Order>> bestPriceEntry = sellOrders.firstEntry();
        Money bestPrice = bestPriceEntry.getKey();
        ImmutableList<Order> ordersAtBestPrice = bestPriceEntry.getValue();

        // Kiểm tra điều kiện giá
        if (buyOrder.getType() == OrderType.LIMIT && buyOrder.getPrice().compareTo(bestPrice) < 0) {
            // Giá mua thấp hơn giá bán tốt nhất, không thể khớp
            break;
        }

        // Khớp lệnh với các lệnh ở mức giá tốt nhất
        ImmutableList<Order> remainingOrders = ordersAtBestPrice;
        for (int i = 0; i < ordersAtBestPrice.size() && remainingVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
            Order sellOrder = ordersAtBestPrice.get(i);

            // Tính toán khối lượng khớp
            BigDecimal matchVolume = remainingVolume.min(sellOrder.getRemainingVolume());

            // Tạo giao dịch
            Trade trade = new Trade(
                buyOrder,
                sellOrder,
                bestPrice,
                matchVolume,
                LocalDateTime.now()
            );
            trades.add(trade);

            // Cập nhật khối lượng còn lại
            remainingVolume = remainingVolume.subtract(matchVolume);

            // Cập nhật lệnh bán
            Order updatedSellOrder = sellOrder.withRemainingVolume(sellOrder.getRemainingVolume().subtract(matchVolume));

            // Cập nhật danh sách lệnh
            if (updatedSellOrder.getRemainingVolume().compareTo(BigDecimal.ZERO) > 0) {
                // Lệnh bán chưa khớp hết, cập nhật
                remainingOrders = remainingOrders.remove(sellOrder).add(updatedSellOrder);
            } else {
                // Lệnh bán đã khớp hết, xóa
                remainingOrders = remainingOrders.remove(sellOrder);
            }
        }

        // Cập nhật sổ lệnh
        if (remainingOrders.isEmpty()) {
            // Không còn lệnh ở mức giá này, xóa mức giá
            sellOrders = sellOrders.remove(bestPrice);
        } else {
            // Cập nhật danh sách lệnh ở mức giá này
            sellOrders = sellOrders.put(bestPrice, remainingOrders);
        }
    }

    // Cập nhật snapshot với sổ lệnh mới
    snapshot.setSellOrders(sellOrders);

    // Nếu còn khối lượng chưa khớp, thêm lệnh mua vào sổ lệnh
    if (remainingVolume.compareTo(BigDecimal.ZERO) > 0 && buyOrder.getType() == OrderType.LIMIT) {
        Order updatedBuyOrder = buyOrder.withRemainingVolume(remainingVolume);

        // Lấy danh sách lệnh mua
        ImmutableNavigableMap<Money, ImmutableList<Order>> buyOrders = snapshot.getBuyOrders();

        // Thêm lệnh mua vào sổ lệnh
        ImmutableList<Order> ordersAtPrice = buyOrders.get(updatedBuyOrder.getPrice());
        if (ordersAtPrice == null) {
            ordersAtPrice = ImmutableList.of();
        }

        buyOrders = buyOrders.put(updatedBuyOrder.getPrice(), ordersAtPrice.add(updatedBuyOrder));

        // Cập nhật snapshot với sổ lệnh mới
        snapshot.setBuyOrders(buyOrders);
    }
}
```

### 5.2. Khớp lệnh bán

```java
private void matchSellOrder(Order sellOrder, OrderBookSnapshot snapshot, List<Trade> trades) {
    // Tương tự như matchBuyOrder nhưng ngược lại
}
```

## 6. Cải tiến hiệu suất

### 6.1. Sử dụng ConcurrentSkipListMap

Thay vì tự triển khai ImmutableNavigableMap, có thể sử dụng ConcurrentSkipListMap với Copy-On-Write:

```java
public class LockFreeMatchingEngine {
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    public static class OrderBookSnapshot {
        private final ConcurrentSkipListMap<Money, List<Order>> buyOrders;
        private final ConcurrentSkipListMap<Money, List<Order>> sellOrders;

        public OrderBookSnapshot() {
            this.buyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
            this.sellOrders = new ConcurrentSkipListMap<>();
        }

        public OrderBookSnapshot(ConcurrentSkipListMap<Money, List<Order>> buyOrders,
                                ConcurrentSkipListMap<Money, List<Order>> sellOrders) {
            this.buyOrders = buyOrders;
            this.sellOrders = sellOrders;
        }

        public OrderBookSnapshot copy() {
            ConcurrentSkipListMap<Money, List<Order>> newBuyOrders = new ConcurrentSkipListMap<>(Comparator.reverseOrder());
            ConcurrentSkipListMap<Money, List<Order>> newSellOrders = new ConcurrentSkipListMap<>();

            // Deep copy
            for (Map.Entry<Money, List<Order>> entry : buyOrders.entrySet()) {
                newBuyOrders.put(entry.getKey(), new CopyOnWriteArrayList<>(entry.getValue()));
            }

            for (Map.Entry<Money, List<Order>> entry : sellOrders.entrySet()) {
                newSellOrders.put(entry.getKey(), new CopyOnWriteArrayList<>(entry.getValue()));
            }

            return new OrderBookSnapshot(newBuyOrders, newSellOrders);
        }
    }
}
```

### 6.2. Sử dụng CopyOnWriteArrayList

Sử dụng CopyOnWriteArrayList để lưu trữ các lệnh ở cùng một mức giá:

```java
public class LockFreeMatchingEngine {
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    public static class OrderBookSnapshot {
        private final ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> buyOrders;
        private final ConcurrentSkipListMap<Money, CopyOnWriteArrayList<Order>> sellOrders;

        // ...
    }
}
```

### 6.3. Sử dụng Disruptor

Sử dụng Disruptor để xử lý các lệnh một cách hiệu quả:

```java
@Component
public class DisruptorOrderProcessor {
    private final Disruptor<OrderEvent> disruptor;
    private final RingBuffer<OrderEvent> ringBuffer;
    private final LockFreeMatchingEngine matchingEngine;

    public DisruptorOrderProcessor(LockFreeMatchingEngine matchingEngine) {
        this.matchingEngine = matchingEngine;

        // Khởi tạo Disruptor
        disruptor = new Disruptor<>(
            OrderEvent::new,
            1024,
            Executors.defaultThreadFactory(),
            ProducerType.MULTI,
            new BlockingWaitStrategy()
        );

        // Thiết lập handler
        disruptor.handleEventsWith(this::handleOrderEvent);

        // Bắt đầu Disruptor
        ringBuffer = disruptor.start();
    }

    public void placeOrder(Order order) {
        // Publish event vào Disruptor
        long sequence = ringBuffer.next();
        try {
            OrderEvent event = ringBuffer.get(sequence);
            event.setOrder(order);
            event.setType(OrderEventType.PLACE_ORDER);
        } finally {
            ringBuffer.publish(sequence);
        }
    }

    private void handleOrderEvent(OrderEvent event, long sequence, boolean endOfBatch) {
        switch (event.getType()) {
            case PLACE_ORDER:
                matchingEngine.placeOrder(event.getOrder());
                break;
            case CANCEL_ORDER:
                matchingEngine.cancelOrder(event.getOrder());
                break;
            // Xử lý các loại event khác
        }
    }
}
```

## 7. Xử lý trường hợp xấu nhất

### 7.1. Phân đoạn Order Book

```java
public class SegmentedLockFreeMatchingEngine {
    private final BigDecimal segmentSize;
    private final ConcurrentHashMap<PriceRange, LockFreeMatchingEngine> segments = new ConcurrentHashMap<>();

    public SegmentedLockFreeMatchingEngine(BigDecimal segmentSize) {
        this.segmentSize = segmentSize;
    }

    public List<Trade> placeOrder(Order order) {
        // Xác định phân đoạn dựa trên giá
        PriceRange range = calculatePriceRange(order.getPrice());

        // Lấy hoặc tạo matching engine cho phân đoạn
        LockFreeMatchingEngine engine = segments.computeIfAbsent(range, r -> new LockFreeMatchingEngine());

        // Xử lý lệnh trong phân đoạn
        return engine.placeOrder(order);
    }

    private PriceRange calculatePriceRange(Money price) {
        BigDecimal value = price.getValue();
        BigDecimal lowerBound = value.divideToIntegralValue(segmentSize).multiply(segmentSize);
        BigDecimal upperBound = lowerBound.add(segmentSize);
        return new PriceRange(Money.of(lowerBound), Money.of(upperBound));
    }
}
```

### 7.2. Giới hạn kích thước Order Book

```java
public class SizeLimitedLockFreeMatchingEngine {
    private static final int MAX_PRICE_LEVELS = 1000;
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    public List<Trade> placeOrder(Order order) {
        while (true) {
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Kiểm tra và giới hạn kích thước sổ lệnh
            OrderBookSnapshot limitedSnapshot = limitOrderBookSize(currentSnapshot);

            // Nếu sổ lệnh đã thay đổi, cập nhật
            if (limitedSnapshot != currentSnapshot) {
                if (orderBookRef.compareAndSet(currentSnapshot, limitedSnapshot)) {
                    currentSnapshot = limitedSnapshot;
                } else {
                    // Nếu CAS thất bại, thử lại
                    continue;
                }
            }

            // Thực hiện khớp lệnh
            OrderBookResult result = matchOrder(order, currentSnapshot);

            // Cập nhật snapshot
            if (orderBookRef.compareAndSet(currentSnapshot, result.getNewSnapshot())) {
                return result.getTrades();
            }

            // Nếu CAS thất bại, thử lại
        }
    }

    private OrderBookSnapshot limitOrderBookSize(OrderBookSnapshot snapshot) {
        ConcurrentSkipListMap<Money, List<Order>> buyOrders = snapshot.getBuyOrders();
        ConcurrentSkipListMap<Money, List<Order>> sellOrders = snapshot.getSellOrders();

        boolean changed = false;

        // Giới hạn kích thước sổ lệnh mua
        while (buyOrders.size() > MAX_PRICE_LEVELS) {
            buyOrders.pollLastEntry();
            changed = true;
        }

        // Giới hạn kích thước sổ lệnh bán
        while (sellOrders.size() > MAX_PRICE_LEVELS) {
            sellOrders.pollLastEntry();
            changed = true;
        }

        if (changed) {
            return new OrderBookSnapshot(buyOrders, sellOrders);
        } else {
            return snapshot;
        }
    }
}
```

### 7.3. Tái cấu trúc định kỳ

```java
@Service
public class PeriodicRestructuringService {
    private final LockFreeMatchingEngine matchingEngine;

    @Scheduled(fixedRate = 3600000) // 1 giờ
    public void restructureOrderBook() {
        matchingEngine.restructure();
    }
}

public class LockFreeMatchingEngine {
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    public void restructure() {
        while (true) {
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo snapshot mới
            OrderBookSnapshot newSnapshot = new OrderBookSnapshot();

            // Sao chép các lệnh mua
            for (Map.Entry<Money, List<Order>> entry : currentSnapshot.getBuyOrders().entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    newSnapshot.getBuyOrders().put(entry.getKey(), new CopyOnWriteArrayList<>(entry.getValue()));
                }
            }

            // Sao chép các lệnh bán
            for (Map.Entry<Money, List<Order>> entry : currentSnapshot.getSellOrders().entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    newSnapshot.getSellOrders().put(entry.getKey(), new CopyOnWriteArrayList<>(entry.getValue()));
                }
            }

            // Cập nhật snapshot
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                break;
            }
        }
    }
}
```

## 8. Kết luận

Lock-Free Matching Engine cung cấp một giải pháp hiệu quả để cải thiện hiệu suất và giảm thiểu tình trạng nghẽn cổ chai trong hệ thống giao dịch hợp đồng tương lai. Bằng cách sử dụng các atomic operations, CAS, và cấu trúc dữ liệu bất biến, chúng ta có thể xây dựng một matching engine có khả năng xử lý khối lượng lệnh lớn với độ trễ thấp.

Các bước tiếp theo:

1. Triển khai Lock-Free Matching Engine theo thiết kế này
2. Thực hiện kiểm thử đơn vị và tích hợp
3. Thực hiện kiểm thử hiệu suất và so sánh với matching engine hiện tại
4. Triển khai dần dần trong môi trường production
