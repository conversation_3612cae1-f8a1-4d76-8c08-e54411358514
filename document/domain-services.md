# Domain Services trong Future-Core Module

## Giới thiệu

Tài liệu này mô tả chi tiết các domain services trong Future-Core Module. Domain services là các dịch vụ chứa logic nghiệp vụ phức tạp liên quan đến nhiều domain entities, và chúng được triển khai trong domain layer của clean architecture.

## Tình trạng chuyển đổi

Future-Core Module đang trong quá trình chuyển đổi sang clean architecture. Một số domain services đã được chuyển đổi hoàn toàn, trong khi một số khác vẫn đang sử dụng cấu trúc cũ.

### Đã chuyển đổi

Các domain services sau đã được chuyển đổi sang clean architecture:

- `OrderMatchingEngineService`: Đã được chuyển đổi thành domain service trong package `com.icetea.lotus.core.domain.service`.
- `PositionService`: Đ<PERSON> được chuyển đổi thành domain service trong package `com.icetea.lotus.core.domain.service`.
- `FundingService`: Đ<PERSON> được chuyển đổi thành domain service trong package `com.icetea.lotus.core.domain.service`.
- `LiquidationService`: Đã được chuyển đổi thành domain service trong package `com.icetea.lotus.core.domain.service`.
- `PriceManagementService`: Đã được chuyển đổi thành domain service trong package `com.icetea.lotus.core.domain.service`.

### Chưa chuyển đổi

Các domain services sau vẫn chưa được chuyển đổi sang clean architecture:

- `ContractSpecialOrderService`: Vẫn đang sử dụng cấu trúc cũ trong package `com.icetea.lotus.service`.
- `HybridPricingService`: Vẫn đang sử dụng cấu trúc cũ trong package `com.icetea.lotus.service`.

## Domain Services

### 1. OrderMatchingEngineService

`OrderMatchingEngineService` là domain service chịu trách nhiệm khớp lệnh trong hệ thống.

#### Interface

```java
public interface OrderMatchingEngineService {
    List<Trade> placeOrder(Order order);
    List<Trade> processOrder(Order order);
    boolean cancelOrder(Order order);
    boolean cancelOrder(OrderId orderId, Symbol symbol);
    List<Order> cancelAllOrders(Long memberId, Symbol symbol);
    void pauseTrading(Symbol symbol);
    void resumeTrading(Symbol symbol);
    void updateMarkPrice(Symbol symbol, Money markPrice);
    void updateIndexPrice(Symbol symbol, Money indexPrice);
    Money getMarkPrice(Symbol symbol);
    Money getIndexPrice(Symbol symbol);
    Money getLastPrice(Symbol symbol);
    void checkTriggerOrders(Symbol symbol);
    void checkLiquidations(Symbol symbol);
    List<Trade> liquidatePosition(Position position);
    void synchronizeWithContracts();
    void initialize(Symbol symbol);
    void shutdown(Symbol symbol);
    OrderBook getOrderBook(Symbol symbol);
    Optional<Order> findOrderById(OrderId orderId);
    void setMatchingAlgorithm(Symbol symbol, MatchingAlgorithm algorithm);
    MatchingAlgorithm getMatchingAlgorithm(Symbol symbol);
    List<Trade> createLiquidationOrder(Position position, Money liquidationPrice);
    List<Trade> createADLOrder(Position position, Money volume);
    void addPositionToCheck(Position position);
    void removePositionToCheck(PositionId positionId);
    void updateFundingRate(Symbol symbol, Money fundingRate);
    List<Order> matchOrder(Order newOrder, List<Order> existingOrders, Contract contract);
    Money calculateMatchPrice(Order buyOrder, Order sellOrder, Contract contract);
    BigDecimal calculateMatchVolume(Order buyOrder, Order sellOrder);
    boolean canMatch(Order order1, Order order2);
    List<Order> getMatchingOrders(Order order, List<Order> existingOrders);
    List<Order> sortOrders(List<Order> orders, OrderDirection direction, MatchingAlgorithm algorithm);
}
```

#### Implementation

`OrderMatchingEngineServiceImpl` là implementation của `OrderMatchingEngineService`. Nó triển khai các phương thức để khớp lệnh theo các thuật toán khác nhau và quản lý các matching engine cho từng symbol.

```java
@Slf4j
@Service
public class OrderMatchingEngineServiceImpl implements OrderMatchingEngineService {
    private final ContractRepository contractRepository;
    private final OrderRepository orderRepository;
    private final TradeRepository tradeRepository;
    private final ImpliedMatchingService impliedMatchingService;
    private final OptimizedMatchingEngine optimizedMatchingEngine;
    private final DistributedLockingMatchingEngine distributedLockingMatchingEngine;
    private final MatchingEngineConfig matchingEngineConfig;

    // Maps cho các matching engine
    private final Map<String, MatchingEngine> matchingEngines = new ConcurrentHashMap<>();
    private final Map<String, OptimizedMatchingEngine> optimizedMatchingEngines = new ConcurrentHashMap<>();
    private final Map<String, LockFreeMatchingEngine> lockFreeMatchingEngines = new ConcurrentHashMap<>();

    // Maps cho các tính năng mở rộng
    private final Map<String, List<Order>> triggerOrdersMap = new ConcurrentHashMap<>();
    private final Map<String, Map<Long, Position>> positionsToCheckMap = new ConcurrentHashMap<>();
    private final Map<String, Money> fundingRateMap = new ConcurrentHashMap<>();
    private final Map<String, LocalDateTime> nextFundingTimeMap = new ConcurrentHashMap<>();

    // Maps cho OrderMatchingService
    private final Map<Symbol, OrderBook> orderBooks = new ConcurrentHashMap<>();

    @Override
    public List<Trade> placeOrder(Order order) {
        // Xử lý đặt lệnh mới
        return processOrder(order);
    }

    @Override
    public List<Trade> processOrder(Order order) {
        // Xử lý lệnh mới
        Symbol symbol = order.getSymbol();

        // Lấy hoặc tạo matching engine cho symbol
        MatchingEngine engine = getOrCreateMatchingEngine(symbol);

        // Xử lý lệnh
        return engine.processOrder(order);
    }

    // Các phương thức khác...
}
```

### 2. PositionService

`PositionService` là domain service chịu trách nhiệm quản lý vị thế trong hệ thống.

#### Interface

```java
public interface PositionService {
    Position openPosition(Order order, Money price, BigDecimal volume, Contract contract);
    Position closePosition(Position position, Money price, BigDecimal volume);
    Position adjustLeverage(Position position, BigDecimal leverage);
    Position adjustMargin(Position position, Money margin, boolean isAdd);
    Position changeMarginMode(Position position, MarginMode marginMode);
    Money calculateUnrealizedProfit(Position position, Money currentPrice);
    BigDecimal calculateUnrealizedProfitRate(Position position, Money currentPrice);
    boolean needsLiquidation(Position position, Money currentPrice, Contract contract);
    Money calculateLiquidationPrice(Position position, Contract contract);
}
```

#### Implementation

`PositionServiceImpl` là implementation của `PositionService`. Nó triển khai các phương thức để quản lý vị thế.

```java
@Service
public class PositionServiceImpl implements PositionService {
    @Override
    public Position openPosition(Order order, Money price, BigDecimal volume, Contract contract) {
        // Tính toán ký quỹ ban đầu
        Money initialMargin = calculateInitialMargin(price, volume, order.getLeverage(), contract);

        // Tính toán giá thanh lý
        Money liquidationPrice = calculateLiquidationPrice(order.getDirection(), price, initialMargin, volume, contract);

        // Tạo vị thế mới
        return Position.builder()
                .id(new PositionId(UUID.randomUUID().toString()))
                .memberId(order.getMemberId())
                .symbol(order.getSymbol())
                .direction(order.getDirection() == OrderDirection.BUY ? PositionDirection.LONG : PositionDirection.SHORT)
                .volume(volume)
                .openPrice(price)
                .liquidationPrice(liquidationPrice)
                .maintenanceMargin(calculateMaintenanceMargin(price, volume, contract))
                .margin(initialMargin)
                .profit(Money.of(BigDecimal.ZERO))
                .marginMode(MarginMode.ISOLATED)
                .leverage(order.getLeverage())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }

    // Các phương thức khác...
}
```

### 3. FundingService

`FundingService` là domain service chịu trách nhiệm quản lý tỷ lệ tài trợ trong hệ thống.

#### Interface

```java
public interface FundingService {
    FundingRate calculateFundingRate(Symbol symbol, Money indexPrice, Money markPrice);
    Money calculateFundingFee(Position position, FundingRate fundingRate);
    void applyFunding(Position position, FundingRate fundingRate);
    LocalDateTime getNextFundingTime(Symbol symbol, LocalDateTime currentTime);
}
```

#### Implementation

`FundingServiceImpl` là implementation của `FundingService`. Nó triển khai các phương thức để quản lý tỷ lệ tài trợ.

```java
@Service
public class FundingServiceImpl implements FundingService {
    @Override
    public FundingRate calculateFundingRate(Symbol symbol, Money indexPrice, Money markPrice) {
        // Tính toán tỷ lệ tài trợ dựa trên giá chỉ số và giá đánh dấu
        BigDecimal premium = markPrice.getValue().subtract(indexPrice.getValue());
        BigDecimal premiumIndex = premium.divide(indexPrice.getValue(), 8, RoundingMode.HALF_UP);

        // Giới hạn tỷ lệ tài trợ trong khoảng [-0.75%, 0.75%]
        BigDecimal fundingRate = premiumIndex;
        if (fundingRate.compareTo(new BigDecimal("0.0075")) > 0) {
            fundingRate = new BigDecimal("0.0075");
        } else if (fundingRate.compareTo(new BigDecimal("-0.0075")) < 0) {
            fundingRate = new BigDecimal("-0.0075");
        }

        // Tạo tỷ lệ tài trợ mới
        return FundingRate.builder()
                .id(new FundingRateId(UUID.randomUUID().toString()))
                .symbol(symbol)
                .rate(Rate.of(fundingRate))
                .indexPrice(indexPrice)
                .markPrice(markPrice)
                .time(LocalDateTime.now())
                .build();
    }

    // Các phương thức khác...
}
```

### 4. LiquidationService

`LiquidationService` là domain service chịu trách nhiệm quản lý thanh lý vị thế trong hệ thống.

#### Interface

```java
public interface LiquidationService {
    boolean needsLiquidation(Position position, Money currentPrice, Contract contract);
    Order createLiquidationOrder(Position position, Money currentPrice);
    void liquidatePosition(Position position, Money currentPrice);
    List<Position> findPositionsToLiquidate(List<Position> positions, Map<Symbol, Money> currentPrices, Map<Symbol, Contract> contracts);
}
```

#### Implementation

`LiquidationServiceImpl` là implementation của `LiquidationService`. Nó triển khai các phương thức để quản lý thanh lý vị thế.

```java
@Service
public class LiquidationServiceImpl implements LiquidationService {
    @Override
    public boolean needsLiquidation(Position position, Money currentPrice, Contract contract) {
        // Tính toán lợi nhuận không thực hiện
        Money unrealizedProfit = position.calculateUnrealizedProfit(currentPrice);

        // Tính toán ký quỹ hiện tại
        Money currentMargin = position.getMargin().add(unrealizedProfit);

        // Tính toán ký quỹ bảo trì
        Money maintenanceMargin = position.getMaintenanceMargin();

        // Nếu ký quỹ hiện tại nhỏ hơn ký quỹ bảo trì, cần thanh lý
        return currentMargin.getValue().compareTo(maintenanceMargin.getValue()) < 0;
    }

    // Các phương thức khác...
}
```

### 5. PriceManagementService

`PriceManagementService` là domain service chịu trách nhiệm quản lý giá trong hệ thống. Service này hợp nhất các chức năng từ `PriceService`, `IndexPriceService`, `MarkPriceService` và `PricingService`.

#### Interface

```java
public interface PriceManagementService {
    Money getCurrentIndexPrice(Symbol symbol);
    Money getCurrentMarkPrice(Symbol symbol);
    void updateIndexPrice(Symbol symbol, Money price, LocalDateTime time);
    void updateMarkPrice(Symbol symbol, Money price, LocalDateTime time);
    Money calculateIndexPrice(Symbol symbol, Map<String, BigDecimal> prices, Contract contract);
    Money calculateMarkPrice(Symbol symbol, Money indexPrice, Contract contract, BigDecimal fundingRate);
    List<PricePoint> getIndexPriceHistory(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime, int interval);
    List<PricePoint> getMarkPriceHistory(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime, int interval);
    FundingRate calculateFundingRate(Symbol symbol, Money indexPrice, Money markPrice, Contract contract);
    BigDecimal getCurrentFundingRate(Symbol symbol);
    LocalDateTime getNextFundingTime(Symbol symbol);
}
```

#### Implementation

`PriceManagementServiceImpl` là implementation của `PriceManagementService`. Nó triển khai các phương thức để quản lý giá.

```java
@Service
@RequiredArgsConstructor
@Transactional
public class PriceManagementServiceImpl implements PriceManagementService {
    private final IndexPriceManagementService indexPriceManagementService;
    private final MarkPriceManagementService markPriceManagementService;
    private final FundingRateManagementService fundingRateManagementService;

    // Delegating methods for IndexPriceManagementService
    @Override
    public Money getCurrentIndexPrice(Symbol symbol) {
        return indexPriceManagementService.getCurrentIndexPrice(symbol);
    }

    @Override
    public void updateIndexPrice(Symbol symbol, Money price, LocalDateTime time) {
        indexPriceManagementService.updateIndexPrice(symbol, price, time);
    }

    @Override
    public Money calculateIndexPrice(Symbol symbol, Map<String, BigDecimal> prices, Contract contract) {
        return indexPriceManagementService.calculateIndexPrice(symbol, prices, contract);
    }

    // Delegating methods for MarkPriceManagementService
    @Override
    public Money getCurrentMarkPrice(Symbol symbol) {
        return markPriceManagementService.getCurrentMarkPrice(symbol);
    }

    @Override
    public void updateMarkPrice(Symbol symbol, Money price, LocalDateTime time) {
        markPriceManagementService.updateMarkPrice(symbol, price, time);
    }

    @Override
    public Money calculateMarkPrice(Symbol symbol, Money indexPrice, Contract contract, BigDecimal fundingRate) {
        return markPriceManagementService.calculateMarkPrice(symbol, indexPrice, contract, fundingRate);
    }

    // Delegating methods for FundingRateManagementService
    @Override
    public FundingRate calculateFundingRate(Symbol symbol, Money indexPrice, Money markPrice, Contract contract) {
        return fundingRateManagementService.calculateFundingRate(symbol, indexPrice, markPrice, contract);
    }

    // Các phương thức khác...
}
```

## Kết luận

Domain services trong Future-Core Module được thiết kế để chứa logic nghiệp vụ phức tạp liên quan đến nhiều domain entities. Chúng được triển khai trong domain layer của clean architecture, và không phụ thuộc vào bất kỳ framework nào. Việc chuyển đổi các domain services sang clean architecture giúp tạo ra một mô hình miền rõ ràng, dễ hiểu và dễ bảo trì.
