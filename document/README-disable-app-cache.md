# Hướng dẫn tắt cache ở ứng dụng nhưng vẫn lưu dữ liệu vào Redis

## Tổng quan

Tài liệu này hướng dẫn cách tắt cache ở ứng dụng nhưng vẫn giữ lại khả năng lưu trữ dữ liệu vào Redis. Gi<PERSON>i pháp này bao gồm:

1. Cấu hình Java để vô hiệu hóa các annotation cache
2. Cấu hình application để tắt cache
3. Service để thao tác với Redis một cách thủ công

## Các file đã tạo

1. **DisableAppCacheConfig.java**: Cấu hình để vô hiệu hóa các annotation cache nhưng vẫn giữ lại kết nối với Redis.
2. **RedisDataService.java**: Service cung cấp các phương thức để thao tác với Redis một cách thủ công.
3. **application-no-cache.yaml**: Cấu hình application để tắt cache nhưng vẫn giữ lại kết nối với Redis.
4. **disable-app-cache-guide.md**: Tài liệu hướng dẫn chi tiết cách sử dụng giải pháp.

## Cách sử dụng

### 1. Kích hoạt profile no-cache

Có hai cách để kích hoạt profile no-cache:

#### Cách 1: Thêm vào application.yaml

Thêm profile `no-cache` vào danh sách profiles active trong file `application.yaml`:

```yaml
spring:
  profiles:
    active: dev,no-cache
```

#### Cách 2: Sử dụng tham số khi chạy ứng dụng

```bash
java -jar future-core.jar --spring.profiles.active=dev,no-cache
```

hoặc

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev,no-cache
```

### 2. Sử dụng RedisDataService

Sau khi kích hoạt profile no-cache, bạn có thể sử dụng `RedisDataService` để thao tác với Redis một cách thủ công:

```java
@Service
public class YourService {
    private final RedisDataService redisDataService;
    
    @Autowired
    public YourService(RedisDataService redisDataService) {
        this.redisDataService = redisDataService;
    }
    
    public void saveData(String key, Object value) {
        // Lưu dữ liệu vào Redis
        redisDataService.set(key, value, 30, TimeUnit.MINUTES);
    }
    
    public Object getData(String key) {
        // Lấy dữ liệu từ Redis
        return redisDataService.get(key);
    }
}
```

## Kiểm tra xem cache đã bị tắt chưa

Để kiểm tra xem cache đã bị tắt hay chưa, bạn có thể:

1. Kiểm tra log của ứng dụng, bạn sẽ thấy thông báo:
   ```
   Đang sử dụng NoOpCacheManager - CACHE ĐÃ BỊ VÔ HIỆU HÓA Ở ỨNG DỤNG!
   ```

2. Kiểm tra xem các annotation cache có hoạt động không:
   - Đặt breakpoint trong các phương thức có annotation `@Cacheable`, `@CachePut`, `@CacheEvict`
   - Kiểm tra xem phương thức có được thực thi mỗi lần gọi không (nếu cache bị tắt, phương thức sẽ được thực thi mỗi lần gọi)

3. Kiểm tra xem dữ liệu có được lưu vào Redis không:
   - Sử dụng Redis CLI để kiểm tra:
     ```
     redis-cli -h <host> -p <port> -a <password>
     keys *
     ```
   - Hoặc sử dụng Redis Desktop Manager để kiểm tra

## Khôi phục cache

Khi vấn đề đã được khắc phục, bạn có thể khôi phục lại cache bằng cách:

1. Xóa profile `no-cache` khỏi danh sách profiles active
2. Xóa các file cấu hình đã tạo (nếu không cần thiết nữa)

## Lưu ý

1. Giải pháp này chỉ tắt cache ở ứng dụng, không ảnh hưởng đến dữ liệu đã lưu trong Redis.
2. Dữ liệu vẫn có thể được lưu vào Redis thông qua `RedisDataService`.
3. Các annotation cache (`@Cacheable`, `@CachePut`, `@CacheEvict`) sẽ không có tác dụng.
4. Hiệu suất của ứng dụng có thể giảm do không sử dụng cache.

## Tham khảo

Để biết thêm chi tiết, vui lòng tham khảo tài liệu `disable-app-cache-guide.md`.
