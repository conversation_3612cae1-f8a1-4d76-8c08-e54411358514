# Hướng dẫn triển khai nhiều instance trong môi trường Kubernetes

## 1. Tổng quan

Tài liệu này mô tả cách triển khai hệ thống giao dịch hợp đồng tương lai với nhiều instance trong môi trườ<PERSON>, đ<PERSON><PERSON> bảo tính nhất quán và hiệu suất cao.

> **Lưu ý**: Future-core là thư viện được Future-api sử dụng, không phải là service riêng biệt.

## 2. Kiến trúc hiện tại

<PERSON> tạ<PERSON>, hệ thống được thiết kế với cấu trúc sau:

- **Future-api**: Service chạy trên <PERSON>, xử lý các request từ client
- **Future-core**: Thư viện được nhúng trong Future-api, chứa logic nghiệp vụ
- **Matching Engine**: <PERSON><PERSON> lý khớp lệnh theo các thu<PERSON> to<PERSON>, Pro-Rata, hoặc Hybrid
- **Order Book**: <PERSON><PERSON><PERSON> trữ sổ lệnh cho mỗi symbol
- **Position Management**: Quản lý vị thế của người dùng
- **Risk Management**: Quản lý rủi ro và thanh lý vị thế

Các thành phần này sử dụng cơ chế locking nội bộ (`ReentrantReadWriteLock`, `ConcurrentHashMap`) để đồng bộ hóa truy cập, nhưng không có cơ chế để đồng bộ hóa giữa nhiều instance.

## 3. Vấn đề khi chạy nhiều instance

Khi chạy nhiều instance trong môi trường Kubernetes, sẽ gặp các vấn đề sau:

1. **Trạng thái không đồng bộ**: Mỗi instance có một bản sao riêng của Matching Engine và Order Book
2. **Race condition**: Nhiều instance có thể cùng xử lý các lệnh liên quan đến cùng một symbol
3. **Không có distributed locking**: Không thể đảm bảo chỉ có một instance xử lý một symbol tại một thời điểm

## 4. Giải pháp

### 4.1. Sử dụng Sharding theo Symbol

Mỗi instance sẽ chỉ xử lý một tập hợp các symbol, đảm bảo rằng các lệnh liên quan đến cùng một symbol luôn được xử lý bởi cùng một instance.

#### 4.1.1. Triển khai Redis-based Sharding

```java
@Component
public class SymbolShardingManager {
    private final RedissonClient redissonClient;
    private final String podName;

    public SymbolShardingManager(RedissonClient redissonClient,
                                @Value("${HOSTNAME}") String podName) {
        this.redissonClient = redissonClient;
        this.podName = podName;
    }

    public boolean isSymbolOwnedByThisPod(String symbol) {
        // Sử dụng Redis để lưu trữ mapping giữa symbol và pod
        RMap<String, String> symbolToPodMap = redissonClient.getMap("symbol-to-pod-map");

        // Nếu symbol chưa được gán cho pod nào, thử gán cho pod hiện tại
        return symbolToPodMap.putIfAbsent(symbol, podName) == null ||
               symbolToPodMap.get(symbol).equals(podName);
    }

    public void rebalanceSymbols(List<String> allPods, List<String> allSymbols) {
        // Phân phối lại các symbol giữa các pod
        RMap<String, String> symbolToPodMap = redissonClient.getMap("symbol-to-pod-map");

        // Xóa các pod không còn tồn tại
        symbolToPodMap.entrySet().removeIf(entry -> !allPods.contains(entry.getValue()));

        // Phân phối các symbol chưa được gán
        for (String symbol : allSymbols) {
            if (!symbolToPodMap.containsKey(symbol)) {
                // Chọn pod có ít symbol nhất
                Map<String, Integer> podSymbolCount = new HashMap<>();
                for (String pod : allPods) {
                    podSymbolCount.put(pod, 0);
                }

                for (String assignedPod : symbolToPodMap.values()) {
                    podSymbolCount.put(assignedPod, podSymbolCount.getOrDefault(assignedPod, 0) + 1);
                }

                String podWithMinSymbols = allPods.get(0);
                for (String pod : allPods) {
                    if (podSymbolCount.get(pod) < podSymbolCount.get(podWithMinSymbols)) {
                        podWithMinSymbols = pod;
                    }
                }

                symbolToPodMap.put(symbol, podWithMinSymbols);
            }
        }
    }
}
```

#### 4.1.2. Cấu hình Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: future-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: future-api
  template:
    metadata:
      labels:
        app: future-api
    spec:
      containers:
      - name: future-api
        image: future-api:latest
        env:
        - name: HOSTNAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_COUNT
          value: "3"
```

### 4.2. Sử dụng Distributed Locking với Redis

Để đảm bảo tính nhất quán khi xử lý các lệnh, sử dụng distributed locking với Redis.

#### 4.2.1. Cấu hình Redisson

```java
@Configuration
public class RedissonConfig {
    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
              .setAddress("redis://" + redisHost + ":" + redisPort)
              .setPassword(redisPassword);

        return Redisson.create(config);
    }
}
```

#### 4.2.2. Triển khai Distributed Locking

```java
@Service
public class DistributedOrderMatchingService {
    private final RedissonClient redissonClient;
    private final OrderMatchingEngineService orderMatchingEngineService;

    public List<Trade> placeOrder(Order order) {
        String lockKey = "order_matching:" + order.getSymbol().getValue();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // Lấy lock với timeout
            if (lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                try {
                    // Xử lý lệnh
                    return orderMatchingEngineService.placeOrder(order);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new RuntimeException("Không thể lấy lock cho symbol: " + order.getSymbol().getValue());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Bị gián đoạn khi chờ lock", e);
        }
    }
}
```

### 4.3. Sử dụng Event Sourcing với Kafka

Để đồng bộ hóa trạng thái giữa các instance, sử dụng event sourcing với Kafka.

#### 4.3.1. Định nghĩa Event

```java
@Data
@AllArgsConstructor
public class OrderEvent {
    private OrderEventType type;
    private Order order;

    public enum OrderEventType {
        PLACE_ORDER,
        CANCEL_ORDER,
        UPDATE_ORDER,
        MATCH_ORDER
    }
}
```

#### 4.3.2. Triển khai Event Producer

```java
@Service
public class OrderEventProducer {
    private final KafkaTemplate<String, OrderEvent> kafkaTemplate;

    public void publishOrderEvent(OrderEvent event) {
        kafkaTemplate.send("order-events", event.getOrder().getSymbol().getValue(), event);
    }
}
```

#### 4.3.3. Triển khai Event Consumer

```java
@Service
public class OrderEventConsumer {
    private final OrderMatchingEngineService orderMatchingEngineService;

    @KafkaListener(topics = "order-events")
    public void handleOrderEvent(OrderEvent event) {
        switch (event.getType()) {
            case PLACE_ORDER:
                orderMatchingEngineService.placeOrder(event.getOrder());
                break;
            case CANCEL_ORDER:
                orderMatchingEngineService.cancelOrder(event.getOrder());
                break;
            case UPDATE_ORDER:
                orderMatchingEngineService.updateOrder(event.getOrder());
                break;
            case MATCH_ORDER:
                // Xử lý khớp lệnh
                break;
        }
    }
}
```

### 4.4. Sử dụng Distributed Cache với Redis

Để cải thiện hiệu suất và giảm tải cho cơ sở dữ liệu, sử dụng distributed cache với Redis.

#### 4.4.1. Cấu hình Redis Cache

```java
@Configuration
@EnableCaching
public class RedisCacheConfig {
    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisHost);
        config.setPort(redisPort);
        config.setPassword(redisPassword);

        return new LettuceConnectionFactory(config);
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));

        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        cacheConfigurations.put("orders", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        cacheConfigurations.put("positions", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        cacheConfigurations.put("prices", defaultConfig.entryTtl(Duration.ofSeconds(30)));

        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
```

#### 4.4.2. Sử dụng Cache trong Service

```java
@Service
public class CachedOrderService {
    private final OrderRepository orderRepository;

    @Cacheable(value = "orders", key = "#orderId")
    public Order getOrder(String orderId) {
        return orderRepository.findById(orderId).orElse(null);
    }

    @CachePut(value = "orders", key = "#order.id")
    public Order saveOrder(Order order) {
        return orderRepository.save(order);
    }

    @CacheEvict(value = "orders", key = "#orderId")
    public void deleteOrder(String orderId) {
        orderRepository.deleteById(orderId);
    }
}
```

## 5. Triển khai trong Kubernetes

### 5.1. Cấu hình Deployment

Sử dụng Deployment để triển khai Future-api service.

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: future-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: future-api
  template:
    metadata:
      labels:
        app: future-api
    spec:
      containers:
      - name: future-api
        image: future-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: HOSTNAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_COUNT
          value: "3"
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: SPRING_REDIS_HOST
          value: "redis-service"
        - name: SPRING_REDIS_PORT
          value: "6379"
        - name: SPRING_KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-service:9092"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 5
```

### 5.2. Cấu hình Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: future-api-service
spec:
  selector:
    app: future-api
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

### 5.3. Cấu hình Horizontal Pod Autoscaler

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: future-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: future-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 5.4. Cấu hình Redis

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:6.2
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
```

### 5.5. Cấu hình Kafka

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
spec:
  serviceName: "kafka"
  replicas: 3
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:7.0.0
        ports:
        - containerPort: 9092
        env:
        - name: KAFKA_BROKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "zookeeper-service:2181"
        - name: KAFKA_ADVERTISED_LISTENERS
          value: "PLAINTEXT://$(KAFKA_BROKER_ID).kafka-service:9092"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-service
spec:
  selector:
    app: kafka
  ports:
  - port: 9092
    targetPort: 9092
  clusterIP: None
```

## 6. Giám sát và Logging

### 6.1. Cấu hình Prometheus

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:v2.30.0
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
      - job_name: 'future-core'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app]
            regex: future-core
            action: keep
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            regex: metrics
            action: keep
```

### 6.2. Cấu hình Grafana

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:8.2.0
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: "admin"
        - name: GF_USERS_ALLOW_SIGN_UP
          value: "false"
```

### 6.3. Cấu hình ELK Stack

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
spec:
  replicas: 1
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
        ports:
        - containerPort: 9200
        - containerPort: 9300
        env:
        - name: discovery.type
          value: single-node
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: docker.elastic.co/kibana/kibana:7.15.0
        ports:
        - containerPort: 5601
        env:
        - name: ELASTICSEARCH_HOSTS
          value: "http://elasticsearch-service:9200"
```

## 7. Kết luận

Triển khai hệ thống giao dịch hợp đồng tương lai với nhiều instance trong môi trường Kubernetes đòi hỏi các thay đổi đáng kể về kiến trúc và cách triển khai. Bằng cách sử dụng sharding, distributed locking, event sourcing, và distributed cache, chúng ta có thể đảm bảo tính nhất quán và hiệu suất cao của hệ thống.

Các bước tiếp theo:

1. Triển khai các thay đổi code để hỗ trợ sharding và distributed locking
2. Cấu hình Kubernetes StatefulSet và các service liên quan
3. Thiết lập giám sát và logging
4. Thực hiện kiểm thử hiệu suất và tính nhất quán
5. Triển khai dần dần trong môi trường production
