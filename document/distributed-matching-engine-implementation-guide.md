# Hướng dẫn triển khai Matching Engine phân tán

## 1. Tổng quan

Tài liệu này mô tả cách triển khai Matching Engine phân tán cho hệ thống giao dịch hợp đồng tương lai. Matching Engine phân tán cho phép hệ thống chạy trên nhiều instance trong môi trường Kubernetes, đảm bảo tính nhất quán và hiệu suất cao.

## 2. Kiến trúc

Kiến trúc Matching Engine phân tán bao gồm các thành phần sau:

1. **LockFreeMatchingEngine**: Matching Engine không khóa sử dụng các cấu trúc dữ liệu không khóa để tránh tình trạng nghẽn cổ chai khi nhiều thread cùng truy cập.
2. **SymbolShardingManager**: Quản lý phân phối các symbol giữa các instance.
3. **DistributedLockingMatchingEngine**: Matching Engine phân tán sử dụng Redis để đảm bảo chỉ có một instance xử lý một symbol tại một thời điểm.
4. **Redis**: Sử dụng cho distributed locking và caching.
5. **Kafka**: Sử dụng cho event sourcing và giao tiếp giữa các instance.

```mermaid
graph TD
    A[Client] -->|Đặt lệnh| B[API Gateway]
    B -->|Load Balancing| C[Future-API Pods]
    C -->|Sử dụng| D[Future-Core Library]
    D -->|Sharding| E[SymbolShardingManager]
    E -->|Lấy lock| F[Redis Cluster]
    D -->|Xử lý lệnh| G[DistributedLockingMatchingEngine]
    G -->|Không khóa| H[LockFreeMatchingEngine]
    G -->|Event| I[Kafka Cluster]
    G -->|Cache| J[Redis Cache]
    G -->|Lưu trữ| K[PostgreSQL]
    I -->|Đồng bộ| G
```

## 3. Các thành phần chính

### 3.1. LockFreeMatchingEngine

LockFreeMatchingEngine là thành phần cốt lõi của Matching Engine phân tán, sử dụng các cấu trúc dữ liệu không khóa để tránh tình trạng nghẽn cổ chai khi nhiều thread cùng truy cập.

Các tính năng chính:
- Sử dụng AtomicReference để lưu trữ snapshot của sổ lệnh
- Sử dụng Compare-And-Swap (CAS) để cập nhật trạng thái một cách an toàn
- Hỗ trợ nhiều thuật toán khớp lệnh: FIFO, Pro-Rata, Hybrid
- Xử lý lệnh chờ (stop orders)
- Kiểm tra và thanh lý vị thế

### 3.2. SymbolShardingManager

SymbolShardingManager quản lý phân phối các symbol giữa các instance, đảm bảo rằng mỗi symbol chỉ được xử lý bởi một instance tại một thời điểm.

Các tính năng chính:
- Sử dụng Redis để lưu trữ mapping giữa symbol và pod
- Cân bằng lại các symbol giữa các pod
- Cache local để tránh truy cập Redis quá nhiều

### 3.3. DistributedLockingMatchingEngine

DistributedLockingMatchingEngine là lớp trung gian giữa API và LockFreeMatchingEngine, sử dụng Redis để đảm bảo chỉ có một instance xử lý một symbol tại một thời điểm.

Các tính năng chính:
- Sử dụng Redis để lấy lock cho symbol
- Kiểm tra xem symbol có được gán cho pod này không
- Chuyển tiếp các thao tác đến LockFreeMatchingEngine

### 3.4. Event Sourcing

Event Sourcing được sử dụng để đồng bộ hóa trạng thái giữa các instance, đảm bảo tính nhất quán của dữ liệu.

Các thành phần:
- OrderEvent: Event cho các thay đổi liên quan đến lệnh
- OrderEventProducer: Producer cho OrderEvent
- OrderEventConsumer: Consumer cho OrderEvent

### 3.5. Command Pattern

Command Pattern được sử dụng để gửi lệnh giữa các pod, đảm bảo rằng lệnh được xử lý bởi pod sở hữu symbol.

Các thành phần:
- OrderCommand: Command cho các thao tác liên quan đến lệnh
- OrderCommandProducer: Producer cho OrderCommand
- OrderCommandConsumer: Consumer cho OrderCommand

## 4. Triển khai

### 4.1. Cài đặt Redis

Redis được sử dụng cho distributed locking và caching. Để cài đặt Redis trong Kubernetes:

```bash
kubectl apply -f kubernetes/redis-deployment.yaml
```

### 4.2. Cài đặt Kafka

Kafka được sử dụng cho event sourcing và giao tiếp giữa các instance. Để cài đặt Kafka trong Kubernetes:

```bash
kubectl apply -f kubernetes/kafka-deployment.yaml
```

### 4.3. Cài đặt PostgreSQL

PostgreSQL được sử dụng để lưu trữ dữ liệu. Để cài đặt PostgreSQL trong Kubernetes:

```bash
kubectl apply -f kubernetes/postgres-deployment.yaml
```

### 4.4. Cài đặt Future-API

Future-API là service chính của hệ thống, sử dụng Future-Core Library. Để cài đặt Future-API trong Kubernetes:

```bash
kubectl apply -f kubernetes/future-api-deployment.yaml
```

### 4.5. Cài đặt Monitoring

Prometheus và Grafana được sử dụng để giám sát hệ thống. Để cài đặt Monitoring trong Kubernetes:

```bash
kubectl apply -f kubernetes/monitoring-deployment.yaml
```

## 5. Cấu hình

### 5.1. Cấu hình Redis

Cấu hình Redis trong `application.yml`:

```yaml
spring:
  redis:
    host: redis-service
    port: 6379
    database: 0
```

### 5.2. Cấu hình Kafka

Cấu hình Kafka trong `application.yml`:

```yaml
spring:
  kafka:
    bootstrap-servers: kafka-service:9092
    consumer:
      group-id: future-api
```

### 5.3. Cấu hình PostgreSQL

Cấu hình PostgreSQL trong `application.yml`:

```yaml
spring:
  datasource:
    url: **********************************************
    username: postgres
    password: ${SPRING_DATASOURCE_PASSWORD}
```

## 6. Giám sát

### 6.1. Prometheus

Prometheus được sử dụng để thu thập metrics từ các service. Truy cập Prometheus UI tại:

```
http://prometheus-service:9090
```

### 6.2. Grafana

Grafana được sử dụng để hiển thị metrics từ Prometheus. Truy cập Grafana UI tại:

```
http://grafana-service:3000
```

## 7. Khắc phục sự cố

### 7.1. Kiểm tra trạng thái các pod

```bash
kubectl get pods
```

### 7.2. Xem logs của pod

```bash
kubectl logs <pod-name>
```

### 7.3. Kiểm tra mapping symbol-pod trong Redis

```bash
kubectl exec -it <redis-pod-name> -- redis-cli
> HGETALL symbol-to-pod-map
```

### 7.4. Kiểm tra các topic trong Kafka

```bash
kubectl exec -it <kafka-pod-name> -- kafka-topics --list --bootstrap-server localhost:9092
```

## 8. Kết luận

Matching Engine phân tán cho phép hệ thống giao dịch hợp đồng tương lai chạy trên nhiều instance trong môi trường Kubernetes, đảm bảo tính nhất quán và hiệu suất cao. Bằng cách sử dụng sharding, distributed locking, event sourcing, và distributed cache, chúng ta có thể xây dựng một hệ thống có khả năng mở rộng và chịu lỗi cao.
