# Danh sách các service đã bị thay thế hoặc không còn sử dụng

## Service đã được hợp nhất vào PriceManagementService

Các service sau đây đã được hợp nhất vào `PriceManagementService` và nên được đánh dấu là deprecated:

1. `PriceService` - <PERSON><PERSON> được hợp nhất vào `PriceManagementService`
2. `IndexPriceService` - <PERSON><PERSON> được hợp nhất vào `PriceManagementService`
3. `MarkPriceService` - Đ<PERSON> được hợp nhất vào `PriceManagementService`
4. `PricingService` - <PERSON><PERSON> được hợp nhất vào `PriceManagementService`

## Service không còn sử dụng

Các service sau đây không còn được sử dụng và nên được xóa:

1. `ContractOrderProcessor` - <PERSON><PERSON> đư<PERSON>c thay thế bởi `OrderService` và `OrderMatchingEngineService`
2. `ContractTradeProcessor` - <PERSON><PERSON> được thay thế bởi `TradeService` và `OrderMatchingEngineService`

## Kế hoạch xóa

1. Đánh dấu các service thừa là deprecated
2. Cập nhật tất cả các service khác để sử dụng service mới
3. Xóa các service thừa sau khi đã cập nhật tất cả các service khác

## Lưu ý

Khi xóa các service thừa, cần đảm bảo rằng:
1. Tất cả các service khác đã được cập nhật để sử dụng service mới
2. Tất cả các test case đã được cập nhật để sử dụng service mới
3. Tài liệu đã được cập nhật để phản ánh các thay đổi
