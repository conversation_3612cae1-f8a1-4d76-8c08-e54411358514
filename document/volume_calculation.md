# Tính toán Volume trong Hệ thống Giao dịch Hợp đồng Tương lai

## Tổng quan

Tài liệu này mô tả cách hệ thống tính toán volume trong giao dịch hợp đồng tương lai.

## Đơn vị Volume

Trong hệ thống giao dịch hợp đồng tương lai, volume được tính theo đơn vị của quote coin (USDT), không phải base coin (BTC).

### Ví dụ

Khi người dùng đặt lệnh mua với volume 2000 USDT, đòn bẩy 5x, và giá BTC là 100,000 USDT:

1. Volume đầu vào: 2000 USDT
2. Gi<PERSON> trị danh nghĩa (Notional Value): 2000 USDT
3. Margin cần thiết: 2000 USDT / 5 = 400 USDT
4. Volume thực tế (chuyển đổi sang BTC): 2000 USDT / 100,000 USDT/BTC = 0.02 BTC

## Quy trình xử lý

1. Người dùng nhập volume theo đơn vị USDT
2. <PERSON><PERSON> thống tính toán margin cần thiết bằng cách chia volume cho đòn bẩy
3. Hệ thống chuyển đổi volume từ USDT sang BTC bằng cách chia volume cho giá
4. Hệ thống lưu trữ volume theo đơn vị BTC trong cơ sở dữ liệu
5. Khi hiển thị thông tin lệnh cho người dùng, hệ thống chuyển đổi volume từ BTC sang USDT bằng cách nhân volume với giá

## Lợi ích

1. Người dùng có thể dễ dàng tính toán số tiền cần thiết để đặt lệnh
2. Margin cần thiết được tính toán trực tiếp từ volume đầu vào
3. Phù hợp với cách tính toán của các sàn giao dịch lớn như Binance

## Lưu ý

1. Volume trong cơ sở dữ liệu vẫn được lưu trữ theo đơn vị BTC để đảm bảo tính nhất quán với các phần khác của hệ thống
2. Khi hủy lệnh, hệ thống cần chuyển đổi volume từ BTC sang USDT để tính toán margin cần giải phóng
3. Khi tính toán lợi nhuận, hệ thống vẫn sử dụng volume theo đơn vị BTC

## Ví dụ chi tiết

### Đặt lệnh mua

- Người dùng đặt lệnh mua với volume 2000 USDT, đòn bẩy 5x, giá BTC là 100,000 USDT
- Margin cần thiết: 2000 USDT / 5 = 400 USDT
- Volume lưu trữ: 2000 USDT / 100,000 USDT/BTC = 0.02 BTC

### Đặt lệnh bán

- Người dùng đặt lệnh bán với volume 2000 USDT, đòn bẩy 5x, giá BTC là 100,000 USDT
- Margin cần thiết: 2000 USDT / 5 = 400 USDT
- Volume lưu trữ: 2000 USDT / 100,000 USDT/BTC = 0.02 BTC

### Hủy lệnh

- Người dùng hủy lệnh mua với volume còn lại 0.02 BTC, giá BTC là 100,000 USDT
- Volume chuyển đổi: 0.02 BTC * 100,000 USDT/BTC = 2000 USDT
- Margin giải phóng: 2000 USDT / 5 = 400 USDT
