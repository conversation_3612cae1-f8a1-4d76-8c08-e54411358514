# Tài liệu API Future Core

## Giới thiệu

Thư mục này chứa tài liệu API cho module Future Core. Tài liệu này mô tả các API endpoint có sẵn trong module, bao gồm:
- URL endpoint
- Method (GET, POST, PUT, DELETE)
- Path parameters
- Query parameters
- Request body
- Response

## Cấu trúc tài liệu

Tài liệu API được tổ chức theo các nhóm chức năng:

1. **Quản lý Hợ<PERSON> đồng (Contract)**: Các API để quản lý hợp đồng tương lai
2. **Quản lý Giao dịch (Trading)**: Các API để đặt lệnh, hủy lệnh, và quản lý lệnh
3. **Quản lý Vị thế (Position)**: Các API để quản lý vị thế
4. **Quản lý Ví (Wallet)**: Các API để quản lý ví
5. **Quản lý Giao dịch (Transaction)**: Các API để quản lý giao dịch
6. **Quản lý Giá (Price)**: Các API để lấy thông tin giá
7. **Quản lý Funding Rate**: Các API để quản lý funding rate
8. **Quản lý Phí (Fee)**: Các API để quản lý phí
9. **Quản lý Thanh lý (Liquidation)**: Các API để quản lý thanh lý
10. **Quản lý Bảo hiểm (Insurance)**: Các API để quản lý quỹ bảo hiểm
11. **Quản lý Đòn bẩy (Leverage)**: Các API để quản lý đòn bẩy
12. **Quản lý Chế độ Vị thế (Position Mode)**: Các API để quản lý chế độ vị thế
13. **Quản lý Cấu hình Giá (Price Configuration)**: Các API để quản lý cấu hình giá
14. **Quản lý Orderbook**: Các API để lấy thông tin orderbook
15. **Quản lý K-line**: Các API để lấy thông tin K-line
16. **Quản lý Giao dịch (Trade)**: Các API để quản lý giao dịch
17. **Quản lý Circuit Breaker**: Các API để quản lý circuit breaker

## Cách sử dụng tài liệu

Tài liệu API được viết dưới dạng Markdown và có thể được đọc trực tiếp trong IDE hoặc được chuyển đổi sang HTML để xem trong trình duyệt.

Mỗi API endpoint được mô tả với các thông tin sau:
- URL endpoint
- Method (GET, POST, PUT, DELETE)
- Path parameters (nếu có)
- Query parameters (nếu có)
- Request body (nếu có)
- Response

Ví dụ:

```
#### 1.1. Lấy danh sách tất cả hợp đồng

- **URL**: `/api/v1/contracts`
- **Method**: GET
- **Description**: Lấy danh sách tất cả hợp đồng
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1,
        "symbol": "BTCUSDT",
        "baseSymbol": "BTC",
        "quoteSymbol": "USDT",
        "enable": 1,
        "sort": 1,
        "fee": 0.0004,
        "leverageMin": 1,
        "leverageMax": 125,
        "minVolume": 0.001,
        "maxVolume": 100,
        "priceScale": 1,
        "volumeScale": 3,
        "marginMode": "CROSSED",
        "maintenanceMarginRate": 0.005,
        "initialMarginRate": 0.01
      },
      // ...
    ],
    "timestamp": "2023-05-01T12:00:00"
  }
  ```
```

## Cấu trúc chung của Response

Tất cả các API đều trả về một cấu trúc response chung như sau:

```json
{
  "code": 200,
  "message": "Success",
  "data": { ... },
  "timestamp": "2023-05-01T12:00:00"
}
```

Trong đó:
- `code`: Mã trạng thái HTTP
- `message`: Thông báo
- `data`: Dữ liệu trả về (tùy thuộc vào API)
- `timestamp`: Thời gian phản hồi

## Mã lỗi

Dưới đây là danh sách các mã lỗi có thể được trả về từ API:

| Mã lỗi | Mô tả |
|--------|-------|
| 200 | Thành công |
| 400 | Yêu cầu không hợp lệ |
| 401 | Không được xác thực |
| 403 | Không có quyền truy cập |
| 404 | Không tìm thấy tài nguyên |
| 409 | Xung đột |
| 429 | Quá nhiều yêu cầu |
| 500 | Lỗi máy chủ nội bộ |

## Cập nhật tài liệu

Tài liệu API nên được cập nhật mỗi khi có thay đổi trong API. Điều này bao gồm:
- Thêm API endpoint mới
- Thay đổi URL, method, parameters, hoặc response của API endpoint hiện có
- Xóa API endpoint

## Liên hệ

Nếu bạn có bất kỳ câu hỏi hoặc gặp vấn đề nào, vui lòng liên hệ với đội phát triển.
