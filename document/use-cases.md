# Use Cases trong Future-Core Module

## Giới thiệu

Tài liệu này mô tả các use cases trong Future-Core Module. Use cases là các hành động mà người dùng có thể thực hiện trong hệ thống, và chúng được triển khai trong application layer của clean architecture.

## Tình trạng chuyển đổi

Future-Core Module đang trong quá trình chuyển đổi sang clean architecture. Một số use cases đã được chuyển đổi hoàn toàn, trong khi một số khác vẫn đang sử dụng cấu trúc cũ.

### Đã chuyển đổi

Các use cases sau đã được chuyển đổi sang clean architecture:

- `PlaceOrderUseCase`: Đã được chuyển đổi thành use case interface trong package `com.icetea.lotus.application.port.input`.
- `ManagePositionUseCase`: Đ<PERSON> được chuyển đổi thành use case interface trong package `com.icetea.lotus.application.port.input`.
- `ManagePriceUseCase`: Đã được chuyển đổi thành use case interface trong package `com.icetea.lotus.application.port.input`.
- `ManageFundingUseCase`: Đã được chuyển đổi thành use case interface trong package `com.icetea.lotus.application.port.input`.
- `ManageLiquidationUseCase`: Đã được chuyển đổi thành use case interface trong package `com.icetea.lotus.application.port.input`.

### Chưa chuyển đổi

Các use cases sau vẫn chưa được chuyển đổi sang clean architecture:

- Các service trong package `com.icetea.lotus.service` chưa được chuyển đổi sang use cases.

## PlaceOrderUseCase

Use case này cho phép người dùng đặt lệnh mới, hủy lệnh, và lấy thông tin lệnh.

### Phương thức

1. **placeOrder(PlaceOrderCommand command)**: Đặt lệnh mới.
   - Input: `PlaceOrderCommand` chứa thông tin lệnh (memberId, symbol, direction, type, price, volume, v.v.).
   - Output: `PlaceOrderResult` chứa kết quả đặt lệnh (orderId, success, message, order).
   - Mô tả: Tạo lệnh mới và lưu vào database.

2. **cancelOrder(String orderId)**: Hủy lệnh.
   - Input: `orderId` là ID của lệnh cần hủy.
   - Output: `boolean` cho biết lệnh có được hủy thành công hay không.
   - Mô tả: Tìm lệnh theo orderId, kiểm tra xem lệnh có thể hủy không, và cập nhật trạng thái lệnh thành CANCELED.

3. **cancelAllOrders(Long memberId)**: Hủy tất cả các lệnh của một thành viên.
   - Input: `memberId` là ID của thành viên.
   - Output: `int` là số lượng lệnh đã hủy.
   - Mô tả: Tìm tất cả các lệnh của thành viên và hủy chúng.

4. **cancelAllOrders(Long memberId, String symbol)**: Hủy tất cả các lệnh của một thành viên cho một symbol.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `int` là số lượng lệnh đã hủy.
   - Mô tả: Tìm tất cả các lệnh của thành viên cho symbol và hủy chúng.

5. **getOrder(String orderId)**: Lấy thông tin lệnh.
   - Input: `orderId` là ID của lệnh.
   - Output: `OrderDto` chứa thông tin lệnh.
   - Mô tả: Tìm lệnh theo orderId và trả về thông tin lệnh.

## ManagePositionUseCase

Use case này cho phép người dùng quản lý vị thế, bao gồm lấy thông tin vị thế, đóng vị thế, điều chỉnh đòn bẩy, v.v.

### Phương thức

1. **getPosition(Long memberId, String symbol)**: Lấy vị thế theo memberId và symbol.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `PositionDto` chứa thông tin vị thế.
   - Mô tả: Tìm vị thế theo memberId và symbol, tính toán lợi nhuận không thực hiện và tỷ lệ lợi nhuận, và trả về thông tin vị thế.

2. **getAllPositions(Long memberId)**: Lấy tất cả các vị thế của một thành viên.
   - Input: `memberId` là ID của thành viên.
   - Output: `List<PositionDto>` là danh sách các vị thế.
   - Mô tả: Tìm tất cả các vị thế của thành viên, tính toán lợi nhuận không thực hiện và tỷ lệ lợi nhuận cho mỗi vị thế, và trả về danh sách các vị thế.

3. **closePosition(ClosePositionCommand command)**: Đóng vị thế.
   - Input: `ClosePositionCommand` chứa thông tin đóng vị thế (memberId, symbol, volume, price).
   - Output: `PositionDto` chứa thông tin vị thế sau khi đóng.
   - Mô tả: Tìm vị thế theo memberId và symbol, đóng vị thế với khối lượng và giá được chỉ định, và trả về thông tin vị thế sau khi đóng.

4. **closeAllPositions(Long memberId)**: Đóng tất cả các vị thế của một thành viên.
   - Input: `memberId` là ID của thành viên.
   - Output: `int` là số lượng vị thế đã đóng.
   - Mô tả: Tìm tất cả các vị thế của thành viên và đóng chúng.

5. **adjustLeverage(AdjustLeverageCommand command)**: Điều chỉnh đòn bẩy cho vị thế.
   - Input: `AdjustLeverageCommand` chứa thông tin điều chỉnh đòn bẩy (memberId, symbol, leverage).
   - Output: `PositionDto` chứa thông tin vị thế sau khi điều chỉnh.
   - Mô tả: Tìm vị thế theo memberId và symbol, điều chỉnh đòn bẩy, và trả về thông tin vị thế sau khi điều chỉnh.

6. **adjustMargin(AdjustMarginCommand command)**: Điều chỉnh ký quỹ cho vị thế.
   - Input: `AdjustMarginCommand` chứa thông tin điều chỉnh ký quỹ (memberId, symbol, margin, isAdd).
   - Output: `PositionDto` chứa thông tin vị thế sau khi điều chỉnh.
   - Mô tả: Tìm vị thế theo memberId và symbol, điều chỉnh ký quỹ, và trả về thông tin vị thế sau khi điều chỉnh.

7. **changeMarginMode(Long memberId, String symbol, MarginMode marginMode)**: Thay đổi chế độ ký quỹ cho vị thế.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng, `marginMode` là chế độ ký quỹ mới.
   - Output: `PositionDto` chứa thông tin vị thế sau khi thay đổi.
   - Mô tả: Tìm vị thế theo memberId và symbol, thay đổi chế độ ký quỹ, và trả về thông tin vị thế sau khi thay đổi.

8. **calculateUnrealizedProfit(Long memberId, String symbol)**: Tính toán lợi nhuận không thực hiện cho vị thế.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `BigDecimal` là lợi nhuận không thực hiện.
   - Mô tả: Tìm vị thế theo memberId và symbol, tính toán lợi nhuận không thực hiện, và trả về lợi nhuận không thực hiện.

9. **calculateTotalUnrealizedProfit(Long memberId)**: Tính toán tổng lợi nhuận không thực hiện cho tất cả các vị thế của một thành viên.
   - Input: `memberId` là ID của thành viên.
   - Output: `BigDecimal` là tổng lợi nhuận không thực hiện.
   - Mô tả: Tìm tất cả các vị thế của thành viên, tính toán lợi nhuận không thực hiện cho mỗi vị thế, và trả về tổng lợi nhuận không thực hiện.

## ManageFundingUseCase

Use case này cho phép người dùng quản lý tài trợ, bao gồm lấy tỷ lệ tài trợ, tính toán tỷ lệ tài trợ mới, thanh toán tài trợ, v.v.

### Phương thức

1. **getCurrentFundingRate(String symbol)**: Lấy tỷ lệ tài trợ hiện tại cho một symbol.
   - Input: `symbol` là symbol của hợp đồng.
   - Output: `FundingRateDto` chứa thông tin tỷ lệ tài trợ.
   - Mô tả: Tìm tỷ lệ tài trợ mới nhất cho symbol và trả về thông tin tỷ lệ tài trợ.

2. **getFundingRateHistory(String symbol, LocalDateTime startTime, LocalDateTime endTime)**: Lấy lịch sử tỷ lệ tài trợ cho một symbol.
   - Input: `symbol` là symbol của hợp đồng, `startTime` là thời gian bắt đầu, `endTime` là thời gian kết thúc.
   - Output: `List<FundingRateDto>` là danh sách các tỷ lệ tài trợ.
   - Mô tả: Tìm tất cả các tỷ lệ tài trợ cho symbol trong khoảng thời gian và trả về danh sách các tỷ lệ tài trợ.

3. **calculateFundingRate(String symbol)**: Tính toán tỷ lệ tài trợ mới.
   - Input: `symbol` là symbol của hợp đồng.
   - Output: `FundingRateDto` chứa thông tin tỷ lệ tài trợ mới.
   - Mô tả: Tính toán tỷ lệ tài trợ mới dựa trên giá chỉ số và giá đánh dấu, lưu tỷ lệ tài trợ mới, và trả về thông tin tỷ lệ tài trợ mới.

4. **settleFunding(FundingSettlementCommand command)**: Thực hiện thanh toán tài trợ.
   - Input: `FundingSettlementCommand` chứa thông tin thanh toán tài trợ (symbol, settlementTime).
   - Output: `FundingSettlementResult` chứa kết quả thanh toán tài trợ.
   - Mô tả: Tìm tỷ lệ tài trợ hiện tại, tìm tất cả các vị thế cho symbol, tính toán số tiền tài trợ cho mỗi vị thế, và trả về kết quả thanh toán tài trợ.

5. **calculateFundingAmount(Long memberId, String symbol)**: Tính toán số tiền tài trợ cho một vị thế.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `BigDecimal` là số tiền tài trợ.
   - Mô tả: Tìm vị thế theo memberId và symbol, tìm tỷ lệ tài trợ hiện tại, tính toán số tiền tài trợ, và trả về số tiền tài trợ.

6. **calculateTotalFundingAmount(Long memberId)**: Tính toán tổng số tiền tài trợ cho tất cả các vị thế của một thành viên.
   - Input: `memberId` là ID của thành viên.
   - Output: `BigDecimal` là tổng số tiền tài trợ.
   - Mô tả: Tìm tất cả các vị thế của thành viên, tính toán số tiền tài trợ cho mỗi vị thế, và trả về tổng số tiền tài trợ.

7. **getNextFundingTime(String symbol)**: Lấy thời gian tài trợ tiếp theo.
   - Input: `symbol` là symbol của hợp đồng.
   - Output: `LocalDateTime` là thời gian tài trợ tiếp theo.
   - Mô tả: Tìm tỷ lệ tài trợ hiện tại và trả về thời gian tài trợ tiếp theo.

## ManagePriceUseCase

Use case này cho phép người dùng quản lý giá, bao gồm lấy giá chỉ số, giá đánh dấu, cập nhật giá, tính toán giá, v.v.

### Phương thức

1. **getCurrentIndexPrice(String symbol)**: Lấy giá chỉ số hiện tại cho một symbol.
   - Input: `symbol` là symbol của hợp đồng.
   - Output: `IndexPriceDto` chứa thông tin giá chỉ số.
   - Mô tả: Lấy giá chỉ số hiện tại từ IndexPriceService và trả về thông tin giá chỉ số.

2. **getCurrentMarkPrice(String symbol)**: Lấy giá đánh dấu hiện tại cho một symbol.
   - Input: `symbol` là symbol của hợp đồng.
   - Output: `MarkPriceDto` chứa thông tin giá đánh dấu.
   - Mô tả: Lấy giá đánh dấu hiện tại từ MarkPriceService và trả về thông tin giá đánh dấu.

3. **updateIndexPrice(UpdateIndexPriceCommand command)**: Cập nhật giá chỉ số cho một symbol.
   - Input: `command` chứa thông tin cập nhật giá chỉ số (symbol, price, updateTime).
   - Output: `IndexPriceDto` chứa thông tin giá chỉ số sau khi cập nhật.
   - Mô tả: Cập nhật giá chỉ số cho symbol và trả về thông tin giá chỉ số sau khi cập nhật.

4. **updateMarkPrice(UpdateMarkPriceCommand command)**: Cập nhật giá đánh dấu cho một symbol.
   - Input: `command` chứa thông tin cập nhật giá đánh dấu (symbol, price, updateTime).
   - Output: `MarkPriceDto` chứa thông tin giá đánh dấu sau khi cập nhật.
   - Mô tả: Cập nhật giá đánh dấu cho symbol và trả về thông tin giá đánh dấu sau khi cập nhật.

5. **calculateIndexPrice(String symbol, Map<String, Double> exchangePrices)**: Tính toán giá chỉ số cho một symbol.
   - Input: `symbol` là symbol của hợp đồng, `exchangePrices` là danh sách giá từ các sàn giao dịch.
   - Output: `IndexPriceDto` chứa thông tin giá chỉ số.
   - Mô tả: Tính toán giá chỉ số từ các giá từ các sàn giao dịch, cập nhật giá chỉ số, và trả về thông tin giá chỉ số.

6. **calculateMarkPrice(String symbol)**: Tính toán giá đánh dấu cho một symbol.
   - Input: `symbol` là symbol của hợp đồng.
   - Output: `MarkPriceDto` chứa thông tin giá đánh dấu.
   - Mô tả: Lấy giá chỉ số hiện tại và tỷ lệ tài trợ, tính toán giá đánh dấu, cập nhật giá đánh dấu, và trả về thông tin giá đánh dấu.

7. **getIndexPriceHistory(String symbol, LocalDateTime startTime, LocalDateTime endTime, int interval)**: Lấy lịch sử giá chỉ số cho một symbol.
   - Input: `symbol` là symbol của hợp đồng, `startTime` là thời gian bắt đầu, `endTime` là thời gian kết thúc, `interval` là khoảng thời gian (phút).
   - Output: `List<PriceHistoryDto>` là danh sách giá chỉ số.
   - Mô tả: Lấy lịch sử giá chỉ số từ IndexPriceService và trả về danh sách giá chỉ số.

8. **getMarkPriceHistory(String symbol, LocalDateTime startTime, LocalDateTime endTime, int interval)**: Lấy lịch sử giá đánh dấu cho một symbol.
   - Input: `symbol` là symbol của hợp đồng, `startTime` là thời gian bắt đầu, `endTime` là thời gian kết thúc, `interval` là khoảng thời gian (phút).
   - Output: `List<PriceHistoryDto>` là danh sách giá đánh dấu.
   - Mô tả: Lấy lịch sử giá đánh dấu từ MarkPriceService và trả về danh sách giá đánh dấu.

## ManageLiquidationUseCase

Use case này cho phép người dùng quản lý thanh lý, bao gồm kiểm tra vị thế cần thanh lý, tính toán giá thanh lý, thanh lý vị thế, v.v.

### Phương thức

1. **needsLiquidation(Long memberId, String symbol)**: Kiểm tra xem vị thế có cần thanh lý không.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `boolean` cho biết vị thế có cần thanh lý hay không.
   - Mô tả: Tìm vị thế theo memberId và symbol, kiểm tra xem vị thế có cần thanh lý không, và trả về kết quả.

2. **calculateLiquidationPrice(Long memberId, String symbol)**: Tính toán giá thanh lý cho vị thế.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `BigDecimal` là giá thanh lý.
   - Mô tả: Tìm vị thế theo memberId và symbol, tính toán giá thanh lý, và trả về giá thanh lý.

3. **findPositionsToLiquidate()**: Tìm các vị thế cần thanh lý.
   - Input: Không có.
   - Output: `List<PositionDto>` là danh sách các vị thế cần thanh lý.
   - Mô tả: Tìm tất cả các vị thế, kiểm tra xem vị thế có cần thanh lý không, và trả về danh sách các vị thế cần thanh lý.

4. **liquidatePosition(LiquidationCommand command)**: Thanh lý vị thế.
   - Input: `LiquidationCommand` chứa thông tin thanh lý (memberId, symbol, liquidationPrice).
   - Output: `LiquidationResult` chứa kết quả thanh lý.
   - Mô tả: Tìm vị thế theo memberId và symbol, tính toán phí thanh lý và số tiền bảo hiểm cần sử dụng, thanh lý vị thế, và trả về kết quả thanh lý.

5. **calculateLiquidationFee(Long memberId, String symbol)**: Tính toán phí thanh lý.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `BigDecimal` là phí thanh lý.
   - Mô tả: Tìm vị thế theo memberId và symbol, tính toán phí thanh lý, và trả về phí thanh lý.

6. **calculateInsuranceFundAmount(Long memberId, String symbol)**: Tính toán số tiền bảo hiểm cần sử dụng.
   - Input: `memberId` là ID của thành viên, `symbol` là symbol của hợp đồng.
   - Output: `BigDecimal` là số tiền bảo hiểm cần sử dụng.
   - Mô tả: Tìm vị thế theo memberId và symbol, tính toán số tiền bảo hiểm cần sử dụng, và trả về số tiền bảo hiểm cần sử dụng.
