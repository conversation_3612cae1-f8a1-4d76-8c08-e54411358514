# Luồng Khớp Lệnh Giao Dịch Hợp Đồng Tương Lai

Tài liệu này mô tả các luồng khớp lệnh giao dịch hợp đồng tương lai trong hệ thống, bao gồm các ví dụ cụ thể về cách thực hiện các request API để tạo và khớp lệnh giữa các thành viên.

## 1. Tổng quan về luồng khớp lệnh

Luồng khớp lệnh trong hệ thống giao dịch hợp đồng tương lai bao gồm các bước sau:

1. Th<PERSON><PERSON> viên đặt lệnh mua hoặc bán thông qua API
2. Hệ thống xác thực lệnh và đưa vào sổ lệnh
3. Matching engine tìm kiếm các lệnh phù hợp để khớp
4. <PERSON><PERSON> có lệnh khớp, hệ thống tạo giao dịch và cập nhật vị thế của các thành viên
5. Th<PERSON>ng báo kết quả khớp lệnh cho các thành viên

## 2. Các loại lệnh hỗ trợ

Hệ thống hỗ trợ các loại lệnh sau:

- **LIMIT**: Lệnh giới hạn, chỉ được khớp ở mức giá đặt hoặc tốt hơn
- **MARKET**: Lệnh thị trường, được khớp ngay lập tức với giá tốt nhất hiện có
- **STOP**: Lệnh dừng, chỉ được kích hoạt khi giá đạt đến mức giá kích hoạt
- **STOP_LIMIT**: Lệnh dừng giới hạn, khi được kích hoạt sẽ trở thành lệnh giới hạn
- **TRAILING_STOP**: Lệnh dừng trượt, mức giá kích hoạt thay đổi theo giá thị trường

## 3. Ví dụ luồng khớp lệnh hoàn chỉnh

Dưới đây là ví dụ về luồng khớp lệnh hoàn chỉnh giữa hai thành viên với memberId 100 và 101 cho cặp tiền BTC-USDT.

### 3.1. Khớp lệnh giới hạn (LIMIT)

#### 3.1.1. Đặt lệnh mua (BUY) cho thành viên 100

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 100,
    "symbol": "BTC-USDT",
    "direction": "BUY",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": false,
    "timeInForce": "GTC"
}'
```

#### 3.1.2. Đặt lệnh bán (SELL) cho thành viên 101 với giá tương ứng để khớp lệnh

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 101,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": false,
    "timeInForce": "GTC"
}'
```

#### 3.1.3. Kiểm tra trạng thái lệnh của thành viên 100

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders/member/100/symbol/BTC-USDT/active'
```

#### 3.1.4. Kiểm tra trạng thái lệnh của thành viên 101

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders/member/101/symbol/BTC-USDT/active'
```

#### 3.1.5. Kiểm tra lịch sử giao dịch của thành viên 100

```bash
curl --location 'http://localhost:8080/api/v1/trading/trades/member/100/symbol/BTC-USDT'
```

#### 3.1.6. Kiểm tra lịch sử giao dịch của thành viên 101

```bash
curl --location 'http://localhost:8080/api/v1/trading/trades/member/101/symbol/BTC-USDT'
```

### 3.2. Khớp lệnh thị trường (MARKET)

#### 3.2.1. Đặt lệnh giới hạn (LIMIT) cho thành viên 100

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 100,
    "symbol": "BTC-USDT",
    "direction": "BUY",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": false,
    "timeInForce": "GTC"
}'
```

#### 3.2.2. Đặt lệnh thị trường (MARKET) cho thành viên 101 để khớp ngay lập tức

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 101,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "MARKET",
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": false
}'
```

## 4. Luồng khớp lệnh với Stop Loss và Take Profit

### 4.1. Đặt lệnh mua (BUY) cho thành viên 100 với Stop Loss và Take Profit

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 100,
    "symbol": "BTC-USDT",
    "direction": "BUY",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": false,
    "timeInForce": "GTC",
    "takeProfitSettings": {
        "price": 55000.00,
        "type": "LIMIT"
    },
    "stopLossSettings": {
        "price": 48000.00,
        "type": "MARKET"
    }
}'
```

### 4.2. Đặt lệnh bán (SELL) cho thành viên 101 để khớp lệnh

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 101,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "leverage": 10,
    "reduceOnly": false,
    "timeInForce": "GTC"
}'
```

### 4.3. Cập nhật giá đánh dấu để kích hoạt Take Profit

```bash
curl --location 'http://localhost:8080/api/v1/trading-management/update-mark-price' \
--header 'Content-Type: application/json' \
--data '{
    "symbol": "BTC-USDT",
    "price": 55000.00
}'
```

### 4.4. Kiểm tra lệnh chờ để kích hoạt Take Profit

```bash
curl --location 'http://localhost:8080/api/v1/trading-management/check-trigger-orders/BTC-USDT'
```

## 5. Luồng khớp lệnh với đòn bẩy khác nhau

### 5.1. Đặt lệnh mua (BUY) cho thành viên 100 với đòn bẩy 5x

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 100,
    "symbol": "BTC-USDT",
    "direction": "BUY",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "leverage": 5,
    "reduceOnly": false,
    "timeInForce": "GTC"
}'
```

### 5.2. Đặt lệnh bán (SELL) cho thành viên 101 với đòn bẩy 20x

```bash
curl --location 'http://localhost:8080/api/v1/trading/orders' \
--header 'Content-Type: application/json' \
--data '{
    "memberId": 101,
    "symbol": "BTC-USDT",
    "direction": "SELL",
    "type": "LIMIT",
    "price": 50000.00,
    "volume": 0.1,
    "leverage": 20,
    "reduceOnly": false,
    "timeInForce": "GTC"
}'
```

## 6. Lưu ý quan trọng

1. **Địa chỉ API**: Thay `http://localhost:8080` bằng địa chỉ thực tế của API nếu khác.

2. **Giá và khối lượng**: Điều chỉnh giá và khối lượng phù hợp với cấu hình của hệ thống.

3. **Đòn bẩy**: Đảm bảo đòn bẩy nằm trong phạm vi cho phép của hệ thống.

4. **Thứ tự thực hiện**: Thực hiện các request theo đúng thứ tự để đảm bảo khớp lệnh thành công.

5. **Kiểm tra kết quả**: Sau mỗi request, kiểm tra kết quả trả về để đảm bảo lệnh được xử lý thành công.

6. **Xử lý lỗi**: Nếu có lỗi xảy ra, kiểm tra thông báo lỗi và điều chỉnh request phù hợp.
