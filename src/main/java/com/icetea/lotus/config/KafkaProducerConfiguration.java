package com.icetea.lotus.config;

import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * Kafka Producer Configuration
 * This class uses Spring Boot's auto-configuration for Kafka
 * and leverages properties defined in application.yml
 */
@Configuration
@EnableKafka
public class KafkaProducerConfiguration {

    /**
     * Creates a KafkaTemplate bean for String key and value serializers
     * This is needed for backward compatibility with existing code
     */
    @Bean
    public KafkaTemplate<String, String> kafkaTemplate(KafkaProperties kafkaProperties) {
        return new KafkaTemplate<>(producerFactory(kafkaProperties));
    }

    /**
     * Creates a ProducerFactory with String serializers for both key and value
     */
    private ProducerFactory<String, String> producerFactory(KafkaProperties kafkaProperties) {
        // Start with the properties from application.yml
        var props = kafkaProperties.buildProducerProperties();

        // Override serializers to ensure String serializers are used
        // This maintains compatibility with existing code
        props.put("key.serializer", StringSerializer.class.getName());
        props.put("value.serializer", StringSerializer.class.getName());

        return new DefaultKafkaProducerFactory<>(props);
    }
}
