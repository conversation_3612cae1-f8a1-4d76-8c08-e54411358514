package com.icetea.lotus.model.exchange;

import lombok.Data;

import java.math.BigDecimal;
/**
 * Here, modification requires modification of the robot trading project RobotParams
 *
 */
@Data
public class RobotParams {
    private String coinName = ""; // Such as btcusdt
    private boolean isHalt = true; // Whether to pause status
    private double startAmount = 0.001; // Minimum transaction volume
    private double randRange0 = 20; // Range of random number of transaction volume: 1% probability
    private double randRange1 = 4; // Range of random number of transaction volume 9% probability
    private double randRange2 = 1; // Random number range of transaction volume: 0.1 (0.0001 ~ 0.09) 20% probability
    private double randRange3 = 0.1; // Random number range of transaction volume: 0.1 (0.0001 ~ 0.09) 20% probability
    private double randRange4 = 0.01; // Random number range of transaction volume: 0.1 (0.0001 ~ 0.09) 20% probability
    private double randRange5 = 0.001; // Random number range of transaction volume: 0.1 (0.0001 ~ 0.09) 20% probability
    private double randRange6 = 0.0001; // Random number range of transaction volume: 0.1 (0.0001 ~ 0.09) 10% probability
    private int scale = 4;// Price accuracy requirements
    private int amountScale = 6; // Quantity accuracy requirements
    private BigDecimal maxSubPrice = new BigDecimal(20); // The difference between the highest bid price and the lowest bid price of the sell order is more than US$20
    private int initOrderCount = 30; // Initial order quantity (this number must be greater than 24)
    private BigDecimal priceStepRate = new BigDecimal(0.003); // Price change step length (0.01 = 1%)
    private int runTime = 1000; // Quotation request interval time (5000 = 5 seconds)

    private int robotType = 0; // robot类型
    private int strategyType = 1; // Disk control robot策略（1：跟随，2：自定义）
    private String flowPair = "BTC/USDT"; // Follow the trading pair
    private BigDecimal flowPercent = BigDecimal.valueOf(1); // Follow the ratio

}
