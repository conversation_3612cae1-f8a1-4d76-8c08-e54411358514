package com.icetea.lotus.model.vo;

import com.icetea.lotus.annotation.Excel;
import com.icetea.lotus.annotation.ExcelSheet;
import lombok.Data;

@Data
@ExcelSheet(name = "Withdraw_Excel_VO")
public class WithdrawExcelVO {
    @Excel(name = "User ID")
    private Long memberId;
    @Excel(name = "email")
    private String email;
    @Excel(name = "Mobile phone number")
    private String mobilePhone;
    @Excel(name = "withdrawal currency")
    private String coinname;

    @Excel(name = "Protocol Name")
    private String protocolname;

    @Excel(name = "Registration Address")
    private String address;

    @Excel(name = "Quantity of withdrawal")
    private String money;

    @Excel(name = "processing fee")
    private String fee;

    @Excel(name = "Number of Accounts")
    private String real_money;

    @Excel(name = "withdrawal time")
    private String addtime;

    @Excel(name = "Audit time")
    private String processtime;

    @Excel(name = "Hash")
    private String hash;

    @Excel(name = "status")
    private String status;

}