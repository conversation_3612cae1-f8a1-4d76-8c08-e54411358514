package com.icetea.lotus.event;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constant.PromotionRewardType;
import com.icetea.lotus.constant.RewardRecordType;
import com.icetea.lotus.dao.MemberDao;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.Order;
import com.icetea.lotus.entity.spot.RewardPromotionSetting;
import com.icetea.lotus.entity.spot.RewardRecord;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.RewardPromotionSettingService;
import com.icetea.lotus.service.RewardRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;

import static com.icetea.lotus.util.BigDecimalUtils.add;
import static com.icetea.lotus.util.BigDecimalUtils.getRate;
import static com.icetea.lotus.util.BigDecimalUtils.mulRound;

/**
 * <AUTHOR> @date January 22, 2020
 */
@Service
public class OrderEvent {
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private MemberDao memberDao;
    @Autowired
    private MemberWalletService memberWalletService;
    @Autowired
    private RewardRecordService rewardRecordService;
    @Autowired
    private RewardPromotionSettingService rewardPromotionSettingService;

    public void onOrderCompleted(Order order) {
        Member member = memberDao.findById(order.getMemberId()).orElseThrow();
        member.setTransactions(member.getTransactions() + 1);
        Member member1 = memberDao.findMemberById(order.getCustomerId());
        member1.setTransactions(member1.getTransactions() + 1);
        RewardPromotionSetting rewardPromotionSetting = rewardPromotionSettingService.findByType(PromotionRewardType.TRANSACTION);
        if (rewardPromotionSetting != null) {
            Member[] array = {member, member1};
            Arrays.stream(array).forEach(
                    x -> {
                        if (x.getTransactions() == 1 && x.getInviterId() != null) {
                            Member member2 = memberDao.findMemberById(x.getInviterId());
                            MemberWallet memberWallet1 = memberWalletService.findByCoinAndMember(rewardPromotionSetting.getCoin(), member2);
                            JsonNode jsonNode;
                            try {
                                jsonNode = objectMapper.readTree(rewardPromotionSetting.getInfo());
                            } catch (JsonProcessingException e) {
                                throw new RuntimeException(e);
                            }
                            BigDecimal one = jsonNode.get("one").decimalValue();

                            BigDecimal amount1 = mulRound(order.getNumber(), getRate(one));
                            memberWallet1.setBalance(add(memberWallet1.getBalance(), amount1));
                            memberWalletService.save(memberWallet1);
                            RewardRecord rewardRecord1 = new RewardRecord();
                            rewardRecord1.setAmount(amount1);
                            rewardRecord1.setCoin(rewardPromotionSetting.getCoin());
                            rewardRecord1.setMember(member2);
                            rewardRecord1.setRemark(rewardPromotionSetting.getType().getName());
                            rewardRecord1.setType(RewardRecordType.PROMOTION);
                            rewardRecordService.save(rewardRecord1);
                            if (member2.getInviterId() != null) {
                                Member member3 = memberDao.findOneByInviterId(member2.getInviterId());
                                MemberWallet memberWallet2 = memberWalletService.findByCoinAndMember(rewardPromotionSetting.getCoin(), member3);
                                JsonNode jsonNodeTwo;
                                try {
                                    jsonNodeTwo = objectMapper.readTree(rewardPromotionSetting.getInfo());
                                } catch (JsonProcessingException e) {
                                    throw new RuntimeException(e);
                                }
                                BigDecimal two = jsonNodeTwo.get("two").decimalValue();
                                BigDecimal amount2 = mulRound(order.getNumber(), getRate(two));
                                memberWallet2.setBalance(add(memberWallet2.getBalance(), amount2));
                                RewardRecord rewardRecord2 = new RewardRecord();
                                rewardRecord2.setAmount(amount2);
                                rewardRecord2.setCoin(rewardPromotionSetting.getCoin());
                                rewardRecord2.setMember(member3);
                                rewardRecord2.setRemark(rewardPromotionSetting.getType().getName());
                                rewardRecord2.setType(RewardRecordType.PROMOTION);
                                rewardRecordService.save(rewardRecord2);
                            }
                        }
                    }
            );
        }
    }
}
