package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.FundingRatePersistencePort;
import com.icetea.lotus.core.domain.entity.FundingRate;
import com.icetea.lotus.core.domain.valueobject.FundingRateId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.FundingRateJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.FundingRatePersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.FundingRateJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho FundingRatePersistencePort
 * Triển khai các phương thức của FundingRatePersistencePort
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Transactional
public class FundingRatePersistenceAdapter implements FundingRatePersistencePort {

    private final FundingRateJpaRepository fundingRateJpaRepository;
    private final FundingRatePersistenceMapper fundingRatePersistenceMapper;

    /**
     * Tìm tỷ lệ tài trợ theo id với xử lý ngoại lệ và thử lại
     * @param id ID của tỷ lệ tài trợ
     * @return Optional chứa tỷ lệ tài trợ nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<FundingRate> findById(FundingRateId id) {
        try {
            log.debug("Tìm tỷ lệ tài trợ theo id, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            Optional<FundingRateJpaEntity> entity = fundingRateJpaRepository.findById(id.getValue());

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy tỷ lệ tài trợ, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy tỷ lệ tài trợ, id = {}", id.getValue());
            }

            return entity.map(fundingRatePersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tỷ lệ tài trợ theo id, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tỷ lệ tài trợ theo id, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm tỷ lệ tài trợ theo id: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tỷ lệ tài trợ theo id thất bại
     * @param e Ngoại lệ
     * @param id ID của tỷ lệ tài trợ
     * @return Optional<FundingRate>
     */
    @Recover
    public Optional<FundingRate> recoverFindById(Exception e, FundingRateId id) {
        log.error("Đã thử lại tìm tỷ lệ tài trợ theo id 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm tất cả các tỷ lệ tài trợ theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các tỷ lệ tài trợ
     */
    @Override
    public List<FundingRate> findAllBySymbol(Symbol symbol) {
        List<FundingRateJpaEntity> entities = fundingRateJpaRepository.findAllBySymbol(symbol.getValue());
        return entities.stream()
                .map(fundingRatePersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }

    /**
     * Tìm tỷ lệ tài trợ mới nhất theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa tỷ lệ tài trợ nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<FundingRate> findLatestBySymbol(Symbol symbol) {
        try {
            log.debug("Tìm tỷ lệ tài trợ mới nhất theo symbol, symbol = {}", symbol.getValue());

            if (symbol == null) {
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            // Sử dụng Pageable để lấy 1 bản ghi đầu tiên
            org.springframework.data.domain.Pageable pageable = org.springframework.data.domain.PageRequest.of(0, 1);
            List<FundingRateJpaEntity> entities = fundingRateJpaRepository.findLatestBySymbol(symbol.getValue(), pageable);

            if (entities.isEmpty()) {
                log.debug("Không tìm thấy tỷ lệ tài trợ mới nhất, symbol = {}", symbol.getValue());
                return Optional.empty();
            }

            log.debug("Đã tìm thấy tỷ lệ tài trợ mới nhất, symbol = {}, id = {}",
                    symbol.getValue(), entities.get(0).getId());

            return Optional.of(fundingRatePersistenceMapper.entityToDomain(entities.get(0)));
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tỷ lệ tài trợ mới nhất theo symbol, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tỷ lệ tài trợ mới nhất theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm tỷ lệ tài trợ mới nhất theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tỷ lệ tài trợ mới nhất theo symbol thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @return Optional<FundingRate>
     */
    @Recover
    public Optional<FundingRate> recoverFindLatestBySymbol(Exception e, Symbol symbol) {
        log.error("Đã thử lại tìm tỷ lệ tài trợ mới nhất theo symbol 3 lần nhưng thất bại, symbol = {}", symbol.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm tất cả các tỷ lệ tài trợ theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các tỷ lệ tài trợ
     */
    @Override
    public List<FundingRate> findAllBySymbolAndTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        List<FundingRateJpaEntity> entities = fundingRateJpaRepository.findAllBySymbolAndTimeBetween(symbol.getValue(), startTime, endTime);
        return entities.stream()
                .map(fundingRatePersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }

    /**
     * Lưu tỷ lệ tài trợ với xử lý ngoại lệ và thử lại
     * @param fundingRate Tỷ lệ tài trợ cần lưu
     * @return Tỷ lệ tài trợ đã được lưu
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public FundingRate save(FundingRate fundingRate) {
        try {
            log.debug("Lưu tỷ lệ tài trợ, id = {}, symbol = {}, rate = {}",
                    fundingRate.getId() != null ? fundingRate.getId().getValue() : "null",
                    fundingRate.getSymbol().getValue(),
                    fundingRate.getRate());

            if (fundingRate == null) {
                throw new IllegalArgumentException("FundingRate không được để trống");
            }

            FundingRateJpaEntity entity = fundingRatePersistenceMapper.domainToEntity(fundingRate);
            FundingRateJpaEntity savedEntity = fundingRateJpaRepository.save(entity);

            log.debug("Đã lưu tỷ lệ tài trợ thành công, id = {}", savedEntity.getId());

            return fundingRatePersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu tỷ lệ tài trợ, id = {}",
                    fundingRate.getId() != null ? fundingRate.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu tỷ lệ tài trợ, id = {}",
                    fundingRate.getId() != null ? fundingRate.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu tỷ lệ tài trợ: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu tỷ lệ tài trợ thất bại
     * @param e Ngoại lệ
     * @param fundingRate Tỷ lệ tài trợ cần lưu
     * @return FundingRate
     */
    @Recover
    public FundingRate recoverSave(Exception e, FundingRate fundingRate) {
        log.error("Đã thử lại lưu tỷ lệ tài trợ 3 lần nhưng thất bại, id = {}",
                fundingRate.getId() != null ? fundingRate.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu tỷ lệ tài trợ sau 3 lần thử lại: " + e.getMessage(), e);
    }
}
