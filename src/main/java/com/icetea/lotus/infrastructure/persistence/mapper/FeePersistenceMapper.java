package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Fee;
import com.icetea.lotus.core.domain.valueobject.FeeId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.FeeJpaEntity;
import org.springframework.stereotype.Component;

/**
 * Mapper cho Fee và FeeJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class FeePersistenceMapper {
    
    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Fee entityToDomain(FeeJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return Fee.builder()
                .id(FeeId.of(entity.getId()))
                .contractId(entity.getContractId())
                .memberId(entity.getMemberId())
                .symbol(Symbol.of(entity.getSymbol()))
                .orderId(OrderId.of(entity.getOrderId()))
                .direction(entity.getDirection())
                .volume(entity.getVolume())
                .price(Money.of(entity.getPrice()))
                .turnover(Money.of(entity.getTurnover()))
                .fee(Money.of(entity.getFee()))
                .maker(entity.getMaker() != null ? entity.getMaker() : false)
                .createTime(entity.getCreateTime())
                .build();
    }
    
    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public FeeJpaEntity domainToEntity(Fee domain) {
        if (domain == null) {
            return null;
        }
        
        return FeeJpaEntity.builder()
                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .contractId(domain.getContractId())
                .memberId(domain.getMemberId())
                .symbol(domain.getSymbol() != null ? domain.getSymbol().getValue() : null)
                .orderId(domain.getOrderId() != null ? domain.getOrderId().getValue() : null)
                .direction(domain.getDirection())
                .volume(domain.getVolume())
                .price(domain.getPrice() != null ? domain.getPrice().getValue() : null)
                .turnover(domain.getTurnover() != null ? domain.getTurnover().getValue() : null)
                .fee(domain.getFee() != null ? domain.getFee().getValue() : null)
                .maker(domain.isMaker())
                .createTime(domain.getCreateTime())
                .build();
    }
}
