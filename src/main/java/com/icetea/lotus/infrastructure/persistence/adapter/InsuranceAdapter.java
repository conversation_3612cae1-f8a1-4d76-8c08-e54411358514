package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.InsurancePersistencePort;
import com.icetea.lotus.core.domain.entity.Insurance;
import com.icetea.lotus.core.domain.repository.InsuranceRepository;
import com.icetea.lotus.core.domain.valueobject.InsuranceId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.InsuranceJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.InsurancePersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.InsuranceJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter hợp nhất cho InsurancePersistencePort và InsuranceRepository
 * Triển khai các phương thức của cả hai interface
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class InsuranceAdapter implements InsurancePersistencePort, InsuranceRepository {

    private final InsuranceJpaRepository insuranceJpaRepository;
    private final InsurancePersistenceMapper insurancePersistenceMapper;

    /**
     * Tìm quỹ bảo hiểm theo ID
     * @param id ID của quỹ bảo hiểm
     * @return Optional chứa quỹ bảo hiểm nếu tìm thấy
     */
    @Override
    public Optional<Insurance> findById(InsuranceId id) {
        try {
            log.info("Tìm quỹ bảo hiểm theo ID, id = {}", id.getValue());

            Optional<InsuranceJpaEntity> entity = insuranceJpaRepository.findById(id.getValue());
            Optional<Insurance> result = entity.map(insurancePersistenceMapper::entityToDomain);

            if (result.isPresent()) {
                log.info("Đã tìm thấy quỹ bảo hiểm, id = {}", id.getValue());
            } else {
                log.warn("Không tìm thấy quỹ bảo hiểm, id = {}", id.getValue());
            }

            return result;
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm quỹ bảo hiểm theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm quỹ bảo hiểm theo ID: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm quỹ bảo hiểm theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm quỹ bảo hiểm theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm quỹ bảo hiểm theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa quỹ bảo hiểm nếu tìm thấy
     */
    @Override
    public Optional<Insurance> findBySymbol(Symbol symbol) {
        try {
            log.info("Tìm quỹ bảo hiểm theo symbol, symbol = {}", symbol.getValue());

            Optional<InsuranceJpaEntity> entity = insuranceJpaRepository.findBySymbol(symbol.getValue());
            Optional<Insurance> result = entity.map(insurancePersistenceMapper::entityToDomain);

            if (result.isPresent()) {
                log.info("Đã tìm thấy quỹ bảo hiểm, symbol = {}", symbol.getValue());
            } else {
//                log.warn("Không tìm thấy quỹ bảo hiểm, symbol = {}", symbol.getValue());
            }

            return result;
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm quỹ bảo hiểm theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm quỹ bảo hiểm theo symbol: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm quỹ bảo hiểm theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm quỹ bảo hiểm theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm quỹ bảo hiểm theo contractId
     * @param contractId ID của hợp đồng
     * @return Optional chứa quỹ bảo hiểm nếu tìm thấy
     */
    @Override
    public Optional<Insurance> findByContractId(Long contractId) {
        try {
            log.info("Tìm quỹ bảo hiểm theo contractId, contractId = {}", contractId);

            Optional<InsuranceJpaEntity> entity = insuranceJpaRepository.findByContractId(contractId);
            Optional<Insurance> result = entity.map(insurancePersistenceMapper::entityToDomain);

            if (result.isPresent()) {
                log.info("Đã tìm thấy quỹ bảo hiểm, contractId = {}", contractId);
            } else {
                log.warn("Không tìm thấy quỹ bảo hiểm, contractId = {}", contractId);
            }

            return result;
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm quỹ bảo hiểm theo contractId, contractId = {}", contractId, e);
            throw new DatabaseException("Lỗi khi tìm quỹ bảo hiểm theo contractId: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm quỹ bảo hiểm theo contractId, contractId = {}", contractId, e);
            throw new DatabaseException("Lỗi khi tìm quỹ bảo hiểm theo contractId: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả quỹ bảo hiểm
     * @return Danh sách các quỹ bảo hiểm
     */
    @Override
    public List<Insurance> findAll() {
        try {
            log.info("Lấy tất cả quỹ bảo hiểm");

            List<Insurance> result = insuranceJpaRepository.findAll()
                    .stream()
                    .map(insurancePersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());

            log.info("Đã lấy tất cả quỹ bảo hiểm, size = {}", result.size());

            return result;
        } catch (DataAccessException e) {
            log.error("Lỗi khi lấy tất cả quỹ bảo hiểm", e);
            throw new DatabaseException("Lỗi khi lấy tất cả quỹ bảo hiểm: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lấy tất cả quỹ bảo hiểm", e);
            throw new DatabaseException("Lỗi khi lấy tất cả quỹ bảo hiểm: " + e.getMessage(), e);
        }
    }

    /**
     * Lưu quỹ bảo hiểm
     * @param insurance Quỹ bảo hiểm
     * @return Insurance
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Insurance save(Insurance insurance) {
        try {
            log.info("Lưu quỹ bảo hiểm, symbol = {}", insurance.getSymbol().getValue());

            InsuranceJpaEntity jpaEntity = insurancePersistenceMapper.domainToEntity(insurance);
            InsuranceJpaEntity savedEntity = insuranceJpaRepository.save(jpaEntity);

            log.info("Đã lưu quỹ bảo hiểm, symbol = {}", insurance.getSymbol().getValue());

            return insurancePersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu quỹ bảo hiểm, symbol = {}", insurance.getSymbol().getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu quỹ bảo hiểm, symbol = {}", insurance.getSymbol().getValue(), e);
            throw new DatabaseException("Lỗi khi lưu quỹ bảo hiểm: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu quỹ bảo hiểm thất bại
     * @param e Ngoại lệ
     * @param insurance Quỹ bảo hiểm
     * @return Insurance
     */
    @Recover
    public Insurance recoverSave(Exception e, Insurance insurance) {
        log.error("Đã thử lại lưu quỹ bảo hiểm 3 lần nhưng thất bại, symbol = {}", insurance.getSymbol().getValue(), e);
        throw new DatabaseException("Không thể lưu quỹ bảo hiểm sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Cập nhật số dư quỹ bảo hiểm
     * @param symbol Symbol của hợp đồng
     * @param amount Số tiền
     * @return Insurance
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Insurance updateBalance(Symbol symbol, Money amount) {
        try {
            log.info("Cập nhật số dư quỹ bảo hiểm, symbol = {}, amount = {}", symbol.getValue(), amount.getValue());

            Optional<InsuranceJpaEntity> optionalEntity = insuranceJpaRepository.findBySymbol(symbol.getValue());
            if (optionalEntity.isEmpty()) {
//                log.error("Không tìm thấy quỹ bảo hiểm, symbol = {}", symbol.getValue());
                throw new DatabaseException("Không tìm thấy quỹ bảo hiểm với symbol: " + symbol.getValue());
            }

            InsuranceJpaEntity entity = optionalEntity.get();
            entity.setBalance(entity.getBalance().add(amount.getValue()));
            entity.setAvailableBalance(entity.getAvailableBalance().add(amount.getValue()));
            entity.setUpdateTime(LocalDateTime.now());

            InsuranceJpaEntity savedEntity = insuranceJpaRepository.save(entity);

            log.info("Đã cập nhật số dư quỹ bảo hiểm, symbol = {}, newBalance = {}", 
                    symbol.getValue(), savedEntity.getBalance());

            return insurancePersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi cập nhật số dư quỹ bảo hiểm, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi cập nhật số dư quỹ bảo hiểm, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi cập nhật số dư quỹ bảo hiểm: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi cập nhật số dư quỹ bảo hiểm thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param amount Số tiền
     * @return Insurance
     */
    @Recover
    public Insurance recoverUpdateBalance(Exception e, Symbol symbol, Money amount) {
        log.error("Đã thử lại cập nhật số dư quỹ bảo hiểm 3 lần nhưng thất bại, symbol = {}", symbol.getValue(), e);
        throw new DatabaseException("Không thể cập nhật số dư quỹ bảo hiểm sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Lấy lịch sử quỹ bảo hiểm
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return List<Insurance>
     */
    @Override
    public List<Insurance> getHistory(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        // Phương thức này cần được triển khai khi có bảng lịch sử quỹ bảo hiểm
        // Hiện tại, chúng ta chỉ trả về danh sách rỗng
        log.warn("Phương thức getHistory chưa được triển khai");
        return List.of();
    }
}
