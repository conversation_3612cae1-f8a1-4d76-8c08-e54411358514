package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.LiquidationPersistencePort;
import com.icetea.lotus.core.domain.entity.Liquidation;
import com.icetea.lotus.core.domain.valueobject.LiquidationId;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.LiquidationJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.LiquidationPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.LiquidationJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho LiquidationPersistencePort
 * <PERSON><PERSON><PERSON> khai c<PERSON>h<PERSON> thức của LiquidationPersistencePort
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LiquidationPersistenceAdapter implements LiquidationPersistencePort {
    
    private final LiquidationJpaRepository liquidationJpaRepository;
    private final LiquidationPersistenceMapper liquidationPersistenceMapper;
    
    /**
     * Tìm thanh lý theo ID
     * @param id ID của thanh lý
     * @return Optional chứa thanh lý nếu tìm thấy
     */
    @Override
    public Optional<Liquidation> findById(LiquidationId id) {
        log.info("Tìm thanh lý theo ID, id = {}", id);
        
        try {
            Optional<LiquidationJpaEntity> entity = liquidationJpaRepository.findById(id.getValue());
            return entity.map(liquidationPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Tìm thanh lý theo ID thất bại", e);
            throw e;
        }
    }
    
    /**
     * Tìm thanh lý theo positionId
     * @param positionId ID của vị thế
     * @return Danh sách các thanh lý
     */
    @Override
    public List<Liquidation> findByPositionId(PositionId positionId) {
        log.info("Tìm thanh lý theo positionId, positionId = {}", positionId);
        
        try {
            List<LiquidationJpaEntity> entities = liquidationJpaRepository.findByPositionId(positionId.getValue());
            return entities.stream()
                    .map(liquidationPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm thanh lý theo positionId thất bại", e);
            throw e;
        }
    }
    
    /**
     * Tìm thanh lý theo liquidationOrderId
     * @param liquidationOrderId ID của lệnh thanh lý
     * @return Optional chứa thanh lý nếu tìm thấy
     */
    @Override
    public Optional<Liquidation> findByLiquidationOrderId(OrderId liquidationOrderId) {
        log.info("Tìm thanh lý theo liquidationOrderId, liquidationOrderId = {}", liquidationOrderId);
        
        try {
            Optional<LiquidationJpaEntity> entity = liquidationJpaRepository.findByLiquidationOrderId(liquidationOrderId.getValue());
            return entity.map(liquidationPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Tìm thanh lý theo liquidationOrderId thất bại", e);
            throw e;
        }
    }
    
    /**
     * Tìm thanh lý theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các thanh lý
     */
    @Override
    public List<Liquidation> findByMemberId(Long memberId) {
        log.info("Tìm thanh lý theo memberId, memberId = {}", memberId);
        
        try {
            List<LiquidationJpaEntity> entities = liquidationJpaRepository.findByMemberId(memberId);
            return entities.stream()
                    .map(liquidationPersistenceMapper::entityToDomain)
                    .toList();
        } catch (Exception e) {
            log.error("Tìm thanh lý theo memberId thất bại", e);
            throw e;
        }
    }
    
    /**
     * Tìm thanh lý theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Danh sách các thanh lý
     */
    @Override
    public List<Liquidation> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tìm thanh lý theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);
        
        try {
            List<LiquidationJpaEntity> entities = liquidationJpaRepository.findByMemberIdAndSymbol(memberId, symbol.getValue());
            return entities.stream()
                    .map(liquidationPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm thanh lý theo memberId và symbol thất bại", e);
            throw e;
        }
    }
    
    /**
     * Lưu thanh lý
     * @param liquidation Thanh lý cần lưu
     * @return Thanh lý đã được lưu
     */
    @Override
    public Liquidation save(Liquidation liquidation) {
        log.info("Lưu thanh lý, liquidation = {}", liquidation);
        
        try {
            LiquidationJpaEntity entity = liquidationPersistenceMapper.domainToEntity(liquidation);
            LiquidationJpaEntity savedEntity = liquidationJpaRepository.save(entity);
            return liquidationPersistenceMapper.entityToDomain(savedEntity);
        } catch (Exception e) {
            log.error("Lưu thanh lý thất bại", e);
            throw e;
        }
    }
}
