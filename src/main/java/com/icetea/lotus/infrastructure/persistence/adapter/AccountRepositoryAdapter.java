package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.constants.SystemConstants;
import com.icetea.lotus.core.domain.entity.Account;
import com.icetea.lotus.core.domain.repository.AccountRepository;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.AccountJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.AccountPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.AccountJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Adapter cho AccountRepository
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class AccountRepositoryAdapter implements AccountRepository {

    private final AccountJpaRepository accountJpaRepository;
    private final AccountPersistenceMapper accountPersistenceMapper;

    /**
     * Lưu tài khoản với xử lý ngoại lệ và thử lại
     * @param account Tài khoản
     * @return Account
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Account save(Account account) {
        try {
            log.debug("Lưu tài khoản, memberId = {}", account.getMemberId());

            AccountJpaEntity jpaEntity = accountPersistenceMapper.accountToJpaEntity(account);
            AccountJpaEntity savedEntity = accountJpaRepository.save(jpaEntity);

            log.debug("Đã lưu tài khoản, id = {}", savedEntity.getId());

            return accountPersistenceMapper.jpaEntityToAccount(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu tài khoản, memberId = {}", account.getMemberId(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu tài khoản, memberId = {}", account.getMemberId(), e);
            throw new DatabaseException("Lỗi khi lưu tài khoản: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu tài khoản thất bại
     * @param e Ngoại lệ
     * @param account Tài khoản
     * @return Account
     */
    @Recover
    public Account recoverSave(Exception e, Account account) {
        log.error("Đã thử lại lưu tài khoản 3 lần nhưng thất bại, memberId = {}", account.getMemberId(), e);
        throw new DatabaseException("Không thể lưu tài khoản sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm tài khoản theo ID
     * @param id ID của tài khoản
     * @return Optional chứa tài khoản nếu tìm thấy
     */
    @Override
    public Optional<Account> findById(String id) {
        try {
            log.debug("Tìm tài khoản theo ID, id = {}", id);

            Optional<AccountJpaEntity> jpaEntityOpt = accountJpaRepository.findById(id);
            return jpaEntityOpt.map(accountPersistenceMapper::jpaEntityToAccount);
        } catch (Exception e) {
            log.error("Lỗi khi tìm tài khoản theo ID, id = {}", id, e);
            throw new DatabaseException("Lỗi khi tìm tài khoản theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tài khoản theo ID của thành viên
     * @param memberId ID của thành viên
     * @return Optional chứa tài khoản nếu tìm thấy
     */
    @Override
    public Optional<Account> findByMemberId(String memberId) {
        try {
            log.debug("Tìm tài khoản theo ID của thành viên, memberId = {}", memberId);
            //TODO need to check again
            // Xử lý trường hợp đặc biệt cho quỹ bảo hiểm
            if (SystemConstants.INSURANCE_FUND_ID.equals(memberId)) {
                // Tìm tài khoản với ID số của quỹ bảo hiểm
                Optional<AccountJpaEntity> jpaEntityOpt = accountJpaRepository.findByMemberId(SystemConstants.INSURANCE_FUND_NUMERIC_ID.toString());

                if (jpaEntityOpt.isPresent()) {
                    return jpaEntityOpt.map(accountPersistenceMapper::jpaEntityToAccount);
                } else {
                    // Tạo mới tài khoản cho quỹ bảo hiểm nếu chưa tồn tại
                    AccountJpaEntity entity = new AccountJpaEntity();
                    entity.setId("ACC" + System.currentTimeMillis());
                    entity.setMemberId(SystemConstants.INSURANCE_FUND_NUMERIC_ID.toString());
                    entity.setBalance(java.math.BigDecimal.ZERO);
                    entity.setFrozenBalance(java.math.BigDecimal.ZERO);
                    entity.setAvailableBalance(java.math.BigDecimal.ZERO);
                    entity.setCreateTime(java.time.LocalDateTime.now());
                    entity.setUpdateTime(java.time.LocalDateTime.now());

                    AccountJpaEntity savedEntity = accountJpaRepository.save(entity);
                    return Optional.of(accountPersistenceMapper.jpaEntityToAccount(savedEntity));
                }
            }

            // Xử lý trường hợp thông thường
            Optional<AccountJpaEntity> jpaEntityOpt = accountJpaRepository.findByMemberId(memberId);
            return jpaEntityOpt.map(accountPersistenceMapper::jpaEntityToAccount);
        } catch (Exception e) {
            log.error("Lỗi khi tìm tài khoản theo ID của thành viên, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm tài khoản theo ID của thành viên: " + e.getMessage(), e);
        }
    }
}
