package com.icetea.lotus.infrastructure.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho IndexPrice
 */
@Entity
@Table(name = "contract_index_price")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IndexPriceJpaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "contract_id")
    private Long contractId;

    private String symbol;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal price;

    @Column(name = "reference_prices", columnDefinition = "TEXT")
    private String referencePrices;

    @Column(name = "create_time")
    private LocalDateTime createTime;
}
