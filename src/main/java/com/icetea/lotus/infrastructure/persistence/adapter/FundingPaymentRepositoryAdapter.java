package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.FundingPayment;
import com.icetea.lotus.core.domain.repository.FundingPaymentRepository;
import com.icetea.lotus.core.domain.valueobject.FundingPaymentId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.FundingPaymentJpaEntity;
import com.icetea.lotus.infrastructure.persistence.repository.FundingPaymentJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho FundingPaymentRepository
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class FundingPaymentRepositoryAdapter implements FundingPaymentRepository {

    private final FundingPaymentJpaRepository fundingPaymentJpaRepository;

    /**
     * Lưu funding payment với xử lý ngoại lệ và thử lại
     * @param fundingPayment Funding payment
     * @return FundingPayment
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public FundingPayment save(FundingPayment fundingPayment) {
        try {
            log.debug("Lưu funding payment, id = {}, memberId = {}, symbol = {}",
                    fundingPayment.getId(), fundingPayment.getMemberId(), fundingPayment.getSymbol().getValue());

            FundingPaymentJpaEntity jpaEntity = mapToJpaEntity(fundingPayment);
            FundingPaymentJpaEntity savedEntity = fundingPaymentJpaRepository.save(jpaEntity);

            log.debug("Đã lưu funding payment, id = {}", savedEntity.getId());

            return mapToDomainEntity(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu funding payment, id = {}",
                    fundingPayment.getId() != null ? fundingPayment.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu funding payment, id = {}",
                    fundingPayment.getId() != null ? fundingPayment.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu funding payment: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu funding payment thất bại
     * @param e Ngoại lệ
     * @param fundingPayment Funding payment
     * @return FundingPayment
     */
    @Recover
    public FundingPayment recoverSave(Exception e, FundingPayment fundingPayment) {
        log.error("Đã thử lại lưu funding payment 3 lần nhưng thất bại, id = {}",
                fundingPayment.getId() != null ? fundingPayment.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu funding payment sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm funding payment theo ID với xử lý ngoại lệ
     * @param id ID của funding payment
     * @return Optional<FundingPayment>
     */
    @Override
    public Optional<FundingPayment> findById(FundingPaymentId id) {
        try {
            log.debug("Tìm funding payment theo ID, id = {}", id.getValue());

            Optional<FundingPayment> result = fundingPaymentJpaRepository.findById(id.getValue())
                    .map(this::mapToDomainEntity);

            if (result.isPresent()) {
                log.debug("Đã tìm thấy funding payment, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy funding payment, id = {}", id.getValue());
            }

            return result;
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy funding payment với ID = {}", id.getValue());
            return Optional.empty();
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm funding payment theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm funding payment theo ID: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm funding payment theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm funding payment theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả funding payment theo positionId với xử lý ngoại lệ
     * @param positionId ID của vị thế
     * @return List<FundingPayment>
     */
    @Override
    public List<FundingPayment> findAllByPositionId(Long positionId) {
        try {
            log.debug("Tìm tất cả funding payment theo positionId, positionId = {}", positionId);

            List<FundingPayment> result = fundingPaymentJpaRepository.findAllByPositionId(positionId)
                    .stream()
                    .map(this::mapToDomainEntity)
                    .collect(Collectors.toList());

            log.debug("Đã tìm thấy {} funding payment theo positionId = {}", result.size(), positionId);

            return result;
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm tất cả funding payment theo positionId, positionId = {}", positionId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả funding payment theo positionId: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả funding payment theo positionId, positionId = {}", positionId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả funding payment theo positionId: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FundingPayment> findAllByMemberId(Long memberId) {
        return fundingPaymentJpaRepository.findAllByMemberId(memberId)
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<FundingPayment> findAllBySymbol(Symbol symbol) {
        return fundingPaymentJpaRepository.findAllBySymbol(symbol.getValue())
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<FundingPayment> findAllByPositionIdAndTimeBetween(Long positionId, LocalDateTime startTime, LocalDateTime endTime) {
        return fundingPaymentJpaRepository.findAllByPositionIdAndTimeBetween(positionId, startTime, endTime)
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<FundingPayment> findAllByMemberIdAndTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime) {
        return fundingPaymentJpaRepository.findAllByMemberIdAndTimeBetween(memberId, startTime, endTime)
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<FundingPayment> findAllBySymbolAndTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        return fundingPaymentJpaRepository.findAllBySymbolAndTimeBetween(symbol.getValue(), startTime, endTime)
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    private FundingPayment mapToDomainEntity(FundingPaymentJpaEntity jpaEntity) {
        return FundingPayment.builder()
                .id(FundingPaymentId.of(jpaEntity.getId()))
                .positionId(jpaEntity.getPositionId())
                .memberId(jpaEntity.getMemberId())
                .symbol(Symbol.of(jpaEntity.getSymbol()))
                .amount(Money.of(jpaEntity.getAmount()))
                .rate(jpaEntity.getRate())
                .time(jpaEntity.getTime())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domainEntity Domain entity
     * @return JPA entity
     */
    private FundingPaymentJpaEntity mapToJpaEntity(FundingPayment domainEntity) {
        return FundingPaymentJpaEntity.builder()
                .id(domainEntity.getId() != null ? domainEntity.getId().getValue() : null)
                .positionId(domainEntity.getPositionId())
                .memberId(domainEntity.getMemberId())
                .symbol(domainEntity.getSymbol().getValue())
                .amount(domainEntity.getAmount().getValue())
                .rate(domainEntity.getRate())
                .time(domainEntity.getTime())
                .build();
    }
}
