package com.icetea.lotus.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Insurance
 */
@Entity
@Table(name = "contract_insurance_fund")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InsuranceJpaEntity {
    
    /**
     * ID của quỹ bảo hiểm
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * ID của hợp đồng
     */
    @Column(name = "contract_id")
    private Long contractId;
    
    /**
     * Ký hiệu của hợp đồng
     */
    @Column(name = "symbol")
    private String symbol;
    
    /**
     * Số dư
     */
    @Column(name = "balance")
    private BigDecimal balance;
    
    /**
     * Số dư đóng băng
     */
    @Column(name = "frozen_balance")
    private BigDecimal frozenBalance;
    
    /**
     * <PERSON><PERSON> dư khả dụng
     */
    @Column(name = "available_balance")
    private BigDecimal availableBalance;
    
    /**
     * Tổng số tiền đã sử dụng
     */
    @Column(name = "total_used")
    private BigDecimal totalUsed;
    
    /**
     * Tổng số tiền đã nạp
     */
    @Column(name = "total_deposit")
    private BigDecimal totalDeposit;
    
    /**
     * Thời gian tạo
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    /**
     * Thời gian cập nhật
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    /**
     * Ghi chú
     */
    @Column(name = "remark")
    private String remark;
}
