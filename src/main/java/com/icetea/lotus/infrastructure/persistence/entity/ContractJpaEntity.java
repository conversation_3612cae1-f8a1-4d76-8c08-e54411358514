package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.MarginMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Contract
 */
@Entity
@Table(name = "contract_coin")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class   ContractJpaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String symbol;

    private String name;

    @Column(name = "base_symbol")
    private String baseSymbol;

    @Column(name = "quote_symbol")
    private String quoteSymbol;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal multiplier;

    @Column(name = "min_volume", columnDefinition = "decimal(18,8)")
    private BigDecimal minVolume;

    @Column(name = "max_volume", columnDefinition = "decimal(18,8)")
    private BigDecimal maxVolume;

    @Column(name = "price_precision", columnDefinition = "decimal(18,8)")
    private BigDecimal pricePrecision;

    @Column(name = "volume_precision", columnDefinition = "decimal(18,8)")
    private BigDecimal volumePrecision;

    @Column(name = "maintenance_margin_rate", columnDefinition = "decimal(18,8)")
    private BigDecimal maintenanceMarginRate;

    @Column(name = "initial_margin_rate", columnDefinition = "decimal(18,8)")
    private BigDecimal initialMarginRate;

    @Column(name = "leverage_max")
    private Integer leverageMax;

    @Column(name = "leverage_min")
    private Integer leverageMin;

    @Column(name = "sort")
    private Integer sort;

    @Column(name = "fee", columnDefinition = "decimal(18,8)")
    private BigDecimal fee;

    @Column(name = "funding_rate_coefficient", columnDefinition = "decimal(18,8)")
    private BigDecimal fundingRateCoefficient;

    @Column(name = "max_funding_rate", columnDefinition = "decimal(18,8)")
    private BigDecimal maxFundingRate;

    @Column(name = "min_funding_rate", columnDefinition = "decimal(18,8)")
    private BigDecimal minFundingRate;

    @Column(name = "funding_interval")
    private Integer fundingInterval;

    @Column(name = "margin_mode")
    @Enumerated(EnumType.STRING)
    private MarginMode marginMode;

    @Column(name = "enable")
    private Integer enabled;

    @Column(name = "create_time", updatable = false)
    @CreationTimestamp
    private LocalDateTime createTime;

    @Column(name = "update_time", updatable = false)
    @UpdateTimestamp
    private LocalDateTime updateTime;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate; // Thời điểm đáo hạn hợp đồng
}
