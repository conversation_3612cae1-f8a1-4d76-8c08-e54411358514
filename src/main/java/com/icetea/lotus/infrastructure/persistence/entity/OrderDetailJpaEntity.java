package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.MarginMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA entity cho OrderDetail
 * Đại diện cho chi tiết của một lệnh giao dịch trong cơ sở dữ liệu
 */
@Entity
@Table(name = "contract_order_detail")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDetailJpaEntity {
    
    @Id
    private String id;
    
    private String orderId;
    
    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal price;
    
    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal amount;
    
    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal turnover;
    
    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal fee;
    
    private LocalDateTime time;
    
    private Long contractId;
    
    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal leverage;
    
    @Enumerated(EnumType.STRING)
    private MarginMode marginMode;
    
    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal fundingFee;
}
