package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.OrderDirection;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Fee
 */
@Entity
@Table(name = "contract_fee")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FeeJpaEntity {

    /**
     * ID của phí giao dịch
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * ID của hợp đồng
     */
    @Column(name = "contract_id")
    private Long contractId;

    /**
     * ID của thành viên
     */
    @Column(name = "member_id")
    private Long memberId;

    /**
     * <PERSON><PERSON> hiệu của hợp đồng
     */
    @Column(name = "symbol")
    private String symbol;

    /**
     * ID của lệnh
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * Hướng của lệnh
     */
    @Enumerated(EnumType.STRING)
    private OrderDirection direction;

    /**
     * Khối lượng
     */
    @Column(columnDefinition = "decimal(18,8) default 0")
    private BigDecimal volume;

    /**
     * Giá
     */
    @Column(columnDefinition = "decimal(18,8) default 0")
    private BigDecimal price;

    /**
     * Giá trị giao dịch
     */
    @Column(columnDefinition = "decimal(18,8) default 0")
    private BigDecimal turnover;

    /**
     * Phí
     */
    @Column(columnDefinition = "decimal(18,8) default 0")
    private BigDecimal fee;

    /**
     * Là maker hay taker
     */
    @Column(name = "maker")
    private Boolean maker;

    /**
     * Thời gian tạo
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
}
