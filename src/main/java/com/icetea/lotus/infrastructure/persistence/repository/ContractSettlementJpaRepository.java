package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.ContractSettlementJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho ContractSettlementJpaEntity
 */
@Repository
public interface ContractSettlementJpaRepository extends JpaRepository<ContractSettlementJpaEntity, Long>, JpaSpecificationExecutor<ContractSettlementJpaEntity> {

    /**
     * Tìm tất cả các lần thanh toán hợp đồng theo contractId
     * @param contractId ID của hợp đồng
     * @return Danh sách các lần thanh toán hợp đồng
     */
    List<ContractSettlementJpaEntity> findAllByContractId(Long contractId);

    /**
     * T<PERSON><PERSON> tất cả các lần thanh toán hợp đồng theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các lần thanh toán hợp đồng
     */
    List<ContractSettlementJpaEntity> findAllBySymbol(String symbol);

    /**
     * Tìm tất cả các lần thanh toán hợp đồng theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các lần thanh toán hợp đồng
     */
    List<ContractSettlementJpaEntity> findAllBySymbolAndTimestampBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Tìm tất cả các lần thanh toán hợp đồng theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các lần thanh toán hợp đồng
     */
    List<ContractSettlementJpaEntity> findAllByMemberId(String memberId);

    /**
     * Tìm tất cả các lần thanh toán hợp đồng theo positionId
     * @param positionId ID của vị thế
     * @return Danh sách các lần thanh toán hợp đồng
     */
    List<ContractSettlementJpaEntity> findAllByPositionId(Long positionId);

    /**
     * Tìm tất cả các lần thanh toán hợp đồng theo memberId và khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các lần thanh toán hợp đồng
     */
    List<ContractSettlementJpaEntity> findAllByMemberIdAndTimestampBetween(String memberId, LocalDateTime startTime, LocalDateTime endTime);
}
