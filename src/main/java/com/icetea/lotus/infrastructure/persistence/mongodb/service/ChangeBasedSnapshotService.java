package com.icetea.lotus.infrastructure.persistence.mongodb.service;

import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.matching.distributed.DistributedOrderBookSnapshot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Change-based snapshot service
 * Lưu snapshot khi có thay đổi thay vì định kỳ
 */
@Slf4j
@Service
public class ChangeBasedSnapshotService {

    private final MongoOrderBookSnapshotService mongoSnapshotService;
    private final SnapshotCleanupService snapshotCleanupService;
    private final SnapshotProvider snapshotProvider;
    
    // Track changes per symbol
    private final ConcurrentHashMap<String, ChangeTracker> changeTrackers = new ConcurrentHashMap<>();
    
    // Configuration
    @Value("${snapshot.change.threshold:100}")
    private int changeThreshold; // Save after N changes
    
    @Value("${snapshot.time.threshold.minutes:5}")
    private int timeThresholdMinutes; // Save after N minutes regardless
    
    @Value("${snapshot.enabled:true}")
    private boolean snapshotEnabled;
    
    // Performance monitoring
    private final AtomicLong triggeredSnapshots = new AtomicLong(0);
    private final AtomicLong skippedSnapshots = new AtomicLong(0);
    
    @Autowired
    public ChangeBasedSnapshotService(MongoOrderBookSnapshotService mongoSnapshotService,
                                    SnapshotCleanupService snapshotCleanupService,
                                    @Lazy SnapshotProvider snapshotProvider) {
        this.mongoSnapshotService = mongoSnapshotService;
        this.snapshotCleanupService = snapshotCleanupService;
        this.snapshotProvider = snapshotProvider;
        log.info("ChangeBasedSnapshotService initialized: changeThreshold={}, timeThreshold={}min",
                changeThreshold, timeThresholdMinutes);
    }

    /**
     * Track order change and potentially trigger snapshot
     */
    public void trackOrderChange(Symbol symbol, ChangeType changeType) {
        if (!snapshotEnabled) {
            return;
        }

        try {
            String symbolStr = symbol.getValue();
            ChangeTracker tracker = changeTrackers.computeIfAbsent(symbolStr, k -> new ChangeTracker());

            tracker.recordChange(changeType);

            // Check if snapshot should be triggered
            if (shouldTriggerSnapshot(tracker)) {
                triggerSnapshot(symbol, tracker);
            }

        } catch (Exception e) {
            log.warn("Failed to track order change for symbol: {}", symbol.getValue(), e);
        }
    }

    /**
     * Track order change with snapshot data and potentially trigger snapshot
     */
    public void trackOrderChangeWithSnapshot(Symbol symbol, ChangeType changeType,
                                           DistributedOrderBookSnapshot snapshot) {
        if (!snapshotEnabled) {
            return;
        }

        try {
            String symbolStr = symbol.getValue();
            ChangeTracker tracker = changeTrackers.computeIfAbsent(symbolStr, k -> new ChangeTracker());

            tracker.recordChange(changeType);

            // Check if snapshot should be triggered
            if (shouldTriggerSnapshot(tracker)) {
                triggerSnapshotWithData(symbol, tracker, snapshot);
            }

        } catch (Exception e) {
            log.warn("Failed to track order change with snapshot for symbol: {}", symbol.getValue(), e);
        }
    }

    /**
     * Force snapshot save for symbol
     */
    public void forceSnapshot(Symbol symbol, DistributedOrderBookSnapshot snapshot) {
        if (!snapshotEnabled) {
            return;
        }
        
        try {
            String symbolStr = symbol.getValue();
            ChangeTracker tracker = changeTrackers.get(symbolStr);
            
            if (tracker != null) {
                tracker.reset(); // Reset change counter
            }
            
            // Save snapshot asynchronously
            mongoSnapshotService.saveSnapshotAsync(snapshot, symbol)
                    .thenAccept(snapshotId -> {
                        triggeredSnapshots.incrementAndGet();
                        log.debug("Force snapshot saved for symbol: {}, id: {}", symbolStr, snapshotId);
                    })
                    .exceptionally(throwable -> {
                        log.warn("Failed to save force snapshot for symbol: {}", symbolStr, throwable);
                        return null;
                    });
                    
        } catch (Exception e) {
            log.error("Failed to force snapshot for symbol: {}", symbol.getValue(), e);
        }
    }

    /**
     * Check if snapshot should be triggered based on changes and time
     */
    private boolean shouldTriggerSnapshot(ChangeTracker tracker) {
        // Trigger if change threshold reached
        if (tracker.getChangeCount() >= changeThreshold) {
            log.debug("Snapshot triggered by change threshold: {} >= {}", 
                    tracker.getChangeCount(), changeThreshold);
            return true;
        }
        
        // Trigger if time threshold reached
        long minutesSinceLastSnapshot = tracker.getMinutesSinceLastSnapshot();
        if (minutesSinceLastSnapshot >= timeThresholdMinutes) {
            log.debug("Snapshot triggered by time threshold: {}min >= {}min", 
                    minutesSinceLastSnapshot, timeThresholdMinutes);
            return true;
        }
        
        return false;
    }

    /**
     * Trigger snapshot save
     */
    private void triggerSnapshot(Symbol symbol, ChangeTracker tracker) {
        try {
            log.debug("Snapshot triggered for symbol: {}, changes: {}",
                    symbol.getValue(), tracker.getChangeCount());

            // Get actual snapshot from matching engine and save
            DistributedOrderBookSnapshot snapshot = getSnapshotFromMatchingEngine(symbol);
            if (snapshot != null) {
                // Reset tracker and save snapshot
                tracker.reset();
                triggeredSnapshots.incrementAndGet();

                // Save snapshot asynchronously
                mongoSnapshotService.saveSnapshotAsync(snapshot, symbol)
                        .thenAccept(snapshotId -> {
                            log.debug("Change-based snapshot saved for symbol: {}, id: {}",
                                    symbol.getValue(), snapshotId);

                            // Trigger cleanup for this symbol after successful save
                            triggerSymbolCleanup(symbol);
                        })
                        .exceptionally(throwable -> {
                            log.warn("Failed to save change-based snapshot for symbol: {}",
                                    symbol.getValue(), throwable);
                            return null;
                        });
            } else {
                log.warn("Could not get snapshot from matching engine for symbol: {}", symbol.getValue());
                skippedSnapshots.incrementAndGet();
            }

        } catch (Exception e) {
            log.error("Failed to trigger snapshot for symbol: {}", symbol.getValue(), e);
        }
    }

    /**
     * Trigger snapshot save with actual snapshot data
     */
    private void triggerSnapshotWithData(Symbol symbol, ChangeTracker tracker,
                                       DistributedOrderBookSnapshot snapshot) {
        try {
            tracker.reset();
            triggeredSnapshots.incrementAndGet();

            log.debug("Snapshot triggered with data for symbol: {}, changes: {}",
                    symbol.getValue(), tracker.getChangeCount());

            // Save snapshot asynchronously
            mongoSnapshotService.saveSnapshotAsync(snapshot, symbol)
                    .thenAccept(snapshotId -> {
                        log.debug("Change-based snapshot saved for symbol: {}, id: {}",
                                symbol.getValue(), snapshotId);

                        // Trigger cleanup for this symbol after successful save
                        triggerSymbolCleanup(symbol);
                    })
                    .exceptionally(throwable -> {
                        log.warn("Failed to save change-based snapshot for symbol: {}",
                                symbol.getValue(), throwable);
                        return null;
                    });

        } catch (Exception e) {
            log.error("Failed to trigger snapshot with data for symbol: {}", symbol.getValue(), e);
        }
    }

    /**
     * Get statistics for monitoring
     */
    public SnapshotStatistics getStatistics() {
        int activeTrackers = changeTrackers.size();
        long totalChanges = changeTrackers.values().stream()
                .mapToLong(ChangeTracker::getChangeCount)
                .sum();
        
        return new SnapshotStatistics(
                triggeredSnapshots.get(),
                skippedSnapshots.get(),
                activeTrackers,
                totalChanges
        );
    }

    /**
     * Cleanup inactive trackers
     */
    public void cleanupInactiveTrackers(long maxInactiveMinutes) {
        try {
            long cutoffTime = System.currentTimeMillis() - (maxInactiveMinutes * 60 * 1000);

            changeTrackers.entrySet().removeIf(entry -> {
                ChangeTracker tracker = entry.getValue();
                return tracker.getLastChangeTime() < cutoffTime;
            });

        } catch (Exception e) {
            log.warn("Failed to cleanup inactive trackers", e);
        }
    }

    /**
     * Get snapshot from snapshot provider for the given symbol
     */
    private DistributedOrderBookSnapshot getSnapshotFromMatchingEngine(Symbol symbol) {
        try {
            // Try to get snapshot from SnapshotProvider
            if (snapshotProvider != null && snapshotProvider.isAvailable(symbol)) {
                return snapshotProvider.getCurrentSnapshot(symbol);
            } else {
                log.debug("SnapshotProvider is not available for symbol: {}", symbol.getValue());
            }
        } catch (Exception e) {
            log.error("Failed to get snapshot from snapshot provider for symbol: {}", symbol.getValue(), e);
        }

        return null;
    }

    /**
     * Trigger cleanup for specific symbol after snapshot save
     */
    private void triggerSymbolCleanup(Symbol symbol) {
        try {
            // Run cleanup asynchronously to avoid blocking snapshot save
            if (snapshotCleanupService != null) {
                // Use a separate thread to avoid blocking the snapshot save flow
                new Thread(() -> {
                    try {
                        long cleaned = snapshotCleanupService.cleanupSymbol(symbol.getValue());
                        if (cleaned > 0) {
                            log.debug("Cleaned up {} old snapshots for symbol: {}", cleaned, symbol.getValue());
                        }
                    } catch (Exception e) {
                        log.warn("Failed to cleanup snapshots for symbol: {}", symbol.getValue(), e);
                    }
                }, "snapshot-cleanup-" + symbol.getValue()).start();
            }
        } catch (Exception e) {
            log.warn("Failed to trigger cleanup for symbol: {}", symbol.getValue(), e);
        }
    }

    /**
     * Change types for tracking
     */
    public enum ChangeType {
        ORDER_ADDED,
        ORDER_CANCELLED,
        ORDER_MATCHED,
        ORDER_MODIFIED,
        STOP_ORDER_TRIGGERED
    }

    /**
     * Track changes for a specific symbol
     */
    private static class ChangeTracker {
        private final AtomicInteger changeCount = new AtomicInteger(0);
        private volatile long lastSnapshotTime = System.currentTimeMillis();
        private volatile long lastChangeTime = System.currentTimeMillis();

        public void recordChange(ChangeType changeType) {
            changeCount.incrementAndGet();
            lastChangeTime = System.currentTimeMillis();
        }

        public void reset() {
            changeCount.set(0);
            lastSnapshotTime = System.currentTimeMillis();
        }

        public int getChangeCount() {
            return changeCount.get();
        }

        public long getMinutesSinceLastSnapshot() {
            return (System.currentTimeMillis() - lastSnapshotTime) / (60 * 1000);
        }

        public long getLastChangeTime() {
            return lastChangeTime;
        }
    }

    /**
     * Statistics for monitoring
     */
    public static class SnapshotStatistics {
        public final long triggeredSnapshots;
        public final long skippedSnapshots;
        public final int activeTrackers;
        public final long totalChanges;

        public SnapshotStatistics(long triggeredSnapshots, long skippedSnapshots, 
                                int activeTrackers, long totalChanges) {
            this.triggeredSnapshots = triggeredSnapshots;
            this.skippedSnapshots = skippedSnapshots;
            this.activeTrackers = activeTrackers;
            this.totalChanges = totalChanges;
        }

        @Override
        public String toString() {
            return String.format(
                "SnapshotStats{triggered=%d, skipped=%d, trackers=%d, changes=%d}",
                triggeredSnapshots, skippedSnapshots, activeTrackers, totalChanges
            );
        }
    }
}
