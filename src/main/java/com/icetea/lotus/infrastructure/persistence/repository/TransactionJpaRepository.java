package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.TransactionJpaEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho TransactionJpaEntity
 */
@Repository
public interface TransactionJpaRepository extends JpaRepository<TransactionJpaEntity, String>, JpaSpecificationExecutor<TransactionJpaEntity> {

    /**
     * Tìm giao dịch theo memberId
     *
     * @param memberId ID của thành viên
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberId(Long memberId);

    /**
     * Tìm giao dịch theo memberId và type
     *
     * @param memberId ID của thành viên
     * @param type     Loại giao dịch
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndType(Long memberId, String type);

    /**
     * Tìm giao dịch theo memberId và coin
     *
     * @param memberId ID của thành viên
     * @param coin     Ký hiệu của đồng coin
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndCoin(Long memberId, String coin);

    /**
     * Tìm giao dịch theo memberId, coin và type
     *
     * @param memberId ID của thành viên
     * @param coin     Ký hiệu của đồng coin
     * @param type     Loại giao dịch
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndCoinAndType(Long memberId, String coin, String type);

    /**
     * Tìm giao dịch theo referenceId
     *
     * @param referenceId ID tham chiếu
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByReferenceId(String referenceId);

    /**
     * Tìm giao dịch theo memberId và khoảng thời gian
     *
     * @param memberId  ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime   Thời gian kết thúc
     * @param pageable  Phân trang
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm giao dịch theo memberId, type và khoảng thời gian
     *
     * @param memberId  ID của thành viên
     * @param type      Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime   Thời gian kết thúc
     * @param pageable  Phân trang
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndTypeAndCreateTimeBetween(Long memberId, String type, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm giao dịch theo memberId, coin và khoảng thời gian
     *
     * @param memberId  ID của thành viên
     * @param coin      Ký hiệu của đồng coin
     * @param startTime Thời gian bắt đầu
     * @param endTime   Thời gian kết thúc
     * @param pageable  Phân trang
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndCoinAndCreateTimeBetween(Long memberId, String coin, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm giao dịch theo memberId, coin, type và khoảng thời gian
     *
     * @param memberId  ID của thành viên
     * @param coin      Ký hiệu của đồng coin
     * @param type      Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime   Thời gian kết thúc
     * @param pageable  Phân trang
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndCoinAndTypeAndCreateTimeBetween(Long memberId, String coin, String type, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm giao dịch theo type và khoảng thời gian
     *
     * @param type      Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime   Thời gian kết thúc
     * @param pageable  Phân trang
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByTypeAndCreateTimeBetween(String type, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tính tổng số tiền giao dịch theo memberId và type
     *
     * @param memberId  ID của thành viên
     * @param type      Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime   Thời gian kết thúc
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.type = :type AND t.createTime BETWEEN :startTime AND :endTime")
    BigDecimal sumAmountByMemberIdAndTypeAndCreateTimeBetween(@Param("memberId") Long memberId, @Param("type") String type, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * Đếm số lượng giao dịch theo memberId và type
     *
     * @param memberId  ID của thành viên
     * @param type      Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime   Thời gian kết thúc
     * @return long
     */
    long countByMemberIdAndTypeAndCreateTimeBetween(Long memberId, String type, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Tìm giao dịch theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Ký hiệu của hợp đồng
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndSymbol(Long memberId, String symbol);

    /**
     * Tìm giao dịch theo memberId, symbol và type
     *
     * @param memberId ID của thành viên
     * @param symbol   Ký hiệu của hợp đồng
     * @param type     Loại giao dịch
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByMemberIdAndSymbolAndType(Long memberId, String symbol, String type);

    /**
     * Tìm giao dịch theo orderId
     *
     * @param orderId ID của lệnh
     * @return Danh sách các giao dịch
     */
    List<TransactionJpaEntity> findByOrderId(String orderId);

    /**
     * Tính tổng phí giao dịch theo memberId và coin
     *
     * @param memberId ID của thành viên
     * @param coin     Ký hiệu của đồng coin
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.coin = :coin AND t.type = 'FEE'")
    BigDecimal sumFeeByMemberIdAndCoin(@Param("memberId") Long memberId, @Param("coin") String coin);

    /**
     * Tính tổng phí giao dịch theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.symbol = :symbol AND t.type = 'FEE'")
    BigDecimal sumFeeByMemberIdAndSymbol(@Param("memberId") Long memberId, @Param("symbol") String symbol);

    /**
     * Tính tổng phí tài trợ theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.symbol = :symbol AND t.type = 'FUNDING'")
    BigDecimal sumFundingFeeByMemberIdAndSymbol(@Param("memberId") Long memberId, @Param("symbol") String symbol);

    /**
     * Tính tổng lợi nhuận theo memberId và coin
     *
     * @param memberId ID của thành viên
     * @param coin     Ký hiệu của đồng coin
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.coin = :coin AND t.type = 'TRADE_PROFIT'")
    BigDecimal sumProfitByMemberIdAndCoin(@Param("memberId") Long memberId, @Param("coin") String coin);

    /**
     * Tính tổng lợi nhuận theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.symbol = :symbol AND t.type = 'TRADE_PROFIT'")
    BigDecimal sumProfitByMemberIdAndSymbol(@Param("memberId") Long memberId, @Param("symbol") String symbol);

    /**
     * Tính tổng lỗ theo memberId và coin
     *
     * @param memberId ID của thành viên
     * @param coin     Ký hiệu của đồng coin
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.coin = :coin AND t.type = 'TRADE_LOSS'")
    BigDecimal sumLossByMemberIdAndCoin(@Param("memberId") Long memberId, @Param("coin") String coin);

    /**
     * Tính tổng lỗ theo memberId và symbol
     *
     * @param memberId ID của thành viên
     * @param symbol   Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Query("SELECT SUM(t.amount) FROM TransactionJpaEntity t WHERE t.memberId = :memberId AND t.symbol = :symbol AND t.type = 'TRADE_LOSS'")
    BigDecimal sumLossByMemberIdAndSymbol(@Param("memberId") Long memberId, @Param("symbol") String symbol);
}
