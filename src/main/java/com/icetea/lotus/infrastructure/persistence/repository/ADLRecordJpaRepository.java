package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.ADLRecordJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA repository cho ADLRecordJpaEntity
 */
@Repository
public interface ADLRecordJpaRepository extends JpaRepository<ADLRecordJpaEntity, Long> {
    
    /**
     * Tìm tất cả ADL record của một vị thế
     * @param positionId ID của vị thế
     * @return Danh sách ADL record
     */
    List<ADLRecordJpaEntity> findAllByPositionId(Long positionId);
    
    /**
     * Tìm tất cả ADL record của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách ADL record
     */
    List<ADLRecordJpaEntity> findAllByMemberId(Long memberId);
    
    /**
     * Tìm tất cả ADL record của một symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách ADL record
     */
    List<ADLRecordJpaEntity> findAllBySymbol(String symbol);
    
    /**
     * Tìm tất cả ADL record của một thành viên trong khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách ADL record
     */
    List<ADLRecordJpaEntity> findAllByMemberIdAndTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * Tìm tất cả ADL record của một symbol trong khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách ADL record
     */
    List<ADLRecordJpaEntity> findAllBySymbolAndTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);
}
