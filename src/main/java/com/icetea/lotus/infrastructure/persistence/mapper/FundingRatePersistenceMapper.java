package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.FundingRate;
import com.icetea.lotus.core.domain.valueobject.FundingRateId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.FundingRateJpaEntity;
import org.springframework.stereotype.Component;

/**
 * Mapper cho FundingRate và FundingRateJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class FundingRatePersistenceMapper {

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public FundingRate entityToDomain(FundingRateJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        return FundingRate.builder()
                .id(FundingRateId.of(entity.getId()))
                .symbol(Symbol.of(entity.getSymbol()))
                .rate(entity.getRate())
                .time(entity.getTime())
                .nextTime(entity.getNextTime())
                .contractId(entity.getContractId())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public FundingRateJpaEntity domainToEntity(FundingRate domain) {
        if (domain == null) {
            return null;
        }

        return FundingRateJpaEntity.builder()
                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .symbol(domain.getSymbol().getValue())
                .rate(domain.getRate())
                .markPrice(null) // Sẽ được cập nhật khi lưu
                .indexPrice(null) // Sẽ được cập nhật khi lưu
                .time(domain.getTime())
                .nextTime(domain.getNextTime())
                .contractId(domain.getContractId())
                .build();
    }
}
