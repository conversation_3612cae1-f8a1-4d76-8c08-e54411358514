package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.core.domain.entity.SettlementPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho SettlementPrice
 */
@Repository
public interface SettlementPriceJpaRepository extends JpaRepository<SettlementPrice, Long>, JpaSpecificationExecutor<SettlementPrice> {

    /**
     * Tìm giá thanh toán mới nhất theo symbol
     * @param symbol Symbol của hợp đồng
     * @return SettlementPrice
     */
    SettlementPrice findTopBySymbolValueOrderByCreateTimeDesc(String symbol);

    /**
     * Tìm danh sách giá thanh toán theo symbol và khoảng thời gian
     * Tìm các bản ghi có createTime nằm trong khoảng từ startTime đến endTime
     * Câu truy vấn SQL tương ứng: SELECT * FROM contract_settlement_price WHERE symbol = ? AND create_time BETWEEN ? AND ?
     *
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return List<SettlementPrice>
     */
    List<SettlementPrice> findBySymbolValueAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);
}
