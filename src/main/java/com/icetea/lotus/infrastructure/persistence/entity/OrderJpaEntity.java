package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.OrderDirection;
import com.icetea.lotus.core.domain.entity.OrderStatus;
import com.icetea.lotus.core.domain.entity.OrderType;
import com.icetea.lotus.core.domain.entity.SelfTradePreventionMode;
import com.icetea.lotus.core.domain.entity.TriggerType;
import com.icetea.lotus.core.domain.valueobject.TimeInForce;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Order
 */
@Entity
@Table(name = "contract_order")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderJpaEntity {

    @Id
    @Column(name = "order_id")
    private String orderId;

    @Column(name = "member_id")
    private Long memberId;

    @Column(name = "contract_id")
    private Long contractId;

    private String symbol;

    @Column(name = "coin_symbol")
    private String coinSymbol;

    @Column(name = "base_symbol")
    private String baseSymbol;

    @Enumerated(EnumType.STRING)
    private OrderDirection direction;

    @Enumerated(EnumType.STRING)
    private OrderType type;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal price;

    @Column(name = "trigger_price", columnDefinition = "decimal(18,8)")
    private BigDecimal triggerPrice;

    @Enumerated(EnumType.STRING)
    @Column(name = "trigger_type")
    private TriggerType triggerType;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal volume;

    @Column(name = "deal_volume", columnDefinition = "decimal(18,8)")
    private BigDecimal dealVolume;

    @Column(name = "deal_money", columnDefinition = "decimal(18,8)")
    private BigDecimal dealMoney;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal fee;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private OrderStatus status;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "complete_time")
    private LocalDateTime completeTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "time_in_force")
    private TimeInForce timeInForce;

    @Column(name = "canceled_time")
    private LocalDateTime canceledTime;

    @Column(name = "execute_time")
    private LocalDateTime executeTime;

    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    @Builder.Default
    private Boolean liquidation = false;

    @Builder.Default
    private Boolean adl = false;

    @Builder.Default
    private Boolean implied = false;

    @Column(name = "source_order_id")
    private String sourceOrderId;

    @Column(name = "oco_id")
    private String ocoId;

    @Column(name = "oco_order_no")
    private String ocoOrderNo;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal leverage;

    @Column(name = "reduce_only")
    private Boolean reduceOnly;

    @Column(name = "callback_rate", columnDefinition = "decimal(18,8)")
    private BigDecimal callbackRate;

    @Column(name = "activation_price", columnDefinition = "decimal(18,8)")
    private BigDecimal activationPrice;

    @Column(name = "post_only")
    private Boolean postOnly;

    @Column(name = "cancel_reason")
    private String cancelReason;

    @Column(name = "max_slippage", columnDefinition = "decimal(18,8)")
    private BigDecimal maxSlippage;

    @Column(name = "fill_or_kill")
    private Boolean fillOrKill;

    @Column(name = "immediate_or_cancel")
    private Boolean immediateOrCancel;

    @Enumerated(EnumType.STRING)
    @Column(name = "self_trade_prevention_mode")
    private SelfTradePreventionMode selfTradePreventionMode;
}
