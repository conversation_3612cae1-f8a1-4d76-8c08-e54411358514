package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.CircuitBreakerJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JPA Repository cho CircuitBreaker
 */
@Repository
public interface CircuitBreakerJpaRepository extends JpaRepository<CircuitBreakerJpaEntity, Long> {
    
    /**
     * Tìm circuit breaker theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Optional<CircuitBreakerJpaEntity>
     */
    Optional<CircuitBreakerJpaEntity> findBySymbol(String symbol);
    
    /**
     * Tìm circuit breaker theo symbol và status
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của circuit breaker
     * @return Optional<CircuitBreakerJpaEntity>
     */
    Optional<CircuitBreakerJpaEntity> findBySymbolAndStatus(String symbol, String status);
    
    /**
     * Tìm tất cả circuit breaker theo status
     * @param status Trạng thái của circuit breaker
     * @return List<CircuitBreakerJpaEntity>
     */
    List<CircuitBreakerJpaEntity> findAllByStatus(String status);
}
