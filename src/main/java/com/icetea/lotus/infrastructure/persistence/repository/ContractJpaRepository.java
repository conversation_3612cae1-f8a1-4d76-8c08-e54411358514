package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.ContractJpaEntity;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * JPA Repository cho ContractJpaEntity
 */
@Repository
public interface ContractJpaRepository extends JpaRepository<ContractJpaEntity, Long>, JpaSpecificationExecutor<ContractJpaEntity> {

    /**
     * Tìm hợp đồng theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa hợp đồng nếu tìm thấy
     */
//    @Query(value = "SELECT t.*\n" +
//            "                  FROM future.contract_coin t\n" +
//            "                  WHERE symbol = :symbol", nativeQuery = true)
    Optional<ContractJpaEntity> findBySymbol(@Param("symbol") String symbol);

    /**
     * Tìm các hợp đồng theo trạng thái kích hoạt
     * @param enabled Trạng thái kích hoạt (1: kích hoạt, 0: không kích hoạt)
     * @return Danh sách các hợp đồng
     */
//    @Query(value = "SELECT t.*\n" +
//            "                  FROM future.contract_coin t\n" +
//            "                  WHERE enable = :enabled", nativeQuery = true)
    List<ContractJpaEntity> findByEnabled(@Param("enabled") Integer enabled);

    /**
     * Tìm hợp đồng theo symbol và trạng thái kích hoạt
     * @param symbol Symbol của hợp đồng
     * @param enabled Trạng thái kích hoạt (1: kích hoạt, 0: không kích hoạt)
     * @return Optional chứa hợp đồng nếu tìm thấy
     */
    Optional<ContractJpaEntity> findBySymbolAndEnabled(String symbol, Integer enabled);

    /**
     * Tìm tất cả các hợp đồng có thời điểm đáo hạn trước một thời điểm cụ thể
     * @param expiryDate Thời điểm cần so sánh
     * @return Danh sách các hợp đồng đáo hạn trước thời điểm đã cho
     */
    List<ContractJpaEntity> findAllByExpiryDateBefore(LocalDateTime expiryDate);
}
