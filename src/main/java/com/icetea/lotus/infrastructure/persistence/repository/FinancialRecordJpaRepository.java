package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.core.domain.valueobject.FinancialRecordType;
import com.icetea.lotus.infrastructure.persistence.entity.FinancialRecordJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA repository cho FinancialRecordJpaEntity
 */
@Repository
public interface FinancialRecordJpaRepository extends JpaRepository<FinancialRecordJpaEntity, String> {
    
    /**
     * Tìm tất cả bản ghi tài chính của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách bản ghi tài chính
     */
    List<FinancialRecordJpaEntity> findAllByMemberId(Long memberId);
    
    /**
     * Tìm tất cả bản ghi tài chính của một vị thế
     * @param positionId ID của vị thế
     * @return Danh sách bản ghi tài chính
     */
    List<FinancialRecordJpaEntity> findAllByPositionId(Long positionId);
    
    /**
     * Tìm tất cả bản ghi tài chính theo loại
     * @param type Loại bản ghi tài chính
     * @return Danh sách bản ghi tài chính
     */
    List<FinancialRecordJpaEntity> findAllByType(FinancialRecordType type);
    
    /**
     * Tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách bản ghi tài chính
     */
    List<FinancialRecordJpaEntity> findAllByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime);
}
