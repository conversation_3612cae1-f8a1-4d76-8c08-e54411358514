package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Liquidation;
import com.icetea.lotus.core.domain.valueobject.*;
import com.icetea.lotus.infrastructure.persistence.entity.LiquidationJpaEntity;
import org.springframework.stereotype.Component;

/**
 * Mapper cho Liquidation và LiquidationJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class LiquidationPersistenceMapper {
    
    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Liquidation entityToDomain(LiquidationJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return Liquidation.builder()
                .id(LiquidationId.of(entity.getId()))
                .contractId(entity.getContractId())
                .symbol(Symbol.of(entity.getSymbol()))
                .memberId(entity.getMemberId())
                .positionId(PositionId.of(entity.getPositionId()))
                .liquidationOrderId(OrderId.of(entity.getLiquidationOrderId()))
                .liquidationPrice(Money.of(entity.getLiquidationPrice()))
                .liquidationVolume(Money.of(entity.getLiquidationVolume()))
                .realizedPnl(Money.of(entity.getRealizedPnl()))
                .insuranceAmount(Money.of(entity.getInsuranceAmount()))
                .type(entity.getType())
                .createTime(entity.getCreateTime())
                .remark(entity.getRemark())
                .build();
    }
    
    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public LiquidationJpaEntity domainToEntity(Liquidation domain) {
        if (domain == null) {
            return null;
        }
        
        return LiquidationJpaEntity.builder()
                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .contractId(domain.getContractId())
                .symbol(domain.getSymbol() != null ? domain.getSymbol().getValue() : null)
                .memberId(domain.getMemberId())
                .positionId(domain.getPositionId() != null ? domain.getPositionId().getValue() : null)
                .liquidationOrderId(domain.getLiquidationOrderId() != null ? domain.getLiquidationOrderId().getValue() : null)
                .liquidationPrice(domain.getLiquidationPrice() != null ? domain.getLiquidationPrice().getValue() : null)
                .liquidationVolume(domain.getLiquidationVolume() != null ? domain.getLiquidationVolume().getValue() : null)
                .realizedPnl(domain.getRealizedPnl() != null ? domain.getRealizedPnl().getValue() : null)
                .insuranceAmount(domain.getInsuranceAmount() != null ? domain.getInsuranceAmount().getValue() : null)
                .type(domain.getType())
                .createTime(domain.getCreateTime())
                .remark(domain.getRemark())
                .build();
    }
}
