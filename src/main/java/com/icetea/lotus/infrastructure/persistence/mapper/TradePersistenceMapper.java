package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.OrderType;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.valueobject.TradeId;
import com.icetea.lotus.infrastructure.persistence.entity.TradeJpaEntity;
import com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * Mapper cho Trade và TradeJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Slf4j
@Component
public class TradePersistenceMapper {

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Trade entityToDomain(TradeJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        // Xử lý trường hợp buyFee hoặc sellFee là null
        Money buyFee = (entity.getBuyFee() != null) ? Money.of(entity.getBuyFee()) : Money.ZERO;
        Money sellFee = (entity.getSellFee() != null) ? Money.of(entity.getSellFee()) : Money.ZERO;

        // Xử lý trường hợp buyTurnover hoặc sellTurnover là null
        Money buyTurnover;
        Money sellTurnover;

        if (entity.getBuyTurnover() != null) {
            buyTurnover = Money.of(entity.getBuyTurnover());
        } else if (entity.getVolume() != null && entity.getPrice() != null) {
            buyTurnover = Money.of(entity.getVolume().multiply(entity.getPrice()));
        } else {
            buyTurnover = Money.ZERO;
        }

        if (entity.getSellTurnover() != null) {
            sellTurnover = Money.of(entity.getSellTurnover());
        } else if (entity.getVolume() != null && entity.getPrice() != null) {
            sellTurnover = Money.of(entity.getVolume().multiply(entity.getPrice()));
        } else {
            sellTurnover = Money.ZERO;
        }

        return Trade.builder()
                .id(TradeId.of(entity.getId()))
                .symbol(Symbol.of(entity.getSymbol()))
                .buyOrderId(OrderId.of(entity.getBuyOrderId()))
                .sellOrderId(OrderId.of(entity.getSellOrderId()))
                .buyMemberId(entity.getBuyMemberId())
                .sellMemberId(entity.getSellMemberId())
                .price(Money.of(entity.getPrice()))
                .volume(entity.getVolume())
                .buyFee(buyFee)
                .sellFee(sellFee)
                .buyTurnover(buyTurnover)
                .sellTurnover(sellTurnover)
                .tradeTime(entity.getTradeTime())
                .buyOrderType(convertToEntityOrderType(entity.getBuyOrderType()))
                .sellOrderType(convertToEntityOrderType(entity.getSellOrderType()))
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public TradeJpaEntity domainToEntity(Trade domain) {
        if (domain == null) {
            return null;
        }

        // Chuyển đổi String thành Long cho id
        Long id = null;
        if (domain.getId() != null) {
            try {
                id = Long.parseLong(domain.getId().getValue());
            } catch (NumberFormatException e) {
                // Nếu không thể chuyển đổi, để id là null (sẽ tạo mới)
                id = null;
            }
        }

        // Xử lý trường hợp buyFee hoặc sellFee là null
        BigDecimal buyFeeValue = (domain.getBuyFee() != null) ?
                validateAndScale(domain.getBuyFee().getValue(), "buyFee") : BigDecimal.ZERO;
        BigDecimal sellFeeValue = (domain.getSellFee() != null) ?
                validateAndScale(domain.getSellFee().getValue(), "sellFee") : BigDecimal.ZERO;

        // ✅ FIX: Xử lý turnover đúng cách
        // Trong futures, volume đã là USDT (notional value), không cần nhân với price
        BigDecimal buyTurnoverValue;
        BigDecimal sellTurnoverValue;

        if (domain.getBuyTurnover() != null) {
            buyTurnoverValue = validateAndScale(domain.getBuyTurnover().getValue(), "buyTurnover");
        } else {
            // Volume đã là USDT, turnover = volume
            buyTurnoverValue = (domain.getVolume() != null) ?
                    validateAndScale(domain.getVolume(), "buyTurnover") : BigDecimal.ZERO;
        }

        if (domain.getSellTurnover() != null) {
            sellTurnoverValue = validateAndScale(domain.getSellTurnover().getValue(), "sellTurnover");
        } else {
            // Volume đã là USDT, turnover = volume
            sellTurnoverValue = (domain.getVolume() != null) ?
                    validateAndScale(domain.getVolume(), "sellTurnover") : BigDecimal.ZERO;
        }

        // Xử lý các trường khác
        String symbolValue = (domain.getSymbol() != null) ? domain.getSymbol().getValue() : "";
        String buyOrderIdValue = (domain.getBuyOrderId() != null) ? domain.getBuyOrderId().getValue() : "";
        String sellOrderIdValue = (domain.getSellOrderId() != null) ? domain.getSellOrderId().getValue() : "";

        // ✅ Validate price và volume
        BigDecimal priceValue = (domain.getPrice() != null) ?
                validateAndScale(domain.getPrice().getValue(), "price") : BigDecimal.ZERO;
        BigDecimal volumeValue = (domain.getVolume() != null) ?
                validateAndScale(domain.getVolume(), "volume") : BigDecimal.ZERO;

        LocalDateTime tradeTimeValue = (domain.getTradeTime() != null) ?
                domain.getTradeTime() : LocalDateTime.now();

        return TradeJpaEntity.builder()
//                .id(id)
                .symbol(symbolValue)
                .buyOrderId(buyOrderIdValue)
                .sellOrderId(sellOrderIdValue)
                .buyMemberId(domain.getBuyMemberId() != null ? domain.getBuyMemberId() : 0L)
                .sellMemberId(domain.getSellMemberId() != null ? domain.getSellMemberId() : 0L)
                .price(priceValue)
                .volume(volumeValue)
                .buyFee(buyFeeValue)
                .sellFee(sellFeeValue)
                .buyTurnover(buyTurnoverValue)
                .sellTurnover(sellTurnoverValue)
                .tradeTime(tradeTimeValue)
                .buyOrderType(convertToValueObjectOrderType(domain.getBuyOrderType()))
                .sellOrderType(convertToValueObjectOrderType(domain.getSellOrderType()))
                .build();
    }

    /**
     * Validate và scale BigDecimal để phù hợp với NUMERIC(18,8)
     * @param value Giá trị cần validate
     * @param fieldName Tên field để log lỗi
     * @return Giá trị đã được validate và scale
     */
    private BigDecimal validateAndScale(BigDecimal value, String fieldName) {
        return BigDecimalValidator.validateAndScale(value, fieldName);
    }

    /**
     * Chuyển đổi từ OrderType của valueobject sang OrderType của entity
     * @param orderType OrderType của valueobject
     * @return OrderType của entity
     */
    private OrderType convertToEntityOrderType(com.icetea.lotus.core.domain.valueobject.OrderType orderType) {
        if (orderType == null) {
            return null;
        }

        switch (orderType) {
            case LIMIT:
                return OrderType.LIMIT;
            case MARKET:
                return OrderType.MARKET;
            case STOP:
            case STOP_MARKET:
                return OrderType.STOP_LOSS;
            case STOP_LIMIT:
                return OrderType.STOP_LOSS_LIMIT;
            case TAKE_PROFIT_MARKET:
                return OrderType.TAKE_PROFIT;
            case TAKE_PROFIT_LIMIT:
                return OrderType.TAKE_PROFIT_LIMIT;
            default:
                return OrderType.MARKET;
        }
    }

    /**
     * Chuyển đổi từ OrderType của entity sang OrderType của valueobject
     * @param orderType OrderType của entity
     * @return OrderType của valueobject
     */
    private com.icetea.lotus.core.domain.valueobject.OrderType convertToValueObjectOrderType(OrderType orderType) {
        if (orderType == null) {
            return null;
        }

        switch (orderType) {
            case LIMIT:
                return com.icetea.lotus.core.domain.valueobject.OrderType.LIMIT;
            case MARKET:
                return com.icetea.lotus.core.domain.valueobject.OrderType.MARKET;
            case STOP_LOSS:
                return com.icetea.lotus.core.domain.valueobject.OrderType.STOP;
            case STOP_LOSS_LIMIT:
                return com.icetea.lotus.core.domain.valueobject.OrderType.STOP_LIMIT;
            case TAKE_PROFIT:
                return com.icetea.lotus.core.domain.valueobject.OrderType.TAKE_PROFIT_MARKET;
            case TAKE_PROFIT_LIMIT:
                return com.icetea.lotus.core.domain.valueobject.OrderType.TAKE_PROFIT_LIMIT;
            case TRAILING_STOP:
                return com.icetea.lotus.core.domain.valueobject.OrderType.STOP;
            default:
                return com.icetea.lotus.core.domain.valueobject.OrderType.MARKET;
        }
    }
}
