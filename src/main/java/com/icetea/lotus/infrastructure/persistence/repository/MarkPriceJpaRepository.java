package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.core.domain.entity.MarkPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho MarkPrice
 */
@Repository
public interface MarkPriceJpaRepository extends JpaRepository<MarkPrice, Long>, JpaSpecificationExecutor<MarkPrice> {

    /**
     * Tìm giá đánh dấu mới nhất theo symbol
     * @param symbol Symbol của hợp đồng
     * @return MarkPrice
     */
    MarkPrice findTopBySymbolValueOrderByCreateTimeDesc(String symbol);

    /**
     * Tìm danh sách giá đánh dấu theo symbol và khoảng thời gian
     * Tìm các bản ghi có createTime nằm trong khoảng từ startTime đến endTime
     * Câu truy vấn SQL tương ứng: SELECT * FROM contract_mark_price WHERE symbol = ? AND create_time BETWEEN ? AND ?
     *
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return List<MarkPrice>
     */
    List<MarkPrice> findBySymbolValueAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);
}
