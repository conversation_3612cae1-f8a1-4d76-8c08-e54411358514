package com.icetea.lotus.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Wallet
 */
@Entity
@Table(name = "contract_wallet", uniqueConstraints = {@UniqueConstraint(columnNames = {"member_id", "coin"})})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletJpaEntity {
    
    /**
     * ID của ví
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * ID của thành viên
     */
    @Column(name = "member_id")
    private Long memberId;
    
    /**
     * Ký hiệu của đồng coin
     */
    private String coin;
    
    /**
     * Số dư
     */
    @Column(columnDefinition = "decimal(26,16)")
    private BigDecimal balance;
    
    /**
     * Số dư đóng băng
     */
    @Column(name = "frozen_balance", columnDefinition = "decimal(26,16)")
    private BigDecimal frozenBalance;
    
    /**
     * Số dư khả dụng
     */
    @Column(name = "available_balance")
    private BigDecimal availableBalance;
    
    /**
     * Địa chỉ nạp tiền
     */
    private String address;
    
    /**
     * Tổng lợi nhuận/lỗ chưa thực hiện
     */
    @Column(name = "unrealized_pnl")
    private BigDecimal unrealizedPnl;
    
    /**
     * Tổng lợi nhuận/lỗ đã thực hiện
     */
    @Column(name = "realized_pnl")
    private BigDecimal realizedPnl;
    
    /**
     * Tổng margin đã sử dụng
     */
    @Column(name = "used_margin")
    private BigDecimal usedMargin;
    
    /**
     * Tổng phí giao dịch đã trả
     */
    @Column(name = "total_fee")
    private BigDecimal totalFee;
    
    /**
     * Tổng phí tài trợ đã trả
     */
    @Column(name = "total_funding_fee")
    private BigDecimal totalFundingFee;
    
    /**
     * Trạng thái khóa ví
     */
    @Column(name = "is_locked")
    private boolean isLocked;
    
    /**
     * Thời gian tạo
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    /**
     * Thời gian cập nhật
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    /**
     * Phiên bản
     */
    @Version
    private Integer version;
    
    /**
     * Cập nhật số dư khả dụng
     */
    @PrePersist
    @PreUpdate
    public void updateAvailableBalance() {
        this.availableBalance = this.balance.subtract(this.frozenBalance);
    }
}
