package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.MarkPrice;
import com.icetea.lotus.core.domain.repository.MarkPriceRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.repository.MarkPriceJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Adapter cho MarkPriceRepository
 * Triể<PERSON> khai c<PERSON><PERSON> ph<PERSON><PERSON>ng thức của MarkPriceRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class MarkPriceRepositoryAdapter implements MarkPriceRepository {

    private final MarkPriceJpaRepository markPriceJpaRepository;

    /**
     * Lưu giá đánh dấu với xử lý ngoại lệ và thử lại
     * @param markPrice Giá đánh dấu
     * @return MarkPrice
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public MarkPrice save(MarkPrice markPrice) {
        try {
            log.debug("Lưu giá đánh dấu, symbol = {}",
                    markPrice != null && markPrice.getSymbol() != null ? markPrice.getSymbol().getValue() : "null");

            if (markPrice == null) {
                throw new IllegalArgumentException("MarkPrice không được để trống");
            }

            return markPriceJpaRepository.save(markPrice);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giá đánh dấu, symbol = {}",
                    markPrice != null && markPrice.getSymbol() != null ? markPrice.getSymbol().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giá đánh dấu, symbol = {}",
                    markPrice != null && markPrice.getSymbol() != null ? markPrice.getSymbol().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu giá đánh dấu: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm giá đánh dấu mới nhất theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return MarkPrice
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public MarkPrice findTopBySymbolOrderByCreateTimeDesc(Symbol symbol) {
        try {
            log.debug("Tìm giá đánh dấu mới nhất theo symbol, symbol = {}",
                    symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return null;
            }

            return markPriceJpaRepository.findTopBySymbolValueOrderByCreateTimeDesc(symbol.getValue());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm giá đánh dấu mới nhất theo symbol, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            // Trả về null thay vì ném ngoại lệ để tránh đánh dấu giao dịch là rollback-only
            return null;
        } catch (TransactionException e) {
            log.error("Lỗi giao dịch khi tìm giá đánh dấu mới nhất theo symbol, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            // Trả về null thay vì ném ngoại lệ để tránh đánh dấu giao dịch là rollback-only
            return null;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giá đánh dấu mới nhất theo symbol, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            // Trả về null thay vì ném ngoại lệ để tránh đánh dấu giao dịch là rollback-only
            return null;
        }
    }

    /**
     * Tìm danh sách giá đánh dấu trong khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách giá đánh dấu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<MarkPrice> findBySymbolAndCreateTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm danh sách giá đánh dấu trong khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol != null ? symbol.getValue() : "null", startTime, endTime);

            if (symbol == null || startTime == null || endTime == null) {
                throw new IllegalArgumentException("Symbol, startTime và endTime không được để trống");
            }

            return markPriceJpaRepository.findBySymbolValueAndCreateTimeBetween(symbol.getValue(), startTime, endTime);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giá đánh dấu trong khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol != null ? symbol.getValue() : "null", startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giá đánh dấu trong khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol != null ? symbol.getValue() : "null", startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm danh sách giá đánh dấu trong khoảng thời gian: " + e.getMessage(), e);
        }
    }

    /**
     * Xử lý khi thử lại thất bại
     * @param e Ngoại lệ
     * @param markPrice Giá đánh dấu
     * @return null
     */
    @Recover
    public MarkPrice recoverSave(Exception e, MarkPrice markPrice) {
        log.error("Thử lại thất bại khi lưu giá đánh dấu, symbol = {}",
                markPrice != null && markPrice.getSymbol() != null ? markPrice.getSymbol().getValue() : "null", e);
        throw new DatabaseException("Thử lại thất bại khi lưu giá đánh dấu: " + e.getMessage(), e);
    }

    /**
     * Xử lý khi thử lại thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @return null
     */
    @Recover
    public MarkPrice recoverFindTopBySymbolOrderByCreateTimeDesc(Exception e, Symbol symbol) {
        log.error("Thử lại thất bại khi tìm giá đánh dấu mới nhất theo symbol, symbol = {}",
                symbol != null ? symbol.getValue() : "null", e);
        throw new DatabaseException("Thử lại thất bại khi tìm giá đánh dấu mới nhất theo symbol: " + e.getMessage(), e);
    }

    /**
     * Xử lý khi thử lại thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return null
     */
    @Recover
    public List<MarkPrice> recoverFindBySymbolAndCreateTimeBetween(Exception e, Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        log.error("Thử lại thất bại khi tìm danh sách giá đánh dấu trong khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                symbol != null ? symbol.getValue() : "null", startTime, endTime, e);
        throw new DatabaseException("Thử lại thất bại khi tìm danh sách giá đánh dấu trong khoảng thời gian: " + e.getMessage(), e);
    }
}
