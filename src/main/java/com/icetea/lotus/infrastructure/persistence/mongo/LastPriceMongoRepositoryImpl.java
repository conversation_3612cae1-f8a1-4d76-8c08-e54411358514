package com.icetea.lotus.infrastructure.persistence.mongo;

import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.core.domain.repository.LastPriceMongoRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Implementation của LastPriceMongoRepository sử dụng MongoDB
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LastPriceMongoRepositoryImpl implements LastPriceMongoRepository {

    private final MongoTemplate mongoTemplate;

    /**
     * Lưu giá cuối cùng vào MongoDB với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param price Giá cuối cùng
     * @param volume Khối lượng
     * @return LastPriceMongoEntity
     */
    @Override
    @Retryable(
            value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public LastPriceMongoEntity save(Symbol symbol, BigDecimal price, BigDecimal volume) {
        try {
            log.debug("Lưu giá cuối cùng vào MongoDB, symbol = {}, price = {}, volume = {}",
                    symbol != null ? symbol.getValue() : "null", price, volume);

            if (symbol == null) {
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            if (price == null) {
                throw new IllegalArgumentException("Price không được để trống");
            }

            // Tạo entity
            LastPriceMongoEntity entity = LastPriceMongoEntity.builder()
                    .id(UUID.randomUUID().toString())
                    .symbol(symbol.getValue())
                    .price(price)
                    .volume(volume)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();

            // Lưu vào MongoDB
            mongoTemplate.save(entity);

            log.debug("Đã lưu giá cuối cùng vào MongoDB thành công, symbol = {}, price = {}, volume = {}",
                    symbol.getValue(), price, volume);

            return entity;
        } catch (Exception e) {
            log.error("Lỗi khi lưu giá cuối cùng vào MongoDB, symbol = {}, price = {}, volume = {}",
                    symbol != null ? symbol.getValue() : "null", price, volume, e);
            throw e;
        }
    }

    /**
     * Phục hồi khi lưu giá cuối cùng vào MongoDB thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param price Giá cuối cùng
     * @param volume Khối lượng
     * @return null
     */
    @Recover
    public LastPriceMongoEntity recoverSave(Exception e, Symbol symbol, BigDecimal price, BigDecimal volume) {
        log.error("Đã thử lại lưu giá cuối cùng vào MongoDB 3 lần nhưng thất bại, symbol = {}, price = {}, volume = {}",
                symbol != null ? symbol.getValue() : "null", price, volume, e);
        throw new DatabaseException("Không thể lưu giá cuối cùng vào MongoDB sau 3 lần thử lại", e);
    }

    /**
     * Tìm giá cuối cùng mới nhất theo symbol từ MongoDB
     * @param symbol Symbol của hợp đồng
     * @return LastPriceMongoEntity
     */
    @Override
    public LastPriceMongoEntity findTopBySymbolOrderByCreateTimeDesc(Symbol symbol) {
        try {
            log.debug("Tìm giá cuối cùng mới nhất từ MongoDB, symbol = {}",
                    symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return null;
            }

            // Tạo query
            Query query = new Query(Criteria.where("symbol").is(symbol.getValue()));
            query.with(Sort.by(Sort.Direction.DESC, "createTime"));
            query.limit(1);

            // Thực hiện truy vấn
            LastPriceMongoEntity entity = mongoTemplate.findOne(query, LastPriceMongoEntity.class);

            if (entity == null) {
                log.debug("Không tìm thấy giá cuối cùng nào từ MongoDB, symbol = {}", symbol.getValue());
                return null;
            }

            log.debug("Đã tìm thấy giá cuối cùng mới nhất từ MongoDB, symbol = {}, price = {}",
                    symbol.getValue(), entity.getPrice());

            return entity;
        } catch (Exception e) {
            log.error("Lỗi khi tìm giá cuối cùng mới nhất từ MongoDB, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi tìm giá cuối cùng mới nhất từ MongoDB", e);
        }
    }

    /**
     * Tìm danh sách giá cuối cùng trong khoảng thời gian từ MongoDB
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách giá cuối cùng
     */
    @Override
    public List<LastPriceMongoEntity> findBySymbolAndCreateTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm danh sách giá cuối cùng trong khoảng thời gian từ MongoDB, symbol = {}, startTime = {}, endTime = {}",
                    symbol != null ? symbol.getValue() : "null", startTime, endTime);

            if (symbol == null) {
                return List.of();
            }

            // Tạo query
            Query query = new Query(Criteria.where("symbol").is(symbol.getValue())
                    .and("createTime").gte(startTime).lte(endTime));
            query.with(Sort.by(Sort.Direction.DESC, "createTime"));

            // Thực hiện truy vấn
            List<LastPriceMongoEntity> entities = mongoTemplate.find(query, LastPriceMongoEntity.class);

            log.debug("Đã tìm thấy {} giá cuối cùng trong khoảng thời gian từ MongoDB, symbol = {}",
                    entities.size(), symbol.getValue());

            return entities;
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách giá cuối cùng trong khoảng thời gian từ MongoDB, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi tìm danh sách giá cuối cùng trong khoảng thời gian từ MongoDB", e);
        }
    }
}
