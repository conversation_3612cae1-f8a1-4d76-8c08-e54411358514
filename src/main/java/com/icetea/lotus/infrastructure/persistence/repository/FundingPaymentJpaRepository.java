package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.FundingPaymentJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA repository cho FundingPaymentJpaEntity
 */
@Repository
public interface FundingPaymentJpaRepository extends JpaRepository<FundingPaymentJpaEntity, Long> {
    
    /**
     * Tìm tất cả funding payment của một vị thế
     * @param positionId ID của vị thế
     * @return Danh sách funding payment
     */
    List<FundingPaymentJpaEntity> findAllByPositionId(Long positionId);
    
    /**
     * Tìm tất cả funding payment của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách funding payment
     */
    List<FundingPaymentJpaEntity> findAllByMemberId(Long memberId);
    
    /**
     * Tìm tất cả funding payment của một symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách funding payment
     */
    List<FundingPaymentJpaEntity> findAllBySymbol(String symbol);
    
    /**
     * Tìm tất cả funding payment của một vị thế trong khoảng thời gian
     * @param positionId ID của vị thế
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách funding payment
     */
    List<FundingPaymentJpaEntity> findAllByPositionIdAndTimeBetween(Long positionId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * Tìm tất cả funding payment của một thành viên trong khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách funding payment
     */
    List<FundingPaymentJpaEntity> findAllByMemberIdAndTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * Tìm tất cả funding payment của một symbol trong khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách funding payment
     */
    List<FundingPaymentJpaEntity> findAllBySymbolAndTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);
}
