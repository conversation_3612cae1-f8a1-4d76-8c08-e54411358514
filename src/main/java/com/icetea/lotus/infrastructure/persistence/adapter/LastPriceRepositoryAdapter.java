package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.LastPrice;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.core.domain.repository.LastPriceRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.repository.LastPriceJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import jakarta.persistence.PersistenceException;

/**
 * Adapter cho LastPriceRepository
 * <PERSON>ển khai các phư<PERSON>ng thức của LastPriceRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class LastPriceRepositoryAdapter implements LastPriceRepository {

    private final LastPriceJpaRepository lastPriceJpaRepository;

    /**
     * Lưu giá cuối cùng với xử lý ngoại lệ và thử lại
     * @param lastPrice Giá cuối cùng
     * @return LastPrice
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public LastPrice save(LastPrice lastPrice) {
        try {
            log.debug("Lưu giá cuối cùng, symbol = {}, price = {}",
                    lastPrice.getSymbol() != null ? lastPrice.getSymbol().getValue() : "null",
                    lastPrice.getPrice() != null ? lastPrice.getPrice().getValue() : "null");

            if (lastPrice.getCreateTime() == null) {
                lastPrice.setCreateTime(LocalDateTime.now());
            }
            lastPrice.setUpdateTime(LocalDateTime.now());

            LastPrice savedLastPrice = lastPriceJpaRepository.save(lastPrice);

            log.debug("Đã lưu giá cuối cùng thành công, id = {}", savedLastPrice.getId());

            return savedLastPrice;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giá cuối cùng, symbol = {}",
                    lastPrice.getSymbol() != null ? lastPrice.getSymbol().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giá cuối cùng, symbol = {}",
                    lastPrice.getSymbol() != null ? lastPrice.getSymbol().getValue() : "null", e);
            throw new DatabaseException("Lỗi không xác định khi lưu giá cuối cùng", e);
        }
    }

    /**
     * Phục hồi khi lưu giá cuối cùng thất bại
     * @param e Ngoại lệ
     * @param lastPrice Giá cuối cùng
     * @return null
     */
    @Recover
    public LastPrice recoverSave(Exception e, LastPrice lastPrice) {
        log.error("Đã thử lại lưu giá cuối cùng 3 lần nhưng thất bại, symbol = {}",
                lastPrice.getSymbol() != null ? lastPrice.getSymbol().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu giá cuối cùng sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm giá cuối cùng mới nhất theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return LastPrice
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 5,
            backoff = @Backoff(delay = 500, multiplier = 2, maxDelay = 5000)
    )
    public LastPrice findTopBySymbolOrderByCreateTimeDesc(Symbol symbol) {
        try {
            log.debug("Tìm giá cuối cùng mới nhất theo symbol, symbol = {}",
                    symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return null;
            }

            // Kiểm tra kết nối trước khi thực hiện truy vấn
            try {
                // Thực hiện truy vấn đơn giản để kiểm tra kết nối
                lastPriceJpaRepository.count();
            } catch (Exception e) {
                log.warn("Kết nối có thể đã bị đóng, thử lại sau, symbol = {}",
                        symbol.getValue(), e);
                throw new DataAccessException("Kết nối có thể đã bị đóng, thử lại sau", e) {};
            }

            return lastPriceJpaRepository.findTopBySymbolValueOrderByCreateTimeDesc(symbol.getValue());
        } catch (DataAccessException e) {
            // Kiểm tra xem lỗi có phải là "Connection is closed" không
            if (e.getCause() != null && e.getCause().getMessage() != null &&
                e.getCause().getMessage().contains("Connection is closed")) {
                log.error("Lỗi kết nối đã bị đóng khi tìm giá cuối cùng mới nhất theo symbol, symbol = {}, thử lại...",
                        symbol != null ? symbol.getValue() : "null", e);
            } else {
                log.error("Lỗi truy cập dữ liệu khi tìm giá cuối cùng mới nhất theo symbol, symbol = {}, thử lại...",
                        symbol != null ? symbol.getValue() : "null", e);
            }
            throw e;
        } catch (TransactionException e) {
            log.error("Lỗi giao dịch khi tìm giá cuối cùng mới nhất theo symbol, symbol = {}, thử lại...",
                    symbol != null ? symbol.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giá cuối cùng mới nhất theo symbol, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            throw new DatabaseException("Lỗi không xác định khi tìm giá cuối cùng mới nhất theo symbol", e);
        }
    }

    /**
     * Phục hồi khi tìm giá cuối cùng mới nhất thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @return LastPrice với giá mặc định
     */
    @Recover
    public LastPrice recoverFindTopBySymbolOrderByCreateTimeDesc(Exception e, Symbol symbol) {
        log.error("Đã thử lại tìm giá cuối cùng mới nhất 5 lần nhưng thất bại, symbol = {}, sử dụng giá mặc định",
                symbol != null ? symbol.getValue() : "null", e);

        // Tạo đối tượng LastPrice với giá mặc định
        LastPrice defaultLastPrice = new LastPrice();
        defaultLastPrice.setSymbol(symbol);

        // Thử lấy giá từ cache hoặc nguồn dữ liệu khác nếu có
        java.math.BigDecimal defaultPrice = java.math.BigDecimal.ONE;
        try {
            // Có thể thêm logic để lấy giá từ cache hoặc nguồn dữ liệu khác ở đây
            // Ví dụ: defaultPrice = cacheService.getLastPrice(symbol.getValue());
            log.info("Sử dụng giá mặc định = {} cho symbol = {}", defaultPrice, symbol.getValue());
        } catch (Exception ex) {
            log.warn("Không thể lấy giá từ cache, sử dụng giá mặc định = 1 cho symbol = {}", symbol.getValue(), ex);
        }

        defaultLastPrice.setPrice(com.icetea.lotus.core.domain.valueobject.Money.of(defaultPrice));
        defaultLastPrice.setVolume(java.math.BigDecimal.ONE); // Khối lượng mặc định
        defaultLastPrice.setCreateTime(java.time.LocalDateTime.now());
        defaultLastPrice.setUpdateTime(java.time.LocalDateTime.now());

        // Ghi log để theo dõi
        log.info("Đã tạo giá cuối cùng mặc định cho symbol = {}, price = {}",
                symbol.getValue(), defaultLastPrice.getPrice().getValue());

        return defaultLastPrice;
    }

    /**
     * Tìm danh sách giá cuối cùng trong khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách giá cuối cùng
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<LastPrice> findBySymbolAndCreateTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm danh sách giá cuối cùng trong khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol != null ? symbol.getValue() : "null", startTime, endTime);

            if (symbol == null) {
                return List.of();
            }

            return lastPriceJpaRepository.findBySymbolValueAndCreateTimeBetween(symbol.getValue(), startTime, endTime);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giá cuối cùng trong khoảng thời gian, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giá cuối cùng trong khoảng thời gian, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            throw new DatabaseException("Lỗi không xác định khi tìm danh sách giá cuối cùng trong khoảng thời gian", e);
        }
    }

    /**
     * Phục hồi khi tìm danh sách giá cuối cùng trong khoảng thời gian thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách rỗng
     */
    @Recover
    public List<LastPrice> recoverFindBySymbolAndCreateTimeBetween(Exception e, Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        log.error("Đã thử lại tìm danh sách giá cuối cùng trong khoảng thời gian 3 lần nhưng thất bại, symbol = {}",
                symbol != null ? symbol.getValue() : "null", e);
        return List.of();
    }
}
