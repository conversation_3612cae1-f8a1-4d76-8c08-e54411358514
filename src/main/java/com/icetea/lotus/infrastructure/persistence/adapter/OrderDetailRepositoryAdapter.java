package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.OrderDetail;
import com.icetea.lotus.core.domain.repository.OrderDetailRepository;
import com.icetea.lotus.core.domain.valueobject.OrderDetailId;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.OrderDetailJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.OrderDetailPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.OrderDetailJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho OrderDetailRepository với xử lý ngoại lệ
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class OrderDetailRepositoryAdapter implements OrderDetailRepository {

    private final OrderDetailJpaRepository orderDetailJpaRepository;
    private final OrderDetailPersistenceMapper orderDetailPersistenceMapper;

    /**
     * Tìm chi tiết lệnh theo id với xử lý ngoại lệ và thử lại
     * @param id ID của chi tiết lệnh
     * @return Optional chứa chi tiết lệnh nếu tìm thấy
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<OrderDetail> findById(OrderDetailId id) {
        try {
            log.debug("Tìm chi tiết lệnh theo id, id = {}", id != null ? id.getValue() : "null");
            
            if (id == null) {
                return Optional.empty();
            }
            
            return orderDetailJpaRepository.findById(id.getValue())
                    .map(orderDetailPersistenceMapper::mapToDomainEntity);
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy chi tiết lệnh với id = {}", id.getValue());
            return Optional.empty();
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm chi tiết lệnh theo id, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm chi tiết lệnh theo id, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm chi tiết lệnh theo id: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm chi tiết lệnh theo id thất bại
     * @param e Ngoại lệ
     * @param id ID của chi tiết lệnh
     * @return Optional.empty()
     */
    @Recover
    public Optional<OrderDetail> recoverFindById(Exception e, OrderDetailId id) {
        log.error("Đã thử lại tìm chi tiết lệnh theo id 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm tất cả chi tiết lệnh theo orderId với xử lý ngoại lệ và thử lại
     * @param orderId ID của lệnh
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<OrderDetail> findAllByOrderId(OrderId orderId) {
        try {
            log.debug("Tìm tất cả chi tiết lệnh theo orderId, orderId = {}", orderId != null ? orderId.getValue() : "null");
            
            if (orderId == null) {
                return List.of();
            }
            
            List<OrderDetailJpaEntity> entities = orderDetailJpaRepository.findAllByOrderId(orderId.getValue());
            return orderDetailPersistenceMapper.mapToDomainEntities(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả chi tiết lệnh theo orderId, orderId = {}", orderId.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả chi tiết lệnh theo orderId, orderId = {}", orderId.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm tất cả chi tiết lệnh theo orderId: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả chi tiết lệnh theo orderId thất bại
     * @param e Ngoại lệ
     * @param orderId ID của lệnh
     * @return Danh sách rỗng
     */
    @Recover
    public List<OrderDetail> recoverFindAllByOrderId(Exception e, OrderId orderId) {
        log.error("Đã thử lại tìm tất cả chi tiết lệnh theo orderId 3 lần nhưng thất bại, orderId = {}", orderId.getValue(), e);
        return List.of();
    }

    /**
     * Tìm tất cả chi tiết lệnh theo danh sách orderId với xử lý ngoại lệ và thử lại
     * @param orderIds Danh sách ID của lệnh
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<OrderDetail> findAllByOrderIdIn(List<OrderId> orderIds) {
        try {
            log.debug("Tìm tất cả chi tiết lệnh theo danh sách orderId, orderIds.size = {}", orderIds != null ? orderIds.size() : 0);
            
            if (orderIds == null || orderIds.isEmpty()) {
                return List.of();
            }
            
            List<String> orderIdValues = orderIds.stream()
                    .map(OrderId::getValue)
                    .collect(Collectors.toList());
            
            List<OrderDetailJpaEntity> entities = orderDetailJpaRepository.findAllByOrderIdIn(orderIdValues);
            return orderDetailPersistenceMapper.mapToDomainEntities(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả chi tiết lệnh theo danh sách orderId, orderIds.size = {}", orderIds.size(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả chi tiết lệnh theo danh sách orderId, orderIds.size = {}", orderIds.size(), e);
            throw new DatabaseException("Lỗi khi tìm tất cả chi tiết lệnh theo danh sách orderId: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả chi tiết lệnh theo danh sách orderId thất bại
     * @param e Ngoại lệ
     * @param orderIds Danh sách ID của lệnh
     * @return Danh sách rỗng
     */
    @Recover
    public List<OrderDetail> recoverFindAllByOrderIdIn(Exception e, List<OrderId> orderIds) {
        log.error("Đã thử lại tìm tất cả chi tiết lệnh theo danh sách orderId 3 lần nhưng thất bại, orderIds.size = {}", orderIds.size(), e);
        return List.of();
    }

    /**
     * Tìm tất cả chi tiết lệnh theo khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<OrderDetail> findAllByTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm tất cả chi tiết lệnh theo khoảng thời gian, startTime = {}, endTime = {}", startTime, endTime);
            
            if (startTime == null || endTime == null) {
                return List.of();
            }
            
            List<OrderDetailJpaEntity> entities = orderDetailJpaRepository.findAllByTimeBetween(startTime, endTime);
            return orderDetailPersistenceMapper.mapToDomainEntities(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả chi tiết lệnh theo khoảng thời gian, startTime = {}, endTime = {}", startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả chi tiết lệnh theo khoảng thời gian, startTime = {}, endTime = {}", startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm tất cả chi tiết lệnh theo khoảng thời gian: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả chi tiết lệnh theo khoảng thời gian thất bại
     * @param e Ngoại lệ
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách rỗng
     */
    @Recover
    public List<OrderDetail> recoverFindAllByTimeBetween(Exception e, LocalDateTime startTime, LocalDateTime endTime) {
        log.error("Đã thử lại tìm tất cả chi tiết lệnh theo khoảng thời gian 3 lần nhưng thất bại, startTime = {}, endTime = {}", startTime, endTime, e);
        return List.of();
    }

    /**
     * Lưu chi tiết lệnh với xử lý ngoại lệ và thử lại
     * @param orderDetail Chi tiết lệnh cần lưu
     * @return Chi tiết lệnh đã được lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public OrderDetail save(OrderDetail orderDetail) {
        try {
            log.debug("Lưu chi tiết lệnh, id = {}", orderDetail != null && orderDetail.getId() != null ? orderDetail.getId().getValue() : "null");
            
            if (orderDetail == null) {
                throw new IllegalArgumentException("OrderDetail không được để trống");
            }
            
            OrderDetailJpaEntity entity = orderDetailPersistenceMapper.mapToJpaEntity(orderDetail);
            OrderDetailJpaEntity savedEntity = orderDetailJpaRepository.save(entity);
            
            return orderDetailPersistenceMapper.mapToDomainEntity(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu chi tiết lệnh, id = {}", orderDetail != null && orderDetail.getId() != null ? orderDetail.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu chi tiết lệnh, id = {}", orderDetail != null && orderDetail.getId() != null ? orderDetail.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu chi tiết lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu chi tiết lệnh thất bại
     * @param e Ngoại lệ
     * @param orderDetail Chi tiết lệnh cần lưu
     * @return null
     */
    @Recover
    public OrderDetail recoverSave(Exception e, OrderDetail orderDetail) {
        log.error("Đã thử lại lưu chi tiết lệnh 3 lần nhưng thất bại, id = {}", orderDetail != null && orderDetail.getId() != null ? orderDetail.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu chi tiết lệnh sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Lưu danh sách chi tiết lệnh với xử lý ngoại lệ và thử lại
     * @param orderDetails Danh sách chi tiết lệnh cần lưu
     * @return Danh sách chi tiết lệnh đã được lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<OrderDetail> saveAll(List<OrderDetail> orderDetails) {
        try {
            log.debug("Lưu danh sách chi tiết lệnh, size = {}", orderDetails != null ? orderDetails.size() : 0);
            
            if (orderDetails == null || orderDetails.isEmpty()) {
                return List.of();
            }
            
            List<OrderDetailJpaEntity> entities = orderDetailPersistenceMapper.mapToJpaEntities(orderDetails);
            List<OrderDetailJpaEntity> savedEntities = orderDetailJpaRepository.saveAll(entities);
            
            return orderDetailPersistenceMapper.mapToDomainEntities(savedEntities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu danh sách chi tiết lệnh, size = {}", orderDetails.size(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu danh sách chi tiết lệnh, size = {}", orderDetails.size(), e);
            throw new DatabaseException("Lỗi khi lưu danh sách chi tiết lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu danh sách chi tiết lệnh thất bại
     * @param e Ngoại lệ
     * @param orderDetails Danh sách chi tiết lệnh cần lưu
     * @return Danh sách rỗng
     */
    @Recover
    public List<OrderDetail> recoverSaveAll(Exception e, List<OrderDetail> orderDetails) {
        log.error("Đã thử lại lưu danh sách chi tiết lệnh 3 lần nhưng thất bại, size = {}", orderDetails.size(), e);
        throw new DatabaseException("Không thể lưu danh sách chi tiết lệnh sau 3 lần thử lại: " + e.getMessage(), e);
    }
}
