package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.FundingSettlementJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho FundingSettlementJpaEntity
 */
@Repository
public interface FundingSettlementJpaRepository extends JpaRepository<FundingSettlementJpaEntity, Long>, JpaSpecificationExecutor<FundingSettlementJpaEntity> {

    /**
     * Tìm tất cả các lần thanh toán tài trợ theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các lần thanh toán tài trợ
     */
    List<FundingSettlementJpaEntity> findAllBySymbol(String symbol);

    /**
     * Tìm tất cả các lần thanh toán tài trợ theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các lần thanh toán tài trợ
     */
    List<FundingSettlementJpaEntity> findAllBySymbolAndTimestampBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Tìm tất cả các lần thanh toán tài trợ theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các lần thanh toán tài trợ
     */
    List<FundingSettlementJpaEntity> findAllByMemberId(String memberId);

    /**
     * Tìm tất cả các lần thanh toán tài trợ theo positionId
     * @param positionId ID của vị thế
     * @return Danh sách các lần thanh toán tài trợ
     */
    List<FundingSettlementJpaEntity> findAllByPositionId(Long positionId);

    /**
     * Tìm tất cả các lần thanh toán tài trợ theo memberId và khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các lần thanh toán tài trợ
     */
    List<FundingSettlementJpaEntity> findAllByMemberIdAndTimestampBetween(String memberId, LocalDateTime startTime, LocalDateTime endTime);
}
