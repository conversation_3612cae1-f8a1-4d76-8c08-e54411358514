package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.valueobject.AccountOperationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA entity cho AccountTransaction
 */
@Entity
@Table(name = "contract_account_transaction")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountTransactionJpaEntity {

    @Id
    private String id;
    
    @Column(name = "member_id", nullable = false)
    private String memberId;
    
    @Column(name = "amount", nullable = false, columnDefinition = "decimal(18,8)")
    private BigDecimal amount;
    
    @Column(name = "balance", nullable = false, columnDefinition = "decimal(18,8)")
    private BigDecimal balance;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private AccountOperationType type;
    
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    @Column(name = "remark")
    private String remark;
}
