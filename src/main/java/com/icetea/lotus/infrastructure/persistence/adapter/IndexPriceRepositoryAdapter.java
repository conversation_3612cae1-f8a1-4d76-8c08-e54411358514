package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.IndexPrice;
import com.icetea.lotus.core.domain.repository.IndexPriceRepository;
import com.icetea.lotus.core.domain.valueobject.ContractId;
import com.icetea.lotus.core.domain.valueobject.IndexPriceId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.IndexPriceJpaEntity;
import com.icetea.lotus.infrastructure.persistence.repository.IndexPriceJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho IndexPriceRepository
 * Triển khai các phương thức của IndexPriceRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class IndexPriceRepositoryAdapter implements IndexPriceRepository {

    private final IndexPriceJpaRepository indexPriceJpaRepository;

    /**
     * Lưu giá chỉ số với xử lý ngoại lệ và thử lại
     * @param indexPrice Giá chỉ số
     * @return IndexPrice
     */
    @Override
    @Transactional(readOnly = false)
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 1000, multiplier = 2)
    )
    public IndexPrice save(IndexPrice indexPrice) {
        try {
            log.debug("Lưu giá chỉ số, symbol = {}",
                    indexPrice != null && indexPrice.getSymbol() != null ? indexPrice.getSymbol().getValue() : "null");

            if (indexPrice == null) {
                throw new IllegalArgumentException("IndexPrice không được để trống");
            }

            IndexPriceJpaEntity entity = mapToJpaEntity(indexPrice);
            IndexPriceJpaEntity savedEntity = indexPriceJpaRepository.save(entity);

            log.debug("Đã lưu giá chỉ số thành công, id = {}", savedEntity.getId());

            return mapToDomainEntity(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giá chỉ số, symbol = {}",
                    indexPrice != null && indexPrice.getSymbol() != null ? indexPrice.getSymbol().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giá chỉ số, symbol = {}",
                    indexPrice != null && indexPrice.getSymbol() != null ? indexPrice.getSymbol().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu giá chỉ số: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu giá chỉ số thất bại
     * @param e Ngoại lệ
     * @param indexPrice Giá chỉ số
     * @return null
     */
    @Recover
    public IndexPrice recoverSave(Exception e, IndexPrice indexPrice) {
        log.error("Đã thử lại lưu giá chỉ số 3 lần nhưng thất bại, symbol = {}",
                indexPrice != null && indexPrice.getSymbol() != null ? indexPrice.getSymbol().getValue() : "null", e);

        // Thay vì ném ngoại lệ, trả về đối tượng IndexPrice ban đầu
        // Điều này giúp ứng dụng tiếp tục hoạt động ngay cả khi không thể lưu vào cơ sở dữ liệu
        return indexPrice;
    }

    /**
     * Tìm giá chỉ số theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của giá chỉ số
     * @return Optional<IndexPrice>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<IndexPrice> findById(IndexPriceId id) {
        try {
            log.debug("Tìm giá chỉ số theo ID, id = {}", id != null ? id.getValue() : "null");

            if (id == null) {
                return Optional.empty();
            }

            // Chuyển đổi String sang Long
            Long idLong;
            try {
                idLong = Long.parseLong(id.getValue());
            } catch (NumberFormatException e) {
                log.error("Không thể chuyển đổi ID từ String sang Long, id = {}", id.getValue(), e);
                return Optional.empty();
            }

            return indexPriceJpaRepository.findById(idLong)
                    .map(this::mapToDomainEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm giá chỉ số theo ID, id = {}", id != null ? id.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giá chỉ số theo ID, id = {}", id != null ? id.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi tìm giá chỉ số theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm giá chỉ số mới nhất theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return IndexPrice
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 1000, multiplier = 2)
    )
    public IndexPrice findTopBySymbolOrderByCreateTimeDesc(Symbol symbol) {
        try {
            log.debug("Tìm giá chỉ số mới nhất theo symbol, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return null;
            }

            IndexPriceJpaEntity entity = indexPriceJpaRepository.findTopBySymbolOrderByCreateTimeDesc(symbol.getValue());
            return entity != null ? mapToDomainEntity(entity) : null;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm giá chỉ số mới nhất theo symbol, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giá chỉ số mới nhất theo symbol, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi tìm giá chỉ số mới nhất theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm danh sách giá chỉ số theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return List<IndexPrice>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 1000, multiplier = 2)
    )
    public List<IndexPrice> findBySymbol(Symbol symbol) {
        try {
            log.debug("Tìm danh sách giá chỉ số theo symbol, symbol = {}", symbol != null ? symbol.getValue() : "null");

            if (symbol == null) {
                return Collections.emptyList();
            }

            List<IndexPriceJpaEntity> entities = indexPriceJpaRepository.findBySymbol(symbol.getValue());
            return entities.stream()
                    .map(this::mapToDomainEntity)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giá chỉ số theo symbol, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giá chỉ số theo symbol, symbol = {}", symbol != null ? symbol.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi tìm danh sách giá chỉ số theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm danh sách giá chỉ số theo symbol và khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return List<IndexPrice>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 1000, multiplier = 2)
    )
    public List<IndexPrice> findBySymbolAndCreateTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm danh sách giá chỉ số theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol != null ? symbol.getValue() : "null", startTime, endTime);

            if (symbol == null || startTime == null || endTime == null) {
                return Collections.emptyList();
            }

            List<IndexPriceJpaEntity> entities = indexPriceJpaRepository.findBySymbolAndCreateTimeBetween(
                    symbol.getValue(), startTime, endTime);
            return entities.stream()
                    .map(this::mapToDomainEntity)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giá chỉ số theo symbol và khoảng thời gian, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giá chỉ số theo symbol và khoảng thời gian, symbol = {}",
                    symbol != null ? symbol.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi tìm danh sách giá chỉ số theo symbol và khoảng thời gian: " + e.getMessage(), e);
        }
    }

    /**
     * Xóa giá chỉ số theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của giá chỉ số
     */
    @Override
    @Transactional(readOnly = false)
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 1000, multiplier = 2)
    )
    public void deleteById(IndexPriceId id) {
        try {
            log.debug("Xóa giá chỉ số theo ID, id = {}", id != null ? id.getValue() : "null");

            if (id == null) {
                return;
            }

            // Chuyển đổi String sang Long
            Long idLong;
            try {
                idLong = Long.parseLong(id.getValue());
            } catch (NumberFormatException e) {
                log.error("Không thể chuyển đổi ID từ String sang Long, id = {}", id.getValue(), e);
                return;
            }

            indexPriceJpaRepository.deleteById(idLong);
            log.debug("Đã xóa giá chỉ số thành công, id = {}", id.getValue());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi xóa giá chỉ số theo ID, id = {}", id != null ? id.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa giá chỉ số theo ID, id = {}", id != null ? id.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi xóa giá chỉ số theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Xóa giá chỉ số cũ hơn thời gian chỉ định với xử lý ngoại lệ và thử lại
     * @param time Thời gian
     * @return Số lượng bản ghi đã xóa
     */
    @Override
    @Transactional(readOnly = false)
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @org.springframework.retry.annotation.Backoff(delay = 1000, multiplier = 2)
    )
    public int deleteByCreateTimeBefore(LocalDateTime time) {
        try {
            log.debug("Xóa giá chỉ số cũ hơn thời gian chỉ định, time = {}", time);

            if (time == null) {
                return 0;
            }

            int count = indexPriceJpaRepository.deleteByCreateTimeBefore(time);
            log.debug("Đã xóa {} giá chỉ số cũ hơn thời gian chỉ định, time = {}", count, time);
            return count;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi xóa giá chỉ số cũ hơn thời gian chỉ định, time = {}", time, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa giá chỉ số cũ hơn thời gian chỉ định, time = {}", time, e);
            throw new DatabaseException("Lỗi khi xóa giá chỉ số cũ hơn thời gian chỉ định: " + e.getMessage(), e);
        }
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    private IndexPrice mapToDomainEntity(IndexPriceJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        return IndexPrice.builder()
                .id(IndexPriceId.of(entity.getId().toString()))
                .contractId(entity.getContractId() != null ? ContractId.of(entity.getContractId()) : null)
                .symbol(Symbol.of(entity.getSymbol()))
                .price(Money.of(entity.getPrice()))
                .referencePrices(entity.getReferencePrices() != null ?
                        new HashMap<>() : new HashMap<>()) // Cần triển khai chuyển đổi referencePrices
                .createTime(entity.getCreateTime())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domainEntity Domain entity
     * @return JPA entity
     */
    private IndexPriceJpaEntity mapToJpaEntity(IndexPrice domainEntity) {
        if (domainEntity == null) {
            return null;
        }

        return IndexPriceJpaEntity.builder()
                .id(domainEntity.getId() != null ? parseLongOrNull(domainEntity.getId().getValue()) : null)
                .contractId(domainEntity.getContractId() != null ? domainEntity.getContractId().getValue() : null)
                .symbol(domainEntity.getSymbol().getValue())
                .price(domainEntity.getPrice().getValue())
                .referencePrices(null) // Cần triển khai chuyển đổi referencePrices
                .createTime(domainEntity.getCreateTime())
                .build();
    }

    /**
     * Chuyển đổi String sang Long, trả về null nếu không thể chuyển đổi
     * @param value Giá trị String cần chuyển đổi
     * @return Long hoặc null
     */
    private Long parseLongOrNull(String value) {
        if (value == null) {
            return null;
        }

        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            log.error("Không thể chuyển đổi giá trị từ String sang Long, value = {}", value, e);
            return null;
        }
    }
}
