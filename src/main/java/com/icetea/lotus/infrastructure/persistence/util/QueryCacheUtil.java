package com.icetea.lotus.infrastructure.persistence.util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Tiện ích để caching truy vấn
 */
@Slf4j
public class QueryCacheUtil {
    
    private static final Cache<String, Object> queryCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();
    
    /**
     * Lấy kết quả từ cache hoặc thực hiện truy vấn
     * @param cacheKey Khóa cache
     * @param supplier Supplier
     * @param <T> Kiểu dữ liệu
     * @return T
     */
    public static <T> T getOrExecute(String cacheKey, Supplier<T> supplier) {
        @SuppressWarnings("unchecked")
        T result = (T) queryCache.getIfPresent(cacheKey);
        
        if (result == null) {
            result = supplier.get();
            queryCache.put(cacheKey, result);
        }
        
        return result;
    }
    
    /**
     * Lấy kết quả từ cache hoặc thực hiện truy vấn với Optional
     * @param cacheKey Khóa cache
     * @param supplier Supplier
     * @param <T> Kiểu dữ liệu
     * @return Optional<T>
     */
    public static <T> Optional<T> getOrExecuteOptional(String cacheKey, Supplier<Optional<T>> supplier) {
        @SuppressWarnings("unchecked")
        Optional<T> result = (Optional<T>) queryCache.getIfPresent(cacheKey);
        
        if (result == null) {
            result = supplier.get();
            queryCache.put(cacheKey, result);
        }
        
        return result;
    }
    
    /**
     * Lấy kết quả từ cache hoặc thực hiện truy vấn với List
     * @param cacheKey Khóa cache
     * @param supplier Supplier
     * @param <T> Kiểu dữ liệu
     * @return List<T>
     */
    public static <T> List<T> getOrExecuteList(String cacheKey, Supplier<List<T>> supplier) {
        @SuppressWarnings("unchecked")
        List<T> result = (List<T>) queryCache.getIfPresent(cacheKey);
        
        if (result == null) {
            result = supplier.get();
            queryCache.put(cacheKey, result);
        }
        
        return result;
    }
    
    /**
     * Lấy kết quả từ cache hoặc thực hiện truy vấn với Page
     * @param cacheKey Khóa cache
     * @param supplier Supplier
     * @param <T> Kiểu dữ liệu
     * @return Page<T>
     */
    public static <T> Page<T> getOrExecutePage(String cacheKey, Supplier<Page<T>> supplier) {
        @SuppressWarnings("unchecked")
        Page<T> result = (Page<T>) queryCache.getIfPresent(cacheKey);
        
        if (result == null) {
            result = supplier.get();
            queryCache.put(cacheKey, result);
        }
        
        return result;
    }
    
    /**
     * Tạo khóa cache cho truy vấn
     * @param entityClass Class<?>
     * @param methodName Tên phương thức
     * @param args Tham số
     * @return String
     */
    public static String createCacheKey(Class<?> entityClass, String methodName, Object... args) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(entityClass.getSimpleName()).append("_").append(methodName);
        
        for (Object arg : args) {
            if (arg != null) {
                if (arg instanceof Specification) {
                    keyBuilder.append("_Spec");
                } else if (arg instanceof Pageable) {
                    Pageable pageable = (Pageable) arg;
                    keyBuilder.append("_Page").append(pageable.getPageNumber())
                            .append("_Size").append(pageable.getPageSize())
                            .append("_Sort").append(pageable.getSort());
                } else {
                    keyBuilder.append("_").append(arg);
                }
            } else {
                keyBuilder.append("_null");
            }
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * Xóa cache
     * @param cacheKey Khóa cache
     */
    public static void invalidate(String cacheKey) {
        queryCache.invalidate(cacheKey);
    }
    
    /**
     * Xóa tất cả cache
     */
    public static void invalidateAll() {
        queryCache.invalidateAll();
    }
}
