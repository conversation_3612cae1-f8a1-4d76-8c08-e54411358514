package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.PricingMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * JPA Entity cho PriceConfiguration
 */
@Entity
@Table(name = "contract_price_configuration")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PriceConfigurationJpaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "symbol")
    private String symbol;

    @Enumerated(EnumType.STRING)
    @Column(name = "index_price_method")
    private PricingMethod indexPriceMethod;

    @Enumerated(EnumType.STRING)
    @Column(name = "mark_price_method")
    private PricingMethod markPriceMethod;

    @Column(name = "custom_index_price_formula", columnDefinition = "TEXT")
    private String customIndexPriceFormula;

    @Column(name = "custom_mark_price_formula", columnDefinition = "TEXT")
    private String customMarkPriceFormula;

    @Column(name = "parameters", columnDefinition = "TEXT")
    private String parameters;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
