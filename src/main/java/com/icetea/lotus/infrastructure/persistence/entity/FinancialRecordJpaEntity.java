package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.valueobject.FinancialRecordType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA entity cho FinancialRecord
 */
@Entity
@Table(name = "contract_financial_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinancialRecordJpaEntity {

    @Id
    private String id;

    @Column(name = "member_id")
    private Long memberId;

    @Column(name = "position_id")
    private Long positionId;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private FinancialRecordType type;

    @Column(name = "amount", columnDefinition = "decimal(18,8)")
    private BigDecimal amount;

    @Column(name = "fee", columnDefinition = "decimal(18,8)")
    private BigDecimal fee;

    @Column(name = "profit", columnDefinition = "decimal(18,8)")
    private BigDecimal profit;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "remark")
    private String remark;
}
