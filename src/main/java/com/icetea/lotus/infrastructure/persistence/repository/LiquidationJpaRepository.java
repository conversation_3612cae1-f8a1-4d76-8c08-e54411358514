package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.LiquidationJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JPA Repository cho LiquidationJpaEntity
 */
@Repository
public interface LiquidationJpaRepository extends JpaRepository<LiquidationJpaEntity, Long>, JpaSpecificationExecutor<LiquidationJpaEntity> {
    
    /**
     * Tìm thanh lý theo positionId
     * @param positionId ID của vị thế
     * @return Danh sách các thanh lý
     */
    List<LiquidationJpaEntity> findByPositionId(Long positionId);
    
    /**
     * Tìm thanh lý theo liquidationOrderId
     * @param liquidationOrderId ID của lệnh thanh lý
     * @return Optional chứa thanh lý nếu tìm thấy
     */
    Optional<LiquidationJpaEntity> findByLiquidationOrderId(String liquidationOrderId);
    
    /**
     * Tìm thanh lý theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các thanh lý
     */
    List<LiquidationJpaEntity> findByMemberId(Long memberId);
    
    /**
     * Tìm thanh lý theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Danh sách các thanh lý
     */
    List<LiquidationJpaEntity> findByMemberIdAndSymbol(Long memberId, String symbol);
}
