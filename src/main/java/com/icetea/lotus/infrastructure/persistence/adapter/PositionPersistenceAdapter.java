package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.PositionPersistencePort;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.repository.PositionRepository;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.icetea.lotus.infrastructure.persistence.entity.PositionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.PositionPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.PositionJpaRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Adapter cho PositionPersistencePort
 * Triển khai các phương thức của PositionPersistencePort
 */
@Component("positionPersistenceAdapter")
@Primary
@RequiredArgsConstructor
@Slf4j
@Transactional
public class PositionPersistenceAdapter implements PositionPersistencePort, PositionRepository {

    @PersistenceContext
    private EntityManager entityManager;

    private final PositionJpaRepository positionJpaRepository;
    private final PositionPersistenceMapper positionPersistenceMapper;

    /**
     * Tìm vị thế theo id với xử lý ngoại lệ và thử lại
     * @param id ID của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Position> findById(PositionId id) {
        try {
            log.debug("Tìm vị thế theo id, id = {}", id.getValue());
            Optional<PositionJpaEntity> entity = positionJpaRepository.findById(id.getValue());

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy vị thế, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy vị thế, id = {}", id.getValue());
            }

            return entity.map(positionPersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm vị thế theo id, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm vị thế theo id, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm vị thế theo id: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm vị thế theo id thất bại
     * @param e Ngoại lệ
     * @param id ID của vị thế
     * @return Optional<Position>
     */
    @Recover
    public Optional<Position> recoverFindById(Exception e, PositionId id) {
        log.error("Đã thử lại tìm vị thế theo id 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm vị thế theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    public Optional<Position> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        Optional<PositionJpaEntity> entity = positionJpaRepository.findByMemberIdAndSymbolAndStatus(memberId, symbol.getValue(), PositionStatus.OPEN);
        return entity.map(positionPersistenceMapper::entityToDomain);
    }

    /**
     * Tìm vị thế theo memberId, symbol và status
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    public Optional<Position> findByMemberIdAndSymbolAndStatus(Long memberId, Symbol symbol, PositionStatus status) {
        Optional<PositionJpaEntity> entity = positionJpaRepository.findByMemberIdAndSymbolAndStatus(memberId, symbol.getValue(), status);
        return entity.map(positionPersistenceMapper::entityToDomain);
    }

    /**
     * Tìm tất cả các vị thế theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberIdAndSymbol(memberId, symbol.getValue());
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm vị thế theo memberId, symbol và direction
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    public Optional<Position> findByMemberIdAndSymbolAndDirection(Long memberId, Symbol symbol, PositionDirection direction) {
        Optional<PositionJpaEntity> entity = positionJpaRepository.findByMemberIdAndSymbolAndDirection(memberId, symbol.getValue(), direction);
        return entity.map(positionPersistenceMapper::entityToDomain);
    }

    /**
     * Tìm tất cả các vị thế theo memberId, symbol và direction
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberIdAndSymbolAndDirection(Long memberId, Symbol symbol, PositionDirection direction) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberIdAndSymbolAndDirection(memberId, symbol.getValue(), direction);
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm tất cả các vị thế theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberId(Long memberId) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberId(memberId);
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm tất cả các vị thế theo memberId và status
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberIdAndStatus(Long memberId, PositionStatus status) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberIdAndStatus(memberId, status);
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm tất cả các vị thế theo memberId, status và khoảng thời gian với phân trang
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param page Số trang
     * @param size Kích thước trang
     * @return Page chứa danh sách các vị thế
     */
    @Override
    public Page<Position> findByMemberIdAndStatusAndTimeRange(Long memberId, PositionStatus status,
                                                              LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<PositionJpaEntity> query = cb.createQuery(PositionJpaEntity.class);
        Root<PositionJpaEntity> root = query.from(PositionJpaEntity.class);

        // Helper method to build predicates
        List<Predicate> mainPredicates = buildPredicates(cb, root, memberId, status, startTime, endTime);

        query.where(mainPredicates.toArray(new Predicate[0]));
        query.orderBy(cb.desc(root.get("createTime")));

        TypedQuery<PositionJpaEntity> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<PositionJpaEntity> results = typedQuery.getResultList();

        // Count query for total elements
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<PositionJpaEntity> countRoot = countQuery.from(PositionJpaEntity.class);
        countQuery.select(cb.count(countRoot));

        // Tạo predicates riêng cho count query
        List<Predicate> countPredicates = buildPredicates(cb, countRoot, memberId, status, startTime, endTime);
        countQuery.where(countPredicates.toArray(new Predicate[0]));

        Long total = entityManager.createQuery(countQuery).getSingleResult();

        // Tạo Page<PositionJpaEntity> trước
        Page<PositionJpaEntity> entityPage = new PageImpl<>(results, pageable, total);

        // Map sang Page<Position>
        return entityPage.map(positionPersistenceMapper::entityToDomain);
    }

    // Helper method to build predicates
    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<PositionJpaEntity> root,
                                            Long memberId, PositionStatus status,
                                            LocalDateTime startTime, LocalDateTime endTime) {
        List<Predicate> predicates = new ArrayList<>();

        // memberId condition
        predicates.add(cb.equal(root.get("memberId"), memberId));

        // status condition
        if (status != null) {
            predicates.add(cb.equal(root.get("status"), status));
        }

        // startTime condition
        if (startTime != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), startTime));
        }

        // endTime condition
        if (endTime != null) {
            predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), endTime));
        }

        return predicates;
    }


    /**
     * Tìm tất cả các vị thế theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllBySymbol(Symbol symbol) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllBySymbol(symbol.getValue());
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm tất cả các vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAll() {
        List<PositionJpaEntity> entities = positionJpaRepository.findAll();
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Lưu vị thế với xử lý ngoại lệ và thử lại
     * @param position Vị thế cần lưu
     * @return Vị thế đã được lưu
     */
    @Override
    @Transactional
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position save(Position position) {
        // Kiểm tra position có null hay không
        if (position == null) {
            log.error("Không thể lưu vị thế vì position là null");
            throw new IllegalArgumentException("Position không thể là null");
        }

        try {
            log.debug("Lưu vị thế, id = {}, memberId = {}, symbol = {}, direction = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    position.getMemberId(),
                    position.getSymbol() != null ? position.getSymbol().getValue() : "null",
                    position.getDirection());

            PositionJpaEntity entity = positionPersistenceMapper.domainToEntity(position);
            PositionJpaEntity savedEntity = positionJpaRepository.save(entity);

            log.debug("Đã lưu vị thế thành công, id = {}", savedEntity.getId());

            return positionPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu vị thế, id = {}",
                    position.getId() != null ? position.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu vị thế, id = {}",
                    position.getId() != null ? position.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế cần lưu
     * @return Position
     */
    @Recover
    public Position recoverSave(Exception e, Position position) {
        if (position == null) {
            log.error("Đã thử lại lưu vị thế 3 lần nhưng thất bại, position là null", e);
            throw new DatabaseException("Không thể lưu vị thế sau 3 lần thử lại vì position là null: " + e.getMessage(), e);
        }

        log.error("Đã thử lại lưu vị thế 3 lần nhưng thất bại, id = {}",
                position.getId() != null ? position.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu vị thế sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Xóa vị thế
     * @param position Vị thế cần xóa
     */
    @Override
    public void delete(Position position) {
        PositionJpaEntity entity = positionPersistenceMapper.domainToEntity(position);
        positionJpaRepository.delete(entity);
    }

    /**
     * Tính tổng khối lượng vị thế theo symbol, direction và status
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @param status Trạng thái của vị thế
     * @return Tổng khối lượng
     */
    @Override
    public BigDecimal sumVolumeBySymbolAndDirectionAndStatus(Symbol symbol, PositionDirection direction, PositionStatus status) {
        BigDecimal sum = positionJpaRepository.sumVolumeBySymbolAndDirectionAndStatus(symbol.getValue(), direction, status);
        return sum != null ? sum : BigDecimal.ZERO;
    }

    /**
     * Tìm tất cả các vị thế theo symbol và direction
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllBySymbolAndDirection(Symbol symbol, PositionDirection direction) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllBySymbolAndDirection(symbol.getValue(), direction);
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm tất cả các vị thế theo trạng thái
     * @param status Trạng thái của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByStatus(PositionStatus status) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllByStatus(status);
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm tất cả các vị thế theo symbol và status
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllBySymbolAndStatus(Symbol symbol, PositionStatus status) {
        List<PositionJpaEntity> entities = positionJpaRepository.findAllBySymbolAndStatus(symbol.getValue(), status);
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm các vị thế có nguy cơ cao theo symbol
     * @param symbol Symbol của hợp đồng
     * @param marginRatio Tỷ lệ ký quỹ tối thiểu
     * @param limit Số lượng vị thế tối đa
     * @return Danh sách các vị thế có nguy cơ cao
     */
    @Override
    public List<Position> findRiskPositionsBySymbol(Symbol symbol, BigDecimal marginRatio, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<PositionJpaEntity> entities = positionJpaRepository.findRiskPositionsBySymbol(symbol.getValue(), marginRatio, pageable);
        return entities.stream()
                .map(positionPersistenceMapper::entityToDomain)
                .toList();
    }
}
