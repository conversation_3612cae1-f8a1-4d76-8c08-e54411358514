package com.icetea.lotus.infrastructure.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho FundingRate
 */
@Entity
@Table(name = "contract_funding_rate")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FundingRateJpaEntity {

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * ID của hợp đồng
     */
    @Column(name = "contract_id")
    private Long contractId;

    /**
     * Ký hiệu của hợp đồng
     */
    @Column(name = "symbol")
    private String symbol;

    /**
     * Tỷ lệ tài trợ
     */
    @Column(name = "rate")
    private BigDecimal rate;

    /**
     * <PERSON><PERSON><PERSON> đánh dấu
     */
    @Column(name = "mark_price")
    private BigDecimal markPrice;

    /**
     * Gi<PERSON> chỉ số
     */
    @Column(name = "index_price")
    private BigDecimal indexPrice;

    /**
     * Thời gian
     */
    @Column(name = "time")
    private LocalDateTime time;

    /**
     * Thời gian funding payment tiếp theo
     */
    @Column(name = "next_time")
    private LocalDateTime nextTime;
}
