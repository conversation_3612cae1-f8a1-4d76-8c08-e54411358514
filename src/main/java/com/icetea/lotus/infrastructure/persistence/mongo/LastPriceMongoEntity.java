package com.icetea.lotus.infrastructure.persistence.mongo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * MongoDB Entity cho LastPrice
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "last_price")
public class LastPriceMongoEntity {
    
    @Id
    private String id;
    
    private String symbol;
    
    private BigDecimal price;
    
    private BigDecimal volume;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
}
