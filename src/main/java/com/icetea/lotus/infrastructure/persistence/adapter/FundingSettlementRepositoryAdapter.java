package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.FundingSettlement;
import com.icetea.lotus.core.domain.repository.FundingSettlementRepository;
import com.icetea.lotus.core.domain.valueobject.FundingSettlementId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.FundingSettlementJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.FundingSettlementPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.FundingSettlementJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Adapter cho FundingSettlementRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class FundingSettlementRepositoryAdapter implements FundingSettlementRepository {

    private final FundingSettlementJpaRepository fundingSettlementJpaRepository;
    private final FundingSettlementPersistenceMapper fundingSettlementPersistenceMapper;

    /**
     * Lưu một FundingSettlement với xử lý ngoại lệ và thử lại
     * @param fundingSettlement FundingSettlement cần lưu
     * @return FundingSettlement đã lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public FundingSettlement save(FundingSettlement fundingSettlement) {
        try {
            log.debug("Lưu FundingSettlement, id = {}", fundingSettlement.getId().getValue());

            FundingSettlementJpaEntity entity = fundingSettlementPersistenceMapper.domainToEntity(fundingSettlement);
            FundingSettlementJpaEntity savedEntity = fundingSettlementJpaRepository.save(entity);

            log.debug("Đã lưu FundingSettlement thành công, id = {}", savedEntity.getId());

            return fundingSettlementPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu FundingSettlement, id = {}", fundingSettlement.getId().getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu FundingSettlement, id = {}", fundingSettlement.getId().getValue(), e);
            throw new DatabaseException("Lỗi khi lưu FundingSettlement: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu FundingSettlement thất bại
     * @param e Ngoại lệ
     * @param fundingSettlement FundingSettlement cần lưu
     * @return FundingSettlement
     */
    @Recover
    public FundingSettlement recoverSave(Exception e, FundingSettlement fundingSettlement) {
        log.error("Đã thử lại lưu FundingSettlement 3 lần nhưng thất bại, id = {}", fundingSettlement.getId().getValue(), e);
        throw new DatabaseException("Không thể lưu FundingSettlement sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm FundingSettlement theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của FundingSettlement
     * @return FundingSettlement nếu tìm thấy, null nếu không
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public FundingSettlement findById(FundingSettlementId id) {
        try {
            log.debug("Tìm FundingSettlement theo ID, id = {}", id.getValue());

            FundingSettlementJpaEntity entity = fundingSettlementJpaRepository.findById(id.getValue()).orElse(null);

            if (entity == null) {
                log.debug("Không tìm thấy FundingSettlement, id = {}", id.getValue());
                return null;
            }

            log.debug("Đã tìm thấy FundingSettlement, id = {}", id.getValue());

            return fundingSettlementPersistenceMapper.entityToDomain(entity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm FundingSettlement theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm FundingSettlement theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm FundingSettlement theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm FundingSettlement theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của FundingSettlement
     * @return null
     */
    @Recover
    public FundingSettlement recoverFindById(Exception e, FundingSettlementId id) {
        log.error("Đã thử lại tìm FundingSettlement theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return null;
    }

    /**
     * Tìm tất cả FundingSettlement theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các FundingSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FundingSettlement> findAllBySymbol(Symbol symbol) {
        try {
            log.debug("Tìm tất cả FundingSettlement theo symbol, symbol = {}", symbol.getValue());

            List<FundingSettlementJpaEntity> entities = fundingSettlementJpaRepository.findAllBySymbol(symbol.getValue());

            log.debug("Đã tìm thấy {} FundingSettlement, symbol = {}", entities.size(), symbol.getValue());

            return fundingSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả FundingSettlement theo symbol, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả FundingSettlement theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm tất cả FundingSettlement theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả FundingSettlement theo symbol thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @return Danh sách rỗng
     */
    @Recover
    public List<FundingSettlement> recoverFindAllBySymbol(Exception e, Symbol symbol) {
        log.error("Đã thử lại tìm tất cả FundingSettlement theo symbol 3 lần nhưng thất bại, symbol = {}", symbol.getValue(), e);
        return List.of();
    }

    /**
     * Tìm tất cả FundingSettlement theo symbol và khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các FundingSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FundingSettlement> findAllBySymbolAndTimestampBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm tất cả FundingSettlement theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), startTime, endTime);

            List<FundingSettlementJpaEntity> entities = fundingSettlementJpaRepository.findAllBySymbolAndTimestampBetween(
                    symbol.getValue(), startTime, endTime);

            log.debug("Đã tìm thấy {} FundingSettlement, symbol = {}, startTime = {}, endTime = {}",
                    entities.size(), symbol.getValue(), startTime, endTime);

            return fundingSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả FundingSettlement theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả FundingSettlement theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm tất cả FundingSettlement theo symbol và khoảng thời gian: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả FundingSettlement theo memberId với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @return Danh sách các FundingSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FundingSettlement> findAllByMemberId(String memberId) {
        try {
            log.debug("Tìm tất cả FundingSettlement theo memberId, memberId = {}", memberId);

            List<FundingSettlementJpaEntity> entities = fundingSettlementJpaRepository.findAllByMemberId(memberId);

            log.debug("Đã tìm thấy {} FundingSettlement, memberId = {}", entities.size(), memberId);

            return fundingSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả FundingSettlement theo memberId, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả FundingSettlement theo memberId, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả FundingSettlement theo memberId: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả FundingSettlement theo memberId và khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các FundingSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FundingSettlement> findAllByMemberIdAndTimestampBetween(String memberId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm tất cả FundingSettlement theo memberId và khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime);

            List<FundingSettlementJpaEntity> entities = fundingSettlementJpaRepository.findAllByMemberIdAndTimestampBetween(
                    memberId, startTime, endTime);

            log.debug("Đã tìm thấy {} FundingSettlement, memberId = {}, startTime = {}, endTime = {}",
                    entities.size(), memberId, startTime, endTime);

            return fundingSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả FundingSettlement theo memberId và khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả FundingSettlement theo memberId và khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm tất cả FundingSettlement theo memberId và khoảng thời gian: " + e.getMessage(), e);
        }
    }
}
