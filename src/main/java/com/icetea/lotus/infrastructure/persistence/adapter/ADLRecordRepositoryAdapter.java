package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.ADLRecord;
import com.icetea.lotus.core.domain.repository.ADLRecordRepository;
import com.icetea.lotus.core.domain.valueobject.ADLRecordId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.ADLRecordJpaEntity;
import com.icetea.lotus.infrastructure.persistence.repository.ADLRecordJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho ADLRecordRepository
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class ADLRecordRepositoryAdapter implements ADLRecordRepository {

    private final ADLRecordJpaRepository adlRecordJpaRepository;

    /**
     * Lưu ADL record với xử lý ngoại lệ và thử lại
     * @param adlRecord ADL record
     * @return ADLRecord
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public ADLRecord save(ADLRecord adlRecord) {
        try {
            log.debug("Lưu ADL record, id = {}, memberId = {}, symbol = {}",
                    adlRecord.getId(), adlRecord.getMemberId(), adlRecord.getSymbol().getValue());

            ADLRecordJpaEntity jpaEntity = mapToJpaEntity(adlRecord);
            ADLRecordJpaEntity savedEntity = adlRecordJpaRepository.save(jpaEntity);

            log.debug("Đã lưu ADL record, id = {}", savedEntity.getId());

            return mapToDomainEntity(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu ADL record, id = {}",
                    adlRecord.getId() != null ? adlRecord.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu ADL record, id = {}",
                    adlRecord.getId() != null ? adlRecord.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu ADL record: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu ADL record thất bại
     * @param e Ngoại lệ
     * @param adlRecord ADL record
     * @return ADLRecord
     */
    @Recover
    public ADLRecord recoverSave(Exception e, ADLRecord adlRecord) {
        log.error("Đã thử lại lưu ADL record 3 lần nhưng thất bại, id = {}",
                adlRecord.getId() != null ? adlRecord.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu ADL record sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm ADL record theo ID với xử lý ngoại lệ
     * @param id ID của ADL record
     * @return Optional<ADLRecord>
     */
    @Override
    public Optional<ADLRecord> findById(ADLRecordId id) {
        try {
            log.debug("Tìm ADL record theo ID, id = {}", id.getValue());

            Optional<ADLRecord> result = adlRecordJpaRepository.findById(id.getValue())
                    .map(this::mapToDomainEntity);

            if (result.isPresent()) {
                log.debug("Đã tìm thấy ADL record, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy ADL record, id = {}", id.getValue());
            }

            return result;
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy ADL record với ID = {}", id.getValue());
            return Optional.empty();
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm ADL record theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm ADL record theo ID: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm ADL record theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm ADL record theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả ADL record theo positionId với xử lý ngoại lệ
     * @param positionId ID của vị thế
     * @return List<ADLRecord>
     */
    @Override
    public List<ADLRecord> findAllByPositionId(Long positionId) {
        try {
            log.debug("Tìm tất cả ADL record theo positionId, positionId = {}", positionId);

            List<ADLRecord> result = adlRecordJpaRepository.findAllByPositionId(positionId)
                    .stream()
                    .map(this::mapToDomainEntity)
                    .collect(Collectors.toList());

            log.debug("Đã tìm thấy {} ADL record theo positionId = {}", result.size(), positionId);

            return result;
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm tất cả ADL record theo positionId, positionId = {}", positionId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả ADL record theo positionId: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả ADL record theo positionId, positionId = {}", positionId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả ADL record theo positionId: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ADLRecord> findAllByMemberId(Long memberId) {
        return adlRecordJpaRepository.findAllByMemberId(memberId)
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<ADLRecord> findAllBySymbol(Symbol symbol) {
        return adlRecordJpaRepository.findAllBySymbol(symbol.getValue())
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<ADLRecord> findAllByMemberIdAndTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime) {
        return adlRecordJpaRepository.findAllByMemberIdAndTimeBetween(memberId, startTime, endTime)
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<ADLRecord> findAllBySymbolAndTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        return adlRecordJpaRepository.findAllBySymbolAndTimeBetween(symbol.getValue(), startTime, endTime)
                .stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    private ADLRecord mapToDomainEntity(ADLRecordJpaEntity jpaEntity) {
        return ADLRecord.builder()
                .id(ADLRecordId.of(jpaEntity.getId()))
                .positionId(jpaEntity.getPositionId())
                .memberId(jpaEntity.getMemberId())
                .symbol(Symbol.of(jpaEntity.getSymbol()))
                .direction(jpaEntity.getDirection())
                .volume(Money.of(jpaEntity.getVolume()))
                .price(Money.of(jpaEntity.getPrice()))
                .time(jpaEntity.getTime())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domainEntity Domain entity
     * @return JPA entity
     */
    private ADLRecordJpaEntity mapToJpaEntity(ADLRecord domainEntity) {
        ADLRecordJpaEntity.ADLRecordJpaEntityBuilder builder = ADLRecordJpaEntity.builder()
                .positionId(domainEntity.getPositionId())
                .memberId(domainEntity.getMemberId())
                .symbol(domainEntity.getSymbol().getValue())
                .direction(domainEntity.getDirection())
                .volume(domainEntity.getVolume().getValue())
                .price(domainEntity.getPrice().getValue())
                .time(domainEntity.getTime());

        // Chỉ set ID nếu đã có (update case), không set nếu null (insert case)
        if (domainEntity.getId() != null) {
            builder.id(domainEntity.getId().getValue());
        }
        // Nếu ID là null, JPA sẽ để database tự động tạo với IDENTITY strategy

        return builder.build();
    }
}
