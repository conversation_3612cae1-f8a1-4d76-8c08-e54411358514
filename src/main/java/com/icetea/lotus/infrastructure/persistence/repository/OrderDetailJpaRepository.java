package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.OrderDetailJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA repository cho OrderDetailJpaEntity
 */
@Repository
public interface OrderDetailJpaRepository extends JpaRepository<OrderDetailJpaEntity, String>, JpaSpecificationExecutor<OrderDetailJpaEntity> {
    
    /**
     * Tìm tất cả chi tiết lệnh theo orderId
     * @param orderId ID của lệnh
     * @return Danh sách các chi tiết lệnh
     */
    List<OrderDetailJpaEntity> findAllByOrderId(String orderId);
    
    /**
     * Tìm tất cả chi tiết lệnh theo danh sách orderId
     * @param orderIds Danh sách ID của lệnh
     * @return Danh sách các chi tiết lệnh
     */
    List<OrderDetailJpaEntity> findAllByOrderIdIn(List<String> orderIds);
    
    /**
     * Tìm tất cả chi tiết lệnh theo khoảng thời gian
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các chi tiết lệnh
     */
    List<OrderDetailJpaEntity> findAllByTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
}
