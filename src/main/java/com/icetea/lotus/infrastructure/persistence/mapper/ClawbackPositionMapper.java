package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.ClawbackPosition;
import com.icetea.lotus.core.domain.valueobject.ClawbackPositionId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.ClawbackPositionJpaEntity;
import org.springframework.stereotype.Component;

/**
 * Mapper for ClawbackPosition and ClawbackPositionJpaEntity
 */
@Component
public class ClawbackPositionMapper {

    /**
     * Convert JPA entity to domain entity
     * @param jpaEntity ClawbackPositionJpaEntity
     * @return ClawbackPosition
     */
    public ClawbackPosition toDomainEntity(ClawbackPositionJpaEntity jpaEntity) {
        return ClawbackPosition.builder()
                .id(ClawbackPositionId.of(jpaEntity.getId()))
                .contractId(jpaEntity.getContractId())
                .symbol(new Symbol(jpaEntity.getSymbol()))
                .memberId(jpaEntity.getMemberId())
                .positionId(PositionId.of(jpaEntity.getPositionId()))
                .unrealizedPnl(Money.of(jpaEntity.getUnrealizedPnl()))
                .clawbackAmount(Money.of(jpaEntity.getClawbackAmount()))
                .clawbackRate(Money.of(jpaEntity.getClawbackRate()))
                .createTime(jpaEntity.getCreateTime())
                .build();
    }

    /**
     * Convert domain entity to JPA entity
     * @param domainEntity ClawbackPosition
     * @return ClawbackPositionJpaEntity
     */
    public ClawbackPositionJpaEntity toJpaEntity(ClawbackPosition domainEntity) {
        return ClawbackPositionJpaEntity.builder()
                .id(domainEntity.getId() != null ? domainEntity.getId().getValue() : null)
                .contractId(domainEntity.getContractId())
                .symbol(domainEntity.getSymbol().getValue())
                .memberId(domainEntity.getMemberId())
                .positionId(domainEntity.getPositionId().getValue())
                .unrealizedPnl(domainEntity.getUnrealizedPnl().getValue())
                .clawbackAmount(domainEntity.getClawbackAmount().getValue())
                .clawbackRate(domainEntity.getClawbackRate().getValue())
                .createTime(domainEntity.getCreateTime())
                .build();
    }
}
