package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.IndexPriceJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho IndexPriceJpaEntity
 */
@Repository
public interface IndexPriceJpaRepository extends JpaRepository<IndexPriceJpaEntity, Long>, JpaSpecificationExecutor<IndexPriceJpaEntity> {

    /**
     * Tìm giá chỉ số mới nhất theo symbol
     * @param symbol Symbol của hợp đồng
     * @return IndexPriceJpaEntity
     */
    IndexPriceJpaEntity findTopBySymbolOrderByCreateTimeDesc(String symbol);

    /**
     * Tìm danh sách giá chỉ số theo symbol
     * @param symbol Symbol của hợp đồng
     * @return List<IndexPriceJpaEntity>
     */
    List<IndexPriceJpaEntity> findBySymbol(String symbol);

    /**
     * Tìm danh sách giá chỉ số theo symbol và khoảng thời gian
     * Tìm các bản ghi có createTime nằm trong khoảng từ startTime đến endTime
     * Câu truy vấn SQL tương ứng: SELECT * FROM contract_index_price WHERE symbol = ? AND create_time BETWEEN ? AND ?
     *
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return List<IndexPriceJpaEntity>
     */
    List<IndexPriceJpaEntity> findBySymbolAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Xóa giá chỉ số cũ hơn thời gian chỉ định
     * @param time Thời gian
     * @return Số lượng bản ghi đã xóa
     */
    int deleteByCreateTimeBefore(LocalDateTime time);
}
