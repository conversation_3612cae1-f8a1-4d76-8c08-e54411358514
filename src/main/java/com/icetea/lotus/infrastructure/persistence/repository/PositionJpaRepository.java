package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.infrastructure.persistence.entity.PositionJpaEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * JPA Repository cho PositionJpaEntity
 */
@Repository
public interface PositionJpaRepository extends JpaRepository<PositionJpaEntity, Long>, JpaSpecificationExecutor<PositionJpaEntity> {

    /**
     * T<PERSON><PERSON> vị thế theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa vị thế nếu tìm thấy
     */
    Optional<PositionJpaEntity> findByMemberIdAndSymbol(Long memberId, String symbol);

    /**
     * Tìm vị thế theo memberId, symbol và status
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    Optional<PositionJpaEntity> findByMemberIdAndSymbolAndStatus(Long memberId, String symbol, PositionStatus status);

    /**
     * Tìm tất cả các vị thế theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllByMemberIdAndSymbol(Long memberId, String symbol);

    /**
     * Tìm vị thế theo memberId, symbol và direction
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    Optional<PositionJpaEntity> findByMemberIdAndSymbolAndDirection(Long memberId, String symbol, PositionDirection direction);

    /**
     * Tìm tất cả các vị thế theo memberId, symbol và direction
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllByMemberIdAndSymbolAndDirection(Long memberId, String symbol, PositionDirection direction);

    /**
     * Tìm tất cả các vị thế theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllByMemberId(Long memberId);

    /**
     * Tìm tất cả các vị thế theo memberId và status
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllByMemberIdAndStatus(Long memberId, PositionStatus status);

    /**
     * Tìm tất cả các vị thế theo memberId, status và khoảng thời gian
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param pageable Phân trang
     * @return Danh sách các vị thế
     */
    @Query("SELECT p FROM PositionJpaEntity p WHERE p.memberId = :memberId " +
           "AND (:status IS NULL OR p.status = :status) " +
           "AND (:startTime IS NULL OR p.createTime >= :startTime) " +
           "AND (:endTime IS NULL OR p.createTime <= :endTime) " +
           "ORDER BY p.createTime DESC")
    Page<PositionJpaEntity> findByMemberIdAndStatusAndTimeRange(
            @Param("memberId") Long memberId,
            @Param("status") PositionStatus status,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);

    /**
     * Tìm tất cả các vị thế theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllBySymbol(String symbol);

    /**
     * Tìm tất cả các vị thế theo symbol và direction
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllBySymbolAndDirection(String symbol, PositionDirection direction);

    /**
     * Tính tổng khối lượng vị thế theo symbol, direction và status
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @param status Trạng thái của vị thế
     * @return Tổng khối lượng
     */
    @Query("SELECT SUM(p.volume) FROM PositionJpaEntity p WHERE p.symbol = :symbol AND p.direction = :direction AND p.status = :status")
    BigDecimal sumVolumeBySymbolAndDirectionAndStatus(@Param("symbol") String symbol, @Param("direction") PositionDirection direction, @Param("status") PositionStatus status);

    /**
     * Tìm tất cả các vị thế theo trạng thái
     * @param status Trạng thái của vị thế
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllByStatus(PositionStatus status);

    /**
     * Tìm tất cả các vị thế theo symbol và trạng thái
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của vị thế
     * @return Danh sách các vị thế
     */
    List<PositionJpaEntity> findAllBySymbolAndStatus(String symbol, PositionStatus status);

    /**
     * Tìm các vị thế có nguy cơ cao cần thanh lý theo symbol
     * @param symbol Symbol của hợp đồng
     * @param marginRatio Tỷ lệ ký quỹ tối đa
     * @param pageable Phân trang
     * @return Danh sách các vị thế có nguy cơ cao
     */
    @Query(value = "SELECT p FROM PositionJpaEntity p WHERE p.symbol = :symbol AND p.status = 'OPEN' " +
            "AND p.margin / (p.volume * p.openPrice) < :marginRatio ORDER BY p.margin / (p.volume * p.openPrice) ASC")
    List<PositionJpaEntity> findRiskPositionsBySymbol(@Param("symbol") String symbol, @Param("marginRatio") BigDecimal marginRatio, Pageable pageable);
}
