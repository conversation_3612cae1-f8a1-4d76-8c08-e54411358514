package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.FundingSettlement;
import com.icetea.lotus.core.domain.valueobject.FundingSettlementId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.FundingSettlementJpaEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper để chuyển đổi giữa domain entity và JPA entity cho FundingSettlement
 */
@Component
public class FundingSettlementPersistenceMapper {
    
    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param fundingSettlement Domain entity
     * @return JPA entity
     */
    public FundingSettlementJpaEntity domainToEntity(FundingSettlement fundingSettlement) {
        if (fundingSettlement == null) {
            return null;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        return FundingSettlementJpaEntity.builder()
                .id(fundingSettlement.getId().getValue())
                .symbol(fundingSettlement.getSymbol().getValue())
                .memberId(fundingSettlement.getMemberId())
                .positionId(fundingSettlement.getPositionId().getValue())
                .fundingRate(fundingSettlement.getFundingRate().getValue())
                .fundingAmount(fundingSettlement.getFundingAmount().getValue())
                .timestamp(fundingSettlement.getTimestamp())
                .createdAt(now)
                .updatedAt(now)
                .build();
    }
    
    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public FundingSettlement entityToDomain(FundingSettlementJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return FundingSettlement.builder()
                .id(FundingSettlementId.of(entity.getId()))
                .symbol(Symbol.of(entity.getSymbol()))
                .memberId(entity.getMemberId())
                .positionId(PositionId.of(entity.getPositionId()))
                .fundingRate(Money.of(entity.getFundingRate()))
                .fundingAmount(Money.of(entity.getFundingAmount()))
                .timestamp(entity.getTimestamp())
                .build();
    }
    
    /**
     * Chuyển đổi danh sách từ JPA entity sang domain entity
     * @param entities Danh sách JPA entity
     * @return Danh sách domain entity
     */
    public List<FundingSettlement> entitiesToDomains(List<FundingSettlementJpaEntity> entities) {
        if (entities == null) {
            return List.of();
        }
        
        return entities.stream()
                .map(this::entityToDomain)
                .collect(Collectors.toList());
    }
    
    /**
     * Chuyển đổi danh sách từ domain entity sang JPA entity
     * @param fundingSettlements Danh sách domain entity
     * @return Danh sách JPA entity
     */
    public List<FundingSettlementJpaEntity> domainsToEntities(List<FundingSettlement> fundingSettlements) {
        if (fundingSettlements == null) {
            return List.of();
        }
        
        return fundingSettlements.stream()
                .map(this::domainToEntity)
                .collect(Collectors.toList());
    }
}
