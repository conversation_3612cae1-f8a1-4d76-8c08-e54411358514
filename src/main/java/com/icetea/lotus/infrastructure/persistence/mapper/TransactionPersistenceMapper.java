package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Transaction;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.valueobject.TradeId;
import com.icetea.lotus.core.domain.valueobject.TransactionId;
import com.icetea.lotus.infrastructure.persistence.entity.TransactionJpaEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * Mapper cho Transaction và TransactionJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class TransactionPersistenceMapper {

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Transaction entityToDomain(TransactionJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        return Transaction.builder()
                .id(TransactionId.of(entity.getId()))
                .memberId(entity.getMemberId())
                .amount(Money.of(entity.getAmount()))
                .createTime(entity.getCreateTime())
                .type(entity.getType())
                .address(entity.getAddress())
                .fee(Money.of(entity.getFee()))
                .flag(entity.getFlag())
                .realFee(entity.getRealFee())
                .discountFee(entity.getDiscountFee())
                .isReward(entity.getIsReward())
                .contractId(entity.getContractId())
                .symbol(Symbol.of(entity.getSymbol()))
                .coin(entity.getCoin())
                .referenceId(entity.getReferenceId())
                .orderId(entity.getOrderId() != null ? OrderId.of(entity.getOrderId()) : null)
                .tradeId(entity.getTradeId() != null ? TradeId.of(Long.parseLong(entity.getTradeId())) : null)
                .leverage(entity.getLeverage())
                .marginMode(entity.getMarginMode())
                .realizedPnl(Money.of(entity.getRealizedPnl()))
                .liquidation(entity.getLiquidation() != null ? entity.getLiquidation() : false)
                .adl(entity.getAdl() != null ? entity.getAdl() : false)
                .fundingFee(Money.of(entity.getFundingFee()))
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public TransactionJpaEntity domainToEntity(Transaction domain) {
        if (domain == null) {
            return null;
        }

        return TransactionJpaEntity.builder()
//                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .memberId(domain.getMemberId())
                .amount(domain.getAmount() != null ? domain.getAmount().getValue() : null)
                .createTime(domain.getCreateTime())
                .type(domain.getType())
                .address(domain.getAddress())
                .fee(domain.getFee() != null ? domain.getFee().getValue() : null)
                .flag(domain.getFlag() != 0 ? domain.getFlag() : 0)
                .realFee(domain.getRealFee())
                .discountFee(domain.getDiscountFee())
                .isReward(domain.getIsReward() != 0 ? domain.getIsReward() : 0)
                .contractId(domain.getContractId())
                .symbol(domain.getSymbol() != null ? domain.getSymbol().getValue() : null)
                .coin(getCoinValue(domain))
                .referenceId(domain.getReferenceId())
                .orderId(domain.getOrderId() != null ? domain.getOrderId().getValue() : null)
                .tradeId(domain.getTradeId() != null ? domain.getTradeId().getValue().toString() : null)
                .leverage(domain.getLeverage())
                .marginMode(domain.getMarginMode())
                .realizedPnl(domain.getRealizedPnl() != null ? domain.getRealizedPnl().getValue() : null)
                .liquidation(domain.isLiquidation())
                .adl(domain.isAdl())
                .fundingFee(domain.getFundingFee() != null ? domain.getFundingFee().getValue() : null)
                .build();
    }

    /**
     * Chuyển đổi từ LocalDateTime sang Date
     * @param localDateTime LocalDateTime
     * @return Date
     */
    private Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Chuyển đổi từ Date sang LocalDateTime
     * @param date Date
     * @return LocalDateTime
     */
    private LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * Lấy giá trị coin từ domain entity
     * Nếu coin không được thiết lập, sử dụng giá trị từ symbol hoặc giá trị mặc định "USDT"
     * @param domain Domain entity
     * @return Giá trị coin
     */
    private String getCoinValue(Transaction domain) {
        if (domain.getCoin() != null) {
            return domain.getCoin();
        }

        // Nếu coin không được thiết lập, sử dụng giá trị từ symbol
        if (domain.getSymbol() != null) {
            String symbolValue = domain.getSymbol().getValue();
            // Nếu symbol có định dạng XXX/YYY hoặc XXX-YYY, lấy phần YYY
            if (symbolValue.contains("/")) {
                return symbolValue.split("/")[1];
            } else if (symbolValue.contains("-")) {
                return symbolValue.split("-")[1];
            } else if (symbolValue.length() > 4) {
                // Nếu symbol có định dạng XXXYYY, giả sử YYY là coin (ví dụ: BTCUSDT -> USDT)
                return symbolValue.substring(symbolValue.length() - 4);
            }
        }

        // Giá trị mặc định nếu không thể xác định coin
        return "USDT";
    }
}
