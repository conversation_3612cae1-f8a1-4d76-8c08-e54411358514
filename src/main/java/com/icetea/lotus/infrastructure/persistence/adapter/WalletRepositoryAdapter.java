package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.Wallet;
import com.icetea.lotus.core.domain.repository.WalletRepository;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.WalletId;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.WalletJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.WalletPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.WalletJpaRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho WalletRepository
 * Triển khai các phương thức của WalletRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class WalletRepositoryAdapter implements WalletRepository {

    @PersistenceContext
    private EntityManager entityManager;

    private final WalletJpaRepository walletJpaRepository;
    private final WalletPersistenceMapper walletPersistenceMapper;

    /**
     * Lấy ví theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của ví
     * @return Optional<Wallet>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Wallet> findById(Long id) {
        try {
            log.debug("Tìm ví theo ID, id = {}", id);

            Optional<WalletJpaEntity> entityOpt = walletJpaRepository.findById(id);

            if (entityOpt.isEmpty()) {
                log.debug("Không tìm thấy ví, id = {}", id);
                return Optional.empty();
            }

            log.debug("Đã tìm thấy ví, id = {}", id);

            return Optional.of(walletPersistenceMapper.entityToDomain(entityOpt.get()));
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm ví theo ID, id = {}", id, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm ví theo ID, id = {}", id, e);
            throw new DatabaseException("Lỗi khi tìm ví theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm ví theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của ví
     * @return Optional.empty()
     */
    @Recover
    public Optional<Wallet> recoverFindById(Exception e, Long id) {
        log.error("Đã thử lại tìm ví theo ID 3 lần nhưng thất bại, id = {}", id, e);
        return Optional.empty();
    }

    /**
     * Lấy ví theo thành viên
     * @param memberId ID của thành viên
     * @return Optional<Wallet>
     */
    @Override
    public Optional<Wallet> findByMemberId(Long memberId) {
        log.debug("Tìm ví theo memberId, memberId = {}", memberId);

        List<WalletJpaEntity> entities = walletJpaRepository.findAllByMemberId(memberId);

        if (entities.isEmpty()) {
            log.debug("Không tìm thấy ví, memberId = {}", memberId);
            return Optional.empty();
        }

        log.debug("Đã tìm thấy ví, memberId = {}", memberId);

        return Optional.of(walletPersistenceMapper.entityToDomain(entities.get(0)));
    }

    /**
     * Lấy ví theo thành viên và coin
     * @param memberId ID của thành viên
     * @param coin Ký hiệu của đồng coin
     * @return Wallet
     */
    @Override
    public Wallet findByMemberIdAndCoin(Long memberId, String coin) {
        log.debug("Tìm ví theo memberId và coin, memberId = {}, coin = {}", memberId, coin);

        Optional<WalletJpaEntity> entityOpt = walletJpaRepository.findByMemberIdAndCoin(memberId, coin);

        if (entityOpt.isEmpty()) {
            log.debug("Không tìm thấy ví, memberId = {}, coin = {}", memberId, coin);
            return null;
        }

        log.debug("Đã tìm thấy ví, memberId = {}, coin = {}", memberId, coin);

        return walletPersistenceMapper.entityToDomain(entityOpt.get());
    }

    /**
     * Lấy tất cả ví theo thành viên
     * @param memberId ID của thành viên
     * @return List<Wallet>
     */
    @Override
    public List<Wallet> findAllByMemberId(Long memberId) {
        log.debug("Tìm tất cả ví theo memberId, memberId = {}", memberId);

        List<WalletJpaEntity> entities = walletJpaRepository.findAllByMemberId(memberId);

        log.debug("Đã tìm thấy {} ví, memberId = {}", entities.size(), memberId);

        return entities.stream()
                .map(walletPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }

    /**
     * Lấy tất cả ví
     * @return List<Wallet>
     */
    @Override
    public List<Wallet> findAll() {
        log.debug("Tìm tất cả ví");

        List<WalletJpaEntity> entities = walletJpaRepository.findAll();

        log.debug("Đã tìm thấy {} ví", entities.size());

        return entities.stream()
                .map(walletPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }

    /**
     * Lưu ví
     * @param wallet Ví
     * @return Wallet
     */
    @Override
    @Retryable(
            value = {
                    DataAccessException.class,
                    TransactionException.class,
                    ObjectOptimisticLockingFailureException.class  // Thêm dòng này
            },
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Wallet save(Wallet wallet) {
        try {
            log.debug("Lưu ví, id = {}", wallet.getId() != null ? wallet.getId().getValue() : "null");

            WalletJpaEntity entity = walletPersistenceMapper.domainToEntity(wallet);

            // Nếu là update (có ID), refresh entity để lấy version mới nhất
            if (entity.getId() != null) {
                entity = refreshEntityBeforeSave(entity);
            }

            WalletJpaEntity savedEntity = walletJpaRepository.save(entity);

            log.debug("Đã lưu ví thành công, id = {}", savedEntity.getId());

            return walletPersistenceMapper.entityToDomain(savedEntity);

        } catch (ObjectOptimisticLockingFailureException e) {
            log.warn("Optimistic locking conflict khi lưu ví, id = {}, attempt sẽ retry",
                    wallet.getId() != null ? wallet.getId().getValue() : "null", e);
            throw e; // Để @Retryable handle

        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi database khi lưu ví, id = {}",
                    wallet.getId() != null ? wallet.getId().getValue() : "null", e);
            throw e;

        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu ví, id = {}",
                    wallet.getId() != null ? wallet.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu ví: " + e.getMessage(), e);
        }
    }
    private WalletJpaEntity refreshEntityBeforeSave(WalletJpaEntity entity) {
        try {
            // Lấy entity mới nhất từ DB
            Optional<WalletJpaEntity> currentEntity = walletJpaRepository.findById(entity.getId());

            if (currentEntity.isPresent()) {
                WalletJpaEntity current = currentEntity.get();

                // Copy các field cần update từ entity mới sang entity hiện tại
                current.setBalance(entity.getBalance());
                current.setFrozenBalance(entity.getFrozenBalance());
                current.setUnrealizedPnl(entity.getUnrealizedPnl());
                current.setRealizedPnl(entity.getRealizedPnl());
                current.setUsedMargin(entity.getUsedMargin());
                current.setTotalFee(entity.getTotalFee());
                current.setTotalFundingFee(entity.getTotalFundingFee());
                current.setLocked(entity.isLocked());
                // updateTime và availableBalance sẽ được tự động update trong @PreUpdate

                return current;
            }

            return entity; // Nếu không tìm thấy, return entity gốc (có thể là create mới)

        } catch (Exception e) {
            log.warn("Không thể refresh entity, sử dụng entity gốc", e);
            return entity;
        }
    }

    @Recover
    public Wallet recover(ObjectOptimisticLockingFailureException ex, Wallet wallet) {
        log.error("Không thể lưu ví sau {} lần thử do optimistic locking conflict, id = {}",
                3, wallet.getId() != null ? wallet.getId().getValue() : "null", ex);
        throw new DatabaseException("Ví đang được cập nhật bởi giao dịch khác. Vui lòng thử lại sau.");
    }

    /**
     * Phục hồi khi lưu ví thất bại
     * @param e Ngoại lệ
     * @param wallet Ví
     * @return null
     */
    @Recover
    public Wallet recoverSave(Exception e, Wallet wallet) {
        log.error("Đã thử lại lưu ví 3 lần nhưng thất bại, id = {}", wallet.getId() != null ? wallet.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu ví sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tăng số dư ví
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return Số lượng bản ghi đã cập nhật
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int increaseBalance(WalletId walletId, Money amount) {
        try {
            log.debug("Tăng số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue());

            int updatedCount = walletJpaRepository.increaseBalance(walletId.getValue(), amount.getValue());

            log.debug("Đã tăng số dư ví thành công, walletId = {}, amount = {}, updatedCount = {}",
                    walletId.getValue(), amount.getValue(), updatedCount);

            return updatedCount;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tăng số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tăng số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw new DatabaseException("Lỗi khi tăng số dư ví: " + e.getMessage(), e);
        }
    }

    /**
     * Giảm số dư ví
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return Số lượng bản ghi đã cập nhật
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int decreaseBalance(WalletId walletId, Money amount) {
        try {
            log.debug("Giảm số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue());

            int updatedCount = walletJpaRepository.decreaseBalance(walletId.getValue(), amount.getValue());

            log.debug("Đã giảm số dư ví thành công, walletId = {}, amount = {}, updatedCount = {}",
                    walletId.getValue(), amount.getValue(), updatedCount);

            return updatedCount;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi giảm số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi giảm số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw new DatabaseException("Lỗi khi giảm số dư ví: " + e.getMessage(), e);
        }
    }

    /**
     * Đóng băng số dư ví
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return Số lượng bản ghi đã cập nhật
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int freezeBalance(WalletId walletId, Money amount) {
        try {
            log.debug("Đóng băng số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue());

            int updatedCount = walletJpaRepository.freezeBalance(walletId.getValue(), amount.getValue());

            log.debug("Đã đóng băng số dư ví thành công, walletId = {}, amount = {}, updatedCount = {}",
                    walletId.getValue(), amount.getValue(), updatedCount);

            return updatedCount;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi đóng băng số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đóng băng số dư ví, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw new DatabaseException("Lỗi khi đóng băng số dư ví: " + e.getMessage(), e);
        }
    }

    /**
     * Giải phóng số dư đóng băng
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return Số lượng bản ghi đã cập nhật
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int unfreezeBalance(WalletId walletId, Money amount) {
        try {
            log.debug("Giải phóng số dư đóng băng, walletId = {}, amount = {}", walletId.getValue(), amount.getValue());

            int updatedCount = walletJpaRepository.unfreezeBalance(walletId.getValue(), amount.getValue());

            log.debug("Đã giải phóng số dư đóng băng thành công, walletId = {}, amount = {}, updatedCount = {}",
                    walletId.getValue(), amount.getValue(), updatedCount);

            return updatedCount;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi giải phóng số dư đóng băng, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi giải phóng số dư đóng băng, walletId = {}, amount = {}", walletId.getValue(), amount.getValue(), e);
            throw new DatabaseException("Lỗi khi giải phóng số dư đóng băng: " + e.getMessage(), e);
        }
    }

    /**
     * Cập nhật số dư ví
     * @param memberId ID của thành viên
     * @param amount Số tiền
     * @return Wallet
     */
    @Override
    public Wallet updateBalance(Long memberId, Money amount) {
        // Tìm ví theo memberId và coin
        // Lưu ý: Cần truyền vào coin thay vì gọi getCurrency() từ Money
        // Vì Money không có phương thức getCurrency()
        // Tạm thời sử dụng tham số coin từ bên ngoài
        String coin = "USDT"; // Mặc định là USDT, cần truyền vào từ bên ngoài
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);

        if (wallet == null) {
            throw new IllegalArgumentException("Ví không tồn tại");
        }

        // Cập nhật số dư
        wallet = Wallet.builder()
                .id(wallet.getId())
                .memberId(wallet.getMemberId())
                .coin(wallet.getCoin())
                .balance(amount)
                .frozenBalance(wallet.getFrozenBalance())
                .availableBalance(Money.of(amount.getValue().subtract(wallet.getFrozenBalance().getValue())))
                .unrealizedPnl(wallet.getUnrealizedPnl())
                .realizedPnl(wallet.getRealizedPnl())
                .usedMargin(wallet.getUsedMargin())
                .totalFee(wallet.getTotalFee())
                .totalFundingFee(wallet.getTotalFundingFee())
                .isLocked(wallet.isLocked())
                .createTime(wallet.getCreateTime())
                .updateTime(wallet.getUpdateTime())
                .build();

        return save(wallet);
    }

    /**
     * Cập nhật số dư khả dụng
     * @param memberId ID của thành viên
     * @param amount Số tiền
     * @return Wallet
     */
    @Override
    public Wallet updateAvailableBalance(Long memberId, Money amount) {
        // Tìm ví theo memberId và coin
        // Lưu ý: Cần truyền vào coin thay vì gọi getCurrency() từ Money
        // Vì Money không có phương thức getCurrency()
        // Tạm thời sử dụng tham số coin từ bên ngoài
        String coin = "USDT"; // Mặc định là USDT, cần truyền vào từ bên ngoài
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);

        if (wallet == null) {
            throw new IllegalArgumentException("Ví không tồn tại");
        }

        // Cập nhật số dư khả dụng
        wallet = Wallet.builder()
                .id(wallet.getId())
                .memberId(wallet.getMemberId())
                .coin(wallet.getCoin())
                .balance(Money.of(amount.getValue().add(wallet.getFrozenBalance().getValue())))
                .frozenBalance(wallet.getFrozenBalance())
                .availableBalance(amount)
                .unrealizedPnl(wallet.getUnrealizedPnl())
                .realizedPnl(wallet.getRealizedPnl())
                .usedMargin(wallet.getUsedMargin())
                .totalFee(wallet.getTotalFee())
                .totalFundingFee(wallet.getTotalFundingFee())
                .isLocked(wallet.isLocked())
                .createTime(wallet.getCreateTime())
                .updateTime(wallet.getUpdateTime())
                .build();

        return save(wallet);
    }

    /**
     * Cập nhật số dư đóng băng
     * @param memberId ID của thành viên
     * @param amount Số tiền
     * @return Wallet
     */
    @Override
    public Wallet updateFrozenBalance(Long memberId, Money amount) {
        // Tìm ví theo memberId và coin
        // Lưu ý: Cần truyền vào coin thay vì gọi getCurrency() từ Money
        // Vì Money không có phương thức getCurrency()
        // Tạm thời sử dụng tham số coin từ bên ngoài
        String coin = "USDT"; // Mặc định là USDT, cần truyền vào từ bên ngoài
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);

        if (wallet == null) {
            throw new IllegalArgumentException("Ví không tồn tại");
        }

        // Cập nhật số dư đóng băng
        wallet = Wallet.builder()
                .id(wallet.getId())
                .memberId(wallet.getMemberId())
                .coin(wallet.getCoin())
                .balance(wallet.getBalance())
                .frozenBalance(amount)
                .availableBalance(Money.of(wallet.getBalance().getValue().subtract(amount.getValue())))
                .unrealizedPnl(wallet.getUnrealizedPnl())
                .realizedPnl(wallet.getRealizedPnl())
                .usedMargin(wallet.getUsedMargin())
                .totalFee(wallet.getTotalFee())
                .totalFundingFee(wallet.getTotalFundingFee())
                .isLocked(wallet.isLocked())
                .createTime(wallet.getCreateTime())
                .updateTime(wallet.getUpdateTime())
                .build();

        return save(wallet);
    }

    /**
     * Cập nhật tổng phí giao dịch
     * @param walletId ID của ví
     * @param fee Phí giao dịch
     * @return Số lượng bản ghi đã cập nhật
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int updateTotalFee(WalletId walletId, Money fee) {
        try {
            log.debug("Cập nhật tổng phí giao dịch, walletId = {}, fee = {}", walletId.getValue(), fee.getValue());

            int updatedCount = walletJpaRepository.updateTotalFee(walletId.getValue(), fee.getValue());

            log.debug("Đã cập nhật tổng phí giao dịch thành công, walletId = {}, fee = {}, updatedCount = {}",
                    walletId.getValue(), fee.getValue(), updatedCount);

            return updatedCount;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi cập nhật tổng phí giao dịch, walletId = {}, fee = {}", walletId.getValue(), fee.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi cập nhật tổng phí giao dịch, walletId = {}, fee = {}", walletId.getValue(), fee.getValue(), e);
            throw new DatabaseException("Lỗi khi cập nhật tổng phí giao dịch: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi cập nhật tổng phí giao dịch thất bại
     * @param e Ngoại lệ
     * @param walletId ID của ví
     * @param fee Phí giao dịch
     * @return 0
     */
    @Recover
    public int recoverUpdateTotalFee(Exception e, WalletId walletId, Money fee) {
        log.error("Đã thử lại cập nhật tổng phí giao dịch 3 lần nhưng thất bại, walletId = {}, fee = {}", walletId.getValue(), fee.getValue(), e);
        throw new DatabaseException("Không thể cập nhật tổng phí giao dịch sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Cập nhật lợi nhuận đã thực hiện
     * @param walletId ID của ví
     * @param realizedPnl Lợi nhuận đã thực hiện
     * @return Số lượng bản ghi đã cập nhật
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int updateRealizedPnl(WalletId walletId, Money realizedPnl) {
        try {
            log.debug("Cập nhật lợi nhuận đã thực hiện, walletId = {}, realizedPnl = {}", walletId.getValue(), realizedPnl.getValue());

            int updatedCount = walletJpaRepository.updateRealizedPnl(walletId.getValue(), realizedPnl.getValue());

            log.debug("Đã cập nhật lợi nhuận đã thực hiện thành công, walletId = {}, realizedPnl = {}, updatedCount = {}",
                    walletId.getValue(), realizedPnl.getValue(), updatedCount);

            return updatedCount;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi cập nhật lợi nhuận đã thực hiện, walletId = {}, realizedPnl = {}", walletId.getValue(), realizedPnl.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi cập nhật lợi nhuận đã thực hiện, walletId = {}, realizedPnl = {}", walletId.getValue(), realizedPnl.getValue(), e);
            throw new DatabaseException("Lỗi khi cập nhật lợi nhuận đã thực hiện: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi cập nhật lợi nhuận đã thực hiện thất bại
     * @param e Ngoại lệ
     * @param walletId ID của ví
     * @param realizedPnl Lợi nhuận đã thực hiện
     * @return 0
     */
    @Recover
    public int recoverUpdateRealizedPnl(Exception e, WalletId walletId, Money realizedPnl) {
        log.error("Đã thử lại cập nhật lợi nhuận đã thực hiện 3 lần nhưng thất bại, walletId = {}, realizedPnl = {}", walletId.getValue(), realizedPnl.getValue(), e);
        throw new DatabaseException("Không thể cập nhật lợi nhuận đã thực hiện sau 3 lần thử lại: " + e.getMessage(), e);
    }
}
