package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.ContractPersistencePort;
import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.valueobject.ContractId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.ContractJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.ContractPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.ContractJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Adapter cho ContractPersistencePort
 * Triển khai các phương thức của ContractPersistencePort
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class ContractPersistenceAdapter implements ContractPersistencePort {

    private final ContractJpaRepository contractJpaRepository;
    private final ContractPersistenceMapper contractPersistenceMapper;

    /**
     * Tìm hợp đồng theo id
     * @param id ID của hợp đồng
     * @return Optional chứa hợp đồng nếu tìm thấy
     */
    @Override
    public Optional<Contract> findById(ContractId id) {
        Optional<ContractJpaEntity> entity = contractJpaRepository.findById(id.getValue());
        return entity.map(contractPersistenceMapper::entityToDomain);
    }

    /**
     * Tìm hợp đồng theo id (Long)
     * @param id ID của hợp đồng
     * @return Optional chứa hợp đồng nếu tìm thấy
     */
    public Optional<Contract> findById(Long id) {
        try {
            log.debug("Tìm hợp đồng theo ID, id = {}", id);

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            Optional<ContractJpaEntity> entity = contractJpaRepository.findById(id);
            return entity.map(contractPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Lỗi khi tìm hợp đồng theo ID, id = {}", id, e);
            throw new DatabaseException("Lỗi khi tìm hợp đồng theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm hợp đồng theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa hợp đồng nếu tìm thấy
     */
    @Override
    public Optional<Contract> findBySymbol(Symbol symbol) {
        try {
            log.debug("Tìm hợp đồng theo symbol, symbol = {}", symbol.getValue());

            if (symbol == null) {
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            Optional<ContractJpaEntity> entity = contractJpaRepository.findBySymbol(symbol.getValue());
            return entity.map(contractPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Lỗi khi tìm hợp đồng theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm hợp đồng theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các hợp đồng
     * @return Danh sách các hợp đồng
     */
    @Override
    public List<Contract> findAll() {
        try {
            log.debug("Tìm tất cả các hợp đồng");

            // Sử dụng findAll() trực tiếp và bỏ qua QueryLimiter
            List<ContractJpaEntity> entities = contractJpaRepository.findAll(org.springframework.data.domain.Sort.unsorted());

            log.debug("Đã tìm thấy {} hợp đồng", entities.size());

            return entities.stream()
                    .map(contractPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm tất cả các hợp đồng", e);
            throw new DatabaseException("Lỗi khi tìm tất cả các hợp đồng: " + e.getMessage(), e);
        }
    }



    /**
     * Lưu hợp đồng với xử lý ngoại lệ và thử lại
     * @param contract Hợp đồng cần lưu
     * @return Hợp đồng đã được lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Contract save(Contract contract) {
        try {
            log.debug("Lưu hợp đồng, id = {}, symbol = {}",
                    contract.getId() != null ? contract.getId() : "null",
                    contract.getSymbol().getValue());

            if (contract == null) {
                throw new IllegalArgumentException("Contract không được để trống");
            }

            ContractJpaEntity entity = contractPersistenceMapper.domainToEntity(contract);
            ContractJpaEntity savedEntity = contractJpaRepository.save(entity);

            log.debug("Đã lưu hợp đồng thành công, id = {}", savedEntity.getId());

            return contractPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu hợp đồng, symbol = {}", contract.getSymbol().getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu hợp đồng, symbol = {}", contract.getSymbol().getValue(), e);
            throw new DatabaseException("Lỗi khi lưu hợp đồng: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu hợp đồng thất bại
     * @param e Ngoại lệ
     * @param contract Hợp đồng cần lưu
     * @return Contract
     */
    @Recover
    public Contract recoverSave(Exception e, Contract contract) {
        log.error("Đã thử lại lưu hợp đồng 3 lần nhưng thất bại, symbol = {}", contract.getSymbol().getValue(), e);
        throw new DatabaseException("Không thể lưu hợp đồng sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Xóa hợp đồng với xử lý ngoại lệ và thử lại
     * @param contract Hợp đồng cần xóa
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void delete(Contract contract) {
        try {
            log.debug("Xóa hợp đồng, id = {}, symbol = {}",
                    contract.getId() != null ? contract.getId() : "null",
                    contract.getSymbol().getValue());

            if (contract == null) {
                throw new IllegalArgumentException("Contract không được để trống");
            }

            ContractJpaEntity entity = contractPersistenceMapper.domainToEntity(contract);
            contractJpaRepository.delete(entity);

            log.debug("Đã xóa hợp đồng thành công, symbol = {}", contract.getSymbol().getValue());
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy hợp đồng để xóa, symbol = {}", contract.getSymbol().getValue());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi xóa hợp đồng, symbol = {}", contract.getSymbol().getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa hợp đồng, symbol = {}", contract.getSymbol().getValue(), e);
            throw new DatabaseException("Lỗi khi xóa hợp đồng: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi xóa hợp đồng thất bại
     * @param e Ngoại lệ
     * @param contract Hợp đồng cần xóa
     */
    @Recover
    public void recoverDelete(Exception e, Contract contract) {
        log.error("Đã thử lại xóa hợp đồng 3 lần nhưng thất bại, symbol = {}", contract.getSymbol().getValue(), e);
        throw new DatabaseException("Không thể xóa hợp đồng sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm các hợp đồng đã được kích hoạt
     * @return Danh sách các hợp đồng đã được kích hoạt
     */
    public List<Contract> findAllEnabled() {
        List<ContractJpaEntity> entities = contractJpaRepository.findByEnabled(1);
        return entities.stream()
                .map(contractPersistenceMapper::entityToDomain)
                .collect(Collectors.toList());
    }

    /**
     * Tìm hợp đồng theo symbol và trạng thái kích hoạt
     * @param symbol Symbol của hợp đồng
     * @param enabled Trạng thái kích hoạt
     * @return Hợp đồng nếu tìm thấy, null nếu không tìm thấy
     */
    public Contract findBySymbolAndEnabled(Symbol symbol, boolean enabled) {
        Optional<ContractJpaEntity> entity = contractJpaRepository.findBySymbolAndEnabled(symbol.getValue(), enabled ? 1 : 0);
        return entity.map(contractPersistenceMapper::entityToDomain).orElse(null);
    }

    /**
     * Tìm các hợp đồng đang được kích hoạt
     * @return Danh sách các hợp đồng đang được kích hoạt
     */
    public List<Contract> findAllActive() {
        try {
            log.debug("Tìm tất cả các hợp đồng đang được kích hoạt");

            // Sử dụng findAll() trực tiếp và bỏ qua QueryLimiter
            List<ContractJpaEntity> entities = contractJpaRepository.findAll(org.springframework.data.domain.Sort.unsorted());

            List<Contract> activeContracts = entities.stream()
                    .filter(entity -> entity.getEnabled() != null && entity.getEnabled() == 1)
                    .map(contractPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());

            log.debug("Đã tìm thấy {} hợp đồng đang được kích hoạt", activeContracts.size());

            return activeContracts;
        } catch (Exception e) {
            log.error("Lỗi khi tìm tất cả các hợp đồng đang được kích hoạt", e);
            throw new DatabaseException("Lỗi khi tìm tất cả các hợp đồng đang được kích hoạt: " + e.getMessage(), e);
        }
    }

    /**
     * Xóa hợp đồng theo ID
     * @param id ID của hợp đồng cần xóa
     */
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void deleteById(Long id) {
        try {
            log.debug("Xóa hợp đồng theo ID, id = {}", id);

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            contractJpaRepository.deleteById(id);

            log.debug("Đã xóa hợp đồng thành công, id = {}", id);
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy hợp đồng để xóa, id = {}", id);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi xóa hợp đồng theo ID, id = {}", id, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa hợp đồng theo ID, id = {}", id, e);
            throw new DatabaseException("Lỗi khi xóa hợp đồng theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các hợp đồng có thời điểm đáo hạn trước một thời điểm cụ thể
     * @param date Thời điểm cần so sánh
     * @return Danh sách các hợp đồng đáo hạn trước thời điểm đã cho
     */
    @Override
    public List<Contract> findAllByExpiryDateBefore(LocalDateTime date) {
        try {
            log.debug("Tìm tất cả các hợp đồng có thời điểm đáo hạn trước {}", date);

            List<ContractJpaEntity> entities = contractJpaRepository.findAllByExpiryDateBefore(date);

            log.debug("Đã tìm thấy {} hợp đồng có thời điểm đáo hạn trước {}", entities.size(), date);

            return entities.stream()
                    .map(contractPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm các hợp đồng có thời điểm đáo hạn trước {}", date, e);
            throw new DatabaseException("Lỗi khi tìm các hợp đồng có thời điểm đáo hạn: " + e.getMessage(), e);
        }
    }
}
