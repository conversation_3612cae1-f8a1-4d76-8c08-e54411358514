package com.icetea.lotus.infrastructure.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho CircuitBreaker
 */
@Entity
@Table(name = "contract_circuit_breaker")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CircuitBreakerJpaEntity {
    
    /**
     * ID của circuit breaker
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Ký hiệu của hợp đồng
     */
    @Column(name = "symbol")
    private String symbol;
    
    /**
     * Trạng thái của circuit breaker
     */
    @Column(name = "status")
    private String status;
    
    /**
     * Lý do kích hoạt circuit breaker
     */
    @Column(name = "reason")
    private String reason;
    
    /**
     * G<PERSON><PERSON> kích hoạt
     */
    @Column(name = "trigger_price", columnDefinition = "decimal(18,8)")
    private BigDecimal triggerPrice;
    
    /**
     * G<PERSON><PERSON> tham chiếu
     */
    @Column(name = "reference_price", columnDefinition = "decimal(18,8)")
    private BigDecimal referencePrice;
    
    /**
     * Phần trăm thay đổi giá
     */
    @Column(name = "price_change", columnDefinition = "decimal(18,8)")
    private BigDecimal priceChange;
    
    /**
     * Thời gian kích hoạt
     */
    @Column(name = "trigger_time")
    private LocalDateTime triggerTime;
    
    /**
     * Thời gian kết thúc
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    /**
     * Thời gian tạo
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    /**
     * Thời gian cập nhật
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
