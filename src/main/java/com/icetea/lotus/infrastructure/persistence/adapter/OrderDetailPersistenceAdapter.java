package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.OrderDetailPersistencePort;
import com.icetea.lotus.core.domain.entity.OrderDetail;
import com.icetea.lotus.core.domain.valueobject.OrderDetailId;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.OrderDetailJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.OrderDetailPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.OrderDetailJpaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho OrderDetailPersistencePort
 * <PERSON><PERSON><PERSON> khai cá<PERSON> ph<PERSON><PERSON><PERSON> thức để lưu trữ OrderDetail
 */
@Component
@RequiredArgsConstructor
public class OrderDetailPersistenceAdapter implements OrderDetailPersistencePort {
    
    private final OrderDetailJpaRepository orderDetailJpaRepository;
    private final OrderDetailPersistenceMapper orderDetailPersistenceMapper;
    
    /**
     * Tìm chi tiết lệnh theo id
     * @param id ID của chi tiết lệnh
     * @return Optional chứa chi tiết lệnh nếu tìm thấy
     */
    @Override
    public Optional<OrderDetail> findById(OrderDetailId id) {
        return orderDetailJpaRepository.findById(id.getValue())
                .map(orderDetailPersistenceMapper::mapToDomainEntity);
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo orderId
     * @param orderId ID của lệnh
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllByOrderId(OrderId orderId) {
        return orderDetailPersistenceMapper.mapToDomainEntities(
                orderDetailJpaRepository.findAllByOrderId(orderId.getValue())
        );
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo danh sách orderId
     * @param orderIds Danh sách ID của lệnh
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllByOrderIdIn(List<OrderId> orderIds) {
        List<String> orderIdValues = orderIds.stream()
                .map(OrderId::getValue)
                .collect(Collectors.toList());
        
        return orderDetailPersistenceMapper.mapToDomainEntities(
                orderDetailJpaRepository.findAllByOrderIdIn(orderIdValues)
        );
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo khoảng thời gian
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllByTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return orderDetailPersistenceMapper.mapToDomainEntities(
                orderDetailJpaRepository.findAllByTimeBetween(startTime, endTime)
        );
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllBySymbol(Symbol symbol) {
        // Implement this method
        return List.of();
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllBySymbolAndTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        // Implement this method
        return List.of();
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllByMemberId(Long memberId) {
        // Implement this method
        return List.of();
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        // Implement this method
        return List.of();
    }
    
    /**
     * Tìm tất cả chi tiết lệnh theo memberId và khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các chi tiết lệnh
     */
    @Override
    public List<OrderDetail> findAllByMemberIdAndTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime) {
        // Implement this method
        return List.of();
    }
    
    /**
     * Lưu chi tiết lệnh
     * @param orderDetail Chi tiết lệnh cần lưu
     * @return Chi tiết lệnh đã được lưu
     */
    @Override
    public OrderDetail save(OrderDetail orderDetail) {
        OrderDetailJpaEntity jpaEntity = orderDetailPersistenceMapper.mapToJpaEntity(orderDetail);
        OrderDetailJpaEntity savedEntity = orderDetailJpaRepository.save(jpaEntity);
        return orderDetailPersistenceMapper.mapToDomainEntity(savedEntity);
    }
    
    /**
     * Lưu danh sách chi tiết lệnh
     * @param orderDetails Danh sách chi tiết lệnh cần lưu
     * @return Danh sách chi tiết lệnh đã được lưu
     */
    @Override
    public List<OrderDetail> saveAll(List<OrderDetail> orderDetails) {
        List<OrderDetailJpaEntity> jpaEntities = orderDetailPersistenceMapper.mapToJpaEntities(orderDetails);
        List<OrderDetailJpaEntity> savedEntities = orderDetailJpaRepository.saveAll(jpaEntities);
        return orderDetailPersistenceMapper.mapToDomainEntities(savedEntities);
    }
}
