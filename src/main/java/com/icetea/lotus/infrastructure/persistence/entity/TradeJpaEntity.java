package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.valueobject.OrderType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Trade
 */
@Entity
@Table(name = "contract_trade")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TradeJpaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "symbol")
    private String symbol;

    @Column(name = "buy_order_id")
    private String buyOrderId;

    @Column(name = "sell_order_id")
    private String sellOrderId;

    @Column(name = "buy_member_id")
    private Long buyMemberId;

    @Column(name = "sell_member_id")
    private Long sellMemberId;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal price;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal volume;

    @Column(name = "buy_fee", columnDefinition = "decimal(18,8)")
    private BigDecimal buyFee;

    @Column(name = "sell_fee", columnDefinition = "decimal(18,8)")
    private BigDecimal sellFee;

    @Column(name = "buy_turnover", columnDefinition = "decimal(18,8)")
    private BigDecimal buyTurnover;

    @Column(name = "sell_turnover", columnDefinition = "decimal(18,8)")
    private BigDecimal sellTurnover;

    @Column(name = "time")
    private LocalDateTime tradeTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "buy_order_type")
    private OrderType buyOrderType;

    @Enumerated(EnumType.STRING)
    @Column(name = "sell_order_type")
    private OrderType sellOrderType;
}
