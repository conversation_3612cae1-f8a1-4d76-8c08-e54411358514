package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.WalletPersistencePort;
import com.icetea.lotus.core.domain.entity.Wallet;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.WalletId;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.WalletJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.WalletPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.WalletJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho việc lưu trữ Wallet
 * Triển khai WalletPersistencePort
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class WalletPersistenceAdapter implements WalletPersistencePort {

    private final WalletJpaRepository walletJpaRepository;
    private final WalletPersistenceMapper walletPersistenceMapper;

    /**
     * Tìm ví theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của ví
     * @return Optional chứa ví nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Wallet> findById(WalletId id) {
        try {
            log.debug("Tìm ví theo ID, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            Optional<WalletJpaEntity> entity = walletJpaRepository.findById(id.getValue());

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy ví, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy ví, id = {}", id.getValue());
            }

            return entity.map(walletPersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm ví theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm ví theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm ví theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm ví theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của ví
     * @return Optional<Wallet>
     */
    @Recover
    public Optional<Wallet> recoverFindById(Exception e, WalletId id) {
        log.error("Đã thử lại tìm ví theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm ví theo memberId và coin với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @param coin Ký hiệu của đồng coin
     * @return Optional chứa ví nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Wallet> findByMemberIdAndCoin(Long memberId, String coin) {
        try {
            log.debug("Tìm ví theo memberId và coin, memberId = {}, coin = {}", memberId, coin);

            if (memberId == null) {
                throw new IllegalArgumentException("MemberId không được để trống");
            }

            if (coin == null || coin.isEmpty()) {
                throw new IllegalArgumentException("Coin không được để trống");
            }

            Optional<WalletJpaEntity> entity = walletJpaRepository.findByMemberIdAndCoin(memberId, coin);

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy ví, memberId = {}, coin = {}", memberId, coin);
            } else {
                log.debug("Không tìm thấy ví, memberId = {}, coin = {}", memberId, coin);
            }

            return entity.map(walletPersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm ví theo memberId và coin, memberId = {}, coin = {}", memberId, coin, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm ví theo memberId và coin, memberId = {}, coin = {}", memberId, coin, e);
            throw new DatabaseException("Lỗi khi tìm ví theo memberId và coin: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm ví theo memberId và coin thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @param coin Ký hiệu của đồng coin
     * @return Optional<Wallet>
     */
    @Recover
    public Optional<Wallet> recoverFindByMemberIdAndCoin(Exception e, Long memberId, String coin) {
        log.error("Đã thử lại tìm ví theo memberId và coin 3 lần nhưng thất bại, memberId = {}, coin = {}", memberId, coin, e);
        return Optional.empty();
    }

    /**
     * Tìm tất cả các ví của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách các ví
     */
    @Override
    public List<Wallet> findAllByMemberId(Long memberId) {
        log.info("Tìm tất cả các ví của một thành viên, memberId = {}", memberId);

        try {
            List<WalletJpaEntity> entities = walletJpaRepository.findAllByMemberId(memberId);
            return entities.stream()
                    .map(walletPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm tất cả các ví của một thành viên thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm tất cả các ví theo coin
     * @param coin Ký hiệu của đồng coin
     * @return Danh sách các ví
     */
    @Override
    public List<Wallet> findAllByCoin(String coin) {
        log.info("Tìm tất cả các ví theo coin, coin = {}", coin);

        try {
            List<WalletJpaEntity> entities = walletJpaRepository.findAllByCoin(coin);
            return entities.stream()
                    .map(walletPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm tất cả các ví theo coin thất bại", e);
            throw e;
        }
    }

    /**
     * Lưu ví với xử lý ngoại lệ và thử lại
     * @param wallet Ví cần lưu
     * @return Ví đã được lưu
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Wallet save(Wallet wallet) {
        try {
            log.debug("Lưu ví, id = {}, memberId = {}, coin = {}, balance = {}",
                    wallet.getId() != null ? wallet.getId().getValue() : "null",
                    wallet.getMemberId(),
                    wallet.getCoin(),
                    wallet.getBalance().getValue());

            if (wallet == null) {
                throw new IllegalArgumentException("Wallet không được để trống");
            }

            WalletJpaEntity entity = walletPersistenceMapper.domainToEntity(wallet);
            WalletJpaEntity savedEntity = walletJpaRepository.save(entity);

            log.debug("Đã lưu ví thành công, id = {}", savedEntity.getId());

            return walletPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu ví, id = {}",
                    wallet.getId() != null ? wallet.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu ví, id = {}",
                    wallet.getId() != null ? wallet.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu ví: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu ví thất bại
     * @param e Ngoại lệ
     * @param wallet Ví cần lưu
     * @return Wallet
     */
    @Recover
    public Wallet recoverSave(Exception e, Wallet wallet) {
        log.error("Đã thử lại lưu ví 3 lần nhưng thất bại, id = {}",
                wallet.getId() != null ? wallet.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu ví sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Kiểm tra số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     * @return true nếu số dư đủ
     * @throws IllegalArgumentException nếu ví không tồn tại
     */
    @Override
    public boolean hasEnoughBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("Kiểm tra số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            // Kiểm tra xem ví có tồn tại không
            Optional<WalletJpaEntity> entityOpt = walletJpaRepository.findByMemberIdAndCoin(memberId, coin);
            if (entityOpt.isEmpty()) {
                log.warn("Ví không tồn tại, memberId = {}, coin = {}", memberId, coin);
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            BigDecimal balance = entityOpt.get().getAvailableBalance();
            if (balance == null) {
                balance = BigDecimal.ZERO;
            }

            return balance.compareTo(amount) >= 0;
        } catch (IllegalArgumentException e) {
            // Ném lại ngoại lệ để xử lý ở tầng trên
            throw e;
        } catch (Exception e) {
            log.error("Kiểm tra số dư thất bại", e);
            return false;
        }
    }

    /**
     * Kiểm tra số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     * @return true nếu số dư đủ
     */
    @Override
    public boolean hasEnoughBalance(Long memberId, String coin, Money amount) {
        return hasEnoughBalance(memberId, coin, amount.getValue());
    }

    /**
     * Đóng băng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Override
    public void freezeBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("Đóng băng số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            walletJpaRepository.freezeBalance(memberId, coin, amount);
        } catch (Exception e) {
            log.error("Đóng băng số dư thất bại", e);
            throw e;
        }
    }

    /**
     * Đóng băng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     * @return Ví đã được cập nhật
     */
    @Override
    public Wallet freezeBalance(Long memberId, String coin, Money amount) {
        log.info("Đóng băng số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            Optional<WalletJpaEntity> entityOpt = walletJpaRepository.findByMemberIdAndCoin(memberId, coin);

            if (entityOpt.isEmpty()) {
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            WalletJpaEntity entity = entityOpt.get();

            if (entity.getAvailableBalance().compareTo(amount.getValue()) < 0) {
                throw new IllegalArgumentException("Số dư không đủ");
            }

            walletJpaRepository.freezeBalance(entity.getId(), amount.getValue());

            // Lấy ví mới nhất
            Optional<WalletJpaEntity> updatedEntityOpt = walletJpaRepository.findById(entity.getId());

            if (updatedEntityOpt.isEmpty()) {
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            return walletPersistenceMapper.entityToDomain(updatedEntityOpt.get());
        } catch (Exception e) {
            log.error("Đóng băng số dư thất bại", e);
            throw e;
        }
    }

    /**
     * Giải phóng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Override
    public void unfreezeBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("Giải phóng số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            walletJpaRepository.unfreezeBalance(memberId, coin, amount);
        } catch (Exception e) {
            log.error("Giải phóng số dư thất bại", e);
            throw e;
        }
    }

    /**
     * Giải phóng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     * @return Ví đã được cập nhật
     */
    @Override
    public Wallet unfreezeBalance(Long memberId, String coin, Money amount) {
        log.info("Giải phóng số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            Optional<WalletJpaEntity> entityOpt = walletJpaRepository.findByMemberIdAndCoin(memberId, coin);

            if (entityOpt.isEmpty()) {
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            WalletJpaEntity entity = entityOpt.get();

            if (entity.getFrozenBalance().compareTo(amount.getValue()) < 0) {
                throw new IllegalArgumentException("Số dư đóng băng không đủ");
            }

            walletJpaRepository.unfreezeBalance(entity.getId(), amount.getValue());

            // Lấy ví mới nhất
            Optional<WalletJpaEntity> updatedEntityOpt = walletJpaRepository.findById(entity.getId());

            if (updatedEntityOpt.isEmpty()) {
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            return walletPersistenceMapper.entityToDomain(updatedEntityOpt.get());
        } catch (Exception e) {
            log.error("Giải phóng số dư thất bại", e);
            throw e;
        }
    }

    /**
     * Trừ số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Override
    public void subtractBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("Trừ số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            walletJpaRepository.subtractBalance(memberId, coin, amount);
        } catch (Exception e) {
            log.error("Trừ số dư thất bại", e);
            throw e;
        }
    }

    /**
     * Giảm số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     * @return Ví đã được cập nhật
     */
    @Override
    public Wallet decreaseBalance(Long memberId, String coin, Money amount) {
        log.info("Giảm số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            Optional<WalletJpaEntity> entityOpt = walletJpaRepository.findByMemberIdAndCoin(memberId, coin);

            if (entityOpt.isEmpty()) {
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            WalletJpaEntity entity = entityOpt.get();

            if (entity.getAvailableBalance().compareTo(amount.getValue()) < 0) {
                throw new IllegalArgumentException("Số dư không đủ");
            }

            walletJpaRepository.decreaseBalance(entity.getId(), amount.getValue());

            // Lấy ví mới nhất
            Optional<WalletJpaEntity> updatedEntityOpt = walletJpaRepository.findById(entity.getId());

            if (updatedEntityOpt.isEmpty()) {
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            return walletPersistenceMapper.entityToDomain(updatedEntityOpt.get());
        } catch (Exception e) {
            log.error("Giảm số dư thất bại", e);
            throw e;
        }
    }

    /**
     * Cộng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Override
    public void addBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("Cộng số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            walletJpaRepository.addBalance(memberId, coin, amount);
        } catch (Exception e) {
            log.error("Cộng số dư thất bại", e);
            throw e;
        }
    }

    /**
     * Tăng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     * @return Ví đã được cập nhật
     */
    @Override
    public Wallet increaseBalance(Long memberId, String coin, Money amount) {
        log.info("Tăng số dư, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        try {
            Optional<WalletJpaEntity> entityOpt = walletJpaRepository.findByMemberIdAndCoin(memberId, coin);

            if (entityOpt.isEmpty()) {
                // Tạo ví mới
                WalletJpaEntity entity = WalletJpaEntity.builder()
                        .memberId(memberId)
                        .coin(coin)
                        .balance(amount.getValue())
                        .frozenBalance(BigDecimal.ZERO)
                        .availableBalance(amount.getValue())
                        .unrealizedPnl(BigDecimal.ZERO)
                        .realizedPnl(BigDecimal.ZERO)
                        .usedMargin(BigDecimal.ZERO)
                        .totalFee(BigDecimal.ZERO)
                        .totalFundingFee(BigDecimal.ZERO)
                        .isLocked(false)
                        .build();

                WalletJpaEntity savedEntity = walletJpaRepository.save(entity);
                return walletPersistenceMapper.entityToDomain(savedEntity);
            }

            WalletJpaEntity entity = entityOpt.get();
            walletJpaRepository.increaseBalance(entity.getId(), amount.getValue());

            // Lấy ví mới nhất
            Optional<WalletJpaEntity> updatedEntityOpt = walletJpaRepository.findById(entity.getId());

            if (updatedEntityOpt.isEmpty()) {
                throw new IllegalArgumentException("Ví không tồn tại");
            }

            return walletPersistenceMapper.entityToDomain(updatedEntityOpt.get());
        } catch (Exception e) {
            log.error("Tăng số dư thất bại", e);
            throw e;
        }
    }
}
