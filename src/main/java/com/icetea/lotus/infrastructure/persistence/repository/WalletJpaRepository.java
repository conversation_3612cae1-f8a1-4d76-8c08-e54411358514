package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.WalletJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * JPA Repository cho WalletJpaEntity
 */
@Repository
public interface WalletJpaRepository extends JpaRepository<WalletJpaEntity, Long>, JpaSpecificationExecutor<WalletJpaEntity> {

    /**
     * Tìm ví theo memberId và coin
     * @param memberId ID của thành viên
     * @param coin Ký hiệu của đồng coin
     * @return Optional chứa ví nếu tìm thấy
     */
    Optional<WalletJpaEntity> findByMemberIdAndCoin(Long memberId, String coin);

    /**
     * Tìm tất cả các ví của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách các ví
     */
    List<WalletJpaEntity> findAllByMemberId(Long memberId);

    /**
     * Tìm tất cả các ví theo coin
     * @param coin Ký hiệu của đồng coin
     * @return Danh sách các ví
     */
    List<WalletJpaEntity> findAllByCoin(String coin);

    /**
     * Lấy số dư khả dụng
     * @param memberId ID của thành viên
     * @param coin Coin
     * @return Số dư khả dụng
     */
    @Query("SELECT w.availableBalance FROM WalletJpaEntity w WHERE w.memberId = :memberId AND w.coin = :coin")
    BigDecimal getAvailableBalance(@Param("memberId") Long memberId, @Param("coin") String coin);

    /**
     * Tăng số dư ví
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return int
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE WalletJpaEntity w SET w.balance = w.balance + :amount, w.availableBalance = w.availableBalance + :amount WHERE w.id = :walletId")
    int increaseBalance(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    /**
     * Giảm số dư ví
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return int
     */
    @Transactional
    @Modifying
    @Query("UPDATE WalletJpaEntity w SET w.balance = w.balance - :amount, w.availableBalance = w.availableBalance - :amount WHERE w.id = :walletId AND w.availableBalance >= :amount")
    int decreaseBalance(@Param("walletId") long walletId, @Param("amount") BigDecimal amount);

    /**
     * Đóng băng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Modifying
    @Transactional
    @Query("UPDATE WalletJpaEntity w SET w.availableBalance = w.availableBalance - :amount, w.frozenBalance = w.frozenBalance + :amount WHERE w.memberId = :memberId AND w.coin = :coin AND w.availableBalance >= :amount")
    void freezeBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Đóng băng số dư
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return int
     */
    @Transactional
    @Modifying
    @Query("UPDATE WalletJpaEntity w SET w.frozenBalance = w.frozenBalance + :amount, w.availableBalance = w.availableBalance - :amount WHERE w.id = :walletId AND w.availableBalance >= :amount")
    int freezeBalance(@Param("walletId") long walletId, @Param("amount") BigDecimal amount);

    /**
     * Giải phóng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Modifying
    @Transactional
    @Query("UPDATE WalletJpaEntity w SET w.availableBalance = w.availableBalance + :amount, w.frozenBalance = w.frozenBalance - :amount WHERE w.memberId = :memberId AND w.coin = :coin AND w.frozenBalance >= :amount")
    void unfreezeBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Giải phóng số dư đóng băng
     * @param walletId ID của ví
     * @param amount Số tiền
     * @return int
     */
    @Transactional
    @Modifying
    @Query("UPDATE WalletJpaEntity w SET w.frozenBalance = w.frozenBalance - :amount, w.availableBalance = w.availableBalance + :amount WHERE w.id = :walletId AND w.frozenBalance >= :amount")
    int unfreezeBalance(@Param("walletId") long walletId, @Param("amount") BigDecimal amount);

    /**
     * Trừ số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Modifying
    @Transactional
    @Query("UPDATE WalletJpaEntity w SET w.frozenBalance = w.frozenBalance - :amount WHERE w.memberId = :memberId AND w.coin = :coin AND w.frozenBalance >= :amount")
    void subtractBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Cộng số dư
     * @param memberId ID của thành viên
     * @param coin Coin
     * @param amount Số lượng
     */
    @Modifying
    @Transactional
    @Query("UPDATE WalletJpaEntity w SET w.availableBalance = w.availableBalance + :amount, w.balance = w.balance + :amount WHERE w.memberId = :memberId AND w.coin = :coin")
    void addBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Cập nhật tổng phí giao dịch
     * @param walletId ID của ví
     * @param fee Phí giao dịch
     * @return int
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE WalletJpaEntity w SET w.totalFee = w.totalFee + :fee, w.updateTime = CURRENT_TIMESTAMP WHERE w.id = :walletId")
    int updateTotalFee(@Param("walletId") Long walletId, @Param("fee") BigDecimal fee);

    /**
     * Cập nhật lợi nhuận đã thực hiện
     * @param walletId ID của ví
     * @param realizedPnl Lợi nhuận đã thực hiện
     * @return int
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE WalletJpaEntity w SET w.realizedPnl = w.realizedPnl + :realizedPnl, w.updateTime = CURRENT_TIMESTAMP WHERE w.id = :walletId")
    int updateRealizedPnl(@Param("walletId") Long walletId, @Param("realizedPnl") BigDecimal realizedPnl);
}
