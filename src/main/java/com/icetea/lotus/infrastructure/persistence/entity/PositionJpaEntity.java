package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.MarginMode;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Position
 */
@Entity
@Table(name = "contract_position")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PositionJpaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "member_id", nullable = false)
    private Long memberId;

    @Column(name = "symbol", nullable = false, length = 50)
    private String symbol;

    @Enumerated(EnumType.STRING)
    @Column(name = "direction", nullable = false, length = 10)
    private PositionDirection direction;

    @Column(name = "volume", nullable = false, columnDefinition = "numeric")
    private BigDecimal volume;

    @Column(name = "open_price", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal openPrice;

    @Column(name = "close_price", columnDefinition = "numeric(18,8)")
    private BigDecimal closePrice;

    @Column(name = "liquidation_price", columnDefinition = "numeric(18,8)")
    private BigDecimal liquidationPrice;

    @Column(name = "maintenance_margin", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal maintenanceMargin;

    @Column(name = "margin", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal margin;

    @Column(name = "profit", columnDefinition = "numeric(18,8)")
    private BigDecimal profit;

    @Enumerated(EnumType.STRING)
    @Column(name = "margin_mode", nullable = false, length = 20)
    private MarginMode marginMode;

    @Column(name = "leverage", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal leverage;

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;

    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    @Column(name = "remark", length = 255)
    private String remark;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private PositionStatus status;
}
