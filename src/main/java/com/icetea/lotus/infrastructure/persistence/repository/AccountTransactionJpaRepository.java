package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.core.domain.valueobject.AccountOperationType;
import com.icetea.lotus.infrastructure.persistence.entity.AccountTransactionJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA repository cho AccountTransactionJpaEntity
 */
@Repository
public interface AccountTransactionJpaRepository extends JpaRepository<AccountTransactionJpaEntity, String> {
    
    /**
     * Tìm tất cả giao dịch tài khoản của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách giao dịch tài khoản
     */
    List<AccountTransactionJpaEntity> findAllByMemberId(String memberId);
    
    /**
     * Tìm tất cả giao dịch tài khoản của một thành viên theo loại
     * @param memberId ID của thành viên
     * @param type Loại giao dịch
     * @return Danh sách giao dịch tài khoản
     */
    List<AccountTransactionJpaEntity> findAllByMemberIdAndType(String memberId, AccountOperationType type);
    
    /**
     * Tìm tất cả giao dịch tài khoản của một thành viên trong khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách giao dịch tài khoản
     */
    List<AccountTransactionJpaEntity> findAllByMemberIdAndCreateTimeBetween(String memberId, LocalDateTime startTime, LocalDateTime endTime);
}
