package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.LiquidationType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Liquidation
 */
@Entity
@Table(name = "contract_liquidation")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LiquidationJpaEntity {
    
    /**
     * ID của thanh lý
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * ID của hợp đồng
     */
    @Column(name = "contract_id")
    private Long contractId;
    
    /**
     * Ký hiệu của hợp đồng
     */
    @Column(name = "symbol")
    private String symbol;
    
    /**
     * ID của thành viên
     */
    @Column(name = "member_id")
    private Long memberId;
    
    /**
     * ID của vị thế
     */
    @Column(name = "position_id")
    private Long positionId;
    
    /**
     * ID của lệnh thanh lý
     */
    @Column(name = "liquidation_order_id")
    private String liquidationOrderId;
    
    /**
     * Giá thanh lý
     */
    @Column(name = "liquidation_price")
    private BigDecimal liquidationPrice;
    
    /**
     * Khối lượng thanh lý
     */
    @Column(name = "liquidation_volume")
    private BigDecimal liquidationVolume;
    
    /**
     * Lợi nhuận/lỗ đã thực hiện
     */
    @Column(name = "realized_pnl")
    private BigDecimal realizedPnl;
    
    /**
     * Số tiền bảo hiểm sử dụng
     */
    @Column(name = "insurance_amount")
    private BigDecimal insuranceAmount;
    
    /**
     * Loại thanh lý (FULL/PARTIAL)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private LiquidationType type;
    
    /**
     * Thời gian tạo
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    /**
     * Ghi chú
     */
    @Column(name = "remark")
    private String remark;
}
