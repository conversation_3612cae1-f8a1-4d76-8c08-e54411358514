package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.repository.ContractRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Adapter cho ContractRepository
 * <PERSON><PERSON><PERSON> khai các phương thức của ContractRepository
 * <PERSON>ử dụng ContractPersistenceAdapter để thực hiện các thao tác với database
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class ContractRepositoryAdapter implements ContractRepository {

    private final ContractPersistenceAdapter contractPersistenceAdapter;

    /**
     * Tìm tất cả các hợp đồng
     * @return Danh sách các hợp đồng
     */
    @Override
    public List<Contract> findAll() {
        return contractPersistenceAdapter.findAll();
    }

    /**
     * Tìm hợp đồng theo ID
     * @param id ID của hợp đồng
     * @return Optional chứa hợp đồng nếu tìm thấy
     */
    @Override
    public Optional<Contract> findById(Long id) {
        return contractPersistenceAdapter.findById(id);
    }

    /**
     * Tìm hợp đồng theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Hợp đồng nếu tìm thấy, null nếu không tìm thấy
     */
    @Override
    public Contract findBySymbol(Symbol symbol) {
        return contractPersistenceAdapter.findBySymbol(symbol).orElse(null);
    }

    /**
     * Tìm hợp đồng theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa hợp đồng nếu tìm thấy
     */
    @Override
    public Optional<Contract> findBySymbolOptional(Symbol symbol) {
        return contractPersistenceAdapter.findBySymbol(symbol);
    }

    /**
     * Tìm các hợp đồng đang được kích hoạt
     * @return Danh sách các hợp đồng đang được kích hoạt
     */
    @Override
    public List<Contract> findAllActive() {
        return contractPersistenceAdapter.findAllActive();
    }

    /**
     * Lưu hợp đồng
     * @param contract Hợp đồng cần lưu
     * @return Hợp đồng đã được lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Contract save(Contract contract) {
        return contractPersistenceAdapter.save(contract);
    }

    /**
     * Phục hồi khi lưu hợp đồng thất bại
     * @param e Ngoại lệ
     * @param contract Hợp đồng cần lưu
     * @return Contract
     */
    @Recover
    public Contract recoverSave(Exception e, Contract contract) {
        log.error("Đã thử lại lưu hợp đồng 3 lần nhưng thất bại, symbol = {}", contract.getSymbol().getValue(), e);
        throw new DatabaseException("Không thể lưu hợp đồng sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Xóa hợp đồng
     * @param contract Hợp đồng cần xóa
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void delete(Contract contract) {
        contractPersistenceAdapter.delete(contract);
    }

    /**
     * Phục hồi khi xóa hợp đồng thất bại
     * @param e Ngoại lệ
     * @param contract Hợp đồng cần xóa
     */
    @Recover
    public void recoverDelete(Exception e, Contract contract) {
        log.error("Đã thử lại xóa hợp đồng 3 lần nhưng thất bại, symbol = {}", contract.getSymbol().getValue(), e);
        throw new DatabaseException("Không thể xóa hợp đồng sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Xóa hợp đồng theo ID
     * @param id ID của hợp đồng cần xóa
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void deleteById(Long id) {
        try {
            contractPersistenceAdapter.deleteById(id);
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy hợp đồng để xóa, id = {}", id);
        }
    }

    /**
     * Phục hồi khi xóa hợp đồng theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của hợp đồng cần xóa
     */
    @Recover
    public void recoverDeleteById(Exception e, Long id) {
        log.error("Đã thử lại xóa hợp đồng theo ID 3 lần nhưng thất bại, id = {}", id, e);
        throw new DatabaseException("Không thể xóa hợp đồng theo ID sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm các hợp đồng đã được kích hoạt
     * @return Danh sách các hợp đồng đã được kích hoạt
     */
    @Override
    public List<Contract> findAllEnabled() {
        return contractPersistenceAdapter.findAllEnabled();
    }

    /**
     * Tìm hợp đồng theo symbol và trạng thái kích hoạt
     * @param symbol Symbol của hợp đồng
     * @param enabled Trạng thái kích hoạt
     * @return Hợp đồng nếu tìm thấy, null nếu không tìm thấy
     */
    @Override
    public Contract findBySymbolAndEnabled(Symbol symbol, boolean enabled) {
        return contractPersistenceAdapter.findBySymbolAndEnabled(symbol, enabled);
    }

    /**
     * Tìm hợp đồng theo symbol và trạng thái kích hoạt
     * @param symbol Symbol của hợp đồng
     * @param enable Trạng thái kích hoạt (Integer)
     * @return Hợp đồng nếu tìm thấy, null nếu không tìm thấy
     */
    @Override
    public Contract findBySymbolAndEnable(Symbol symbol, Integer enable) {
        boolean enabled = (enable != null && enable == 1);
        return contractPersistenceAdapter.findBySymbolAndEnabled(symbol, enabled);
    }

    /**
     * Tìm tất cả các hợp đồng có thời điểm đáo hạn trước một thời điểm cụ thể
     * @param date Thời điểm cần so sánh
     * @return Danh sách các hợp đồng đáo hạn trước thời điểm đã cho
     */
    @Override
    public List<Contract> findAllByExpiryDateBefore(LocalDateTime date) {
        return contractPersistenceAdapter.findAllByExpiryDateBefore(date);
    }
}
