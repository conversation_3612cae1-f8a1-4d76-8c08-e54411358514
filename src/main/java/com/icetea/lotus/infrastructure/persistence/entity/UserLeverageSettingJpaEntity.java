package com.icetea.lotus.infrastructure.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA entity cho UserLeverageSetting
 */
@Entity
@Table(name = "contract_user_leverage_setting")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserLeverageSettingJpaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "member_id")
    private Long memberId;

    @Column(name = "symbol")
    private String symbol;

    @Column(name = "leverage", columnDefinition = "decimal(18,8)")
    private BigDecimal leverage;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;
}
