package com.icetea.lotus.infrastructure.persistence.mongodb.service;

import com.icetea.lotus.infrastructure.persistence.mongodb.document.OrderBookSnapshotDocument;
import com.icetea.lotus.infrastructure.persistence.mongodb.repository.OrderBookSnapshotRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Service để cleanup các snapshot cũ trong MongoDB
 * Tránh việc bị đầy bộ nhớ do tích lũy snapshot theo thờ<PERSON> gian
 */
@Slf4j
@Service
public class SnapshotCleanupService {

    private final OrderBookSnapshotRepository snapshotRepository;
    
    // Configuration
    @Value("${snapshot.cleanup.retention.days:7}")
    private int retentionDays; // Giữ snapshot trong 7 ngày
    
    @Value("${snapshot.cleanup.max.per.symbol:100}")
    private int maxSnapshotsPerSymbol; // Tối đa 100 snapshots per symbol
    
    @Value("${snapshot.cleanup.batch.size:50}")
    private int batchSize; // Xóa 50 snapshots mỗi lần
    
    @Value("${snapshot.cleanup.enabled:true}")
    private boolean cleanupEnabled = true; // Default to true
    
    // Statistics
    private final AtomicLong totalCleaned = new AtomicLong(0);
    private final AtomicLong lastCleanupTime = new AtomicLong(0);
    private final AtomicLong cleanupErrors = new AtomicLong(0);
    
    @Autowired
    public SnapshotCleanupService(OrderBookSnapshotRepository snapshotRepository) {
        this.snapshotRepository = snapshotRepository;

        // Set default values if not injected (for tests)
        if (retentionDays == 0) retentionDays = 7;
        if (maxSnapshotsPerSymbol == 0) maxSnapshotsPerSymbol = 100;
        if (batchSize == 0) batchSize = 50;

        log.info("SnapshotCleanupService initialized: retentionDays={}, maxPerSymbol={}, batchSize={}",
                retentionDays, maxSnapshotsPerSymbol, batchSize);
    }

    /**
     * Scheduled cleanup task - chạy mỗi 6 giờ
     */
    @Scheduled(fixedRate = 21600000) // 6 hours = 6 * 60 * 60 * 1000
    public void performScheduledCleanup() {
        if (!cleanupEnabled) {
            log.debug("Snapshot cleanup is disabled");
            return;
        }
        
        try {
            log.info("Starting scheduled snapshot cleanup...");
            long startTime = System.currentTimeMillis();
            
            CleanupResult result = performCleanup();
            
            long duration = System.currentTimeMillis() - startTime;
            lastCleanupTime.set(System.currentTimeMillis());
            
            log.info("Scheduled cleanup completed: {} snapshots cleaned in {}ms. " +
                    "By age: {}, by count: {}, errors: {}", 
                    result.totalCleaned, duration, result.cleanedByAge, 
                    result.cleanedByCount, result.errors);
                    
        } catch (Exception e) {
            cleanupErrors.incrementAndGet();
            log.error("Failed to perform scheduled cleanup", e);
        }
    }

    /**
     * Manual cleanup trigger
     */
    public CleanupResult performCleanup() {
        CleanupResult result = new CleanupResult();

        if (!cleanupEnabled) {
            log.debug("Snapshot cleanup is disabled");
            return result; // Return empty result
        }

        try {
            // 1. Cleanup by age (older than retention days)
            result.cleanedByAge = cleanupByAge();

            // 2. Cleanup by count (keep only latest N snapshots per symbol)
            result.cleanedByCount = cleanupByCount();

            result.totalCleaned = result.cleanedByAge + result.cleanedByCount;
            totalCleaned.addAndGet(result.totalCleaned);

        } catch (Exception e) {
            result.errors++;
            cleanupErrors.incrementAndGet();
            log.error("Error during cleanup", e);
        }

        return result;
    }

    /**
     * Cleanup snapshots older than retention period
     */
    private long cleanupByAge() {
        try {
            Instant cutoffTime = Instant.now().minus(retentionDays, ChronoUnit.DAYS);
            
            log.debug("Cleaning up snapshots older than: {}", cutoffTime);
            
            // Find old snapshots in batches
            long totalDeleted = 0;
            boolean hasMore = true;
            
            while (hasMore) {
                List<OrderBookSnapshotDocument> oldSnapshots = snapshotRepository
                        .findByTimestampBeforeOrderByTimestampAsc(cutoffTime, PageRequest.of(0, batchSize));
                
                if (oldSnapshots.isEmpty()) {
                    hasMore = false;
                } else {
                    // Delete batch
                    snapshotRepository.deleteAll(oldSnapshots);
                    totalDeleted += oldSnapshots.size();
                    
                    log.debug("Deleted {} old snapshots (batch)", oldSnapshots.size());
                    
                    // Check if we have more
                    hasMore = oldSnapshots.size() == batchSize;
                }
            }
            
            if (totalDeleted > 0) {
                log.info("Cleaned up {} snapshots older than {} days", totalDeleted, retentionDays);
            }
            
            return totalDeleted;
            
        } catch (Exception e) {
            log.error("Failed to cleanup snapshots by age", e);
            return 0;
        }
    }

    /**
     * Cleanup excess snapshots per symbol (keep only latest N)
     */
    private long cleanupByCount() {
        try {
            // Get all distinct symbols
            List<String> symbols = snapshotRepository.findDistinctSymbols();
            
            long totalDeleted = 0;
            
            for (String symbol : symbols) {
                try {
                    long deleted = cleanupSymbolExcessSnapshots(symbol);
                    totalDeleted += deleted;
                } catch (Exception e) {
                    log.warn("Failed to cleanup excess snapshots for symbol: {}", symbol, e);
                }
            }
            
            if (totalDeleted > 0) {
                log.info("Cleaned up {} excess snapshots across {} symbols", totalDeleted, symbols.size());
            }
            
            return totalDeleted;
            
        } catch (Exception e) {
            log.error("Failed to cleanup snapshots by count", e);
            return 0;
        }
    }

    /**
     * Cleanup excess snapshots for a specific symbol
     */
    private long cleanupSymbolExcessSnapshots(String symbol) {
        // Count total snapshots for this symbol
        long totalCount = snapshotRepository.countBySymbol(symbol);
        
        if (totalCount <= maxSnapshotsPerSymbol) {
            return 0; // No cleanup needed
        }
        
        long excessCount = totalCount - maxSnapshotsPerSymbol;
        
        // Find oldest snapshots to delete
        List<OrderBookSnapshotDocument> excessSnapshots = snapshotRepository
                .findBySymbolOrderByTimestampAsc(symbol, PageRequest.of(0, (int) excessCount));
        
        if (!excessSnapshots.isEmpty()) {
            snapshotRepository.deleteAll(excessSnapshots);
            log.debug("Deleted {} excess snapshots for symbol: {}", excessSnapshots.size(), symbol);
        }
        
        return excessSnapshots.size();
    }

    /**
     * Force cleanup for specific symbol
     */
    public long cleanupSymbol(String symbol) {
        try {
            log.info("Force cleanup for symbol: {}", symbol);
            
            long deletedByAge = 0;
            long deletedByCount = 0;
            
            // Cleanup by age for this symbol
            Instant cutoffTime = Instant.now().minus(retentionDays, ChronoUnit.DAYS);
            List<OrderBookSnapshotDocument> oldSnapshots = snapshotRepository
                    .findBySymbolAndTimestampBefore(symbol, cutoffTime);
            
            if (!oldSnapshots.isEmpty()) {
                snapshotRepository.deleteAll(oldSnapshots);
                deletedByAge = oldSnapshots.size();
            }
            
            // Cleanup by count for this symbol
            deletedByCount = cleanupSymbolExcessSnapshots(symbol);
            
            long totalDeleted = deletedByAge + deletedByCount;
            totalCleaned.addAndGet(totalDeleted);
            
            log.info("Force cleanup completed for symbol: {}. Deleted: {} (age: {}, count: {})", 
                    symbol, totalDeleted, deletedByAge, deletedByCount);
            
            return totalDeleted;
            
        } catch (Exception e) {
            cleanupErrors.incrementAndGet();
            log.error("Failed to force cleanup symbol: {}", symbol, e);
            return 0;
        }
    }

    /**
     * Get cleanup statistics
     */
    public CleanupStatistics getStatistics() {
        return new CleanupStatistics(
                totalCleaned.get(),
                cleanupErrors.get(),
                lastCleanupTime.get(),
                retentionDays,
                maxSnapshotsPerSymbol,
                cleanupEnabled
        );
    }

    /**
     * Update cleanup configuration
     */
    public void updateConfiguration(int newRetentionDays, int newMaxPerSymbol, boolean enabled) {
        this.retentionDays = newRetentionDays;
        this.maxSnapshotsPerSymbol = newMaxPerSymbol;
        this.cleanupEnabled = enabled;
        
        log.info("Updated cleanup configuration: retentionDays={}, maxPerSymbol={}, enabled={}", 
                retentionDays, maxSnapshotsPerSymbol, cleanupEnabled);
    }

    /**
     * Cleanup result data class
     */
    public static class CleanupResult {
        public long totalCleaned = 0;
        public long cleanedByAge = 0;
        public long cleanedByCount = 0;
        public long errors = 0;

        @Override
        public String toString() {
            return String.format("CleanupResult{total=%d, byAge=%d, byCount=%d, errors=%d}", 
                    totalCleaned, cleanedByAge, cleanedByCount, errors);
        }
    }

    /**
     * Cleanup statistics data class
     */
    public static class CleanupStatistics {
        public final long totalCleaned;
        public final long errors;
        public final long lastCleanupTime;
        public final int retentionDays;
        public final int maxSnapshotsPerSymbol;
        public final boolean enabled;

        public CleanupStatistics(long totalCleaned, long errors, long lastCleanupTime, 
                               int retentionDays, int maxSnapshotsPerSymbol, boolean enabled) {
            this.totalCleaned = totalCleaned;
            this.errors = errors;
            this.lastCleanupTime = lastCleanupTime;
            this.retentionDays = retentionDays;
            this.maxSnapshotsPerSymbol = maxSnapshotsPerSymbol;
            this.enabled = enabled;
        }

        @Override
        public String toString() {
            return String.format(
                "CleanupStats{cleaned=%d, errors=%d, lastRun=%d, retention=%dd, maxPerSymbol=%d, enabled=%s}",
                totalCleaned, errors, lastCleanupTime, retentionDays, maxSnapshotsPerSymbol, enabled
            );
        }
    }
}
