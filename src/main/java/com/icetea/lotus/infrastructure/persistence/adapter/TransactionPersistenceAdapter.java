package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.TransactionPersistencePort;
import com.icetea.lotus.core.domain.entity.Transaction;
import com.icetea.lotus.core.domain.entity.TransactionType;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.valueobject.TransactionId;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.TransactionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.TransactionPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.TransactionJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho việc lưu trữ Transaction
 * Triển khai TransactionPersistencePort
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class TransactionPersistenceAdapter implements TransactionPersistencePort {

    private final TransactionJpaRepository transactionJpaRepository;
    private final TransactionPersistenceMapper transactionPersistenceMapper;

    /**
     * Tìm giao dịch theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của giao dịch
     * @return Optional chứa giao dịch nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Transaction> findById(TransactionId id) {
        try {
            log.debug("Tìm giao dịch theo ID, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            // Chuyển đổi Long sang String
            String idString = String.valueOf(id.getValue());

            Optional<TransactionJpaEntity> entity = transactionJpaRepository.findById(idString);

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy giao dịch, id = {}", idString);
            } else {
                log.debug("Không tìm thấy giao dịch, id = {}", idString);
            }

            return entity.map(transactionPersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm giao dịch theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giao dịch theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm giao dịch theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm giao dịch theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của giao dịch
     * @return Optional<Transaction>
     */
    @Recover
    public Optional<Transaction> recoverFindById(Exception e, TransactionId id) {
        log.error("Đã thử lại tìm giao dịch theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm giao dịch theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Transaction> findByMemberId(Long memberId) {
        log.info("Tìm giao dịch theo memberId, memberId = {}", memberId);

        try {
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberId(memberId);
            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm giao dịch theo memberId thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm giao dịch theo memberId và type
     * @param memberId ID của thành viên
     * @param type Loại giao dịch
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Transaction> findByMemberIdAndType(Long memberId, TransactionType type) {
        log.info("Tìm giao dịch theo memberId và type, memberId = {}, type = {}", memberId, type);

        try {
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberIdAndType(memberId, type.name());
            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm giao dịch theo memberId và type thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm giao dịch theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Transaction> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tìm giao dịch theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

        try {
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberIdAndSymbol(memberId, symbol.getValue());
            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm giao dịch theo memberId và symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm giao dịch theo memberId, symbol và type
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @param type Loại giao dịch
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Transaction> findByMemberIdAndSymbolAndType(Long memberId, Symbol symbol, TransactionType type) {
        log.info("Tìm giao dịch theo memberId, symbol và type, memberId = {}, symbol = {}, type = {}", memberId, symbol, type);

        try {
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberIdAndSymbolAndType(memberId, symbol.getValue(), type.name());
            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm giao dịch theo memberId, symbol và type thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm giao dịch theo orderId
     * @param orderId ID của lệnh
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Transaction> findByOrderId(OrderId orderId) {
        log.info("Tìm giao dịch theo orderId, orderId = {}", orderId);

        try {
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByOrderId(orderId.getValue());
            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm giao dịch theo orderId thất bại", e);
            throw e;
        }
    }

    /**
     * Lưu giao dịch với xử lý ngoại lệ và thử lại
     * @param transaction Giao dịch cần lưu
     * @return Giao dịch đã được lưu
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Transaction save(Transaction transaction) {
        try {
            log.debug("Lưu giao dịch, id = {}, memberId = {}, type = {}, amount = {}",
                    transaction.getId() != null ? transaction.getId().getValue() : "null",
                    transaction.getMemberId(),
                    transaction.getType(),
                    transaction.getAmount().getValue());

            if (transaction == null) {
                throw new IllegalArgumentException("Transaction không được để trống");
            }

            TransactionJpaEntity entity = transactionPersistenceMapper.domainToEntity(transaction);
            TransactionJpaEntity savedEntity = transactionJpaRepository.save(entity);

            log.debug("Đã lưu giao dịch thành công, id = {}", savedEntity.getId());

            return transactionPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giao dịch, id = {}",
                    transaction.getId() != null ? transaction.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giao dịch, id = {}",
                    transaction.getId() != null ? transaction.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu giao dịch: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu giao dịch thất bại
     * @param e Ngoại lệ
     * @param transaction Giao dịch cần lưu
     * @return Transaction
     */
    @Recover
    public Transaction recoverSave(Exception e, Transaction transaction) {
        log.error("Đã thử lại lưu giao dịch 3 lần nhưng thất bại, id = {}",
                transaction.getId() != null ? transaction.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu giao dịch sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tính tổng phí giao dịch theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Tổng phí giao dịch
     */
    @Override
    public Money sumFeeByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tính tổng phí giao dịch theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

        try {
            BigDecimal sum = transactionJpaRepository.sumFeeByMemberIdAndSymbol(memberId, symbol.getValue());
            return sum != null ? Money.of(sum) : Money.ZERO;
        } catch (Exception e) {
            log.error("Tính tổng phí giao dịch theo memberId và symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Tính tổng phí tài trợ theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Tổng phí tài trợ
     */
    @Override
    public Money sumFundingFeeByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tính tổng phí tài trợ theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

        try {
            BigDecimal sum = transactionJpaRepository.sumFundingFeeByMemberIdAndSymbol(memberId, symbol.getValue());
            return sum != null ? Money.of(sum) : Money.ZERO;
        } catch (Exception e) {
            log.error("Tính tổng phí tài trợ theo memberId và symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Tính tổng lợi nhuận theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Tổng lợi nhuận
     */
    @Override
    public Money sumProfitByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tính tổng lợi nhuận theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

        try {
            BigDecimal sum = transactionJpaRepository.sumProfitByMemberIdAndSymbol(memberId, symbol.getValue());
            return sum != null ? Money.of(sum) : Money.ZERO;
        } catch (Exception e) {
            log.error("Tính tổng lợi nhuận theo memberId và symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Tính tổng lỗ theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Tổng lỗ
     */
    @Override
    public Money sumLossByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tính tổng lỗ theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

        try {
            BigDecimal sum = transactionJpaRepository.sumLossByMemberIdAndSymbol(memberId, symbol.getValue());
            return sum != null ? Money.of(sum) : Money.ZERO;
        } catch (Exception e) {
            log.error("Tính tổng lỗ theo memberId và symbol thất bại", e);
            throw e;
        }
    }
}
