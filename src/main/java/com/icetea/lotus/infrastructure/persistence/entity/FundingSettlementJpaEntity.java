package com.icetea.lotus.infrastructure.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho FundingSettlement
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "funding_settlement")
public class FundingSettlementJpaEntity {
    
    @Id
    @Column(name = "id")
    private Long id;
    
    @Column(name = "symbol", nullable = false)
    private String symbol;
    
    @Column(name = "member_id", nullable = false)
    private String memberId;
    
    @Column(name = "position_id", nullable = false)
    private Long positionId;
    
    @Column(name = "funding_rate", nullable = false, precision = 18, scale = 8)
    private BigDecimal fundingRate;
    
    @Column(name = "funding_amount", nullable = false, precision = 18, scale = 8)
    private BigDecimal fundingAmount;
    
    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
