package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Insurance;
import com.icetea.lotus.core.domain.valueobject.InsuranceId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.InsuranceJpaEntity;
import org.springframework.stereotype.Component;

/**
 * Mapper cho Insurance và InsuranceJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class InsurancePersistenceMapper {
    
    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Insurance entityToDomain(InsuranceJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return Insurance.builder()
                .id(InsuranceId.of(entity.getId()))
                .contractId(entity.getContractId())
                .symbol(Symbol.of(entity.getSymbol()))
                .balance(Money.of(entity.getBalance()))
                .frozenBalance(Money.of(entity.getFrozenBalance()))
                .availableBalance(Money.of(entity.getAvailableBalance()))
                .totalUsed(Money.of(entity.getTotalUsed()))
                .totalDeposit(Money.of(entity.getTotalDeposit()))
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .remark(entity.getRemark())
                .build();
    }
    
    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public InsuranceJpaEntity domainToEntity(Insurance domain) {
        if (domain == null) {
            return null;
        }
        
        return InsuranceJpaEntity.builder()
                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .contractId(domain.getContractId())
                .symbol(domain.getSymbol() != null ? domain.getSymbol().getValue() : null)
                .balance(domain.getBalance() != null ? domain.getBalance().getValue() : null)
                .frozenBalance(domain.getFrozenBalance() != null ? domain.getFrozenBalance().getValue() : null)
                .availableBalance(domain.getAvailableBalance() != null ? domain.getAvailableBalance().getValue() : null)
                .totalUsed(domain.getTotalUsed() != null ? domain.getTotalUsed().getValue() : null)
                .totalDeposit(domain.getTotalDeposit() != null ? domain.getTotalDeposit().getValue() : null)
                .createTime(domain.getCreateTime())
                .updateTime(domain.getUpdateTime())
                .remark(domain.getRemark())
                .build();
    }
}
