package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.Transaction;
import com.icetea.lotus.core.domain.repository.TransactionRepository;
import com.icetea.lotus.core.domain.valueobject.TransactionId;
import com.icetea.lotus.core.domain.valueobject.TransactionType;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.TransactionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.TransactionPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.TransactionJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho TransactionRepository
 * Triển khai các phương thức của TransactionRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class TransactionRepositoryAdapter implements TransactionRepository {

    private final TransactionJpaRepository transactionJpaRepository;
    private final TransactionPersistenceMapper transactionPersistenceMapper;

    /**
     * Lấy giao dịch theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của giao dịch
     * @return Optional<Transaction>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Transaction> findById(TransactionId id) {
        try {
            log.debug("Tìm giao dịch theo ID, id = {}", id.getValue());

            // Chuyển đổi Long sang String
            String idString = String.valueOf(id.getValue());

            Optional<TransactionJpaEntity> entityOpt = transactionJpaRepository.findById(idString);

            if (entityOpt.isEmpty()) {
                log.debug("Không tìm thấy giao dịch, id = {}", idString);
                return Optional.empty();
            }

            log.debug("Đã tìm thấy giao dịch, id = {}", idString);

            return Optional.of(transactionPersistenceMapper.entityToDomain(entityOpt.get()));
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm giao dịch theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giao dịch theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm giao dịch theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm giao dịch theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của giao dịch
     * @return Optional.empty()
     */
    @Recover
    public Optional<Transaction> recoverFindById(Exception e, TransactionId id) {
        log.error("Đã thử lại tìm giao dịch theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Lấy danh sách giao dịch theo thành viên
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param limit Giới hạn số lượng
     * @param offset Vị trí bắt đầu
     * @return List<Transaction>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Transaction> findByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime, int limit, int offset) {
        try {
            log.debug("Tìm danh sách giao dịch theo thành viên, memberId = {}, startTime = {}, endTime = {}, limit = {}, offset = {}",
                    memberId, startTime, endTime, limit, offset);

            Pageable pageable = PageRequest.of(offset / limit, limit, Sort.by(Sort.Direction.DESC, "createTime"));
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberIdAndCreateTimeBetween(memberId, startTime, endTime, pageable);

            log.debug("Đã tìm thấy {} giao dịch, memberId = {}", entities.size(), memberId);

            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giao dịch theo thành viên, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giao dịch theo thành viên, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm danh sách giao dịch theo thành viên: " + e.getMessage(), e);
        }
    }

    /**
     * Lấy danh sách giao dịch theo thành viên và loại
     * @param memberId ID của thành viên
     * @param type Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param limit Giới hạn số lượng
     * @param offset Vị trí bắt đầu
     * @return List<Transaction>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Transaction> findByMemberIdAndTypeAndCreateTimeBetween(Long memberId, TransactionType type, LocalDateTime startTime, LocalDateTime endTime, int limit, int offset) {
        try {
            log.debug("Tìm danh sách giao dịch theo thành viên và loại, memberId = {}, type = {}, startTime = {}, endTime = {}, limit = {}, offset = {}",
                    memberId, type, startTime, endTime, limit, offset);

            Pageable pageable = PageRequest.of(offset / limit, limit, Sort.by(Sort.Direction.DESC, "createTime"));
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberIdAndTypeAndCreateTimeBetween(memberId, type.name(), startTime, endTime, pageable);

            log.debug("Đã tìm thấy {} giao dịch, memberId = {}, type = {}", entities.size(), memberId, type);

            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giao dịch theo thành viên và loại, memberId = {}, type = {}", memberId, type, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giao dịch theo thành viên và loại, memberId = {}, type = {}", memberId, type, e);
            throw new DatabaseException("Lỗi khi tìm danh sách giao dịch theo thành viên và loại: " + e.getMessage(), e);
        }
    }

    /**
     * Lấy danh sách giao dịch theo thành viên và coin
     * @param memberId ID của thành viên
     * @param coin Ký hiệu của đồng coin
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param limit Giới hạn số lượng
     * @param offset Vị trí bắt đầu
     * @return List<Transaction>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Transaction> findByMemberIdAndCoinAndCreateTimeBetween(Long memberId, String coin, LocalDateTime startTime, LocalDateTime endTime, int limit, int offset) {
        try {
            log.debug("Tìm danh sách giao dịch theo thành viên và coin, memberId = {}, coin = {}, startTime = {}, endTime = {}, limit = {}, offset = {}",
                    memberId, coin, startTime, endTime, limit, offset);

            Pageable pageable = PageRequest.of(offset / limit, limit, Sort.by(Sort.Direction.DESC, "createTime"));
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberIdAndCoinAndCreateTimeBetween(memberId, coin, startTime, endTime, pageable);

            log.debug("Đã tìm thấy {} giao dịch, memberId = {}, coin = {}", entities.size(), memberId, coin);

            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giao dịch theo thành viên và coin, memberId = {}, coin = {}", memberId, coin, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giao dịch theo thành viên và coin, memberId = {}, coin = {}", memberId, coin, e);
            throw new DatabaseException("Lỗi khi tìm danh sách giao dịch theo thành viên và coin: " + e.getMessage(), e);
        }
    }

    /**
     * Lấy danh sách giao dịch theo thành viên, coin và loại
     * @param memberId ID của thành viên
     * @param coin Ký hiệu của đồng coin
     * @param type Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param limit Giới hạn số lượng
     * @param offset Vị trí bắt đầu
     * @return List<Transaction>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Transaction> findByMemberIdAndCoinAndTypeAndCreateTimeBetween(Long memberId, String coin, TransactionType type, LocalDateTime startTime, LocalDateTime endTime, int limit, int offset) {
        try {
            log.debug("Tìm danh sách giao dịch theo thành viên, coin và loại, memberId = {}, coin = {}, type = {}, startTime = {}, endTime = {}, limit = {}, offset = {}",
                    memberId, coin, type, startTime, endTime, limit, offset);

            Pageable pageable = PageRequest.of(offset / limit, limit, Sort.by(Sort.Direction.DESC, "createTime"));
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByMemberIdAndCoinAndTypeAndCreateTimeBetween(memberId, coin, type.name(), startTime, endTime, pageable);

            log.debug("Đã tìm thấy {} giao dịch, memberId = {}, coin = {}, type = {}", entities.size(), memberId, coin, type);

            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giao dịch theo thành viên, coin và loại, memberId = {}, coin = {}, type = {}", memberId, coin, type, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giao dịch theo thành viên, coin và loại, memberId = {}, coin = {}, type = {}", memberId, coin, type, e);
            throw new DatabaseException("Lỗi khi tìm danh sách giao dịch theo thành viên, coin và loại: " + e.getMessage(), e);
        }
    }

    /**
     * Lấy danh sách giao dịch theo referenceId
     * @param referenceId ID tham chiếu
     * @return List<Transaction>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Transaction> findByReferenceId(String referenceId) {
        try {
            log.debug("Tìm danh sách giao dịch theo referenceId, referenceId = {}", referenceId);

            List<TransactionJpaEntity> entities = transactionJpaRepository.findByReferenceId(referenceId);

            log.debug("Đã tìm thấy {} giao dịch, referenceId = {}", entities.size(), referenceId);

            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giao dịch theo referenceId, referenceId = {}", referenceId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giao dịch theo referenceId, referenceId = {}", referenceId, e);
            throw new DatabaseException("Lỗi khi tìm danh sách giao dịch theo referenceId: " + e.getMessage(), e);
        }
    }

    /**
     * Lấy danh sách giao dịch theo loại
     * @param type Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param limit Giới hạn số lượng
     * @param offset Vị trí bắt đầu
     * @return List<Transaction>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Transaction> findByTypeAndCreateTimeBetween(TransactionType type, LocalDateTime startTime, LocalDateTime endTime, int limit, int offset) {
        try {
            log.debug("Tìm danh sách giao dịch theo loại, type = {}, startTime = {}, endTime = {}, limit = {}, offset = {}",
                    type, startTime, endTime, limit, offset);

            Pageable pageable = PageRequest.of(offset / limit, limit, Sort.by(Sort.Direction.DESC, "createTime"));
            List<TransactionJpaEntity> entities = transactionJpaRepository.findByTypeAndCreateTimeBetween(type.name(), startTime, endTime, pageable);

            log.debug("Đã tìm thấy {} giao dịch, type = {}", entities.size(), type);

            return entities.stream()
                    .map(transactionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giao dịch theo loại, type = {}", type, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giao dịch theo loại, type = {}", type, e);
            throw new DatabaseException("Lỗi khi tìm danh sách giao dịch theo loại: " + e.getMessage(), e);
        }
    }

    /**
     * Tính tổng số tiền giao dịch theo thành viên và loại
     * @param memberId ID của thành viên
     * @param type Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return BigDecimal
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal sumAmountByMemberIdAndTypeAndCreateTimeBetween(Long memberId, TransactionType type, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tính tổng số tiền giao dịch theo thành viên và loại, memberId = {}, type = {}, startTime = {}, endTime = {}",
                    memberId, type, startTime, endTime);

            BigDecimal totalAmount = transactionJpaRepository.sumAmountByMemberIdAndTypeAndCreateTimeBetween(memberId, type.name(), startTime, endTime);

            log.debug("Tổng số tiền giao dịch, memberId = {}, type = {}, totalAmount = {}", memberId, type, totalAmount);

            return totalAmount != null ? totalAmount : BigDecimal.ZERO;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính tổng số tiền giao dịch theo thành viên và loại, memberId = {}, type = {}", memberId, type, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính tổng số tiền giao dịch theo thành viên và loại, memberId = {}, type = {}", memberId, type, e);
            throw new DatabaseException("Lỗi khi tính tổng số tiền giao dịch theo thành viên và loại: " + e.getMessage(), e);
        }
    }

    /**
     * Đếm số lượng giao dịch theo thành viên và loại
     * @param memberId ID của thành viên
     * @param type Loại giao dịch
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return long
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public long countByMemberIdAndTypeAndCreateTimeBetween(Long memberId, TransactionType type, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Đếm số lượng giao dịch theo thành viên và loại, memberId = {}, type = {}, startTime = {}, endTime = {}",
                    memberId, type, startTime, endTime);

            long count = transactionJpaRepository.countByMemberIdAndTypeAndCreateTimeBetween(memberId, type.name(), startTime, endTime);

            log.debug("Số lượng giao dịch, memberId = {}, type = {}, count = {}", memberId, type, count);

            return count;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi đếm số lượng giao dịch theo thành viên và loại, memberId = {}, type = {}", memberId, type, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đếm số lượng giao dịch theo thành viên và loại, memberId = {}, type = {}", memberId, type, e);
            throw new DatabaseException("Lỗi khi đếm số lượng giao dịch theo thành viên và loại: " + e.getMessage(), e);
        }
    }

    /**
     * Lưu giao dịch
     * @param transaction Giao dịch
     * @return Transaction
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Transaction save(Transaction transaction) {
        try {
            log.debug("Lưu giao dịch, id = {}", transaction.getId() != null ? transaction.getId().getValue() : "null");

            TransactionJpaEntity entity = transactionPersistenceMapper.domainToEntity(transaction);
            TransactionJpaEntity savedEntity = transactionJpaRepository.save(entity);

            log.debug("Đã lưu giao dịch thành công, id = {}", savedEntity.getId());

            return transactionPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giao dịch, id = {}", transaction.getId() != null ? transaction.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giao dịch, id = {}", transaction.getId() != null ? transaction.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu giao dịch: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu giao dịch thất bại
     * @param e Ngoại lệ
     * @param transaction Giao dịch
     * @return null
     */
    @Recover
    public Transaction recoverSave(Exception e, Transaction transaction) {
        log.error("Đã thử lại lưu giao dịch 3 lần nhưng thất bại, id = {}", transaction.getId() != null ? transaction.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu giao dịch sau 3 lần thử lại: " + e.getMessage(), e);
    }
}
