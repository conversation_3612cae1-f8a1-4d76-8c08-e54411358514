package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.MarginMode;
import com.icetea.lotus.core.domain.entity.TransactionType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Transaction
 */
@Entity
@Table(name = "contract_transaction")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionJpaEntity {

    /**
     * ID của giao dịch
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * ID của thành viên
     */
    @Column(name = "member_id")
    private Long memberId;

    /**
     * Số tiền giao dịch
     */
    @Column(name = "amount", columnDefinition = "decimal(26,16)")
    private BigDecimal amount;

    /**
     * <PERSON>ý hiệu của đồng coin
     */
    @Column(name = "coin")
    private String coin;

    /**
     * ID tham chiếu
     */
    @Column(name = "reference_id")
    private String referenceId;

    /**
     * Thời gian tạo
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * Loại giao dịch
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private TransactionType type;

    /**
     * Địa chỉ giao dịch
     */
    @Column(name = "address")
    private String address;

    /**
     * Phí giao dịch
     */
    @Column(name = "fee", precision = 26, scale = 16)
    private BigDecimal fee;

    /**
     * Cờ đánh dấu
     */
    @Column(name = "flag", nullable = false, columnDefinition = "int default 0")
    private int flag;

    /**
     * Phí thực tế
     */
    @Column(name = "real_fee")
    private String realFee;

    /**
     * Phí giảm giá
     */
    @Column(name = "discount_fee")
    private String discountFee;

    /**
     * Đã trả thưởng chưa (0: Chưa, 1: Rồi)
     */
    @Column(name = "is_reward", columnDefinition = "int default 0")
    private int isReward;

    /**
     * ID của hợp đồng
     */
    @Column(name = "contract_id")
    private Long contractId;

    /**
     * Ký hiệu của hợp đồng
     */
    @Column(name = "symbol")
    private String symbol;

    /**
     * ID của lệnh
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * ID của giao dịch
     */
    @Column(name = "trade_id")
    private String tradeId;

    /**
     * Đòn bẩy
     */
    @Column(name = "leverage")
    private Integer leverage;

    /**
     * Chế độ margin
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "margin_mode")
    private MarginMode marginMode;

    /**
     * Lợi nhuận/lỗ đã thực hiện
     */
    @Column(name = "realized_pnl")
    private BigDecimal realizedPnl;

    /**
     * Thanh lý
     */
    @Column(name = "liquidation")
    private Boolean liquidation;

    /**
     * ADL
     */
    @Column(name = "adl")
    private Boolean adl;

    /**
     * Phí tài trợ
     */
    @Column(name = "funding_fee")
    private BigDecimal fundingFee;
}
