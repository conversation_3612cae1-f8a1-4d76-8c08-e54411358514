package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.InsuranceJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * JPA Repository cho InsuranceJpaEntity
 */
@Repository
public interface InsuranceJpaRepository extends JpaRepository<InsuranceJpaEntity, Long>, JpaSpecificationExecutor<InsuranceJpaEntity> {
    
    /**
     * Tìm quỹ bảo hiểm theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa quỹ bảo hiểm nếu tìm thấy
     */
    Optional<InsuranceJpaEntity> findBySymbol(String symbol);
    
    /**
     * Tìm quỹ bảo hiểm theo contractId
     * @param contractId ID của hợp đồng
     * @return Optional chứa quỹ bảo hiểm nếu tìm thấy
     */
    Optional<InsuranceJpaEntity> findByContractId(Long contractId);
}
