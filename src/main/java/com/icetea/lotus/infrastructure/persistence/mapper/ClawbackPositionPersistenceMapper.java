package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.ClawbackPosition;
import com.icetea.lotus.core.domain.valueobject.*;
import com.icetea.lotus.infrastructure.persistence.entity.ClawbackPositionJpaEntity;
import org.springframework.stereotype.Component;

/**
 * Mapper cho ClawbackPosition và ClawbackPositionJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class ClawbackPositionPersistenceMapper {
    
    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public ClawbackPosition entityToDomain(ClawbackPositionJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return ClawbackPosition.builder()
                .id(ClawbackPositionId.of(entity.getId()))
                .contractId(entity.getContractId())
                .symbol(Symbol.of(entity.getSymbol()))
                .memberId(entity.getMemberId())
                .positionId(PositionId.of(entity.getPositionId()))
                .unrealizedPnl(Money.of(entity.getUnrealizedPnl()))
                .clawbackAmount(Money.of(entity.getClawbackAmount()))
                .clawbackRate(Money.of(entity.getClawbackRate()))
                .createTime(entity.getCreateTime())
                .build();
    }
    
    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public ClawbackPositionJpaEntity domainToEntity(ClawbackPosition domain) {
        if (domain == null) {
            return null;
        }
        
        return ClawbackPositionJpaEntity.builder()
                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .contractId(domain.getContractId())
                .symbol(domain.getSymbol() != null ? domain.getSymbol().getValue() : null)
                .memberId(domain.getMemberId())
                .positionId(domain.getPositionId() != null ? domain.getPositionId().getValue() : null)
                .unrealizedPnl(domain.getUnrealizedPnl() != null ? domain.getUnrealizedPnl().getValue() : null)
                .clawbackAmount(domain.getClawbackAmount() != null ? domain.getClawbackAmount().getValue() : null)
                .clawbackRate(domain.getClawbackRate() != null ? domain.getClawbackRate().getValue() : null)
                .createTime(domain.getCreateTime())
                .build();
    }
}
