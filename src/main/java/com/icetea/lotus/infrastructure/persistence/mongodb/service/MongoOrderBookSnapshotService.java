package com.icetea.lotus.infrastructure.persistence.mongodb.service;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.matching.distributed.DistributedOrderBookSnapshot;
import com.icetea.lotus.infrastructure.persistence.mongodb.document.OrderBookSnapshotDocument;
import com.icetea.lotus.infrastructure.persistence.mongodb.repository.OrderBookSnapshotRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * MongoDB-based Order Book Snapshot Service
 * Optimized cho high-frequency trading với async operations
 */
@Slf4j
@Service
public class MongoOrderBookSnapshotService {

    private final OrderBookSnapshotRepository repository;
    private final AtomicLong versionCounter = new AtomicLong(System.currentTimeMillis());
    
    // Configuration
    @Value("${orderbook.snapshot.ttl.hours:24}")
    private int snapshotTtlHours;
    
    @Value("${orderbook.snapshot.max.versions:100}")
    private int maxVersionsPerSymbol;
    
    @Value("${orderbook.snapshot.compression.enabled:true}")
    private boolean compressionEnabled;

    // Performance monitoring
    private final AtomicLong saveCount = new AtomicLong(0);
    private final AtomicLong loadCount = new AtomicLong(0);
    private final AtomicLong totalSaveTime = new AtomicLong(0);
    private final AtomicLong totalLoadTime = new AtomicLong(0);

    @Autowired
    public MongoOrderBookSnapshotService(OrderBookSnapshotRepository repository) {
        this.repository = repository;
        log.info("MongoOrderBookSnapshotService initialized with TTL: {}h, MaxVersions: {}", 
                snapshotTtlHours, maxVersionsPerSymbol);
    }

    /**
     * Save snapshot to MongoDB (async)
     * @param snapshot Order book snapshot
     * @param symbol Trading symbol
     * @return CompletableFuture with saved document ID
     */
    @Async
    public CompletableFuture<String> saveSnapshotAsync(DistributedOrderBookSnapshot snapshot, Symbol symbol) {
        return CompletableFuture.supplyAsync(() -> saveSnapshot(snapshot, symbol));
    }

    /**
     * Save snapshot to MongoDB (sync)
     * @param snapshot Order book snapshot
     * @param symbol Trading symbol
     * @return Saved document ID
     */
    public String saveSnapshot(DistributedOrderBookSnapshot snapshot, Symbol symbol) {
        long startTime = System.nanoTime();

        try {
            log.debug("Starting snapshot save for symbol: {}", symbol.getValue());

            // Validate inputs - these checks are necessary for runtime safety
            Objects.requireNonNull(snapshot, "Snapshot cannot be null");
            Objects.requireNonNull(symbol, "Symbol cannot be null");

            // Generate version and document
            long version = versionCounter.incrementAndGet();
            log.debug("Converting snapshot to document for symbol: {}, version: {}", symbol.getValue(), version);
            OrderBookSnapshotDocument document = convertToDocument(snapshot, symbol, version);
            
            // Save to MongoDB
            OrderBookSnapshotDocument saved = repository.save(document);
            
            // Cleanup old versions asynchronously
            cleanupOldVersionsAsync(symbol.getValue());
            
            // Update statistics
            saveCount.incrementAndGet();
            totalSaveTime.addAndGet(System.nanoTime() - startTime);
            
            log.debug("Saved snapshot for symbol: {}, version: {}, size: {} bytes", 
                    symbol.getValue(), version, document.getMetadata().getSnapshotSizeBytes());
            
            return saved.getId();
            
        } catch (Exception e) {
            log.error("Failed to save snapshot for symbol: {}", symbol.getValue(), e);
            throw new SnapshotPersistenceException("Failed to save snapshot", e);
        }
    }

    /**
     * Load latest snapshot from MongoDB
     * @param symbol Trading symbol
     * @return Optional snapshot
     */
    public Optional<DistributedOrderBookSnapshot> loadLatestSnapshot(Symbol symbol) {
        long startTime = System.nanoTime();
        
        try {
            Optional<OrderBookSnapshotDocument> document = repository.findLatestBySymbol(symbol.getValue());
            
            if (document.isPresent()) {
                DistributedOrderBookSnapshot snapshot = convertFromDocument(document.get());
                
                // Update statistics
                loadCount.incrementAndGet();
                totalLoadTime.addAndGet(System.nanoTime() - startTime);
                
                log.debug("Loaded snapshot for symbol: {}, version: {}", 
                        symbol.getValue(), document.get().getVersion());
                
                return Optional.of(snapshot);
            }
            
            log.debug("No snapshot found for symbol: {}", symbol.getValue());
            return Optional.empty();
            
        } catch (Exception e) {
            log.error("Failed to load snapshot for symbol: {}", symbol.getValue(), e);
            return Optional.empty();
        }
    }

    /**
     * Load snapshot by version
     * @param symbol Trading symbol
     * @param version Snapshot version
     * @return Optional snapshot
     */
    public Optional<DistributedOrderBookSnapshot> loadSnapshotByVersion(Symbol symbol, long version) {
        try {
            Optional<OrderBookSnapshotDocument> document = 
                    repository.findBySymbolAndVersion(symbol.getValue(), version);
            
            return document.map(this::convertFromDocument);
            
        } catch (Exception e) {
            log.error("Failed to load snapshot for symbol: {}, version: {}", symbol.getValue(), version, e);
            return Optional.empty();
        }
    }

    /**
     * Get snapshot history for symbol
     * @param symbol Trading symbol
     * @param limit Maximum number of snapshots
     * @return List of snapshot metadata
     */
    public List<SnapshotMetadata> getSnapshotHistory(Symbol symbol, int limit) {
        try {
            Pageable pageable = PageRequest.of(0, limit);
            return repository.findBySymbolOrderByVersionDesc(symbol.getValue(), pageable)
                    .getContent()
                    .stream()
                    .map(doc -> new SnapshotMetadata(
                            doc.getSymbol(),
                            doc.getVersion(),
                            doc.getTimestamp(),
                            doc.getMetadata().getTotalOrders(),
                            doc.getMetadata().getSnapshotSizeBytes()
                    ))
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Failed to get snapshot history for symbol: {}", symbol.getValue(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Delete snapshots older than specified time
     * @param olderThan Cutoff time
     * @return Number of deleted snapshots
     */
    public long deleteOldSnapshots(Instant olderThan) {
        try {
            List<OrderBookSnapshotDocument> oldSnapshots = repository.findExpiredSnapshots(olderThan);
            int count = oldSnapshots.size();
            
            if (count > 0) {
                repository.deleteByTimestampBefore(olderThan);
                log.info("Deleted {} old snapshots older than {}", count, olderThan);
            }
            
            return count;
            
        } catch (Exception e) {
            log.error("Failed to delete old snapshots", e);
            return 0;
        }
    }

    /**
     * Get snapshot statistics
     * @return Service statistics
     */
    public ServiceStatistics getStatistics() {
        long saves = saveCount.get();
        long loads = loadCount.get();
        double avgSaveTime = saves > 0 ? (double) totalSaveTime.get() / saves / 1000.0 : 0.0; // μs
        double avgLoadTime = loads > 0 ? (double) totalLoadTime.get() / loads / 1000.0 : 0.0; // μs
        
        return new ServiceStatistics(saves, loads, avgSaveTime, avgLoadTime);
    }

    /**
     * Convert domain snapshot to MongoDB document with comprehensive error handling
     */
    private OrderBookSnapshotDocument convertToDocument(DistributedOrderBookSnapshot snapshot,
                                                       Symbol symbol, long version) {
        try {
            log.debug("Converting snapshot to document: symbol={}, version={}, orders={}",
                    symbol.getValue(), version,
                    snapshot.getAllOrders() != null ? snapshot.getAllOrders().size() : 0);
        
            // Convert orders to documents with null safety and validation
            List<OrderBookSnapshotDocument.OrderDocument> allOrderDocs = new ArrayList<>();
            if (snapshot.getAllOrders() != null) {
                allOrderDocs = snapshot.getAllOrders()
                        .stream()
                        .filter(Objects::nonNull) // Filter out null orders
                        .filter(this::isValidOrder) // Filter out invalid orders
                        .map(this::convertOrderToDocument)
                        .collect(Collectors.toList());
            }

            log.debug("Converted {} orders to documents", allOrderDocs.size());

        // Group by price levels
        Map<String, List<OrderBookSnapshotDocument.OrderDocument>> buyOrders = new HashMap<>();
        Map<String, List<OrderBookSnapshotDocument.OrderDocument>> sellOrders = new HashMap<>();
        
            // Process buy/sell orders with null safety and MongoDB key escaping
            for (OrderBookSnapshotDocument.OrderDocument orderDoc : allOrderDocs) {
                try {
                    String priceKey = "0"; // Default price key
                    if (orderDoc.getPrice() != null && orderDoc.getPrice().getValue() != null) {
                        priceKey = orderDoc.getPrice().getValue();
                    }

                    // FIXED: Escape price key for MongoDB (dots not allowed in map keys)
                    String escapedPriceKey = OrderBookSnapshotDocument.escapePriceKey(priceKey);

                    if ("BUY".equals(orderDoc.getDirection())) {
                        buyOrders.computeIfAbsent(escapedPriceKey, k -> new ArrayList<>()).add(orderDoc);
                    } else {
                        sellOrders.computeIfAbsent(escapedPriceKey, k -> new ArrayList<>()).add(orderDoc);
                    }
                } catch (Exception e) {
                    log.warn("Failed to process order document: {}", orderDoc.getOrderId(), e);
                }
            }

            // Convert stop orders with null safety and validation
            List<OrderBookSnapshotDocument.OrderDocument> stopOrderDocs = new ArrayList<>();
            if (snapshot.getStopOrders() != null) {
                stopOrderDocs = snapshot.getStopOrders()
                        .stream()
                        .filter(Objects::nonNull) // Filter out null orders
                        .filter(this::isValidOrder) // Filter out invalid orders
                        .map(this::convertOrderToDocument)
                        .collect(Collectors.toList());
            }

            log.debug("Converted {} stop orders to documents", stopOrderDocs.size());

        // Calculate metadata
        OrderBookSnapshotDocument.SnapshotMetadata metadata =
                OrderBookSnapshotDocument.SnapshotMetadata.builder()
                        .totalOrders(allOrderDocs.size())
                        .buyOrdersCount(buyOrders.values().stream().mapToInt(List::size).sum())
                        .sellOrdersCount(sellOrders.values().stream().mapToInt(List::size).sum())
                        .stopOrdersCount(stopOrderDocs.size())
                        .priceLevelsBuy(buyOrders.size())
                        .priceLevelsSell(sellOrders.size())
                        .snapshotSizeBytes(estimateDocumentSize(allOrderDocs, stopOrderDocs))
                        .compressionRatio(1.0) // Default compression ratio
                        .creationTimeMicros(System.nanoTime() / 1000)
                        .sourceNode(getNodeId())
                        .checksum(calculateChecksum(allOrderDocs.size(), stopOrderDocs.size()))
                        .build();

            return OrderBookSnapshotDocument.builder()
                    .id(OrderBookSnapshotDocument.generateId(symbol.getValue(), version))
                    .symbol(symbol.getValue())
                    .version(version)
                    .timestamp(Instant.now())
                    .buyOrders(buyOrders)
                    .sellOrders(sellOrders)
                    .allOrders(allOrderDocs)
                    .stopOrders(stopOrderDocs)
                    .metadata(metadata)
                    .createdAt(Instant.now())
                    .expiresAt(OrderBookSnapshotDocument.calculateExpiryTime(snapshotTtlHours))
                    .build();

        } catch (Exception e) {
            log.error("Failed to convert snapshot to document for symbol: {}, version: {}",
                    symbol.getValue(), version, e);
            throw new SnapshotPersistenceException("Failed to convert snapshot to document", e);
        }
    }

    /**
     * Convert MongoDB document to domain snapshot
     */
    private DistributedOrderBookSnapshot convertFromDocument(OrderBookSnapshotDocument document) {
        // Create new snapshot
        DistributedOrderBookSnapshot snapshot = new DistributedOrderBookSnapshot(
                java.math.BigDecimal.valueOf(0.01) // Default segment size
        );

        // Convert and add orders
        for (OrderBookSnapshotDocument.OrderDocument orderDoc : document.getAllOrders()) {
            Order order = convertDocumentToOrder(orderDoc);
            snapshot.addOrder(order);
        }

        // Add stop orders
        for (OrderBookSnapshotDocument.OrderDocument stopOrderDoc : document.getStopOrders()) {
            Order stopOrder = convertDocumentToOrder(stopOrderDoc);
            snapshot.addStopOrder(stopOrder);
        }

        return snapshot;
    }

    /**
     * Convert Order entity to document with comprehensive null safety
     */
    private OrderBookSnapshotDocument.OrderDocument convertOrderToDocument(Order order) {
        if (order == null) {
            throw new IllegalArgumentException("Order cannot be null");
        }

        return OrderBookSnapshotDocument.OrderDocument.builder()
                .orderId(order.getOrderId() != null ? order.getOrderId().getValue() : "unknown")
                .memberId(order.getMemberId() != null ? order.getMemberId() : 0L)
                .direction(order.getDirection() != null ? order.getDirection().name() : "BUY")
                .type(order.getType() != null ? order.getType().name() : "LIMIT")
                .status(order.getStatus() != null ? order.getStatus().name() : "NEW")
                .price(convertMoneyToDocument(order.getPrice()))
                .volume(order.getVolume() != null ? order.getVolume().toString() : "0")
                .dealVolume(order.getDealVolume() != null ? order.getDealVolume().toString() : "0")
                .turnover(calculateTurnover(order).toString())
                .fee(order.getFee() != null ? order.getFee().toString() : "0")
                .leverage(order.getLeverage() != null ? order.getLeverage().intValue() : 1)
                .stopPrice(null) // Stop price not available in current Order entity
                .timeInForce(order.getTimeInForce() != null ? order.getTimeInForce().name() : "GTC")
                .stpMode(order.getSelfTradePreventionMode() != null ? order.getSelfTradePreventionMode().name() : "NONE")
                .createdAt(order.getCreateTime() != null ?
                    order.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toInstant() :
                    java.time.Instant.now())
                .updatedAt(order.getCreateTime() != null ?
                    order.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toInstant() :
                    java.time.Instant.now())
                .expiryTime(null) // Expiry time not available in current Order entity
                .clientOrderId(null) // Client order ID not available in current Order entity
                .reduceOnly(false) // Reduce only not available in current Order entity
                .postOnly(false) // Post only not available in current Order entity
                .build();
    }

    /**
     * Convert document to Order entity (simplified)
     */
    private Order convertDocumentToOrder(OrderBookSnapshotDocument.OrderDocument orderDoc) {
        // This would be a full conversion - simplified for now
        throw new UnsupportedOperationException("Order conversion from document not fully implemented");
    }

    /**
     * Convert Money to document with null safety
     */
    private OrderBookSnapshotDocument.MoneyDocument convertMoneyToDocument(Money money) {
        if (money == null) {
            return OrderBookSnapshotDocument.MoneyDocument.builder()
                    .value("0")
                    .currency("USDT")
                    .build();
        }

        return OrderBookSnapshotDocument.MoneyDocument.builder()
                .value(money.getValue() != null ? money.getValue().toString() : "0")
                .currency("USDT") // Default currency
                .build();
    }

    /**
     * Calculate turnover for order with null safety
     */
    private java.math.BigDecimal calculateTurnover(Order order) {
        if (order == null) {
            return java.math.BigDecimal.ZERO;
        }

        java.math.BigDecimal price = java.math.BigDecimal.ZERO;
        if (order.getPrice() != null && order.getPrice().getValue() != null) {
            price = order.getPrice().getValue();
        }

        java.math.BigDecimal dealVolume = java.math.BigDecimal.ZERO;
        if (order.getDealVolume() != null) {
            dealVolume = order.getDealVolume();
        }

        return price.multiply(dealVolume);
    }

    /**
     * Calculate simple checksum for data integrity
     */
    private String calculateChecksum(int totalOrders, int stopOrders) {
        int hash = totalOrders * 31 + stopOrders * 17;
        return String.format("%08x", hash);
    }

    /**
     * Validate order before conversion
     */
    private boolean isValidOrder(Order order) {
        if (order == null) {
            return false;
        }

        // Check essential fields
        if (order.getOrderId() == null) {
            log.warn("Order has null orderId, skipping");
            return false;
        }

        if (order.getMemberId() == null) {
            log.warn("Order {} has null memberId, skipping", order.getOrderId().getValue());
            return false;
        }

        // Allow orders with null price (market orders) but log warning
        if (order.getPrice() == null) {
            log.debug("Order {} has null price (possibly market order)", order.getOrderId().getValue());
        }

        return true;
    }

    /**
     * Estimate document size for metadata
     */
    private long estimateDocumentSize(List<OrderBookSnapshotDocument.OrderDocument> allOrders,
                                    List<OrderBookSnapshotDocument.OrderDocument> stopOrders) {
        // Rough estimation - would be more accurate with actual serialization
        return (allOrders.size() + stopOrders.size()) * 500L; // ~500 bytes per order
    }

    /**
     * Get current node ID
     */
    private String getNodeId() {
        return System.getProperty("node.id", "unknown");
    }

    /**
     * Cleanup old versions asynchronously
     */
    @Async
    protected void cleanupOldVersionsAsync(String symbol) {
        try {
            List<OrderBookSnapshotDocument> snapshots = repository.findBySymbolOrderByVersionDesc(symbol);
            
            if (snapshots.size() > maxVersionsPerSymbol) {
                // Keep only latest maxVersionsPerSymbol
                long minVersionToKeep = snapshots.get(maxVersionsPerSymbol - 1).getVersion();
                repository.deleteBySymbolAndVersionLessThan(symbol, minVersionToKeep);
                
                log.debug("Cleaned up old snapshots for symbol: {}, kept {} versions", 
                        symbol, maxVersionsPerSymbol);
            }
            
        } catch (Exception e) {
            log.warn("Failed to cleanup old snapshots for symbol: {}", symbol, e);
        }
    }

    /**
     * Snapshot metadata class
     */
    public static class SnapshotMetadata {
        public final String symbol;
        public final long version;
        public final Instant timestamp;
        public final int orderCount;
        public final long sizeBytes;

        public SnapshotMetadata(String symbol, long version, Instant timestamp, int orderCount, long sizeBytes) {
            this.symbol = symbol;
            this.version = version;
            this.timestamp = timestamp;
            this.orderCount = orderCount;
            this.sizeBytes = sizeBytes;
        }
    }

    /**
     * Service statistics class
     */
    public static class ServiceStatistics {
        public final long saveCount;
        public final long loadCount;
        public final double avgSaveTimeMicros;
        public final double avgLoadTimeMicros;

        public ServiceStatistics(long saveCount, long loadCount, double avgSaveTimeMicros, double avgLoadTimeMicros) {
            this.saveCount = saveCount;
            this.loadCount = loadCount;
            this.avgSaveTimeMicros = avgSaveTimeMicros;
            this.avgLoadTimeMicros = avgLoadTimeMicros;
        }

        @Override
        public String toString() {
            return String.format("MongoSnapshotStats{saves=%d(%.1fμs), loads=%d(%.1fμs)}", 
                    saveCount, avgSaveTimeMicros, loadCount, avgLoadTimeMicros);
        }
    }

    /**
     * Custom exception for snapshot persistence errors
     */
    public static class SnapshotPersistenceException extends RuntimeException {
        public SnapshotPersistenceException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
