package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.core.domain.entity.LastPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho LastPrice
 */
@Repository
public interface LastPriceJpaRepository extends JpaRepository<LastPrice, Long>, JpaSpecificationExecutor<LastPrice> {

    /**
     * Tìm giá cuối cùng mới nhất theo symbol
     * @param symbol Symbol của hợp đồng
     * @return LastPrice
     */
    LastPrice findTopBySymbolValueOrderByCreateTimeDesc(String symbol);

    /**
     * Tìm danh sách giá cuối cùng theo symbol và khoảng thời gian
     * Tìm các bản ghi có createTime nằm trong khoảng từ startTime đến endTime
     * Câu truy vấn SQL tương ứng: SELECT * FROM contract_last_price WHERE symbol = ? AND create_time BETWEEN ? AND ?
     *
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return List<LastPrice>
     */
    List<LastPrice> findBySymbolValueAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);
}
