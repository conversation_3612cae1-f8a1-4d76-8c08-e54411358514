package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.OrderJpaEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Mapper cho Order và OrderJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderPersistenceMapper {

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Order entityToDomain(OrderJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        try {
            log.debug("Bắt đầu chuyển đổi OrderJpaEntity sang Order, orderId = {}", entity.getOrderId());

            // Kiểm tra và log các trường quan trọng
            if (entity.getOrderId() == null) {
                log.error("OrderId là null, không thể chuyển đổi");
                throw new IllegalArgumentException("OrderId không được để trống");
            }

            if (entity.getSymbol() == null) {
                log.error("Symbol là null, không thể chuyển đổi, orderId = {}", entity.getOrderId());
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            // Log các trường Money để kiểm tra
            log.debug("Giá trị price = {}, triggerPrice = {}, dealMoney = {}, fee = {}, activationPrice = {}, orderId = {}",
                    entity.getPrice(), entity.getTriggerPrice(), entity.getDealMoney(), entity.getFee(), entity.getActivationPrice(), entity.getOrderId());

            // Chuyển đổi từng trường Money một cách an toàn
            Money price = null;
            if (entity.getPrice() != null) {
                try {
                    price = Money.of(entity.getPrice());
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi price, value = {}, orderId = {}", entity.getPrice(), entity.getOrderId(), e);
                }
            }

            Money triggerPrice = null;
            if (entity.getTriggerPrice() != null) {
                try {
                    triggerPrice = Money.of(entity.getTriggerPrice());
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi triggerPrice, value = {}, orderId = {}", entity.getTriggerPrice(), entity.getOrderId(), e);
                }
            }

            Money dealMoney = null;
            if (entity.getDealMoney() != null) {
                try {
                    dealMoney = Money.of(entity.getDealMoney());
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi dealMoney, value = {}, orderId = {}", entity.getDealMoney(), entity.getOrderId(), e);
                }
            }

            Money fee = null;
            if (entity.getFee() != null) {
                try {
                    fee = Money.of(entity.getFee());
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi fee, value = {}, orderId = {}", entity.getFee(), entity.getOrderId(), e);
                }
            }

            Money activationPrice = null;
            if (entity.getActivationPrice() != null) {
                try {
                    activationPrice = Money.of(entity.getActivationPrice());
                } catch (Exception e) {
                    log.error("Lỗi khi chuyển đổi activationPrice, value = {}, orderId = {}", entity.getActivationPrice(), entity.getOrderId(), e);
                }
            }

            // Xây dựng đối tượng Order
            Order order = Order.builder()
                    .orderId(OrderId.of(entity.getOrderId()))
                    .memberId(entity.getMemberId())
                    .contractId(entity.getContractId())
                    .symbol(Symbol.of(entity.getSymbol()))
                    .coinSymbol(entity.getCoinSymbol())
                    .baseSymbol(entity.getBaseSymbol())
                    .direction(entity.getDirection())
                    .type(entity.getType())
                    .price(price)
                    .triggerPrice(triggerPrice)
                    .triggerType(entity.getTriggerType())
                    .volume(entity.getVolume())
                    .dealVolume(entity.getDealVolume())
                    .dealMoney(dealMoney)
                    .fee(fee)
                    .status(entity.getStatus())
                    .createTime(entity.getCreateTime())
                    .completeTime(entity.getCompleteTime())
                    .canceledTime(entity.getCanceledTime())
                    .executeTime(entity.getExecuteTime())
                    .expireTime(entity.getExpireTime())
                    .timeInForce(entity.getTimeInForce())
                    .leverage(entity.getLeverage())
                    .reduceOnly(entity.getReduceOnly())
                    .liquidation(entity.getLiquidation())
                    .adl(entity.getAdl())
                    .implied(entity.getImplied())
                    .sourceOrderId(entity.getSourceOrderId())
                    .ocoId(entity.getOcoId())
                    .ocoOrderNo(entity.getOcoOrderNo())
                    .callbackRate(entity.getCallbackRate())
                    .activationPrice(activationPrice)
                    .postOnly(entity.getPostOnly())
                    .cancelReason(entity.getCancelReason())
                    .build();

            log.debug("Đã chuyển đổi thành công OrderJpaEntity sang Order, orderId = {}", entity.getOrderId());
            return order;
        } catch (Exception e) {
            // Ghi log chi tiết về lỗi và entity
            log.error("Lỗi khi chuyển đổi OrderJpaEntity sang Order: {}, orderId: {}, entity: {}", e.getMessage(), entity.getOrderId(), entity, e);
            throw new RuntimeException("Lỗi khi chuyển đổi OrderJpaEntity sang Order: " + e.getMessage() + ", orderId: " + entity.getOrderId(), e);
        }
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public OrderJpaEntity domainToEntity(Order domain) {
        if (domain == null) {
            return null;
        }

        return OrderJpaEntity.builder()
                .orderId(domain.getOrderId().getValue())
                .memberId(domain.getMemberId())
                .contractId(domain.getContractId())
                .symbol(domain.getSymbol().getValue())
                .coinSymbol(domain.getCoinSymbol())
                .baseSymbol(domain.getBaseSymbol())
                .direction(domain.getDirection())
                .type(domain.getType())
                .price(domain.getPrice() != null ? domain.getPrice().getValue() : null)
                .triggerPrice(domain.getTriggerPrice() != null ? domain.getTriggerPrice().getValue() : null)
                .triggerType(domain.getTriggerType())
                .volume(domain.getVolume())
                .dealVolume(domain.getDealVolume())
                .dealMoney(domain.getDealMoney() != null ? domain.getDealMoney().getValue() : null)
                .fee(domain.getFee() != null ? domain.getFee().getValue() : null)
                .status(domain.getStatus())
                .createTime(domain.getCreateTime())
                .completeTime(domain.getCompleteTime())
                .canceledTime(domain.getCanceledTime())
                .executeTime(domain.getExecuteTime())
                .expireTime(domain.getExpireTime())
                .timeInForce(domain.getTimeInForce())
                .leverage(domain.getLeverage())
                .reduceOnly(domain.getReduceOnly())
                .liquidation(domain.getLiquidation())
                .adl(domain.getAdl())
                .implied(domain.getImplied())
                .sourceOrderId(domain.getSourceOrderId())
                .ocoId(domain.getOcoId())
                .ocoOrderNo(domain.getOcoOrderNo())
                .callbackRate(domain.getCallbackRate())
                .activationPrice(domain.getActivationPrice() != null ? domain.getActivationPrice().getValue() : null)
                .postOnly(domain.getPostOnly())
                .cancelReason(domain.getCancelReason())
                .build();
    }
}
