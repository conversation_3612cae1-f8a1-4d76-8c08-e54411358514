package com.icetea.lotus.infrastructure.persistence.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * Tiện ích để tối ưu hóa truy vấn
 */
@Slf4j
public class QueryOptimizationUtil {

    private static final int DEFAULT_MAX_RESULTS = 1000;

    /**
     * <PERSON><PERSON>ớ<PERSON> hạn số lượng kết quả trả về
     * @param query TypedQuery
     * @param <T> <PERSON><PERSON><PERSON> dữ liệu
     * @return TypedQuery
     */
    public static <T> TypedQuery<T> limitResults(TypedQuery<T> query) {
        return limitResults(query, DEFAULT_MAX_RESULTS);
    }

    /**
     * Giới hạn số lượng kết quả trả về
     * @param query TypedQuery
     * @param maxResults Số lượng kết quả tối đa
     * @param <T> Kiểu dữ liệu
     * @return TypedQuery
     */
    public static <T> TypedQuery<T> limitResults(TypedQuery<T> query, int maxResults) {
        return query.setMaxResults(maxResults);
    }

    /**
     * Giới hạn số lượng kết quả trả về từ Stream
     * @param stream Stream
     * @param <T> Kiểu dữ liệu
     * @return Stream
     */
    public static <T> Stream<T> limitResults(Stream<T> stream) {
        return limitResults(stream, DEFAULT_MAX_RESULTS);
    }

    /**
     * Giới hạn số lượng kết quả trả về từ Stream
     * @param stream Stream
     * @param maxResults Số lượng kết quả tối đa
     * @param <T> Kiểu dữ liệu
     * @return Stream
     */
    public static <T> Stream<T> limitResults(Stream<T> stream, int maxResults) {
        return stream.limit(maxResults);
    }

    /**
     * Chuyển đổi kết quả sang Page
     * @param query TypedQuery
     * @param countQuery TypedQuery
     * @param pageable Pageable
     * @param <T> Kiểu dữ liệu
     * @return Page<T>
     */
    public static <T> Page<T> toPage(TypedQuery<T> query, TypedQuery<Long> countQuery, Pageable pageable) {
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        List<T> content = query.getResultList();
        Long total = countQuery.getSingleResult();

        return new PageImpl<>(content, pageable, total);
    }

    /**
     * Tạo truy vấn với phân trang
     * @param entityManager EntityManager
     * @param entityClass Class<T>
     * @param specification Specification<T>
     * @param pageable Pageable
     * @param <T> Kiểu dữ liệu
     * @return Page<T>
     */
    public static <T> Page<T> findAll(EntityManager entityManager, Class<T> entityClass,
                                     Specification<T> specification, Pageable pageable) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();

        // Tạo truy vấn để lấy dữ liệu
        CriteriaQuery<T> query = builder.createQuery(entityClass);
        Root<T> root = query.from(entityClass);

        if (specification != null) {
            Predicate predicate = specification.toPredicate(root, (CriteriaQuery<?>) query, builder);
            query.where(predicate);
        }

        // Áp dụng sắp xếp
        if (pageable.getSort().isSorted()) {
            List<jakarta.persistence.criteria.Order> orders = new ArrayList<>();

            for (Sort.Order order : pageable.getSort()) {
                if (order.isAscending()) {
                    orders.add(builder.asc(root.get(order.getProperty())));
                } else {
                    orders.add(builder.desc(root.get(order.getProperty())));
                }
            }

            query.orderBy(orders);
        }

        TypedQuery<T> typedQuery = entityManager.createQuery(query);

        // Tạo truy vấn để đếm tổng số bản ghi
        CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
        Root<T> countRoot = countQuery.from(entityClass);

        if (specification != null) {
            Predicate predicate = specification.toPredicate(countRoot, countQuery, builder);
            countQuery.where(predicate);
        }

        countQuery.select(builder.count(countRoot));

        TypedQuery<Long> typedCountQuery = entityManager.createQuery(countQuery);

        return toPage(typedQuery, typedCountQuery, pageable);
    }

    /**
     * Tạo truy vấn với phân trang và entity graph
     * @param entityManager EntityManager
     * @param entityClass Class<T>
     * @param specification Specification<T>
     * @param pageable Pageable
     * @param entityGraphName Tên entity graph
     * @param <T> Kiểu dữ liệu
     * @return Page<T>
     */
    public static <T> Page<T> findAll(EntityManager entityManager, Class<T> entityClass,
                                     Specification<T> specification, Pageable pageable,
                                     String entityGraphName) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();

        // Tạo truy vấn để lấy dữ liệu
        CriteriaQuery<T> query = builder.createQuery(entityClass);
        Root<T> root = query.from(entityClass);

        if (specification != null) {
            Predicate predicate = specification.toPredicate(root, query, builder);
            query.where(predicate);
        }

        // Áp dụng sắp xếp
        if (pageable.getSort().isSorted()) {
            List<jakarta.persistence.criteria.Order> orders = new ArrayList<>();

            for (Sort.Order order : pageable.getSort()) {
                if (order.isAscending()) {
                    orders.add(builder.asc(root.get(order.getProperty())));
                } else {
                    orders.add(builder.desc(root.get(order.getProperty())));
                }
            }

            query.orderBy(orders);
        }

        TypedQuery<T> typedQuery = entityManager.createQuery(query);

        // Áp dụng entity graph
        if (entityGraphName != null && !entityGraphName.isEmpty()) {
            typedQuery.setHint("jakarta.persistence.fetchgraph", entityManager.getEntityGraph(entityGraphName));
        }

        // Tạo truy vấn để đếm tổng số bản ghi
        CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
        Root<T> countRoot = countQuery.from(entityClass);

        if (specification != null) {
            Predicate predicate = specification.toPredicate(countRoot, countQuery, builder);
            countQuery.where(predicate);
        }

        countQuery.select(builder.count(countRoot));

        TypedQuery<Long> typedCountQuery = entityManager.createQuery(countQuery);

        return toPage(typedQuery, typedCountQuery, pageable);
    }
}
