package com.icetea.lotus.infrastructure.persistence.util;

import com.icetea.lotus.core.domain.valueobject.PageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Tiện ích cho truy vấn database
 */
@Slf4j
public class QueryUtils {
    
    /**
     * S<PERSON> lượng kết quả tối đa mặc định
     */
    public static final int DEFAULT_MAX_RESULTS = 1000;
    
    /**
     * <PERSON><PERSON><PERSON><PERSON> đổi PageRequest sang org.springframework.data.domain.PageRequest
     * @param pageRequest PageRequest
     * @return org.springframework.data.domain.PageRequest
     */
    public static org.springframework.data.domain.PageRequest toSpringPageRequest(PageRequest pageRequest) {
        return org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize());
    }
    
    /**
     * <PERSON>y<PERSON><PERSON> đổi PageRequest sang org.springframework.data.domain.PageRequest với sắp xếp
     * @param pageRequest PageRequest
     * @param sort Sort
     * @return org.springframework.data.domain.PageRequest
     */
    public static org.springframework.data.domain.PageRequest toSpringPageRequest(PageRequest pageRequest, Sort sort) {
        return org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize(), sort);
    }
    
    /**
     * Chuyển đổi PageRequest sang org.springframework.data.domain.PageRequest với sắp xếp
     * @param pageRequest PageRequest
     * @param sortBy Tên trường sắp xếp
     * @param sortDirection Hướng sắp xếp
     * @return org.springframework.data.domain.PageRequest
     */
    public static org.springframework.data.domain.PageRequest toSpringPageRequest(PageRequest pageRequest, String sortBy, Sort.Direction sortDirection) {
        return org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize(), Sort.by(sortDirection, sortBy));
    }
    
    /**
     * Chuyển đổi PageRequest sang org.springframework.data.domain.PageRequest với nhiều sắp xếp
     * @param pageRequest PageRequest
     * @param sortByList Danh sách tên trường sắp xếp
     * @param sortDirectionList Danh sách hướng sắp xếp
     * @return org.springframework.data.domain.PageRequest
     */
    public static org.springframework.data.domain.PageRequest toSpringPageRequest(PageRequest pageRequest, List<String> sortByList, List<Sort.Direction> sortDirectionList) {
        if (sortByList.size() != sortDirectionList.size()) {
            throw new IllegalArgumentException("Số lượng trường sắp xếp và hướng sắp xếp phải bằng nhau");
        }
        
        List<Sort.Order> orders = new ArrayList<>();
        for (int i = 0; i < sortByList.size(); i++) {
            orders.add(new Sort.Order(sortDirectionList.get(i), sortByList.get(i)));
        }
        
        return org.springframework.data.domain.PageRequest.of(pageRequest.getPage(), pageRequest.getSize(), Sort.by(orders));
    }
    
    /**
     * Chuyển đổi org.springframework.data.domain.Page sang Page
     * @param springPage org.springframework.data.domain.Page
     * @param pageRequest PageRequest
     * @param mapper Hàm chuyển đổi từ entity sang domain
     * @param <T> Kiểu dữ liệu của domain
     * @param <E> Kiểu dữ liệu của entity
     * @return Page
     */
    public static <T, E> com.icetea.lotus.core.domain.valueobject.Page<T> toPage(
            org.springframework.data.domain.Page<E> springPage, 
            PageRequest pageRequest, 
            Function<E, T> mapper) {
        
        List<T> content = springPage.getContent().stream()
                .map(mapper)
                .collect(Collectors.toList());
        
        return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, springPage.getTotalElements());
    }
    
    /**
     * Giới hạn số lượng kết quả trả về
     * @param list Danh sách kết quả
     * @param limit Số lượng kết quả tối đa
     * @param <T> Kiểu dữ liệu của kết quả
     * @return Danh sách kết quả đã giới hạn
     */
    public static <T> List<T> limitResults(List<T> list, int limit) {
        if (list == null) {
            return new ArrayList<>();
        }
        
        if (list.size() <= limit) {
            return list;
        }
        
        log.warn("Số lượng kết quả ({}) vượt quá giới hạn ({}), chỉ trả về {} kết quả đầu tiên", list.size(), limit, limit);
        return list.subList(0, limit);
    }
    
    /**
     * Giới hạn số lượng kết quả trả về với giới hạn mặc định
     * @param list Danh sách kết quả
     * @param <T> Kiểu dữ liệu của kết quả
     * @return Danh sách kết quả đã giới hạn
     */
    public static <T> List<T> limitResults(List<T> list) {
        return limitResults(list, DEFAULT_MAX_RESULTS);
    }
    
    /**
     * Phân trang danh sách kết quả
     * @param list Danh sách kết quả
     * @param pageRequest Yêu cầu phân trang
     * @param <T> Kiểu dữ liệu của kết quả
     * @return Trang chứa các kết quả
     */
    public static <T> com.icetea.lotus.core.domain.valueobject.Page<T> paginate(List<T> list, PageRequest pageRequest) {
        if (list == null) {
            return com.icetea.lotus.core.domain.valueobject.Page.empty();
        }
        
        int start = pageRequest.getOffset();
        int end = Math.min(start + pageRequest.getSize(), list.size());
        
        if (start >= list.size()) {
            return com.icetea.lotus.core.domain.valueobject.Page.empty();
        }
        
        List<T> content = list.subList(start, end);
        return com.icetea.lotus.core.domain.valueobject.Page.of(content, pageRequest, list.size());
    }
}
