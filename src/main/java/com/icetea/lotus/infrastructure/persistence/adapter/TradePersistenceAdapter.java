package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.TradePersistencePort;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.repository.TradeRepository;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Page;
import com.icetea.lotus.core.domain.valueobject.PageRequest;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.valueobject.TradeId;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.TradeJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.TradePersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.TradeJpaRepository;
import com.icetea.lotus.infrastructure.persistence.util.QueryUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Adapter cho TradePersistencePort
 * Triển khai các phương thức của TradePersistencePort
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TradePersistenceAdapter implements TradePersistencePort, TradeRepository {

    private final TradeJpaRepository tradeJpaRepository;
    private final TradePersistenceMapper tradePersistenceMapper;

    /**
     * Tìm giao dịch theo id với xử lý ngoại lệ và thử lại
     * @param id ID của giao dịch
     * @return Optional chứa giao dịch nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Trade> findById(TradeId id) {
        try {
            log.debug("Tìm giao dịch theo id, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            // Chuyển đổi String thành Long
            Long longId;
            try {
                longId = Long.parseLong(id.getValue());
            } catch (NumberFormatException e) {
                log.error("Không thể chuyển đổi id {} thành Long", id.getValue(), e);
                throw new IllegalArgumentException("ID không hợp lệ: " + id.getValue(), e);
            }

            Optional<TradeJpaEntity> entity = tradeJpaRepository.findById(longId);

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy giao dịch, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy giao dịch, id = {}", id.getValue());
            }

            return entity.map(tradePersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm giao dịch theo id, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giao dịch theo id, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm giao dịch theo id: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm giao dịch theo id thất bại
     * @param e Ngoại lệ
     * @param id ID của giao dịch
     * @return Optional<Trade>
     */
    @Recover
    public Optional<Trade> recoverFindById(Exception e, TradeId id) {
        log.error("Đã thử lại tìm giao dịch theo id 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm tất cả các giao dịch theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findAllBySymbol(Symbol symbol) {
        log.debug("Tìm tất cả các giao dịch theo symbol, symbol = {}", symbol);

        List<TradeJpaEntity> entities = tradeJpaRepository.findAllBySymbol(symbol.getValue());
        List<Trade> trades = entities.stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();

        return QueryUtils.limitResults(trades);
    }

    /**
     * Tìm tất cả các giao dịch theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findAllBySymbolAndTradeTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("Tìm tất cả các giao dịch theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                symbol, startTime, endTime);

        List<TradeJpaEntity> entities = tradeJpaRepository.findAllBySymbolAndTradeTimeBetween(symbol.getValue(), startTime, endTime);
        List<Trade> trades = entities.stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();

        return QueryUtils.limitResults(trades);
    }

    /**
     * Tìm tất cả các giao dịch theo buyMemberId
     * @param buyMemberId ID của thành viên mua
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findAllByBuyMemberId(Long buyMemberId) {
        log.debug("Tìm tất cả các giao dịch theo buyMemberId, buyMemberId = {}", buyMemberId);

        List<TradeJpaEntity> entities = tradeJpaRepository.findAllByBuyMemberId(buyMemberId);
        List<Trade> trades = entities.stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();

        return QueryUtils.limitResults(trades);
    }

    /**
     * Tìm tất cả các giao dịch theo sellMemberId
     * @param sellMemberId ID của thành viên bán
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findAllBySellMemberId(Long sellMemberId) {
        log.debug("Tìm tất cả các giao dịch theo sellMemberId, sellMemberId = {}", sellMemberId);

        List<TradeJpaEntity> entities = tradeJpaRepository.findAllBySellMemberId(sellMemberId);
        List<Trade> trades = entities.stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();

        return QueryUtils.limitResults(trades);
    }

    /**
     * Tìm tất cả các giao dịch theo buyOrderId
     * @param buyOrderId ID của lệnh mua
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findAllByBuyOrderId(OrderId buyOrderId) {
        log.debug("Tìm tất cả các giao dịch theo buyOrderId, buyOrderId = {}", buyOrderId);

        List<TradeJpaEntity> entities = tradeJpaRepository.findAllByBuyOrderId(buyOrderId.getValue());
        List<Trade> trades = entities.stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();

        return QueryUtils.limitResults(trades);
    }

    /**
     * Tìm tất cả các giao dịch theo sellOrderId
     * @param sellOrderId ID của lệnh bán
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findAllBySellOrderId(OrderId sellOrderId) {
        log.debug("Tìm tất cả các giao dịch theo sellOrderId, sellOrderId = {}", sellOrderId);

        List<TradeJpaEntity> entities = tradeJpaRepository.findAllBySellOrderId(sellOrderId.getValue());
        List<Trade> trades = entities.stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();

        return QueryUtils.limitResults(trades);
    }

    /**
     * Lưu giao dịch với xử lý ngoại lệ và thử lại
     * @param trade Giao dịch cần lưu
     * @return Giao dịch đã được lưu
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Trade save(Trade trade) {
        try {
            log.debug("Lưu giao dịch, id = {}, symbol = {}, buyOrderId = {}, sellOrderId = {}",
                    trade.getId() != null ? trade.getId().getValue() : "null",
                    trade.getSymbol().getValue(),
                    trade.getBuyOrderId().getValue(),
                    trade.getSellOrderId().getValue());

            if (trade == null) {
                throw new IllegalArgumentException("Trade không được để trống");
            }

            TradeJpaEntity entity = tradePersistenceMapper.domainToEntity(trade);
            TradeJpaEntity savedEntity = tradeJpaRepository.save(entity);

            log.debug("Đã lưu giao dịch thành công, id = {}", savedEntity.getId());

            return tradePersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giao dịch, id = {}",
                    trade.getId() != null ? trade.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giao dịch, id = {}",
                    trade.getId() != null ? trade.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu giao dịch: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu giao dịch thất bại
     * @param e Ngoại lệ
     * @param trade Giao dịch cần lưu
     * @return Trade
     */
    @Recover
    public Trade recoverSave(Exception e, Trade trade) {
        log.error("Đã thử lại lưu giao dịch 3 lần nhưng thất bại, id = {}",
                trade.getId() != null ? trade.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu giao dịch sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Lưu danh sách giao dịch với xử lý ngoại lệ và thử lại
     * @param trades Danh sách giao dịch cần lưu
     * @return Danh sách giao dịch đã được lưu
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Trade> saveAll(List<Trade> trades) {
        try {
            log.debug("Lưu danh sách giao dịch, số lượng = {}", trades.size());

            if (trades == null || trades.isEmpty()) {
                log.warn("Danh sách giao dịch trống");
                return List.of();
            }

            List<TradeJpaEntity> entities = trades.stream()
                    .map(tradePersistenceMapper::domainToEntity)
                    .toList();
            List<TradeJpaEntity> savedEntities = tradeJpaRepository.saveAll(entities);

            log.debug("Đã lưu danh sách giao dịch thành công, số lượng = {}", savedEntities.size());

            return savedEntities.stream()
                    .map(tradePersistenceMapper::entityToDomain)
                    .toList();
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu danh sách giao dịch, số lượng = {}", trades.size(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu danh sách giao dịch, số lượng = {}", trades.size(), e);
            throw new DatabaseException("Lỗi khi lưu danh sách giao dịch: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu danh sách giao dịch thất bại
     * @param e Ngoại lệ
     * @param trades Danh sách giao dịch cần lưu
     * @return List<Trade>
     */
    @Recover
    public List<Trade> recoverSaveAll(Exception e, List<Trade> trades) {
        log.error("Đã thử lại lưu danh sách giao dịch 3 lần nhưng thất bại, số lượng = {}", trades.size(), e);
        throw new DatabaseException("Không thể lưu danh sách giao dịch sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm giao dịch theo symbol và sắp xếp theo thời gian giao dịch giảm dần
     * @param symbol Symbol của hợp đồng
     * @param limit Số lượng giao dịch tối đa cần lấy
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findBySymbolOrderByTradeTimeDesc(Symbol symbol, int limit) {
        log.debug("Tìm giao dịch theo symbol và sắp xếp theo thời gian giao dịch giảm dần, symbol = {}, limit = {}",
                symbol, limit);

        org.springframework.data.domain.Page<TradeJpaEntity> pageResult = tradeJpaRepository.findBySymbolOrderByTradeTimeDesc(
                symbol.getValue(),
                org.springframework.data.domain.PageRequest.of(0, limit));
        return pageResult.getContent().stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();
    }

    /**
     * Tìm giao dịch theo symbol và sắp xếp theo thời gian giao dịch giảm dần với phân trang
     * @param symbol Symbol của hợp đồng
     * @param pageRequest Yêu cầu phân trang
     * @return Trang chứa các giao dịch
     */
    @Override
    public Page<Trade> findBySymbolOrderByTradeTimeDesc(Symbol symbol, PageRequest pageRequest) {
        log.debug("Tìm giao dịch theo symbol và sắp xếp theo thời gian giao dịch giảm dần với phân trang, symbol = {}, page = {}, size = {}",
                symbol, pageRequest.getPage(), pageRequest.getSize());

        org.springframework.data.domain.PageRequest springPageRequest =
                QueryUtils.toSpringPageRequest(pageRequest, "tradeTime", Sort.Direction.DESC);

        org.springframework.data.domain.Page<TradeJpaEntity> pageResult =
                tradeJpaRepository.findBySymbolOrderByTradeTimeDesc(symbol.getValue(), springPageRequest);

        return QueryUtils.toPage(pageResult, pageRequest, tradePersistenceMapper::entityToDomain);
    }

    /**
     * Tìm tất cả các giao dịch theo symbol với phân trang
     * @param symbol Symbol của hợp đồng
     * @param pageRequest Yêu cầu phân trang
     * @return Trang chứa các giao dịch
     */
    @Override
    public Page<Trade> findAllBySymbol(Symbol symbol, PageRequest pageRequest) {
        log.debug("Tìm tất cả các giao dịch theo symbol với phân trang, symbol = {}, page = {}, size = {}",
                symbol, pageRequest.getPage(), pageRequest.getSize());

        org.springframework.data.domain.PageRequest springPageRequest =
                QueryUtils.toSpringPageRequest(pageRequest);

        org.springframework.data.domain.Page<TradeJpaEntity> pageResult =
                tradeJpaRepository.findAllBySymbol(symbol.getValue(), springPageRequest);

        return QueryUtils.toPage(pageResult, pageRequest, tradePersistenceMapper::entityToDomain);
    }

    /**
     * Tìm tất cả các giao dịch theo symbol và khoảng thời gian với phân trang
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param pageRequest Yêu cầu phân trang
     * @return Trang chứa các giao dịch
     */
    @Override
    public Page<Trade> findAllBySymbolAndTradeTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime, PageRequest pageRequest) {
        log.debug("Tìm tất cả các giao dịch theo symbol và khoảng thời gian với phân trang, symbol = {}, startTime = {}, endTime = {}, page = {}, size = {}",
                symbol, startTime, endTime, pageRequest.getPage(), pageRequest.getSize());

        org.springframework.data.domain.PageRequest springPageRequest =
                QueryUtils.toSpringPageRequest(pageRequest, "tradeTime", Sort.Direction.DESC);

        org.springframework.data.domain.Page<TradeJpaEntity> pageResult =
                tradeJpaRepository.findAllBySymbolAndTradeTimeBetween(symbol.getValue(), startTime, endTime, springPageRequest);

        return QueryUtils.toPage(pageResult, pageRequest, tradePersistenceMapper::entityToDomain);
    }

    /**
     * Tìm giao dịch mới nhất theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Giao dịch mới nhất
     */
    @Override
    public Trade findTopBySymbolOrderByTradeTimeDesc(Symbol symbol) {
        log.debug("Tìm giao dịch mới nhất theo symbol, symbol = {}", symbol);

        TradeJpaEntity entity = tradeJpaRepository.findTopBySymbolOrderByTradeTimeDesc(symbol.getValue());
        if (entity == null) {
            log.debug("Không tìm thấy giao dịch nào cho symbol = {}", symbol);
            return null;
        }

        return tradePersistenceMapper.entityToDomain(entity);
    }


    /**
     * Tìm tất cả các giao dịch theo symbol
     * @param memberId memberId của hợp đồng
     * @param pageable phân trang
     * @return Danh sách các giao dịch
     */
    @Override
    public List<Trade> findAllByBuyMemberIdOrSellMemberId(Long memberId, Pageable pageable) {
        log.debug("Tìm tất cả các giao dịch theo memberId, memberId = {}", memberId);

        org.springframework.data.domain.Page<TradeJpaEntity> pageResult = tradeJpaRepository.findAllByBuyMemberIdOrSellMemberId(
                memberId,memberId, pageable);
        return pageResult.getContent().stream()
                .map(tradePersistenceMapper::entityToDomain)
                .toList();
    }
}
