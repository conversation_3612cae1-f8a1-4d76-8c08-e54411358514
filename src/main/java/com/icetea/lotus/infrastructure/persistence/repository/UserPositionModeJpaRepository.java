package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.UserPositionModeJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * JPA Repository cho UserPositionModeJpaEntity
 */
@Repository
public interface UserPositionModeJpaRepository extends JpaRepository<UserPositionModeJpaEntity, Long> {
    
    /**
     * Tìm UserPositionModeJpaEntity theo memberId
     * @param memberId ID của thành viên
     * @return Optional<UserPositionModeJpaEntity>
     */
    Optional<UserPositionModeJpaEntity> findByMemberId(Long memberId);
}
