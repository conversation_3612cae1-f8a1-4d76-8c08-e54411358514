package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.FundingRateJpaEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho FundingRateJpaEntity
 */
@Repository
public interface FundingRateJpaRepository extends JpaRepository<FundingRateJpaEntity, Long>, JpaSpecificationExecutor<FundingRateJpaEntity> {

    /**
     * Tìm tất cả các tỷ lệ tài trợ theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các tỷ lệ tài trợ
     */
    List<FundingRateJpaEntity> findAllBySymbol(String symbol);

    /**
     * Tìm tỷ lệ tài trợ mới nhất theo symbol
     * @param symbol Symbol của hợp đồng
     * @param pageable Pageable
     * @return Danh sách các tỷ lệ tài trợ
     */
    @Query("SELECT f FROM FundingRateJpaEntity f WHERE f.symbol = :symbol ORDER BY f.time DESC")
    List<FundingRateJpaEntity> findLatestBySymbol(@Param("symbol") String symbol, Pageable pageable);

    /**
     * Tìm tất cả các tỷ lệ tài trợ theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các tỷ lệ tài trợ
     */
    List<FundingRateJpaEntity> findAllBySymbolAndTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);
}
