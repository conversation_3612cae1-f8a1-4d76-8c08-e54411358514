package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.SettlementPrice;
import com.icetea.lotus.core.domain.repository.SettlementPriceRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.repository.SettlementPriceJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Adapter cho SettlementPriceRepository
 * Triể<PERSON> khai c<PERSON><PERSON> ph<PERSON><PERSON>ng thức của SettlementPriceRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class SettlementPriceRepositoryAdapter implements SettlementPriceRepository {

    private final SettlementPriceJpaRepository settlementPriceJpaRepository;

    /**
     * Lưu giá thanh toán với xử lý ngoại lệ và thử lại
     * @param settlementPrice Giá thanh toán
     * @return SettlementPrice
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public SettlementPrice save(SettlementPrice settlementPrice) {
        try {
            log.debug("Lưu giá thanh toán, symbol = {}, price = {}",
                    settlementPrice.getSymbol().getValue(), settlementPrice.getPrice().getValue());

            SettlementPrice savedSettlementPrice = settlementPriceJpaRepository.save(settlementPrice);

            log.debug("Đã lưu giá thanh toán thành công, id = {}", savedSettlementPrice.getId());

            return savedSettlementPrice;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giá thanh toán, symbol = {}, price = {}",
                    settlementPrice.getSymbol().getValue(), settlementPrice.getPrice().getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giá thanh toán, symbol = {}, price = {}",
                    settlementPrice.getSymbol().getValue(), settlementPrice.getPrice().getValue(), e);
            throw new DatabaseException("Lỗi không xác định khi lưu giá thanh toán", e);
        }
    }

    /**
     * Phục hồi khi lưu giá thanh toán thất bại
     * @param e Ngoại lệ
     * @param settlementPrice Giá thanh toán
     * @return null
     */
    @Recover
    public SettlementPrice recoverSave(Exception e, SettlementPrice settlementPrice) {
        log.error("Đã thử lại lưu giá thanh toán 3 lần nhưng thất bại, symbol = {}, price = {}",
                settlementPrice.getSymbol().getValue(), settlementPrice.getPrice().getValue(), e);
        throw new DatabaseException("Không thể lưu giá thanh toán sau 3 lần thử lại", e);
    }

    /**
     * Tìm giá thanh toán mới nhất theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return SettlementPrice
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public SettlementPrice findTopBySymbolOrderByCreateTimeDesc(Symbol symbol) {
        try {
            log.debug("Tìm giá thanh toán mới nhất, symbol = {}", symbol.getValue());

            SettlementPrice settlementPrice = settlementPriceJpaRepository.findTopBySymbolValueOrderByCreateTimeDesc(symbol.getValue());

            if (settlementPrice == null) {
                log.debug("Không tìm thấy giá thanh toán nào, symbol = {}", symbol.getValue());
                return null;
            }

            log.debug("Đã tìm thấy giá thanh toán mới nhất, symbol = {}, price = {}",
                    symbol.getValue(), settlementPrice.getPrice().getValue());

            return settlementPrice;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm giá thanh toán mới nhất, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm giá thanh toán mới nhất, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi không xác định khi tìm giá thanh toán mới nhất", e);
        }
    }

    /**
     * Phục hồi khi tìm giá thanh toán mới nhất thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @return null
     */
    @Recover
    public SettlementPrice recoverFindTopBySymbolOrderByCreateTimeDesc(Exception e, Symbol symbol) {
        log.error("Đã thử lại tìm giá thanh toán mới nhất 3 lần nhưng thất bại, symbol = {}", symbol.getValue(), e);
        return null;
    }

    /**
     * Tìm danh sách giá thanh toán trong khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách giá thanh toán
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<SettlementPrice> findBySymbolAndCreateTimeBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm danh sách giá thanh toán trong khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), startTime, endTime);

            List<SettlementPrice> settlementPrices = settlementPriceJpaRepository.findBySymbolValueAndCreateTimeBetween(
                    symbol.getValue(), startTime, endTime);

            log.debug("Đã tìm thấy {} giá thanh toán, symbol = {}", settlementPrices.size(), symbol.getValue());

            return settlementPrices;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm danh sách giá thanh toán, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm danh sách giá thanh toán, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi không xác định khi tìm danh sách giá thanh toán", e);
        }
    }

    /**
     * Phục hồi khi tìm danh sách giá thanh toán thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách rỗng
     */
    @Recover
    public List<SettlementPrice> recoverFindBySymbolAndCreateTimeBetween(Exception e, Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        log.error("Đã thử lại tìm danh sách giá thanh toán 3 lần nhưng thất bại, symbol = {}", symbol.getValue(), e);
        return List.of();
    }
}
