package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.repository.PositionRepository;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.PositionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.PositionPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.PositionJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho PositionRepository
 * Triển khai các phương thức của PositionRepository
 */
@Slf4j
@Component("positionRepositoryAdapter")
@Transactional
@RequiredArgsConstructor
public class PositionRepositoryAdapter implements PositionRepository {

    private final PositionJpaRepository positionJpaRepository;
    private final PositionPersistenceMapper positionPersistenceMapper;

    /**
     * Tìm vị thế theo ID
     * @param id ID của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    public Optional<Position> findById(PositionId id) {
        try {
            if (id == null) {
                return Optional.empty();
            }

            Optional<PositionJpaEntity> entity = positionJpaRepository.findById(id.getValue());
            return entity.map(positionPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Lỗi khi tìm vị thế theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm vị thế theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm vị thế theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    public Optional<Position> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        try {
            if (memberId == null || symbol == null) {
                return Optional.empty();
            }

            Optional<PositionJpaEntity> entity = positionJpaRepository.findByMemberIdAndSymbol(memberId, symbol.getValue());
            return entity.map(positionPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Lỗi khi tìm vị thế theo memberId và symbol, memberId = {}, symbol = {}",
                    memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm vị thế theo memberId và symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm vị thế theo memberId, symbol và status
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    public Optional<Position> findByMemberIdAndSymbolAndStatus(Long memberId, Symbol symbol, PositionStatus status) {
        try {
            if (memberId == null || symbol == null || status == null) {
                return Optional.empty();
            }

            Optional<PositionJpaEntity> entity = positionJpaRepository.findByMemberIdAndSymbolAndStatus(
                    memberId, symbol.getValue(), status);
            return entity.map(positionPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Lỗi khi tìm vị thế theo memberId, symbol và status, memberId = {}, symbol = {}, status = {}",
                    memberId, symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm vị thế theo memberId, symbol và status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        try {
            if (memberId == null || symbol == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberIdAndSymbol(memberId, symbol.getValue());
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo memberId và symbol, memberId = {}, symbol = {}",
                    memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo memberId và symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm vị thế theo memberId, symbol và direction
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Optional chứa vị thế nếu tìm thấy
     */
    @Override
    public Optional<Position> findByMemberIdAndSymbolAndDirection(Long memberId, Symbol symbol, PositionDirection direction) {
        try {
            if (memberId == null || symbol == null || direction == null) {
                return Optional.empty();
            }

            Optional<PositionJpaEntity> entity = positionJpaRepository.findByMemberIdAndSymbolAndDirection(
                    memberId, symbol.getValue(), direction);
            return entity.map(positionPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Lỗi khi tìm vị thế theo memberId, symbol và direction, memberId = {}, symbol = {}, direction = {}",
                    memberId, symbol.getValue(), direction, e);
            throw new DatabaseException("Lỗi khi tìm vị thế theo memberId, symbol và direction: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo memberId, symbol và direction
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberIdAndSymbolAndDirection(Long memberId, Symbol symbol, PositionDirection direction) {
        try {
            if (memberId == null || symbol == null || direction == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberIdAndSymbolAndDirection(
                    memberId, symbol.getValue(), direction);
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo memberId, symbol và direction, memberId = {}, symbol = {}, direction = {}",
                    memberId, symbol.getValue(), direction, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo memberId, symbol và direction: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberId(Long memberId) {
        try {
            if (memberId == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberId(memberId);
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo memberId, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo memberId: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo memberId và status
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByMemberIdAndStatus(Long memberId, PositionStatus status) {
        try {
            if (memberId == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllByMemberIdAndStatus(memberId, status);
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo memberId và status, memberId = {}, status = {}",
                    memberId, status, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo memberId và status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo memberId, status và khoảng thời gian với phân trang
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param page Số trang
     * @param size Kích thước trang
     * @return Page chứa danh sách các vị thế
     */
    @Override
    public Page<Position> findByMemberIdAndStatusAndTimeRange(Long memberId, PositionStatus status,
            LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        try {
            if (memberId == null) {
                return Page.empty();
            }

            Pageable pageable = PageRequest.of(page, size);
            Page<PositionJpaEntity> entityPage = positionJpaRepository.findByMemberIdAndStatusAndTimeRange(
                    memberId, status, startTime, endTime, pageable);

            return entityPage.map(positionPersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo memberId, status và khoảng thời gian, memberId = {}, status = {}, startTime = {}, endTime = {}",
                    memberId, status, startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo memberId, status và khoảng thời gian: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllBySymbol(Symbol symbol) {
        try {
            if (symbol == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllBySymbol(symbol.getValue());
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo symbol và direction
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllBySymbolAndDirection(Symbol symbol, PositionDirection direction) {
        try {
            if (symbol == null || direction == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllBySymbolAndDirection(symbol.getValue(), direction);
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo symbol và direction, symbol = {}, direction = {}",
                    symbol.getValue(), direction, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo symbol và direction: " + e.getMessage(), e);
        }
    }

    /**
     * Lưu vị thế
     * @param position Vị thế
     * @return Vị thế đã được lưu
     */
    @Override
    public Position save(Position position) {
        try {
            if (position == null) {
                throw new IllegalArgumentException("Position không được để trống");
            }

            PositionJpaEntity entity = positionPersistenceMapper.domainToEntity(position);
            PositionJpaEntity savedEntity = positionJpaRepository.save(entity);

            return positionPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException e) {
            log.error("Lỗi khi lưu vị thế, position = {}", position, e);
            throw new DatabaseException("Lỗi khi lưu vị thế: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu vị thế, position = {}", position, e);
            throw new DatabaseException("Lỗi khi lưu vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Xóa vị thế
     * @param position Vị thế
     */
    @Override
    public void delete(Position position) {
        try {
            if (position == null || position.getId() == null) {
                throw new IllegalArgumentException("Position không được để trống");
            }

            positionJpaRepository.deleteById(position.getId().getValue());
        } catch (DataAccessException e) {
            log.error("Lỗi khi xóa vị thế, position = {}", position, e);
            throw new DatabaseException("Lỗi khi xóa vị thế: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa vị thế, position = {}", position, e);
            throw new DatabaseException("Lỗi khi xóa vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Tính tổng khối lượng vị thế theo symbol, direction và status
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @param status Trạng thái của vị thế
     * @return Tổng khối lượng
     */
    @Override
    public BigDecimal sumVolumeBySymbolAndDirectionAndStatus(Symbol symbol, PositionDirection direction, PositionStatus status) {
        try {
            if (symbol == null || direction == null || status == null) {
                return BigDecimal.ZERO;
            }

            BigDecimal sum = positionJpaRepository.sumVolumeBySymbolAndDirectionAndStatus(symbol.getValue(), direction, status);
            return sum != null ? sum : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Lỗi khi tính tổng khối lượng vị thế theo symbol, direction và status, symbol = {}, direction = {}, status = {}",
                    symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tính tổng khối lượng vị thế theo symbol, direction và status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo trạng thái
     * @param status Trạng thái cần tìm
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllByStatus(PositionStatus status) {
        try {
            if (status == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllByStatus(status);
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo trạng thái, status = {}", status, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo trạng thái: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế theo symbol và trạng thái
     * @param symbol Symbol cần tìm
     * @param status Trạng thái cần tìm
     * @return Danh sách các vị thế
     */
    @Override
    public List<Position> findAllBySymbolAndStatus(Symbol symbol, PositionStatus status) {
        try {
            if (symbol == null || status == null) {
                return Collections.emptyList();
            }

            List<PositionJpaEntity> entities = positionJpaRepository.findAllBySymbolAndStatus(symbol.getValue(), status);
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế theo symbol và trạng thái, symbol = {}, status = {}",
                    symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế theo symbol và trạng thái: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm các vị thế có nguy cơ cao cần thanh lý theo symbol
     * @param symbol Symbol cần tìm
     * @param marginRatio Tỷ lệ ký quỹ tối đa
     * @param limit Số lượng vị thế tối đa
     * @return Danh sách các vị thế có nguy cơ cao
     */
    @Override
    public List<Position> findRiskPositionsBySymbol(Symbol symbol, BigDecimal marginRatio, int limit) {
        try {
            if (symbol == null || marginRatio == null || limit <= 0) {
                return Collections.emptyList();
            }

            Pageable pageable = PageRequest.of(0, limit);
            List<PositionJpaEntity> entities = positionJpaRepository.findRiskPositionsBySymbol(
                    symbol.getValue(), marginRatio, pageable);
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách vị thế có nguy cơ cao, symbol = {}, marginRatio = {}, limit = {}",
                    symbol.getValue(), marginRatio, limit, e);
            throw new DatabaseException("Lỗi khi tìm danh sách vị thế có nguy cơ cao: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả các vị thế
     * @return Danh sách tất cả các vị thế
     */
    @Override
    public List<Position> findAll() {
        try {
            List<PositionJpaEntity> entities = positionJpaRepository.findAll();
            return entities.stream()
                    .map(positionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm tất cả các vị thế", e);
            throw new DatabaseException("Lỗi khi tìm tất cả các vị thế: " + e.getMessage(), e);
        }
    }
}
