package com.icetea.lotus.infrastructure.persistence.adapter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.core.domain.entity.PriceConfiguration;
import com.icetea.lotus.core.domain.repository.PriceConfigurationRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.PriceConfigurationJpaEntity;
import com.icetea.lotus.infrastructure.persistence.repository.PriceConfigurationJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho PriceConfigurationRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PriceConfigurationRepositoryAdapter implements PriceConfigurationRepository {
    
    private final PriceConfigurationJpaRepository priceConfigurationJpaRepository;
    private final ObjectMapper objectMapper;
    
    @Override
    public Optional<PriceConfiguration> findBySymbol(Symbol symbol) {
        if (symbol == null) {
            return Optional.empty();
        }
        
        return priceConfigurationJpaRepository.findBySymbol(symbol.getValue())
                .map(this::mapToDomainEntity);
    }
    
    @Override
    public List<PriceConfiguration> findAll() {
        return priceConfigurationJpaRepository.findAll().stream()
                .map(this::mapToDomainEntity)
                .toList();
    }
    
    @Override
    public PriceConfiguration save(PriceConfiguration priceConfiguration) {
        if (priceConfiguration == null) {
            throw new IllegalArgumentException("PriceConfiguration cannot be null");
        }
        
        PriceConfigurationJpaEntity jpaEntity = mapToJpaEntity(priceConfiguration);
        PriceConfigurationJpaEntity savedEntity = priceConfigurationJpaRepository.save(jpaEntity);
        
        return mapToDomainEntity(savedEntity);
    }
    
    @Override
    public void deleteBySymbol(Symbol symbol) {
        if (symbol == null) {
            throw new IllegalArgumentException("Symbol cannot be null");
        }
        
        priceConfigurationJpaRepository.deleteBySymbol(symbol.getValue());
    }
    
    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    private PriceConfiguration mapToDomainEntity(PriceConfigurationJpaEntity jpaEntity) {
        if (jpaEntity == null) {
            return null;
        }
        
        Map<String, Object> parameters = parseParameters(jpaEntity.getParameters());
        
        return PriceConfiguration.builder()
                .id(jpaEntity.getId())
                .symbol(Symbol.of(jpaEntity.getSymbol()))
                .indexPriceMethod(jpaEntity.getIndexPriceMethod())
                .markPriceMethod(jpaEntity.getMarkPriceMethod())
                .customIndexPriceFormula(jpaEntity.getCustomIndexPriceFormula())
                .customMarkPriceFormula(jpaEntity.getCustomMarkPriceFormula())
                .parameters(parameters)
                .createTime(jpaEntity.getCreateTime())
                .updateTime(jpaEntity.getUpdateTime())
                .build();
    }
    
    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domainEntity Domain entity
     * @return JPA entity
     */
    private PriceConfigurationJpaEntity mapToJpaEntity(PriceConfiguration domainEntity) {
        if (domainEntity == null) {
            return null;
        }
        
        String parametersJson = serializeParameters(domainEntity.getParameters());
        
        return PriceConfigurationJpaEntity.builder()
                .id(domainEntity.getId())
                .symbol(domainEntity.getSymbol().getValue())
                .indexPriceMethod(domainEntity.getIndexPriceMethod())
                .markPriceMethod(domainEntity.getMarkPriceMethod())
                .customIndexPriceFormula(domainEntity.getCustomIndexPriceFormula())
                .customMarkPriceFormula(domainEntity.getCustomMarkPriceFormula())
                .parameters(parametersJson)
                .createTime(domainEntity.getCreateTime())
                .updateTime(domainEntity.getUpdateTime())
                .build();
    }
    
    /**
     * Parse parameters từ JSON string
     * @param parametersJson JSON string
     * @return Map<String, Object>
     */
    private Map<String, Object> parseParameters(String parametersJson) {
        if (parametersJson == null || parametersJson.isEmpty()) {
            return Collections.emptyMap();
        }
        
        try {
            return objectMapper.readValue(parametersJson, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("Lỗi khi parse parameters", e);
            return Collections.emptyMap();
        }
    }
    
    /**
     * Serialize parameters thành JSON string
     * @param parameters Map<String, Object>
     * @return JSON string
     */
    private String serializeParameters(Map<String, Object> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return "{}";
        }
        
        try {
            return objectMapper.writeValueAsString(parameters);
        } catch (JsonProcessingException e) {
            log.error("Lỗi khi serialize parameters", e);
            return "{}";
        }
    }
}
