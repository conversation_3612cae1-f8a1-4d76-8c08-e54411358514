package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.PriceConfigurationJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * JPA Repository cho PriceConfigurationJpaEntity
 */
@Repository
public interface PriceConfigurationJpaRepository extends JpaRepository<PriceConfigurationJpaEntity, Long> {
    
    /**
     * Tìm PriceConfigurationJpaEntity theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Optional<PriceConfigurationJpaEntity>
     */
    Optional<PriceConfigurationJpaEntity> findBySymbol(String symbol);
    
    /**
     * Xóa PriceConfigurationJpaEntity theo symbol
     * @param symbol Symbol của hợp đồng
     */
    void deleteBySymbol(String symbol);
}
