package com.icetea.lotus.infrastructure.persistence.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Emergency validator để force validate tất cả BigDecimal values
 * S<PERSON> dụng khi có numeric overflow issues
 */
@Slf4j
public class EmergencyBigDecimalValidator {

    // Giới hạn cho NUMERIC(18,8): tối đa 10 chữ số trước dấu phẩy, 8 chữ số sau dấu phẩy
    private static final BigDecimal MAX_VALUE = new BigDecimal("9999999999.99999999"); // 10^10 - 1
    private static final BigDecimal MIN_VALUE = new BigDecimal("-9999999999.99999999");
    private static final int SCALE = 8;

    /**
     * Force validate và cap BigDecimal value
     * @param value Gi<PERSON> trị cần validate
     * @param fieldName Tên field để log
     * @return Gi<PERSON> trị đã được validate và cap
     */
    public static BigDecimal forceValidateAndCap(BigDecimal value, String fieldName) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        try {
            // Log original value
            log.info("EMERGENCY_VALIDATION: Validating {} with value {}", fieldName, value);

            // Set scale to 8 decimal places
            BigDecimal scaledValue = value.setScale(SCALE, RoundingMode.HALF_UP);

            // Check if value exceeds limits
            if (scaledValue.compareTo(MAX_VALUE) > 0) {
                log.error("EMERGENCY_VALIDATION: {} value {} exceeds maximum limit, capping to {}", 
                        fieldName, scaledValue, MAX_VALUE);
                return MAX_VALUE;
            } else if (scaledValue.compareTo(MIN_VALUE) < 0) {
                log.error("EMERGENCY_VALIDATION: {} value {} below minimum limit, capping to {}", 
                        fieldName, scaledValue, MIN_VALUE);
                return MIN_VALUE;
            }

            // Log if value was scaled
            if (scaledValue.compareTo(value) != 0) {
                log.warn("EMERGENCY_VALIDATION: {} value scaled from {} to {}", fieldName, value, scaledValue);
            }

            return scaledValue;
        } catch (Exception e) {
            log.error("EMERGENCY_VALIDATION: Error validating {} with value {}, returning ZERO", fieldName, value, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * Validate multiple values at once
     * @param amount Amount value
     * @param fee Fee value
     * @param profit Profit value
     * @return Array of validated values [amount, fee, profit]
     */
    public static BigDecimal[] validateFinancialRecordValues(BigDecimal amount, BigDecimal fee, BigDecimal profit) {
        log.info("EMERGENCY_VALIDATION: Validating financial record values - amount: {}, fee: {}, profit: {}", 
                amount, fee, profit);

        BigDecimal validatedAmount = forceValidateAndCap(amount, "amount");
        BigDecimal validatedFee = forceValidateAndCap(fee, "fee");
        BigDecimal validatedProfit = forceValidateAndCap(profit, "profit");

        log.info("EMERGENCY_VALIDATION: Validated financial record values - amount: {}, fee: {}, profit: {}", 
                validatedAmount, validatedFee, validatedProfit);

        return new BigDecimal[]{validatedAmount, validatedFee, validatedProfit};
    }

    /**
     * Check if value is within safe limits
     * @param value Value to check
     * @return true if value is safe
     */
    public static boolean isSafeValue(BigDecimal value) {
        if (value == null) {
            return true;
        }

        try {
            BigDecimal scaledValue = value.setScale(SCALE, RoundingMode.HALF_UP);
            return scaledValue.compareTo(MAX_VALUE) <= 0 && scaledValue.compareTo(MIN_VALUE) >= 0;
        } catch (Exception e) {
            log.error("EMERGENCY_VALIDATION: Error checking if value {} is safe", value, e);
            return false;
        }
    }

    /**
     * Get maximum allowed value
     * @return Maximum value
     */
    public static BigDecimal getMaxValue() {
        return MAX_VALUE;
    }

    /**
     * Get minimum allowed value
     * @return Minimum value
     */
    public static BigDecimal getMinValue() {
        return MIN_VALUE;
    }

    /**
     * Get scale (decimal places)
     * @return Scale
     */
    public static int getScale() {
        return SCALE;
    }
}
