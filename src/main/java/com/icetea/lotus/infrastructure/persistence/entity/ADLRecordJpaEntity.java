package com.icetea.lotus.infrastructure.persistence.entity;

import com.icetea.lotus.core.domain.entity.PositionDirection;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA entity cho ADLRecord
 */
@Entity
@Table(name = "contract_adl_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ADLRecordJpaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "position_id")
    private Long positionId;

    @Column(name = "member_id")
    private Long memberId;

    @Column(name = "symbol")
    private String symbol;

    @Enumerated(EnumType.STRING)
    @Column(name = "direction")
    private PositionDirection direction;

    @Column(name = "volume", columnDefinition = "decimal(18,8)")
    private BigDecimal volume;

    @Column(name = "price", columnDefinition = "decimal(18,8)")
    private BigDecimal price;

    @Column(name = "time")
    private LocalDateTime time;
}
