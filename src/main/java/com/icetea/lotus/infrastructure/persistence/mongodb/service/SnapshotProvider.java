package com.icetea.lotus.infrastructure.persistence.mongodb.service;

import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.matching.distributed.DistributedOrderBookSnapshot;

/**
 * Interface for providing order book snapshots
 * This interface helps to decouple ChangeBasedSnapshotService from DistributedLockingMatchingEngine
 * and avoid circular dependency
 */
public interface SnapshotProvider {
    
    /**
     * Get current order book snapshot for the given symbol
     * @param symbol Symbol to get snapshot for
     * @return Current snapshot or null if not available
     */
    DistributedOrderBookSnapshot getCurrentSnapshot(Symbol symbol);
    
    /**
     * Check if snapshot provider is available for the given symbol
     * @param symbol Symbol to check
     * @return true if provider can provide snapshot for this symbol
     */
    boolean isAvailable(Symbol symbol);
}
