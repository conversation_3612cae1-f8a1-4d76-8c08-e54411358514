package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.OrderStatus;
import com.icetea.lotus.core.domain.entity.OrderType;
import com.icetea.lotus.core.domain.repository.OrderRepository;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Page;
import com.icetea.lotus.core.domain.valueobject.PageRequest;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.OrderJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.OrderJpaMapper;
import com.icetea.lotus.infrastructure.persistence.repository.OrderJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.dao.OptimisticLockingFailureException;
import com.icetea.lotus.core.domain.entity.OrderDirection;
import org.springframework.data.domain.Sort;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho OrderRepository với xử lý ngoại lệ
 */
@Slf4j
@Component("orderRepositoryAdapter")
@RequiredArgsConstructor
public class OrderRepositoryAdapter implements OrderRepository {

    private final OrderJpaRepository orderJpaRepository;
    private final OrderJpaMapper orderJpaMapper;

    /**
     * Lưu lệnh với xử lý ngoại lệ và thử lại
     * @param order Lệnh
     * @return Order
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class, OptimisticLockingFailureException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Order save(Order order) {
        try {
            OrderJpaEntity jpaEntity = orderJpaMapper.domainToJpa(order);
            OrderJpaEntity savedEntity = orderJpaRepository.save(jpaEntity);
            return orderJpaMapper.jpaToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu lệnh, orderId = {}", order.getId(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu lệnh, orderId = {}", order.getId(), e);
            throw new DatabaseException("Lỗi khi lưu lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu lệnh thất bại
     * @param e Ngoại lệ
     * @param order Lệnh
     * @return Order
     */
    @Recover
    public Order recoverSave(Exception e, Order order) {
        log.error("Đã thử lại lưu lệnh 3 lần nhưng thất bại, orderId = {}", order.getId(), e);
        throw new DatabaseException("Không thể lưu lệnh sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Lưu danh sách lệnh với xử lý ngoại lệ và thử lại
     * @param orders Danh sách lệnh
     * @return List<Order>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Order> saveAll(List<Order> orders) {
        try {
            List<OrderJpaEntity> jpaEntities = orders.stream()
                    .map(orderJpaMapper::domainToJpa)
                    .collect(Collectors.toList());

            List<OrderJpaEntity> savedEntities = orderJpaRepository.saveAll(jpaEntities);

            return savedEntities.stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu danh sách lệnh, size = {}", orders.size(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu danh sách lệnh, size = {}", orders.size(), e);
            throw new DatabaseException("Lỗi khi lưu danh sách lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu danh sách lệnh thất bại
     * @param e Ngoại lệ
     * @param orders Danh sách lệnh
     * @return List<Order>
     */
    @Recover
    public List<Order> recoverSaveAll(Exception e, List<Order> orders) {
        log.error("Đã thử lại lưu danh sách lệnh 3 lần nhưng thất bại, size = {}", orders.size(), e);
        throw new DatabaseException("Không thể lưu danh sách lệnh sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm lệnh theo ID với xử lý ngoại lệ
     * @param id ID của lệnh
     * @return Optional<Order>
     */
    @Override
    public Optional<Order> findById(OrderId id) {
        try {
            return orderJpaRepository.findById(id.getValue())
                    .map(orderJpaMapper::jpaToDomain);
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy lệnh với ID = {}", id.getValue());
            return Optional.empty();
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo ID, orderId = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo ID: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo ID, orderId = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả lệnh với xử lý ngoại lệ
     * @return List<Order>
     */
    @Override
    public List<Order> findAll() {
        try {
            return orderJpaRepository.findAll().stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm tất cả lệnh", e);
            throw new DatabaseException("Lỗi khi tìm tất cả lệnh: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả lệnh", e);
            throw new DatabaseException("Lỗi khi tìm tất cả lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @return List<Order>
     */
    @Override
    public List<Order> findByMemberId(Long memberId) {
        try {
            return orderJpaRepository.findAllByMemberId(memberId).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId và symbol với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return List<Order>
     */
    @Override
    public List<Order> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        try {
            return orderJpaRepository.findAllByMemberIdAndSymbol(memberId, symbol.getValue()).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId và symbol: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId và symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId, symbol và status với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findByMemberIdAndSymbolAndStatus(Long memberId, Symbol symbol, OrderStatus status) {
        try {
            return orderJpaRepository.findByMemberIdAndSymbolAndStatus(memberId, symbol.getValue(), status.name()).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId, symbol và status, memberId = {}, symbol = {}, status = {}",
                    memberId, symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và status: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId, symbol và status, memberId = {}, symbol = {}, status = {}",
                    memberId, symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo status và executeTime với xử lý ngoại lệ
     * @param status Trạng thái của lệnh
     * @param expireTime Thời gian thực hiện
     * @return List<Order>
     */
    @Override
    public List<Order> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime) {
        try {
            return orderJpaRepository.findByStatusAndExpireTimeBefore(status, expireTime).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo status và expireTime, status = {}, expireTime = {}", status, expireTime, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status và expireTime: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo status và expireTime, status = {}, expireTime = {}", status, expireTime, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status và expireTime: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo status với xử lý ngoại lệ
     * @param status Trạng thái của lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findByStatus(OrderStatus status) {
        try {
            return orderJpaRepository.findAllByStatus(status.name()).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo status, status = {}", status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo status, status = {}", status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo symbol và status với xử lý ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findBySymbolAndStatus(Symbol symbol, OrderStatus status) {
        try {
            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            return orderJpaRepository.findBySymbolAndStatus(symbol.getValue(), jpaStatus).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo symbol và status, symbol = {}, status = {}", symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol và status: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo symbol và status, symbol = {}, status = {}", symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol và status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo status với phân trang và xử lý ngoại lệ
     * @param status Trạng thái của lệnh
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findByStatus(OrderStatus status, PageRequest pageRequest) {
        try {
            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findAllByStatus(
                    status.name(),
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo status với phân trang, status = {}", status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo status với phân trang, status = {}", status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status với phân trang: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo symbol và status với phân trang và xử lý ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findBySymbolAndStatus(Symbol symbol, OrderStatus status, PageRequest pageRequest) {
        try {
            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findBySymbolAndStatus(
                    symbol.getValue(),
                    jpaStatus,
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo symbol và status với phân trang, symbol = {}, status = {}", symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol và status với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo symbol và status với phân trang, symbol = {}, status = {}", symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol và status với phân trang: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId và symbol với phân trang và xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findByMemberId(Long memberId, PageRequest pageRequest) {
        try {
            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findAllByMemberId(
                    memberId,
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId với phân trang, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId với phân trang, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId với phân trang: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId và symbol với phân trang và xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findByMemberIdAndSymbol(Long memberId, Symbol symbol, PageRequest pageRequest) {
        try {
            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findAllByMemberIdAndSymbol(
                    memberId,
                    symbol.getValue(),
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId và symbol với phân trang, memberId = {}, symbol = {}", memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId và symbol với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId và symbol với phân trang, memberId = {}, symbol = {}", memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId và symbol với phân trang: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId, symbol và status với phân trang và xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findByMemberIdAndSymbolAndStatus(Long memberId, Symbol symbol, OrderStatus status, PageRequest pageRequest) {
        try {
            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findByMemberIdAndSymbolAndStatus(
                    memberId,
                    symbol.getValue(),
                    status.name(),
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId, symbol và status với phân trang, memberId = {}, symbol = {}, status = {}", memberId, symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và status với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId, symbol và status với phân trang, memberId = {}, symbol = {}, status = {}", memberId, symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và status với phân trang: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId, symbol và danh sách status với phân trang và xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param statuses Danh sách trạng thái của lệnh
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findByMemberIdAndSymbolAndStatusIn(Long memberId, Symbol symbol, List<OrderStatus> statuses, PageRequest pageRequest) {
        try {
            List<String> statusNames = statuses.stream().map(OrderStatus::name).collect(Collectors.toList());
            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findAllByMemberIdAndSymbolAndStatusIn(
                    memberId,
                    symbol.getValue(),
                    statusNames,
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId, symbol và danh sách status với phân trang, memberId = {}, symbol = {}, statuses = {}", memberId, symbol.getValue(), statuses, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và danh sách status với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId, symbol và danh sách status với phân trang, memberId = {}, symbol = {}, statuses = {}", memberId, symbol.getValue(), statuses, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và danh sách status với phân trang: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo symbol, direction và status với phân trang và xử lý ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findBySymbolAndDirectionAndStatus(Symbol symbol, OrderDirection direction, OrderStatus status, PageRequest pageRequest) {
        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findAllBySymbolAndDirectionAndStatus(
                    symbol.getValue(),
                    jpaDirection,
                    jpaStatus,
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo symbol, direction và status với phân trang, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo symbol, direction và status với phân trang, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status với phân trang: " + e.getMessage(), e);
        }
    }

    /**
     * Xóa lệnh với xử lý ngoại lệ
     * @param order Lệnh cần xóa
     */
    @Override
    public void delete(Order order) {
        try {
            OrderJpaEntity jpaEntity = orderJpaMapper.domainToJpa(order);
            orderJpaRepository.delete(jpaEntity);
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy lệnh để xóa, orderId = {}", order.getId());
        } catch (DataAccessException e) {
            log.error("Lỗi khi xóa lệnh, orderId = {}", order.getId(), e);
            throw new DatabaseException("Lỗi khi xóa lệnh: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa lệnh, orderId = {}", order.getId(), e);
            throw new DatabaseException("Lỗi khi xóa lệnh: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo orderId với xử lý ngoại lệ
     * @param orderId ID của lệnh
     * @return Optional<Order>
     */
    @Override
    public Optional<Order> findByOrderId(OrderId orderId) {
        try {
            return orderJpaRepository.findById(orderId.getValue())
                    .map(orderJpaMapper::jpaToDomain);
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy lệnh với orderId = {}", orderId.getValue());
            return Optional.empty();
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo orderId, orderId = {}", orderId.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo orderId: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo orderId, orderId = {}", orderId.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo orderId: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo memberId, symbol và danh sách status với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param statuses Danh sách trạng thái của lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findByMemberIdAndSymbolAndStatusIn(Long memberId, Symbol symbol, List<OrderStatus> statuses) {
        try {
            List<String> statusNames = statuses.stream().map(OrderStatus::name).collect(Collectors.toList());
            return orderJpaRepository.findAllByMemberIdAndSymbolAndStatusIn(memberId, symbol.getValue(), statusNames).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo memberId, symbol và danh sách status, memberId = {}, symbol = {}, statuses = {}", memberId, symbol.getValue(), statuses, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và danh sách status: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo memberId, symbol và danh sách status, memberId = {}, symbol = {}, statuses = {}", memberId, symbol.getValue(), statuses, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo memberId, symbol và danh sách status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo symbol, direction và status với xử lý ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findBySymbolAndDirectionAndStatus(Symbol symbol, OrderDirection direction, OrderStatus status) {
        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            return orderJpaRepository.findAllBySymbolAndDirectionAndStatus(
                    symbol.getValue(),
                    jpaDirection,
                    jpaStatus
                ).stream()
                .map(orderJpaMapper::jpaToDomain)
                .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo symbol, direction và status, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo symbol, direction và status, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo status và expireTime với phân trang và xử lý ngoại lệ
     * @param status Trạng thái của lệnh
     * @param expireTime Thời gian hết hạn
     * @param pageRequest Thông tin phân trang
     * @return Page<Order>
     */
    @Override
    public Page<Order> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime, PageRequest pageRequest) {
        try {
            org.springframework.data.domain.Page<OrderJpaEntity> jpaPage = orderJpaRepository.findByStatusAndExpireTimeBefore(
                    status,
                    expireTime,
                    org.springframework.data.domain.PageRequest.of(
                            pageRequest.getPageNumber(),
                            pageRequest.getPageSize(),
                            Sort.by(pageRequest.getSortDirection().name(), pageRequest.getSortBy())
                    )
            );

            return new Page<>(
                    jpaPage.getContent().stream().map(orderJpaMapper::jpaToDomain).collect(Collectors.toList()),
                    jpaPage.getNumber(),
                    jpaPage.getSize(),
                    (int) jpaPage.getTotalElements(),
                    jpaPage.getTotalPages()
            );
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo status và expireTime với phân trang, status = {}, expireTime = {}", status, expireTime, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status và expireTime với phân trang: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo status và expireTime với phân trang, status = {}, expireTime = {}", status, expireTime, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo status và expireTime với phân trang: " + e.getMessage(), e);
        }
    }



    /**
     * Xóa lệnh theo ID với xử lý ngoại lệ
     * @param id ID của lệnh
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void deleteById(OrderId id) {
        try {
            orderJpaRepository.deleteById(id.getValue());
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy lệnh để xóa với ID = {}", id.getValue());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi xóa lệnh theo ID, orderId = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa lệnh theo ID, orderId = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi xóa lệnh theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi xóa lệnh thất bại
     * @param e Ngoại lệ
     * @param id ID của lệnh
     */
    @Recover
    public void recoverDeleteById(Exception e, OrderId id) {
        log.error("Đã thử lại xóa lệnh 3 lần nhưng thất bại, orderId = {}", id.getValue(), e);
        throw new DatabaseException("Không thể xóa lệnh sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần với xử lý ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findBySymbolAndDirectionAndStatusOrderByPriceDesc(Symbol symbol, OrderDirection direction, OrderStatus status) {
        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            return orderJpaRepository.findAllBySymbolAndDirectionAndStatusOrderByPriceDesc(
                    symbol.getValue(),
                    jpaDirection,
                    jpaStatus
                ).stream()
                .map(orderJpaMapper::jpaToDomain)
                .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần với xử lý ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findBySymbolAndDirectionAndStatusOrderByPriceAsc(Symbol symbol, OrderDirection direction, OrderStatus status) {
        try {
            // Chuyển đổi OrderDirection từ domain entity sang OrderDirection trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderDirection jpaDirection =
                com.icetea.lotus.core.domain.entity.OrderDirection.valueOf(direction.name());

            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            return orderJpaRepository.findAllBySymbolAndDirectionAndStatusOrderByPriceAsc(
                    symbol.getValue(),
                    jpaDirection,
                    jpaStatus
                ).stream()
                .map(orderJpaMapper::jpaToDomain)
                .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần, symbol = {}, direction = {}, status = {}", symbol.getValue(), direction, status, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm lệnh theo symbol, status và danh sách type với xử lý ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @param types Danh sách loại lệnh
     * @return List<Order>
     */
    @Override
    public List<Order> findBySymbolAndStatusAndTypeIn(Symbol symbol, OrderStatus status, List<OrderType> types) {
        try {
            // Chuyển đổi OrderStatus từ domain entity sang OrderStatus trong JPA entity
            com.icetea.lotus.core.domain.entity.OrderStatus jpaStatus =
                com.icetea.lotus.core.domain.entity.OrderStatus.valueOf(status.name());

            List<String> typeNames = types.stream().map(OrderType::name).collect(Collectors.toList());
            return orderJpaRepository.findAllBySymbolAndStatusAndTypeIn(symbol.getValue(), jpaStatus, typeNames).stream()
                    .map(orderJpaMapper::jpaToDomain)
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            log.error("Lỗi khi tìm lệnh theo symbol, status và danh sách type, symbol = {}, status = {}, types = {}", symbol.getValue(), status, types, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, status và danh sách type: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm lệnh theo symbol, status và danh sách type, symbol = {}, status = {}, types = {}", symbol.getValue(), status, types, e);
            throw new DatabaseException("Lỗi khi tìm lệnh theo symbol, status và danh sách type: " + e.getMessage(), e);
        }
    }
}
