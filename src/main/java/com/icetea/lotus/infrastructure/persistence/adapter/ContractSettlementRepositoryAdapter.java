package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.ContractSettlement;
import com.icetea.lotus.core.domain.repository.ContractSettlementRepository;
import com.icetea.lotus.core.domain.valueobject.ContractId;
import com.icetea.lotus.core.domain.valueobject.ContractSettlementId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.ContractSettlementJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.ContractSettlementPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.ContractSettlementJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Adapter cho ContractSettlementRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class ContractSettlementRepositoryAdapter implements ContractSettlementRepository {

    private final ContractSettlementJpaRepository contractSettlementJpaRepository;
    private final ContractSettlementPersistenceMapper contractSettlementPersistenceMapper;

    /**
     * Lưu một ContractSettlement với xử lý ngoại lệ và thử lại
     * @param contractSettlement ContractSettlement cần lưu
     * @return ContractSettlement đã lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public ContractSettlement save(ContractSettlement contractSettlement) {
        try {
            log.debug("Lưu ContractSettlement, id = {}", contractSettlement.getId().getValue());

            ContractSettlementJpaEntity entity = contractSettlementPersistenceMapper.domainToEntity(contractSettlement);
            ContractSettlementJpaEntity savedEntity = contractSettlementJpaRepository.save(entity);

            log.debug("Đã lưu ContractSettlement thành công, id = {}", savedEntity.getId());

            return contractSettlementPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu ContractSettlement, id = {}", contractSettlement.getId().getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu ContractSettlement, id = {}", contractSettlement.getId().getValue(), e);
            throw new DatabaseException("Lỗi khi lưu ContractSettlement: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu ContractSettlement thất bại
     * @param e Ngoại lệ
     * @param contractSettlement ContractSettlement cần lưu
     * @return ContractSettlement
     */
    @Recover
    public ContractSettlement recoverSave(Exception e, ContractSettlement contractSettlement) {
        log.error("Đã thử lại lưu ContractSettlement 3 lần nhưng thất bại, id = {}", contractSettlement.getId().getValue(), e);
        throw new DatabaseException("Không thể lưu ContractSettlement sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm ContractSettlement theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của ContractSettlement
     * @return ContractSettlement nếu tìm thấy, null nếu không
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public ContractSettlement findById(ContractSettlementId id) {
        try {
            log.debug("Tìm ContractSettlement theo ID, id = {}", id.getValue());

            ContractSettlementJpaEntity entity = contractSettlementJpaRepository.findById(id.getValue()).orElse(null);

            if (entity == null) {
                log.debug("Không tìm thấy ContractSettlement, id = {}", id.getValue());
                return null;
            }

            log.debug("Đã tìm thấy ContractSettlement, id = {}", id.getValue());

            return contractSettlementPersistenceMapper.entityToDomain(entity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm ContractSettlement theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm ContractSettlement theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm ContractSettlement theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm ContractSettlement theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của ContractSettlement
     * @return null
     */
    @Recover
    public ContractSettlement recoverFindById(Exception e, ContractSettlementId id) {
        log.error("Đã thử lại tìm ContractSettlement theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return null;
    }

    /**
     * Tìm tất cả ContractSettlement theo contractId với xử lý ngoại lệ và thử lại
     * @param contractId ID của hợp đồng
     * @return Danh sách các ContractSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<ContractSettlement> findAllByContractId(ContractId contractId) {
        try {
            log.debug("Tìm tất cả ContractSettlement theo contractId, contractId = {}", contractId.getValue());

            List<ContractSettlementJpaEntity> entities = contractSettlementJpaRepository.findAllByContractId(contractId.getValue());

            log.debug("Đã tìm thấy {} ContractSettlement, contractId = {}", entities.size(), contractId.getValue());

            return contractSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả ContractSettlement theo contractId, contractId = {}", contractId.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả ContractSettlement theo contractId, contractId = {}", contractId.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm tất cả ContractSettlement theo contractId: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả ContractSettlement theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các ContractSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<ContractSettlement> findAllBySymbol(Symbol symbol) {
        try {
            log.debug("Tìm tất cả ContractSettlement theo symbol, symbol = {}", symbol.getValue());

            List<ContractSettlementJpaEntity> entities = contractSettlementJpaRepository.findAllBySymbol(symbol.getValue());

            log.debug("Đã tìm thấy {} ContractSettlement, symbol = {}", entities.size(), symbol.getValue());

            return contractSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả ContractSettlement theo symbol, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả ContractSettlement theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm tất cả ContractSettlement theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả ContractSettlement theo symbol và khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các ContractSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<ContractSettlement> findAllBySymbolAndTimestampBetween(Symbol symbol, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm tất cả ContractSettlement theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), startTime, endTime);

            List<ContractSettlementJpaEntity> entities = contractSettlementJpaRepository.findAllBySymbolAndTimestampBetween(
                    symbol.getValue(), startTime, endTime);

            log.debug("Đã tìm thấy {} ContractSettlement, symbol = {}, startTime = {}, endTime = {}",
                    entities.size(), symbol.getValue(), startTime, endTime);

            return contractSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả ContractSettlement theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả ContractSettlement theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm tất cả ContractSettlement theo symbol và khoảng thời gian: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả ContractSettlement theo memberId với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @return Danh sách các ContractSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<ContractSettlement> findAllByMemberId(String memberId) {
        try {
            log.debug("Tìm tất cả ContractSettlement theo memberId, memberId = {}", memberId);

            List<ContractSettlementJpaEntity> entities = contractSettlementJpaRepository.findAllByMemberId(memberId);

            log.debug("Đã tìm thấy {} ContractSettlement, memberId = {}", entities.size(), memberId);

            return contractSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả ContractSettlement theo memberId, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả ContractSettlement theo memberId, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả ContractSettlement theo memberId: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả ContractSettlement theo memberId và khoảng thời gian với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @param startTime Thời điểm bắt đầu
     * @param endTime Thời điểm kết thúc
     * @return Danh sách các ContractSettlement
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<ContractSettlement> findAllByMemberIdAndTimestampBetween(String memberId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm tất cả ContractSettlement theo memberId và khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime);

            List<ContractSettlementJpaEntity> entities = contractSettlementJpaRepository.findAllByMemberIdAndTimestampBetween(
                    memberId, startTime, endTime);

            log.debug("Đã tìm thấy {} ContractSettlement, memberId = {}, startTime = {}, endTime = {}",
                    entities.size(), memberId, startTime, endTime);

            return contractSettlementPersistenceMapper.entitiesToDomains(entities);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả ContractSettlement theo memberId và khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả ContractSettlement theo memberId và khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm tất cả ContractSettlement theo memberId và khoảng thời gian: " + e.getMessage(), e);
        }
    }
}
