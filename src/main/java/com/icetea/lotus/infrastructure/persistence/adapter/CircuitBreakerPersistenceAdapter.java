package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.CircuitBreaker;
import com.icetea.lotus.core.domain.repository.CircuitBreakerRepository;
import com.icetea.lotus.core.domain.valueobject.CircuitBreakerId;
import com.icetea.lotus.core.domain.valueobject.CircuitBreakerStatus;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.CircuitBreakerJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.CircuitBreakerPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.CircuitBreakerJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho CircuitBreakerRepository
 * Triển khai các phương thức của CircuitBreakerRepository
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class CircuitBreakerPersistenceAdapter implements CircuitBreakerRepository {

    private final CircuitBreakerJpaRepository circuitBreakerJpaRepository;
    private final CircuitBreakerPersistenceMapper circuitBreakerPersistenceMapper;

    /**
     * Lưu circuit breaker với xử lý ngoại lệ và thử lại
     * @param circuitBreaker CircuitBreaker
     * @return CircuitBreaker
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public CircuitBreaker save(CircuitBreaker circuitBreaker) {
        try {
            log.debug("Lưu circuit breaker, id = {}, symbol = {}",
                    circuitBreaker.getId() != null ? circuitBreaker.getId().getValue() : "null",
                    circuitBreaker.getSymbol() != null ? circuitBreaker.getSymbol().getValue() : "null");

            if (circuitBreaker == null) {
                throw new IllegalArgumentException("CircuitBreaker không được để trống");
            }

            CircuitBreakerJpaEntity entity = circuitBreakerPersistenceMapper.domainToEntity(circuitBreaker);
            CircuitBreakerJpaEntity savedEntity = circuitBreakerJpaRepository.save(entity);

            log.debug("Đã lưu circuit breaker thành công, id = {}", savedEntity.getId());

            return circuitBreakerPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu circuit breaker, id = {}, symbol = {}",
                    circuitBreaker.getId() != null ? circuitBreaker.getId().getValue() : "null",
                    circuitBreaker.getSymbol() != null ? circuitBreaker.getSymbol().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu circuit breaker, id = {}, symbol = {}",
                    circuitBreaker.getId() != null ? circuitBreaker.getId().getValue() : "null",
                    circuitBreaker.getSymbol() != null ? circuitBreaker.getSymbol().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu circuit breaker: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu circuit breaker thất bại
     * @param e Ngoại lệ
     * @param circuitBreaker CircuitBreaker
     * @return CircuitBreaker
     */
    @Recover
    public CircuitBreaker recoverSave(Exception e, CircuitBreaker circuitBreaker) {
        log.error("Đã thử lại lưu circuit breaker 3 lần nhưng thất bại, id = {}, symbol = {}",
                circuitBreaker.getId() != null ? circuitBreaker.getId().getValue() : "null",
                circuitBreaker.getSymbol() != null ? circuitBreaker.getSymbol().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu circuit breaker sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm circuit breaker theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của circuit breaker
     * @return Optional chứa circuit breaker nếu tìm thấy
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<CircuitBreaker> findById(CircuitBreakerId id) {
        try {
            log.debug("Tìm circuit breaker theo id, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            Optional<CircuitBreakerJpaEntity> entity = circuitBreakerJpaRepository.findById(Long.valueOf(id.getValue()));
            Optional<CircuitBreaker> result = entity.map(circuitBreakerPersistenceMapper::entityToDomain);

            if (result.isPresent()) {
                log.debug("Đã tìm thấy circuit breaker, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy circuit breaker, id = {}", id.getValue());
            }

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm circuit breaker theo id, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm circuit breaker theo id, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm circuit breaker theo id: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm circuit breaker theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của circuit breaker
     * @return Optional.empty()
     */
    @Recover
    public Optional<CircuitBreaker> recoverFindById(Exception e, CircuitBreakerId id) {
        log.error("Đã thử lại tìm circuit breaker theo id 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm circuit breaker theo symbol với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @return Optional chứa circuit breaker nếu tìm thấy
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<CircuitBreaker> findBySymbol(Symbol symbol) {
        try {
            log.debug("Tìm circuit breaker theo symbol, symbol = {}", symbol.getValue());

            if (symbol == null) {
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            Optional<CircuitBreakerJpaEntity> entity = circuitBreakerJpaRepository.findBySymbol(symbol.getValue());
            Optional<CircuitBreaker> result = entity.map(circuitBreakerPersistenceMapper::entityToDomain);

            if (result.isPresent()) {
                log.debug("Đã tìm thấy circuit breaker, symbol = {}", symbol.getValue());
            } else {
                log.debug("Không tìm thấy circuit breaker, symbol = {}", symbol.getValue());
            }

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm circuit breaker theo symbol, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm circuit breaker theo symbol, symbol = {}", symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm circuit breaker theo symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm circuit breaker theo symbol thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @return Optional.empty()
     */
    @Recover
    public Optional<CircuitBreaker> recoverFindBySymbol(Exception e, Symbol symbol) {
        log.error("Đã thử lại tìm circuit breaker theo symbol 3 lần nhưng thất bại, symbol = {}", symbol.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm circuit breaker theo symbol và status với xử lý ngoại lệ và thử lại
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của circuit breaker
     * @return Optional chứa circuit breaker nếu tìm thấy
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<CircuitBreaker> findBySymbolAndStatus(Symbol symbol, CircuitBreakerStatus status) {
        try {
            log.debug("Tìm circuit breaker theo symbol và status, symbol = {}, status = {}", symbol.getValue(), status);

            if (symbol == null) {
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            if (status == null) {
                throw new IllegalArgumentException("Status không được để trống");
            }

            Optional<CircuitBreakerJpaEntity> entity = circuitBreakerJpaRepository.findBySymbolAndStatus(symbol.getValue(), status.name());
            Optional<CircuitBreaker> result = entity.map(circuitBreakerPersistenceMapper::entityToDomain);

            if (result.isPresent()) {
                log.debug("Đã tìm thấy circuit breaker, symbol = {}, status = {}", symbol.getValue(), status);
            } else {
                log.debug("Không tìm thấy circuit breaker, symbol = {}, status = {}", symbol.getValue(), status);
            }

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm circuit breaker theo symbol và status, symbol = {}, status = {}", symbol.getValue(), status, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm circuit breaker theo symbol và status, symbol = {}, status = {}", symbol.getValue(), status, e);
            throw new DatabaseException("Lỗi khi tìm circuit breaker theo symbol và status: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm circuit breaker theo symbol và status thất bại
     * @param e Ngoại lệ
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của circuit breaker
     * @return Optional.empty()
     */
    @Recover
    public Optional<CircuitBreaker> recoverFindBySymbolAndStatus(Exception e, Symbol symbol, CircuitBreakerStatus status) {
        log.error("Đã thử lại tìm circuit breaker theo symbol và status 3 lần nhưng thất bại, symbol = {}, status = {}", symbol.getValue(), status, e);
        return Optional.empty();
    }

    /**
     * Tìm tất cả circuit breaker theo status với xử lý ngoại lệ và thử lại
     * @param status Trạng thái của circuit breaker
     * @return Danh sách các circuit breaker
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<CircuitBreaker> findAllByStatus(CircuitBreakerStatus status) {
        try {
            log.debug("Tìm tất cả circuit breaker theo status, status = {}", status);

            if (status == null) {
                throw new IllegalArgumentException("Status không được để trống");
            }

            List<CircuitBreakerJpaEntity> entities = circuitBreakerJpaRepository.findAllByStatus(status.name());
            List<CircuitBreaker> result = entities.stream()
                    .map(circuitBreakerPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());

            log.debug("Đã tìm thấy {} circuit breaker theo status = {}", result.size(), status);

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả circuit breaker theo status, status = {}", status, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả circuit breaker theo status, status = {}", status, e);
            throw new DatabaseException("Lỗi khi tìm tất cả circuit breaker theo status: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả circuit breaker theo status thất bại
     * @param e Ngoại lệ
     * @param status Trạng thái của circuit breaker
     * @return Danh sách rỗng
     */
    @Recover
    public List<CircuitBreaker> recoverFindAllByStatus(Exception e, CircuitBreakerStatus status) {
        log.error("Đã thử lại tìm tất cả circuit breaker theo status 3 lần nhưng thất bại, status = {}", status, e);
        return List.of();
    }

    /**
     * Tìm tất cả circuit breaker với xử lý ngoại lệ và thử lại
     * @return Danh sách các circuit breaker
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<CircuitBreaker> findAll() {
        try {
            log.debug("Tìm tất cả circuit breaker");

            List<CircuitBreakerJpaEntity> entities = circuitBreakerJpaRepository.findAll();
            List<CircuitBreaker> result = entities.stream()
                    .map(circuitBreakerPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());

            log.debug("Đã tìm thấy {} circuit breaker", result.size());

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả circuit breaker", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả circuit breaker", e);
            throw new DatabaseException("Lỗi khi tìm tất cả circuit breaker: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả circuit breaker thất bại
     * @param e Ngoại lệ
     * @return Danh sách rỗng
     */
    @Recover
    public List<CircuitBreaker> recoverFindAll(Exception e) {
        log.error("Đã thử lại tìm tất cả circuit breaker 3 lần nhưng thất bại", e);
        return List.of();
    }

    /**
     * Xóa circuit breaker theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của circuit breaker
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void deleteById(CircuitBreakerId id) {
        try {
            log.debug("Xóa circuit breaker theo id, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            circuitBreakerJpaRepository.deleteById(Long.valueOf(id.getValue()));

            log.debug("Đã xóa circuit breaker thành công, id = {}", id.getValue());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi xóa circuit breaker theo id, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa circuit breaker theo id, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi xóa circuit breaker theo id: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi xóa circuit breaker theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của circuit breaker
     */
    @Recover
    public void recoverDeleteById(Exception e, CircuitBreakerId id) {
        log.error("Đã thử lại xóa circuit breaker theo id 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        throw new DatabaseException("Không thể xóa circuit breaker theo id sau 3 lần thử lại: " + e.getMessage(), e);
    }
}
