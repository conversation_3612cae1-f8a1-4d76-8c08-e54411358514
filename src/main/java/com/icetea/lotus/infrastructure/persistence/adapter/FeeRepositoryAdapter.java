package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.Fee;
import com.icetea.lotus.core.domain.repository.FeeRepository;
import com.icetea.lotus.core.domain.valueobject.FeeId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Adapter cho FeeRepository
 * Triển khai các phương thức của FeeRepository
 * Sử dụng FeePersistenceAdapter để thực hiện các thao tác với database
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class FeeRepositoryAdapter implements FeeRepository {

    private final FeePersistenceAdapter feePersistenceAdapter;

    /**
     * Tìm phí giao dịch theo ID
     * @param id ID của phí giao dịch
     * @return Optional<Fee>
     */
    @Override
    public Optional<Fee> findById(FeeId id) {
        return feePersistenceAdapter.findById(id);
    }

    /**
     * Tìm phí giao dịch theo orderId
     * @param orderId ID của lệnh
     * @return Fee
     */
    @Override
    public Fee findByOrderId(OrderId orderId) {
        return feePersistenceAdapter.findByOrderId(orderId).orElse(null);
    }

    /**
     * Lấy phí giao dịch theo thành viên và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Optional<Fee>
     */
    @Override
    public Optional<Fee> findByMemberIdAndSymbolOptional(Long memberId, Symbol symbol) {
        List<Fee> fees = feePersistenceAdapter.findByMemberIdAndSymbol(memberId, symbol);
        if (fees.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(fees.get(0));
    }

    /**
     * Lấy danh sách phí giao dịch theo thành viên và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return List<Fee>
     */
    @Override
    public List<Fee> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        return feePersistenceAdapter.findByMemberIdAndSymbol(memberId, symbol);
    }

    /**
     * Lấy phí giao dịch theo thành viên
     * @param memberId ID của thành viên
     * @return Optional<Fee>
     */
    @Override
    public Optional<Fee> findByMemberIdOptional(Long memberId) {
        List<Fee> fees = feePersistenceAdapter.findByMemberId(memberId);
        if (fees.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(fees.get(0));
    }

    /**
     * Lấy danh sách phí giao dịch theo thành viên
     * @param memberId ID của thành viên
     * @return List<Fee>
     */
    @Override
    public List<Fee> findByMemberId(Long memberId) {
        return feePersistenceAdapter.findByMemberId(memberId);
    }

    /**
     * Tính tổng phí giao dịch theo memberId
     * @param memberId ID của thành viên
     * @return Money
     */
    @Override
    public Money sumFeeByMemberId(Long memberId) {
        return feePersistenceAdapter.sumFeeByMemberId(memberId);
    }

    /**
     * Tính tổng phí giao dịch theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Money
     */
    @Override
    public Money sumFeeByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        return feePersistenceAdapter.sumFeeByMemberIdAndSymbol(memberId, symbol);
    }

    /**
     * Lấy phí giao dịch mặc định
     * @return Fee
     */
    @Override
    public Fee getDefaultFee() {
        // Trả về phí mặc định
        return null;
    }

    /**
     * Lưu phí giao dịch
     * @param fee Phí giao dịch
     * @return Fee
     */
    @Override
    public Fee save(Fee fee) {
        return feePersistenceAdapter.save(fee);
    }

    /**
     * Cập nhật phí giao dịch
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param makerFee Phí maker
     * @param takerFee Phí taker
     * @return Fee
     */
    @Override
    public Fee updateFee(Long memberId, Symbol symbol, BigDecimal makerFee, BigDecimal takerFee) {
        // Chưa triển khai
        return null;
    }

    /**
     * Xóa phí giao dịch
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void deleteFee(Long memberId, Symbol symbol) {
        // Chưa triển khai
    }
}
