package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.FeeJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * JPA Repository cho FeeJpaEntity
 */
@Repository
public interface FeeJpaRepository extends JpaRepository<FeeJpaEntity, Long>, JpaSpecificationExecutor<FeeJpaEntity> {
    
    /**
     * Tìm phí giao dịch theo orderId
     * @param orderId ID của lệnh
     * @return Optional chứa phí giao dịch nếu tìm thấy
     */
    Optional<FeeJpaEntity> findByOrderId(String orderId);
    
    /**
     * Tìm phí giao dịch theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các phí giao dịch
     */
    List<FeeJpaEntity> findByMemberId(Long memberId);
    
    /**
     * Tìm phí giao dịch theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Danh sách các phí giao dịch
     */
    List<FeeJpaEntity> findByMemberIdAndSymbol(Long memberId, String symbol);
    
    /**
     * Tính tổng phí giao dịch theo memberId
     * @param memberId ID của thành viên
     * @return BigDecimal
     */
    @Query("SELECT SUM(f.fee) FROM FeeJpaEntity f WHERE f.memberId = :memberId")
    BigDecimal sumFeeByMemberId(@Param("memberId") Long memberId);
    
    /**
     * Tính tổng phí giao dịch theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Query("SELECT SUM(f.fee) FROM FeeJpaEntity f WHERE f.memberId = :memberId AND f.symbol = :symbol")
    BigDecimal sumFeeByMemberIdAndSymbol(@Param("memberId") Long memberId, @Param("symbol") String symbol);
}
