package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.UserPositionMode;
import com.icetea.lotus.core.domain.repository.UserPositionModeRepository;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.UserPositionModeJpaEntity;
import com.icetea.lotus.infrastructure.persistence.repository.UserPositionModeJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Adapter cho UserPositionModeRepository
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserPositionModeRepositoryAdapter implements UserPositionModeRepository {

    private final UserPositionModeJpaRepository userPositionModeJpaRepository;

    /**
     * Tìm chế độ vị thế của người dùng theo memberId với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @return Optional chứa chế độ vị thế nếu tìm thấy
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<UserPositionMode> findByMemberId(Long memberId) {
        try {
            log.debug("Tìm chế độ vị thế của người dùng theo memberId, memberId = {}", memberId);

            if (memberId == null) {
                throw new IllegalArgumentException("MemberId không được để trống");
            }

            Optional<UserPositionModeJpaEntity> entity = userPositionModeJpaRepository.findByMemberId(memberId);

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy chế độ vị thế của người dùng, memberId = {}", memberId);
            } else {
                log.debug("Không tìm thấy chế độ vị thế của người dùng, memberId = {}", memberId);
            }

            return entity.map(this::mapToDomainEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm chế độ vị thế của người dùng theo memberId, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm chế độ vị thế của người dùng theo memberId, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm chế độ vị thế của người dùng theo memberId: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm chế độ vị thế của người dùng theo memberId thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @return Optional<UserPositionMode>
     */
    @Recover
    public Optional<UserPositionMode> recoverFindByMemberId(Exception e, Long memberId) {
        log.error("Đã thử lại tìm chế độ vị thế của người dùng theo memberId 3 lần nhưng thất bại, memberId = {}", memberId, e);
        return Optional.empty();
    }

    /**
     * Lưu chế độ vị thế của người dùng với xử lý ngoại lệ và thử lại
     * @param userPositionMode Chế độ vị thế của người dùng
     * @return UserPositionMode
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public UserPositionMode save(UserPositionMode userPositionMode) {
        try {
            log.debug("Lưu chế độ vị thế của người dùng, id = {}, memberId = {}, positionMode = {}",
                    userPositionMode.getId(), userPositionMode.getMemberId(), userPositionMode.getPositionMode());

            if (userPositionMode == null) {
                throw new IllegalArgumentException("UserPositionMode không được để trống");
            }

            UserPositionModeJpaEntity jpaEntity = mapToJpaEntity(userPositionMode);
            UserPositionModeJpaEntity savedEntity = userPositionModeJpaRepository.save(jpaEntity);

            log.debug("Đã lưu chế độ vị thế của người dùng thành công, id = {}", savedEntity.getId());

            return mapToDomainEntity(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu chế độ vị thế của người dùng, memberId = {}",
                    userPositionMode.getMemberId(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu chế độ vị thế của người dùng, memberId = {}",
                    userPositionMode.getMemberId(), e);
            throw new DatabaseException("Lỗi khi lưu chế độ vị thế của người dùng: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu chế độ vị thế của người dùng thất bại
     * @param e Ngoại lệ
     * @param userPositionMode Chế độ vị thế của người dùng
     * @return UserPositionMode
     */
    @Recover
    public UserPositionMode recoverSave(Exception e, UserPositionMode userPositionMode) {
        log.error("Đã thử lại lưu chế độ vị thế của người dùng 3 lần nhưng thất bại, memberId = {}",
                userPositionMode.getMemberId(), e);
        throw new DatabaseException("Không thể lưu chế độ vị thế của người dùng sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    private UserPositionMode mapToDomainEntity(UserPositionModeJpaEntity jpaEntity) {
        return UserPositionMode.builder()
                .id(jpaEntity.getId())
                .memberId(jpaEntity.getMemberId())
                .positionMode(jpaEntity.getPositionMode())
                .createTime(jpaEntity.getCreateTime())
                .updateTime(jpaEntity.getUpdateTime())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domainEntity Domain entity
     * @return JPA entity
     */
    private UserPositionModeJpaEntity mapToJpaEntity(UserPositionMode domainEntity) {
        return UserPositionModeJpaEntity.builder()
                .id(domainEntity.getId())
                .memberId(domainEntity.getMemberId())
                .positionMode(domainEntity.getPositionMode())
                .createTime(domainEntity.getCreateTime())
                .updateTime(domainEntity.getUpdateTime())
                .build();
    }
}
