package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.CircuitBreaker;
import com.icetea.lotus.core.domain.valueobject.CircuitBreakerId;
import com.icetea.lotus.core.domain.valueobject.CircuitBreakerStatus;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.CircuitBreakerJpaEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Mapper cho CircuitBreaker
 */
@Component
public class CircuitBreakerPersistenceMapper {

    /**
     * <PERSON><PERSON><PERSON><PERSON> đổ<PERSON> từ domain entity sang JPA entity
     * @param circuitBreaker Domain entity
     * @return JPA entity
     */
    public CircuitBreakerJpaEntity domainToEntity(CircuitBreaker circuitBreaker) {
        if (circuitBreaker == null) {
            return null;
        }

        return CircuitBreakerJpaEntity.builder()
                .id(circuitBreaker.getId() != null ? Long.valueOf(circuitBreaker.getId().getValue()) : null)
                .symbol(circuitBreaker.getSymbol() != null ? circuitBreaker.getSymbol().getValue() : null)
                .status(circuitBreaker.getStatus() != null ? circuitBreaker.getStatus().name() : null)
                .referencePrice(circuitBreaker.getReferencePrice())
                .triggerPrice(circuitBreaker.getUpperTriggerPrice()) // Sử dụng upperTriggerPrice thay vì triggerPrice
                .priceChange(circuitBreaker.getPercentageChange()) // Sử dụng percentageChange thay vì priceChange
                .triggerTime(circuitBreaker.getTriggerTime())
                .endTime(circuitBreaker.getEndTime())
                .createTime(circuitBreaker.getCreateTime())
                .updateTime(LocalDateTime.now())
                .build();
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public CircuitBreaker entityToDomain(CircuitBreakerJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        return CircuitBreaker.builder()
                .id(entity.getId() != null ? CircuitBreakerId.of(entity.getId().toString()) : null)
                .symbol(entity.getSymbol() != null ? Symbol.of(entity.getSymbol()) : null)
                .status(entity.getStatus() != null ? CircuitBreakerStatus.valueOf(entity.getStatus()) : null)
                .referencePrice(entity.getReferencePrice())
                .upperTriggerPrice(entity.getTriggerPrice())
                .percentageChange(entity.getPriceChange())
                .triggerTime(entity.getTriggerTime())
                .endTime(entity.getEndTime())
                .createTime(entity.getCreateTime())
                .build();
    }
}
