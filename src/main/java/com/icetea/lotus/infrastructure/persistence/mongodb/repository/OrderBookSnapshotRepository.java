package com.icetea.lotus.infrastructure.persistence.mongodb.repository;

import com.icetea.lotus.infrastructure.persistence.mongodb.document.OrderBookSnapshotDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * MongoDB Repository cho Order Book Snapshots
 * Optimized queries cho high-frequency trading
 */
@Repository
public interface OrderBookSnapshotRepository extends MongoRepository<OrderBookSnapshotDocument, String> {

    /**
     * Find latest snapshot by symbol
     */
    @Query(value = "{'symbol': ?0}", sort = "{'version': -1}")
    Optional<OrderBookSnapshotDocument> findLatestBySymbol(String symbol);

    /**
     * Find snapshot by symbol and version
     */
    Optional<OrderBookSnapshotDocument> findBySymbolAndVersion(String symbol, Long version);

    /**
     * Find snapshots by symbol within time range
     */
    @Query("{'symbol': ?0, 'timestamp': {'$gte': ?1, '$lte': ?2}}")
    List<OrderBookSnapshotDocument> findBySymbolAndTimestampBetween(
            String symbol, Instant startTime, Instant endTime);

    /**
     * Find snapshots by symbol with pagination
     */
    Page<OrderBookSnapshotDocument> findBySymbolOrderByVersionDesc(String symbol, Pageable pageable);

    /**
     * Find snapshots newer than specific version
     */
    @Query("{'symbol': ?0, 'version': {'$gt': ?1}}")
    List<OrderBookSnapshotDocument> findBySymbolAndVersionGreaterThan(String symbol, Long version);

    /**
     * Count snapshots by symbol
     */
    long countBySymbol(String symbol);

    /**
     * Delete old snapshots by symbol (keep only latest N)
     */
    @Query(value = "{'symbol': ?0}", sort = "{'version': -1}")
    List<OrderBookSnapshotDocument> findBySymbolOrderByVersionDesc(String symbol);

    /**
     * Find snapshots by multiple symbols
     */
    @Query("{'symbol': {'$in': ?0}}")
    List<OrderBookSnapshotDocument> findBySymbolIn(List<String> symbols);

    /**
     * Find expired snapshots for cleanup
     */
    @Query("{'expires_at': {'$lt': ?0}}")
    List<OrderBookSnapshotDocument> findExpiredSnapshots(Instant now);

    /**
     * Delete snapshots older than timestamp
     */
    void deleteByTimestampBefore(Instant timestamp);

    /**
     * Delete snapshots by symbol and keep only latest N versions
     */
    @Query(value = "{'symbol': ?0, 'version': {'$lt': ?1}}")
    void deleteBySymbolAndVersionLessThan(String symbol, Long minVersionToKeep);

    /**
     * Aggregation: Get snapshot statistics by symbol
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'symbol': ?0 } }",
        "{ '$group': { " +
        "    '_id': '$symbol', " +
        "    'totalSnapshots': { '$sum': 1 }, " +
        "    'latestVersion': { '$max': '$version' }, " +
        "    'oldestVersion': { '$min': '$version' }, " +
        "    'avgOrdersCount': { '$avg': '$metadata.total_orders' }, " +
        "    'totalSizeBytes': { '$sum': '$metadata.snapshot_size_bytes' } " +
        "} }"
    })
    Optional<SnapshotStatistics> getSnapshotStatistics(String symbol);

    /**
     * Aggregation: Get symbols with snapshot counts
     */
    @Aggregation(pipeline = {
        "{ '$group': { " +
        "    '_id': '$symbol', " +
        "    'snapshotCount': { '$sum': 1 }, " +
        "    'latestVersion': { '$max': '$version' }, " +
        "    'latestTimestamp': { '$max': '$timestamp' } " +
        "} }",
        "{ '$sort': { 'latestTimestamp': -1 } }"
    })
    List<SymbolSnapshotSummary> getSymbolSnapshotSummaries();

    /**
     * Find snapshots with large size (for optimization)
     */
    @Query("{'metadata.snapshot_size_bytes': {'$gt': ?0}}")
    List<OrderBookSnapshotDocument> findLargeSnapshots(long sizeThreshold);

    /**
     * Find snapshots with low compression ratio
     */
    @Query("{'metadata.compression_ratio': {'$lt': ?0}}")
    List<OrderBookSnapshotDocument> findPoorlyCompressedSnapshots(double compressionThreshold);

    // ===== CLEANUP METHODS =====

    /**
     * Find snapshots older than specified time for cleanup (with pagination)
     */
    @Query(value = "{'timestamp': {'$lt': ?0}}", sort = "{'timestamp': 1}")
    List<OrderBookSnapshotDocument> findByTimestampBeforeOrderByTimestampAsc(Instant cutoffTime, Pageable pageable);

    /**
     * Find snapshots for specific symbol older than specified time
     */
    @Query("{'symbol': ?0, 'timestamp': {'$lt': ?1}}")
    List<OrderBookSnapshotDocument> findBySymbolAndTimestampBefore(String symbol, Instant cutoffTime);

    /**
     * Find oldest snapshots for specific symbol (for count-based cleanup)
     */
    @Query(value = "{'symbol': ?0}", sort = "{'timestamp': 1}")
    List<OrderBookSnapshotDocument> findBySymbolOrderByTimestampAsc(String symbol, Pageable pageable);

    /**
     * Get all distinct symbols that have snapshots
     */
    @Aggregation(pipeline = {
            "{ '$group': { '_id': '$symbol' } }",
            "{ '$project': { '_id': 0, 'symbol': '$_id' } }"
    })
    List<String> findDistinctSymbols();

    /**
     * Statistics interface for aggregation results
     */
    interface SnapshotStatistics {
        String getId(); // symbol
        Long getTotalSnapshots();
        Long getLatestVersion();
        Long getOldestVersion();
        Double getAvgOrdersCount();
        Long getTotalSizeBytes();
    }

    /**
     * Symbol summary interface for aggregation results
     */
    interface SymbolSnapshotSummary {
        String getId(); // symbol
        Long getSnapshotCount();
        Long getLatestVersion();
        Instant getLatestTimestamp();
    }
}
