package com.icetea.lotus.infrastructure.persistence.mongo;

import com.icetea.lotus.core.domain.entity.KLine;
import com.icetea.lotus.core.domain.repository.KLineRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Implementation của KLineRepository sử dụng MongoDB
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MongoKLineRepositoryImpl implements KLineRepository {

    private final MongoTemplate mongoTemplate;

    /**
     * Lưu K-line với xử lý ngoại lệ và thử lại
     * @param kLine K-line
     * @return KLine
     */
    @Override
    @Retryable(
            value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public KLine save(KLine kLine) {
        try {
            log.debug("Lưu K-line, symbol = {}, period = {}, time = {}",
                    kLine.getSymbol().getValue(), kLine.getPeriod(), kLine.getTime());

            // Chuẩn hóa period nếu cần
            String period = normalizePeriod(kLine.getPeriod());
            kLine.setPeriod(period);

            // Đặt thời gian tạo và cập nhật nếu chưa có
            if (kLine.getCreateTime() == null) {
                kLine.setCreateTime(LocalDateTime.now());
            }
            if (kLine.getUpdateTime() == null) {
                kLine.setUpdateTime(LocalDateTime.now());
            }

            // Tạo tên collection
            String collectionName = "contract_kline_" + kLine.getSymbol().getValue() + "_" + period;

            // Lưu K-line vào MongoDB
            mongoTemplate.save(kLine, collectionName);

            log.debug("Đã lưu K-line thành công, symbol = {}, period = {}, time = {}",
                    kLine.getSymbol().getValue(), period, kLine.getTime());

            return kLine;
        } catch (Exception e) {
            log.error("Lỗi khi lưu K-line, symbol = {}, period = {}, time = {}",
                    kLine.getSymbol().getValue(), kLine.getPeriod(), kLine.getTime(), e);
            throw e;
        }
    }

    /**
     * Phục hồi khi lưu K-line thất bại
     * @param e Ngoại lệ
     * @param kLine K-line
     * @return null
     */
    @Recover
    public KLine recoverSave(Exception e, KLine kLine) {
        log.error("Đã thử lại lưu K-line 3 lần nhưng thất bại, symbol = {}, period = {}, time = {}",
                kLine.getSymbol().getValue(), kLine.getPeriod(), kLine.getTime(), e);
        throw new DatabaseException("Không thể lưu K-line sau 3 lần thử lại", e);
    }

    /**
     * Tìm K-line mới nhất theo symbol và period
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @return KLine
     */
    @Override
    public KLine findTopBySymbolAndPeriodOrderByTimeDesc(Symbol symbol, String period) {
        try {
            log.debug("Tìm K-line mới nhất, symbol = {}, period = {}", symbol.getValue(), period);

            // Chuẩn hóa period
            period = normalizePeriod(period);

            // Tạo tên collection
            String collectionName = "contract_kline_" + symbol.getValue() + "_" + period;

            // Tạo query
            Sort sort = Sort.by(Sort.Direction.DESC, "time");
            Query query = new Query().with(sort).limit(1);

            // Thực hiện truy vấn
            KLine kLine = mongoTemplate.findOne(query, KLine.class, collectionName);

            if (kLine == null) {
                log.debug("Không tìm thấy K-line nào, symbol = {}, period = {}", symbol.getValue(), period);
                return null;
            }

            log.debug("Đã tìm thấy K-line mới nhất, symbol = {}, period = {}, time = {}",
                    symbol.getValue(), period, kLine.getTime());

            return kLine;
        } catch (Exception e) {
            log.error("Lỗi khi tìm K-line mới nhất, symbol = {}, period = {}", symbol.getValue(), period, e);
            throw new DatabaseException("Lỗi khi tìm K-line mới nhất", e);
        }
    }

    /**
     * Tìm danh sách K-line theo symbol, period và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách K-line
     */
    @Override
    public List<KLine> findBySymbolAndPeriodAndTimeBetween(Symbol symbol, String period, Long startTime, Long endTime) {
        try {
            log.debug("Tìm danh sách K-line, symbol = {}, period = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), period, startTime, endTime);

            // Chuẩn hóa period
            period = normalizePeriod(period);

            // Tạo tên collection
            String collectionName = "contract_kline_" + symbol.getValue() + "_" + period;

            // Tạo query
            Criteria criteria = Criteria.where("time").gte(startTime).lte(endTime);
            Sort sort = Sort.by(Sort.Direction.ASC, "time");
            Query query = new Query(criteria).with(sort);

            // Thực hiện truy vấn
            List<KLine> kLines = mongoTemplate.find(query, KLine.class, collectionName);

            log.debug("Đã tìm thấy {} K-line, symbol = {}, period = {}, startTime = {}, endTime = {}",
                    kLines.size(), symbol.getValue(), period, startTime, endTime);

            return kLines;
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách K-line, symbol = {}, period = {}, startTime = {}, endTime = {}",
                    symbol.getValue(), period, startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm danh sách K-line", e);
        }
    }

    /**
     * Tìm danh sách K-line theo symbol và period, giới hạn số lượng kết quả
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param limit Số lượng kết quả tối đa
     * @return Danh sách K-line
     */
    @Override
    public List<KLine> findBySymbolAndPeriodOrderByTimeDesc(Symbol symbol, String period, int limit) {
        try {
            log.debug("Tìm danh sách K-line, symbol = {}, period = {}, limit = {}",
                    symbol.getValue(), period, limit);

            // Chuẩn hóa period
            period = normalizePeriod(period);

            // Tạo tên collection
            String collectionName = "contract_kline_" + symbol.getValue() + "_" + period;

            // Tạo query
            Sort sort = Sort.by(Sort.Direction.DESC, "time");
            Query query = new Query().with(sort).limit(limit);

            // Thực hiện truy vấn
            List<KLine> kLines = mongoTemplate.find(query, KLine.class, collectionName);

            log.debug("Đã tìm thấy {} K-line, symbol = {}, period = {}, limit = {}",
                    kLines.size(), symbol.getValue(), period, limit);

            return kLines;
        } catch (Exception e) {
            log.error("Lỗi khi tìm danh sách K-line, symbol = {}, period = {}, limit = {}",
                    symbol.getValue(), period, limit, e);
            throw new DatabaseException("Lỗi khi tìm danh sách K-line", e);
        }
    }

    /**
     * Tìm thời gian lớn nhất của K-line theo symbol và period
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @return Thời gian lớn nhất
     */
    @Override
    public Long findMaxTimeBySymbolAndPeriod(Symbol symbol, String period) {
        try {
            log.debug("Tìm thời gian lớn nhất của K-line, symbol = {}, period = {}", symbol.getValue(), period);

            // Chuẩn hóa period
            period = normalizePeriod(period);

            // Tạo tên collection
            String collectionName = "contract_kline_" + symbol.getValue() + "_" + period;

            // Tạo query
            Sort sort = Sort.by(Sort.Direction.DESC, "time");
            Query query = new Query().with(sort).limit(1);

            // Thực hiện truy vấn
            KLine kLine = mongoTemplate.findOne(query, KLine.class, collectionName);

            if (kLine == null) {
                log.debug("Không tìm thấy K-line nào, symbol = {}, period = {}", symbol.getValue(), period);
                return 0L;
            }

            log.debug("Đã tìm thấy thời gian lớn nhất của K-line, symbol = {}, period = {}, time = {}",
                    symbol.getValue(), period, kLine.getTime());

            return kLine.getTime();
        } catch (Exception e) {
            log.error("Lỗi khi tìm thời gian lớn nhất của K-line, symbol = {}, period = {}",
                    symbol.getValue(), period, e);
            throw new DatabaseException("Lỗi khi tìm thời gian lớn nhất của K-line", e);
        }
    }

    /**
     * Chuẩn hóa period
     * @param period Khoảng thời gian
     * @return Khoảng thời gian đã chuẩn hóa
     */
    private String normalizePeriod(String period) {
        if (period.equals("60min")) {
            return "1hour";
        } else if (period.equals("1mon")) {
            return "1month";
        }
        return period;
    }
}
