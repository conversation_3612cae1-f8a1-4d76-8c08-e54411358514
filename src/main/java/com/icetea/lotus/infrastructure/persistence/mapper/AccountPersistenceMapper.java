package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Account;
import com.icetea.lotus.core.domain.entity.AccountTransaction;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.infrastructure.persistence.entity.AccountJpaEntity;
import com.icetea.lotus.infrastructure.persistence.entity.AccountTransactionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Mapper cho Account và AccountTransaction
 */
@Component
public class AccountPersistenceMapper {

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param account Domain entity
     * @return JPA entity
     */
    public AccountJpaEntity accountToJpaEntity(Account account) {
        if (account == null) {
            return null;
        }

        // Validate tất cả balance fields để tránh numeric overflow
        BigDecimal validatedBalance = null;
        if (account.getBalance() != null) {
            validatedBalance = BigDecimalValidator.validateAndScale(account.getBalance().getValue(), "balance");
        }

        BigDecimal validatedFrozenBalance = null;
        if (account.getFrozenBalance() != null) {
            validatedFrozenBalance = BigDecimalValidator.validateAndScale(account.getFrozenBalance().getValue(), "frozenBalance");
        }

        BigDecimal validatedAvailableBalance = null;
        if (account.getAvailableBalance() != null) {
            validatedAvailableBalance = BigDecimalValidator.validateAndScale(account.getAvailableBalance().getValue(), "availableBalance");
        }

        return AccountJpaEntity.builder()
                .id(account.getId())
                .memberId(account.getMemberId())
                .balance(validatedBalance)
                .frozenBalance(validatedFrozenBalance)
                .availableBalance(validatedAvailableBalance)
                .createTime(account.getCreateTime())
                .updateTime(account.getUpdateTime())
                .build();
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    public Account jpaEntityToAccount(AccountJpaEntity jpaEntity) {
        if (jpaEntity == null) {
            return null;
        }

        return Account.builder()
                .id(jpaEntity.getId())
                .memberId(jpaEntity.getMemberId())
                .balance(jpaEntity.getBalance() != null ? Money.of(jpaEntity.getBalance()) : null)
                .frozenBalance(jpaEntity.getFrozenBalance() != null ? Money.of(jpaEntity.getFrozenBalance()) : null)
                .availableBalance(jpaEntity.getAvailableBalance() != null ? Money.of(jpaEntity.getAvailableBalance()) : null)
                .createTime(jpaEntity.getCreateTime())
                .updateTime(jpaEntity.getUpdateTime())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param transaction Domain entity
     * @return JPA entity
     */
    public AccountTransactionJpaEntity transactionToJpaEntity(AccountTransaction transaction) {
        if (transaction == null) {
            return null;
        }

        // Validate amount và balance để tránh numeric overflow
        BigDecimal validatedAmount = null;
        if (transaction.getAmount() != null) {
            validatedAmount = BigDecimalValidator.validateAndScale(transaction.getAmount().getValue(), "amount");
        }

        BigDecimal validatedBalance = null;
        if (transaction.getBalance() != null) {
            validatedBalance = BigDecimalValidator.validateAndScale(transaction.getBalance().getValue(), "balance");
        }

        return AccountTransactionJpaEntity.builder()
                .id(transaction.getId())
                .memberId(transaction.getMemberId())
                .amount(validatedAmount)
                .balance(validatedBalance)
                .type(transaction.getType())
                .createTime(transaction.getCreateTime())
                .remark(transaction.getRemark())
                .build();
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    public AccountTransaction jpaEntityToTransaction(AccountTransactionJpaEntity jpaEntity) {
        if (jpaEntity == null) {
            return null;
        }

        return AccountTransaction.builder()
                .id(jpaEntity.getId())
                .memberId(jpaEntity.getMemberId())
                .amount(jpaEntity.getAmount() != null ? Money.of(jpaEntity.getAmount()) : null)
                .balance(jpaEntity.getBalance() != null ? Money.of(jpaEntity.getBalance()) : null)
                .type(jpaEntity.getType())
                .createTime(jpaEntity.getCreateTime())
                .remark(jpaEntity.getRemark())
                .build();
    }
}
