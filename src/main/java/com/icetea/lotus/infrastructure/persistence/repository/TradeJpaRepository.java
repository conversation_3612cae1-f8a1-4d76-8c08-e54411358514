package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.TradeJpaEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho TradeJpaEntity
 */
@Repository
public interface TradeJpaRepository extends JpaRepository<TradeJpaEntity, Long>, JpaSpecificationExecutor<TradeJpaEntity> {

    /**
     * Tìm tất cả các giao dịch theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các giao dịch
     */
    List<TradeJpaEntity> findAllBySymbol(String symbol);

    /**
     * Tìm tất cả các giao dịch theo symbol với phân trang
     * @param symbol Symbol của hợp đồng
     * @param pageable Thông tin phân trang
     * @return Trang chứa các giao dịch
     */
    Page<TradeJpaEntity> findAllBySymbol(String symbol, Pageable pageable);

    /**
     * Tìm tất cả các giao dịch theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các giao dịch
     */
    List<TradeJpaEntity> findAllBySymbolAndTradeTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Tìm tất cả các giao dịch theo symbol và khoảng thời gian với phân trang
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param pageable Thông tin phân trang
     * @return Trang chứa các giao dịch
     */
    Page<TradeJpaEntity> findAllBySymbolAndTradeTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm tất cả các giao dịch theo buyMemberId
     * @param buyMemberId ID của thành viên mua
     * @return Danh sách các giao dịch
     */
    List<TradeJpaEntity> findAllByBuyMemberId(Long buyMemberId);

    /**
     * Tìm tất cả các giao dịch theo sellMemberId
     * @param sellMemberId ID của thành viên bán
     * @return Danh sách các giao dịch
     */
    List<TradeJpaEntity> findAllBySellMemberId(Long sellMemberId);

    /**
     * Tìm tất cả các giao dịch theo buyOrderId
     * @param buyOrderId ID của lệnh mua
     * @return Danh sách các giao dịch
     */
    List<TradeJpaEntity> findAllByBuyOrderId(String buyOrderId);

    /**
     * Tìm tất cả các giao dịch theo sellOrderId
     * @param sellOrderId ID của lệnh bán
     * @return Danh sách các giao dịch
     */
    List<TradeJpaEntity> findAllBySellOrderId(String sellOrderId);

    /**
     * Tìm giao dịch theo symbol và sắp xếp theo thời gian giao dịch giảm dần
     * @param symbol Symbol của hợp đồng
     * @param pageable Thông tin phân trang
     * @return Trang chứa các giao dịch
     */
    Page<TradeJpaEntity> findBySymbolOrderByTradeTimeDesc(String symbol, Pageable pageable);

    /**
     * Tìm giao dịch mới nhất theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Giao dịch mới nhất
     */
    TradeJpaEntity findTopBySymbolOrderByTradeTimeDesc(String symbol);

    Page<TradeJpaEntity> findAllByBuyMemberIdOrSellMemberId(Long buyMemberId, Long sellMemberId, Pageable pageable);

}
