package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.AccountTransaction;
import com.icetea.lotus.core.domain.repository.AccountTransactionRepository;
import com.icetea.lotus.core.domain.valueobject.AccountOperationType;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.AccountTransactionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.AccountPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.AccountTransactionJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho AccountTransactionRepository
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class AccountTransactionRepositoryAdapter implements AccountTransactionRepository {

    private final AccountTransactionJpaRepository accountTransactionJpaRepository;
    private final AccountPersistenceMapper accountPersistenceMapper;

    /**
     * Lưu giao dịch tài khoản với xử lý ngoại lệ và thử lại
     * @param transaction Giao dịch tài khoản
     * @return AccountTransaction
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public AccountTransaction save(AccountTransaction transaction) {
        try {
            // Log chi tiết để debug numeric overflow
            log.info("ACCOUNT_TRANSACTION_SAVE_DEBUG: Lưu giao dịch tài khoản, memberId = {}, type = {}, " +
                    "amount = {}, balance = {}, remark = {}",
                    transaction.getMemberId(), transaction.getType(),
                    transaction.getAmount() != null ? transaction.getAmount().getValue() : null,
                    transaction.getBalance() != null ? transaction.getBalance().getValue() : null,
                    transaction.getRemark());

            AccountTransactionJpaEntity jpaEntity = accountPersistenceMapper.transactionToJpaEntity(transaction);

            // Log sau khi mapping để verify validation
            log.info("ACCOUNT_TRANSACTION_SAVE_DEBUG: Sau khi mapping, amount = {}, balance = {}",
                    jpaEntity.getAmount(), jpaEntity.getBalance());

            AccountTransactionJpaEntity savedEntity = accountTransactionJpaRepository.save(jpaEntity);

            log.debug("Đã lưu giao dịch tài khoản, id = {}", savedEntity.getId());

            return accountPersistenceMapper.jpaEntityToTransaction(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu giao dịch tài khoản, memberId = {}, type = {}", transaction.getMemberId(), transaction.getType(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu giao dịch tài khoản, memberId = {}, type = {}", transaction.getMemberId(), transaction.getType(), e);
            throw new DatabaseException("Lỗi khi lưu giao dịch tài khoản: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu giao dịch tài khoản thất bại
     * @param e Ngoại lệ
     * @param transaction Giao dịch tài khoản
     * @return AccountTransaction
     */
    @Recover
    public AccountTransaction recoverSave(Exception e, AccountTransaction transaction) {
        log.error("Đã thử lại lưu giao dịch tài khoản 3 lần nhưng thất bại, memberId = {}, type = {}", transaction.getMemberId(), transaction.getType(), e);
        throw new DatabaseException("Không thể lưu giao dịch tài khoản sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm giao dịch tài khoản theo ID
     * @param id ID của giao dịch tài khoản
     * @return Optional chứa giao dịch tài khoản nếu tìm thấy
     */
    @Override
    public Optional<AccountTransaction> findById(String id) {
        try {
            log.debug("Tìm giao dịch tài khoản theo ID, id = {}", id);

            Optional<AccountTransactionJpaEntity> jpaEntityOpt = accountTransactionJpaRepository.findById(id);
            return jpaEntityOpt.map(accountPersistenceMapper::jpaEntityToTransaction);
        } catch (Exception e) {
            log.error("Lỗi khi tìm giao dịch tài khoản theo ID, id = {}", id, e);
            throw new DatabaseException("Lỗi khi tìm giao dịch tài khoản theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả giao dịch tài khoản của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách giao dịch tài khoản
     */
    @Override
    public List<AccountTransaction> findAllByMemberId(String memberId) {
        try {
            log.debug("Tìm tất cả giao dịch tài khoản của một thành viên, memberId = {}", memberId);

            List<AccountTransactionJpaEntity> jpaEntities = accountTransactionJpaRepository.findAllByMemberId(memberId);
            return jpaEntities.stream()
                    .map(accountPersistenceMapper::jpaEntityToTransaction)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm tất cả giao dịch tài khoản của một thành viên, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả giao dịch tài khoản của một thành viên: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả giao dịch tài khoản của một thành viên theo loại
     * @param memberId ID của thành viên
     * @param type Loại giao dịch
     * @return Danh sách giao dịch tài khoản
     */
    @Override
    public List<AccountTransaction> findAllByMemberIdAndType(String memberId, AccountOperationType type) {
        try {
            log.debug("Tìm tất cả giao dịch tài khoản của một thành viên theo loại, memberId = {}, type = {}", memberId, type);

            List<AccountTransactionJpaEntity> jpaEntities = accountTransactionJpaRepository.findAllByMemberIdAndType(memberId, type);
            return jpaEntities.stream()
                    .map(accountPersistenceMapper::jpaEntityToTransaction)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm tất cả giao dịch tài khoản của một thành viên theo loại, memberId = {}, type = {}", memberId, type, e);
            throw new DatabaseException("Lỗi khi tìm tất cả giao dịch tài khoản của một thành viên theo loại: " + e.getMessage(), e);
        }
    }

    /**
     * Tìm tất cả giao dịch tài khoản của một thành viên trong khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách giao dịch tài khoản
     */
    @Override
    public List<AccountTransaction> findAllByMemberIdAndCreateTimeBetween(String memberId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm tất cả giao dịch tài khoản của một thành viên trong khoảng thời gian, memberId = {}, startTime = {}, endTime = {}", memberId, startTime, endTime);

            List<AccountTransactionJpaEntity> jpaEntities = accountTransactionJpaRepository.findAllByMemberIdAndCreateTimeBetween(memberId, startTime, endTime);
            return jpaEntities.stream()
                    .map(accountPersistenceMapper::jpaEntityToTransaction)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi khi tìm tất cả giao dịch tài khoản của một thành viên trong khoảng thời gian, memberId = {}, startTime = {}, endTime = {}", memberId, startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm tất cả giao dịch tài khoản của một thành viên trong khoảng thời gian: " + e.getMessage(), e);
        }
    }
}
