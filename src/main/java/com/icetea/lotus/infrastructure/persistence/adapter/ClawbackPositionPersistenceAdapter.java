package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.ClawbackPositionPersistencePort;
import com.icetea.lotus.core.domain.entity.ClawbackPosition;
import com.icetea.lotus.core.domain.repository.ClawbackPositionRepository;
import com.icetea.lotus.core.domain.valueobject.ClawbackPositionId;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.ClawbackPositionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.ClawbackPositionMapper;
import com.icetea.lotus.infrastructure.persistence.mapper.ClawbackPositionPersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.ClawbackPositionJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho ClawbackPositionPersistencePort và ClawbackPositionRepository
 * Triển khai các phương thức của cả hai interface để tránh xung đột bean
 */
@Slf4j
@Component("clawbackPositionPersistenceAdapter")
@RequiredArgsConstructor
@Transactional
public class ClawbackPositionPersistenceAdapter implements ClawbackPositionPersistencePort, ClawbackPositionRepository {

    private final ClawbackPositionJpaRepository clawbackPositionJpaRepository;
    private final ClawbackPositionPersistenceMapper clawbackPositionPersistenceMapper;
    private final ClawbackPositionMapper clawbackPositionMapper;

    /**
     * Tìm clawback position theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của clawback position
     * @return Optional chứa clawback position nếu tìm thấy
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<ClawbackPosition> findById(ClawbackPositionId id) {
        try {
            log.debug("Tìm clawback position theo ID, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            Optional<ClawbackPositionJpaEntity> entity = clawbackPositionJpaRepository.findById(id.getValue());

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy clawback position, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy clawback position, id = {}", id.getValue());
            }

            return entity.map(clawbackPositionPersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm clawback position theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm clawback position theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm clawback position theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm clawback position theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của clawback position
     * @return Optional<ClawbackPosition>
     */
    @Recover
    public Optional<ClawbackPosition> recoverFindById(Exception e, ClawbackPositionId id) {
        log.error("Đã thử lại tìm clawback position theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm clawback position theo positionId
     * @param positionId ID của vị thế
     * @return Danh sách các clawback position
     */
    @Override
    public List<ClawbackPosition> findByPositionId(PositionId positionId) {
        log.info("Tìm clawback position theo positionId, positionId = {}", positionId);

        try {
            List<ClawbackPositionJpaEntity> entities = clawbackPositionJpaRepository.findByPositionId(positionId.getValue());
            return entities.stream()
                    .map(clawbackPositionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm clawback position theo positionId thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm clawback position theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các clawback position
     */
    @Override
    public List<ClawbackPosition> findByMemberId(Long memberId) {
        log.info("Tìm clawback position theo memberId, memberId = {}", memberId);

        try {
            List<ClawbackPositionJpaEntity> entities = clawbackPositionJpaRepository.findByMemberId(memberId);
            return entities.stream()
                    .map(clawbackPositionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm clawback position theo memberId thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm clawback position theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @param limit Giới hạn số lượng
     * @return Danh sách các clawback position
     */
    @Override
    public List<ClawbackPosition> findByMemberIdAndSymbol(Long memberId, Symbol symbol, int limit) {
        log.info("Tìm clawback position theo memberId và symbol, memberId = {}, symbol = {}, limit = {}", memberId, symbol, limit);

        try {
            List<ClawbackPositionJpaEntity> entities = clawbackPositionJpaRepository.findByMemberIdAndSymbolOrderByCreateTimeDesc(memberId, symbol.getValue(), limit);
            return entities.stream()
                    .map(clawbackPositionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm clawback position theo memberId và symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm clawback position theo symbol
     * @param symbol Ký hiệu của hợp đồng
     * @param limit Giới hạn số lượng
     * @return Danh sách các clawback position
     */
    @Override
    public List<ClawbackPosition> findBySymbol(Symbol symbol, int limit) {
        log.info("Tìm clawback position theo symbol, symbol = {}, limit = {}", symbol, limit);

        try {
            List<ClawbackPositionJpaEntity> entities = clawbackPositionJpaRepository.findBySymbolOrderByCreateTimeDesc(symbol.getValue(), limit);
            return entities.stream()
                    .map(clawbackPositionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm clawback position theo symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Lưu clawback position với xử lý ngoại lệ và thử lại
     * @param clawbackPosition ClawbackPosition cần lưu
     * @return ClawbackPosition đã được lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public ClawbackPosition save(ClawbackPosition clawbackPosition) {
        try {
            log.debug("Lưu clawback position, id = {}, positionId = {}, memberId = {}, symbol = {}",
                    clawbackPosition.getId() != null ? clawbackPosition.getId().getValue() : "null",
                    clawbackPosition.getPositionId().getValue(),
                    clawbackPosition.getMemberId(),
                    clawbackPosition.getSymbol().getValue());

            if (clawbackPosition == null) {
                throw new IllegalArgumentException("ClawbackPosition không được để trống");
            }

            ClawbackPositionJpaEntity entity = clawbackPositionPersistenceMapper.domainToEntity(clawbackPosition);
            ClawbackPositionJpaEntity savedEntity = clawbackPositionJpaRepository.save(entity);

            log.debug("Đã lưu clawback position thành công, id = {}", savedEntity.getId());

            return clawbackPositionPersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu clawback position, id = {}",
                    clawbackPosition.getId() != null ? clawbackPosition.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu clawback position, id = {}",
                    clawbackPosition.getId() != null ? clawbackPosition.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu clawback position: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu clawback position thất bại
     * @param e Ngoại lệ
     * @param clawbackPosition ClawbackPosition cần lưu
     * @return ClawbackPosition
     */
    @Recover
    public ClawbackPosition recoverSave(Exception e, ClawbackPosition clawbackPosition) {
        log.error("Đã thử lại lưu clawback position 3 lần nhưng thất bại, id = {}",
                clawbackPosition.getId() != null ? clawbackPosition.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu clawback position sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Các phương thức từ ClawbackPositionRepository interface
     */

    @Override
    public List<ClawbackPosition> findAllBySymbol(Symbol symbol) {
        log.info("Tìm tất cả clawback position theo symbol, symbol = {}", symbol);
        return findBySymbol(symbol, 1000);
    }

    @Override
    public List<ClawbackPosition> findAllByMemberId(Long memberId) {
        log.info("Tìm tất cả clawback position theo memberId, memberId = {}", memberId);
        return findByMemberId(memberId);
    }

    @Override
    public List<ClawbackPosition> findAll() {
        log.info("Tìm tất cả clawback position");

        try {
            List<ClawbackPositionJpaEntity> entities = clawbackPositionJpaRepository.findAll();
            return entities.stream()
                    .map(clawbackPositionPersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm tất cả clawback position thất bại", e);
            throw e;
        }
    }

    @Override
    public void delete(ClawbackPosition clawbackPosition) {
        log.info("Xóa clawback position, id = {}",
                clawbackPosition.getId() != null ? clawbackPosition.getId().getValue() : "null");

        try {
            if (clawbackPosition == null || clawbackPosition.getId() == null) {
                throw new IllegalArgumentException("ClawbackPosition hoặc ID không được để trống");
            }

            clawbackPositionJpaRepository.deleteById(clawbackPosition.getId().getValue());
            log.debug("Đã xóa clawback position thành công, id = {}", clawbackPosition.getId().getValue());
        } catch (Exception e) {
            log.error("Xóa clawback position thất bại, id = {}",
                    clawbackPosition.getId() != null ? clawbackPosition.getId().getValue() : "null", e);
            throw e;
        }
    }
}
