package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.UserLeverageSetting;
import com.icetea.lotus.core.domain.repository.UserLeverageSettingRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.valueobject.UserLeverageSettingId;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.UserLeverageSettingJpaEntity;
import com.icetea.lotus.infrastructure.persistence.repository.UserLeverageSettingJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho UserLeverageSettingRepository
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class UserLeverageSettingRepositoryAdapter implements UserLeverageSettingRepository {

    private final UserLeverageSettingJpaRepository userLeverageSettingJpaRepository;

    /**
     * Lưu thiết lập đòn bẩy với xử lý ngoại lệ và thử lại
     * @param userLeverageSetting Thiết lập đòn bẩy
     * @return UserLeverageSetting
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public UserLeverageSetting save(UserLeverageSetting userLeverageSetting) {
        try {
            log.debug("Lưu thiết lập đòn bẩy, id = {}, memberId = {}, symbol = {}",
                    userLeverageSetting.getId() != null ? userLeverageSetting.getId().getValue() : "null",
                    userLeverageSetting.getMemberId(),
                    userLeverageSetting.getSymbol().getValue());

            if (userLeverageSetting == null) {
                throw new IllegalArgumentException("UserLeverageSetting không được để trống");
            }

            UserLeverageSettingJpaEntity jpaEntity = mapToJpaEntity(userLeverageSetting);
            UserLeverageSettingJpaEntity savedEntity = userLeverageSettingJpaRepository.save(jpaEntity);

            log.debug("Đã lưu thiết lập đòn bẩy thành công, id = {}", savedEntity.getId());

            return mapToDomainEntity(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu thiết lập đòn bẩy, memberId = {}, symbol = {}",
                    userLeverageSetting.getMemberId(),
                    userLeverageSetting.getSymbol().getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu thiết lập đòn bẩy, memberId = {}, symbol = {}",
                    userLeverageSetting.getMemberId(),
                    userLeverageSetting.getSymbol().getValue(), e);
            throw new DatabaseException("Lỗi khi lưu thiết lập đòn bẩy: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu thiết lập đòn bẩy thất bại
     * @param e Ngoại lệ
     * @param userLeverageSetting Thiết lập đòn bẩy
     * @return UserLeverageSetting
     */
    @Recover
    public UserLeverageSetting recoverSave(Exception e, UserLeverageSetting userLeverageSetting) {
        log.error("Đã thử lại lưu thiết lập đòn bẩy 3 lần nhưng thất bại, memberId = {}, symbol = {}",
                userLeverageSetting.getMemberId(),
                userLeverageSetting.getSymbol().getValue(), e);
        throw new DatabaseException("Không thể lưu thiết lập đòn bẩy sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm thiết lập đòn bẩy theo ID với xử lý ngoại lệ
     * @param id ID của thiết lập đòn bẩy
     * @return Optional<UserLeverageSetting>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<UserLeverageSetting> findById(UserLeverageSettingId id) {
        try {
            log.debug("Tìm thiết lập đòn bẩy theo ID, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            Optional<UserLeverageSetting> result = userLeverageSettingJpaRepository.findById(id.getValue())
                    .map(this::mapToDomainEntity);

            if (result.isPresent()) {
                log.debug("Đã tìm thấy thiết lập đòn bẩy, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy thiết lập đòn bẩy, id = {}", id.getValue());
            }

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm thiết lập đòn bẩy theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm thiết lập đòn bẩy theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm thiết lập đòn bẩy theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm thiết lập đòn bẩy theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của thiết lập đòn bẩy
     * @return Optional<UserLeverageSetting>
     */
    @Recover
    public Optional<UserLeverageSetting> recoverFindById(Exception e, UserLeverageSettingId id) {
        log.error("Đã thử lại tìm thiết lập đòn bẩy theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm thiết lập đòn bẩy theo memberId và symbol với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Optional<UserLeverageSetting>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<UserLeverageSetting> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        try {
            log.debug("Tìm thiết lập đòn bẩy theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol.getValue());

            if (memberId == null) {
                throw new IllegalArgumentException("MemberId không được để trống");
            }

            if (symbol == null) {
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            Optional<UserLeverageSetting> result = userLeverageSettingJpaRepository.findByMemberIdAndSymbol(memberId, symbol.getValue())
                    .map(this::mapToDomainEntity);

            if (result.isPresent()) {
                log.debug("Đã tìm thấy thiết lập đòn bẩy, memberId = {}, symbol = {}", memberId, symbol.getValue());
            } else {
                log.debug("Không tìm thấy thiết lập đòn bẩy, memberId = {}, symbol = {}", memberId, symbol.getValue());
            }

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm thiết lập đòn bẩy theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm thiết lập đòn bẩy theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm thiết lập đòn bẩy theo memberId và symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm thiết lập đòn bẩy theo memberId và symbol thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Optional<UserLeverageSetting>
     */
    @Recover
    public Optional<UserLeverageSetting> recoverFindByMemberIdAndSymbol(Exception e, Long memberId, Symbol symbol) {
        log.error("Đã thử lại tìm thiết lập đòn bẩy theo memberId và symbol 3 lần nhưng thất bại, memberId = {}, symbol = {}", memberId, symbol.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm tất cả thiết lập đòn bẩy của một thành viên với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @return List<UserLeverageSetting>
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<UserLeverageSetting> findAllByMemberId(Long memberId) {
        try {
            log.debug("Tìm tất cả thiết lập đòn bẩy của thành viên, memberId = {}", memberId);

            if (memberId == null) {
                throw new IllegalArgumentException("MemberId không được để trống");
            }

            List<UserLeverageSetting> result = userLeverageSettingJpaRepository.findAllByMemberId(memberId)
                    .stream()
                    .map(this::mapToDomainEntity)
                    .collect(Collectors.toList());

            log.debug("Đã tìm thấy {} thiết lập đòn bẩy của thành viên, memberId = {}", result.size(), memberId);

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả thiết lập đòn bẩy của thành viên, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả thiết lập đòn bẩy của thành viên, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả thiết lập đòn bẩy của thành viên: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả thiết lập đòn bẩy của một thành viên thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @return List<UserLeverageSetting>
     */
    @Recover
    public List<UserLeverageSetting> recoverFindAllByMemberId(Exception e, Long memberId) {
        log.error("Đã thử lại tìm tất cả thiết lập đòn bẩy của thành viên 3 lần nhưng thất bại, memberId = {}", memberId, e);
        return List.of();
    }

    /**
     * Xóa thiết lập đòn bẩy theo ID với xử lý ngoại lệ
     * @param id ID của thiết lập đòn bẩy
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void deleteById(UserLeverageSettingId id) {
        try {
            log.debug("Xóa thiết lập đòn bẩy theo ID, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            userLeverageSettingJpaRepository.deleteById(id.getValue());

            log.debug("Đã xóa thiết lập đòn bẩy thành công, id = {}", id.getValue());
        } catch (EmptyResultDataAccessException e) {
            log.warn("Không tìm thấy thiết lập đòn bẩy để xóa, id = {}", id.getValue());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi xóa thiết lập đòn bẩy theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi xóa thiết lập đòn bẩy theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi xóa thiết lập đòn bẩy theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi xóa thiết lập đòn bẩy theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của thiết lập đòn bẩy
     */
    @Recover
    public void recoverDeleteById(Exception e, UserLeverageSettingId id) {
        log.error("Đã thử lại xóa thiết lập đòn bẩy theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        throw new DatabaseException("Không thể xóa thiết lập đòn bẩy theo ID sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    private UserLeverageSetting mapToDomainEntity(UserLeverageSettingJpaEntity jpaEntity) {
        return UserLeverageSetting.builder()
                .id(UserLeverageSettingId.of(jpaEntity.getId()))
                .memberId(jpaEntity.getMemberId())
                .symbol(Symbol.of(jpaEntity.getSymbol()))
                .leverage(jpaEntity.getLeverage())
                .createTime(jpaEntity.getCreateTime())
                .updateTime(jpaEntity.getUpdateTime())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domainEntity Domain entity
     * @return JPA entity
     */
    private UserLeverageSettingJpaEntity mapToJpaEntity(UserLeverageSetting domainEntity) {
        return UserLeverageSettingJpaEntity.builder()
                .id(domainEntity.getId() != null ? domainEntity.getId().getValue() : null)
                .memberId(domainEntity.getMemberId())
                .symbol(domainEntity.getSymbol().getValue())
                .leverage(domainEntity.getLeverage())
                .createTime(domainEntity.getCreateTime())
                .updateTime(domainEntity.getUpdateTime())
                .build();
    }
}
