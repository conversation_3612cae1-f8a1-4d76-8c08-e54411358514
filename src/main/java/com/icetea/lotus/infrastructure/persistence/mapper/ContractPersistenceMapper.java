package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.MarginMode;
import com.icetea.lotus.core.domain.entity.MatchingAlgorithm;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.ContractJpaEntity;

import java.math.BigDecimal;
import org.springframework.stereotype.Component;

/**
 * Mapper cho Contract và ContractJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class ContractPersistenceMapper {

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Contract entityToDomain(ContractJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        // Lấy phí giao dịch từ trường fee trong bảng contract_coin
        BigDecimal fee = entity.getFee();

        // Tính phí cho taker và maker
        Money takerFeeRate = fee != null ? Money.of(fee) : Money.of(new BigDecimal("0.0005")); // 0.05% là phí taker mặc định
        Money makerFeeRate = fee != null ? Money.of(fee.multiply(new BigDecimal("0.8"))) : Money.of(new BigDecimal("0.0004")); // 0.04% là phí maker mặc định (80% của phí taker)

        return Contract.builder()
                .id(entity.getId())
                .symbol(Symbol.of(entity.getSymbol()))
                .name(entity.getName())
                .baseCoin(entity.getBaseSymbol())
                .quoteCoin(entity.getQuoteSymbol())
                .minTradeAmount(Money.of(entity.getMinVolume()))
                .maxTradeAmount(Money.of(entity.getMaxVolume()))
                .takerFeeRate(takerFeeRate)
                .makerFeeRate(makerFeeRate)
                .maintenanceMarginRate(Money.of(entity.getMaintenanceMarginRate()))
                .initialMarginRate(Money.of(entity.getInitialMarginRate()))
                .leverageMax(entity.getLeverageMax())
                .leverageMin(entity.getLeverageMin())
                .enable(entity.getEnabled() != null && entity.getEnabled() == 1)
                .visible(true)
                .sort(entity.getSort() != null ? entity.getSort() : 0)
                .minPrice(BigDecimal.ZERO)
                .maxPrice(BigDecimal.valueOf(1000000))
                .priceStep(BigDecimal.valueOf(0.01))
                .amountStep(BigDecimal.valueOf(0.01))
                .matchingAlgorithm(MatchingAlgorithm.FIFO)
                .fundingInterval(entity.getFundingInterval())
                .expiryDate(entity.getExpiryDate())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public ContractJpaEntity domainToEntity(Contract domain) {
        if (domain == null) {
            return null;
        }

        return ContractJpaEntity.builder()
                .id(domain.getId())
                .symbol(domain.getSymbol().getValue())
                .name(domain.getName())
                .baseSymbol(domain.getBaseCoin())
                .quoteSymbol(domain.getQuoteCoin())
                .multiplier(BigDecimal.ONE)
                .minVolume(domain.getMinTradeAmount().getValue())
                .maxVolume(domain.getMaxTradeAmount().getValue())
                .pricePrecision(BigDecimal.valueOf(2))
                .volumePrecision(BigDecimal.valueOf(2))
                .maintenanceMarginRate(domain.getMaintenanceMarginRate().getValue())
                .initialMarginRate(domain.getInitialMarginRate().getValue())
                .leverageMax(domain.getLeverageMax())
                .leverageMin(domain.getLeverageMin())
                .sort(domain.getSort())
                .fee(domain.getTakerFeeRate() != null ? domain.getTakerFeeRate().getValue() : new BigDecimal("0.0005"))
                .fundingRateCoefficient(new BigDecimal("0.01"))
                .maxFundingRate(new BigDecimal("0.0075"))
                .minFundingRate(new BigDecimal("-0.0075"))
                .fundingInterval(domain.getFundingInterval())
                .marginMode(MarginMode.ISOLATED)
                .enabled(domain.isEnable() ? 1 : 0)
                .createTime(null)
                .updateTime(null)
                .expiryDate(domain.getExpiryDate())
                .build();
    }
}
