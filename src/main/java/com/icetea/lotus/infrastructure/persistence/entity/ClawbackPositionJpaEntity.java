package com.icetea.lotus.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho ClawbackPosition
 */
@Entity
@Table(name = "contract_clawback_position")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClawbackPositionJpaEntity {
    
    /**
     * ID của clawback position
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * ID của hợp đồng
     */
    @Column(name = "contract_id")
    private Long contractId;
    
    /**
     * Ký hiệu của hợp đồng
     */
    @Column(name = "symbol")
    private String symbol;
    
    /**
     * ID của thành viên
     */
    @Column(name = "member_id")
    private Long memberId;
    
    /**
     * ID của vị thế
     */
    @Column(name = "position_id")
    private Long positionId;
    
    /**
     * Lợi nhuận chưa thực hiện
     */
    @Column(name = "unrealized_pnl")
    private BigDecimal unrealizedPnl;
    
    /**
     * Số tiền clawback
     */
    @Column(name = "clawback_amount")
    private BigDecimal clawbackAmount;
    
    /**
     * Tỷ lệ clawback
     */
    @Column(name = "clawback_rate")
    private BigDecimal clawbackRate;
    
    /**
     * Thời gian tạo
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
}
