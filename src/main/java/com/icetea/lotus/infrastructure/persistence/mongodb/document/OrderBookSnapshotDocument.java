package com.icetea.lotus.infrastructure.persistence.mongodb.document;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MongoDB Document cho Order Book Snapshot
 * Optimized cho high-frequency trading với proper indexing
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "order_book_snapshots")
@CompoundIndexes({
    @CompoundIndex(name = "symbol_version_idx", def = "{'symbol': 1, 'version': -1}"),
    @CompoundIndex(name = "symbol_timestamp_idx", def = "{'symbol': 1, 'timestamp': -1}"),
    @CompoundIndex(name = "timestamp_idx", def = "{'timestamp': -1}")
})
public class OrderBookSnapshotDocument {

    @Id
    private String id;

    @Field("symbol")
    @Indexed
    private String symbol;

    @Field("version")
    private Long version;

    @Field("timestamp")
    @Indexed
    private Instant timestamp;

    @Field("buy_orders")
    private Map<String, List<OrderDocument>> buyOrders;

    @Field("sell_orders")
    private Map<String, List<OrderDocument>> sellOrders;

    @Field("all_orders")
    private List<OrderDocument> allOrders;

    @Field("stop_orders")
    private List<OrderDocument> stopOrders;

    @Field("metadata")
    private SnapshotMetadata metadata;

    @Field("created_at")
    private Instant createdAt;

    @Field("expires_at")
    @Indexed(expireAfterSeconds = 0) // TTL index
    private Instant expiresAt;

    /**
     * Nested document cho Order data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderDocument {
        @Field("order_id")
        private String orderId;

        @Field("member_id")
        private Long memberId;

        @Field("direction")
        private String direction;

        @Field("type")
        private String type;

        @Field("status")
        private String status;

        @Field("price")
        private MoneyDocument price;

        @Field("volume")
        private String volume;

        @Field("deal_volume")
        private String dealVolume;

        @Field("turnover")
        private String turnover;

        @Field("fee")
        private String fee;

        @Field("leverage")
        private Integer leverage;

        @Field("stop_price")
        private MoneyDocument stopPrice;

        @Field("time_in_force")
        private String timeInForce;

        @Field("stp_mode")
        private String stpMode;

        @Field("created_at")
        private Instant createdAt;

        @Field("updated_at")
        private Instant updatedAt;

        @Field("expiry_time")
        private Instant expiryTime;

        @Field("client_order_id")
        private String clientOrderId;

        @Field("reduce_only")
        private Boolean reduceOnly;

        @Field("post_only")
        private Boolean postOnly;
    }

    /**
     * Nested document cho Money data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyDocument {
        @Field("value")
        private String value;

        @Field("currency")
        private String currency;
    }

    /**
     * Metadata cho snapshot
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SnapshotMetadata {
        @Field("total_orders")
        private Integer totalOrders;

        @Field("buy_orders_count")
        private Integer buyOrdersCount;

        @Field("sell_orders_count")
        private Integer sellOrdersCount;

        @Field("stop_orders_count")
        private Integer stopOrdersCount;

        @Field("price_levels_buy")
        private Integer priceLevelsBuy;

        @Field("price_levels_sell")
        private Integer priceLevelsSell;

        @Field("snapshot_size_bytes")
        private Long snapshotSizeBytes;

        @Field("compression_ratio")
        private Double compressionRatio;

        @Field("creation_time_micros")
        private Long creationTimeMicros;

        @Field("source_node")
        private String sourceNode;

        @Field("checksum")
        private String checksum;
    }

    /**
     * Generate unique ID cho snapshot
     */
    public static String generateId(String symbol, Long version) {
        return String.format("%s_%d_%d", symbol, version, System.currentTimeMillis());
    }

    /**
     * Calculate TTL cho snapshot (default 1 hour)
     */
    public static Instant calculateExpiryTime(int ttlHours) {
        return Instant.now().plusSeconds(ttlHours * 3600L);
    }



    /**
     * Calculate compression ratio
     */
    private static Double calculateCompressionRatio(int totalOrders, long sizeBytes) {
        if (totalOrders == 0) return 0.0;
        // Estimate uncompressed size (rough calculation)
        long estimatedUncompressedSize = totalOrders * 500L; // ~500 bytes per order
        return sizeBytes > 0 ? (double) estimatedUncompressedSize / sizeBytes : 1.0;
    }

    /**
     * Calculate simple checksum for data integrity
     */
    private static String calculateChecksum(int totalOrders, int buyCount, int sellCount) {
        int hash = totalOrders * 31 + buyCount * 17 + sellCount * 13;
        return String.format("%08x", hash);
    }

    /**
     * Escape price key for MongoDB (replace dots with underscores)
     * MongoDB doesn't allow dots in map keys
     */
    public static String escapePriceKey(String priceKey) {
        if (priceKey == null) {
            return "null";
        }
        return priceKey.replace(".", "_DOT_");
    }

    /**
     * Unescape price key from MongoDB (replace underscores back to dots)
     */
    public static String unescapePriceKey(String escapedKey) {
        if (escapedKey == null || "null".equals(escapedKey)) {
            return null;
        }
        return escapedKey.replace("_DOT_", ".");
    }

    /**
     * Escape map keys for MongoDB storage
     */
    public static Map<String, List<OrderDocument>> escapeMapKeys(Map<String, List<OrderDocument>> originalMap) {
        if (originalMap == null || originalMap.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, List<OrderDocument>> escapedMap = new HashMap<>();
        for (Map.Entry<String, List<OrderDocument>> entry : originalMap.entrySet()) {
            String escapedKey = escapePriceKey(entry.getKey());
            escapedMap.put(escapedKey, entry.getValue());
        }
        return escapedMap;
    }

    /**
     * Unescape map keys from MongoDB storage
     */
    public static Map<String, List<OrderDocument>> unescapeMapKeys(Map<String, List<OrderDocument>> escapedMap) {
        if (escapedMap == null || escapedMap.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, List<OrderDocument>> originalMap = new HashMap<>();
        for (Map.Entry<String, List<OrderDocument>> entry : escapedMap.entrySet()) {
            String originalKey = unescapePriceKey(entry.getKey());
            originalMap.put(originalKey, entry.getValue());
        }
        return originalMap;
    }
}
