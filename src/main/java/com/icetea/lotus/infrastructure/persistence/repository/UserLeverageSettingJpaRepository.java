package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.UserLeverageSettingJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JPA repository cho UserLeverageSettingJpaEntity
 */
@Repository
public interface UserLeverageSettingJpaRepository extends JpaRepository<UserLeverageSettingJpaEntity, Long> {
    
    /**
     * Tìm thiết lập đòn bẩy theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Optional<UserLeverageSettingJpaEntity>
     */
    Optional<UserLeverageSettingJpaEntity> findByMemberIdAndSymbol(Long memberId, String symbol);
    
    /**
     * Tìm tất cả thiết lập đòn bẩy của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách thiết lập đòn bẩy
     */
    List<UserLeverageSettingJpaEntity> findAllByMemberId(Long memberId);
}
