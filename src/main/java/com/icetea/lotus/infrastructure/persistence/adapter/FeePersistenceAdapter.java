package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.application.port.output.FeePersistencePort;
import com.icetea.lotus.core.domain.entity.Fee;
import com.icetea.lotus.core.domain.valueobject.FeeId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.FeeJpaEntity;
import com.icetea.lotus.infrastructure.persistence.mapper.FeePersistenceMapper;
import com.icetea.lotus.infrastructure.persistence.repository.FeeJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Adapter cho việc lưu trữ Fee
 * Triển khai FeePersistencePort
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class FeePersistenceAdapter implements FeePersistencePort {

    private final FeeJpaRepository feeJpaRepository;
    private final FeePersistenceMapper feePersistenceMapper;

    /**
     * Tìm phí giao dịch theo ID với xử lý ngoại lệ và thử lại
     * @param id ID của phí giao dịch
     * @return Optional chứa phí giao dịch nếu tìm thấy
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Optional<Fee> findById(FeeId id) {
        try {
            log.debug("Tìm phí giao dịch theo ID, id = {}", id.getValue());

            if (id == null) {
                throw new IllegalArgumentException("ID không được để trống");
            }

            Optional<FeeJpaEntity> entity = feeJpaRepository.findById(id.getValue());

            if (entity.isPresent()) {
                log.debug("Đã tìm thấy phí giao dịch, id = {}", id.getValue());
            } else {
                log.debug("Không tìm thấy phí giao dịch, id = {}", id.getValue());
            }

            return entity.map(feePersistenceMapper::entityToDomain);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm phí giao dịch theo ID, id = {}", id.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm phí giao dịch theo ID, id = {}", id.getValue(), e);
            throw new DatabaseException("Lỗi khi tìm phí giao dịch theo ID: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm phí giao dịch theo ID thất bại
     * @param e Ngoại lệ
     * @param id ID của phí giao dịch
     * @return Optional<Fee>
     */
    @Recover
    public Optional<Fee> recoverFindById(Exception e, FeeId id) {
        log.error("Đã thử lại tìm phí giao dịch theo ID 3 lần nhưng thất bại, id = {}", id.getValue(), e);
        return Optional.empty();
    }

    /**
     * Tìm phí giao dịch theo orderId
     * @param orderId ID của lệnh
     * @return Optional chứa phí giao dịch nếu tìm thấy
     */
    @Override
    public Optional<Fee> findByOrderId(OrderId orderId) {
        log.info("Tìm phí giao dịch theo orderId, orderId = {}", orderId);

        try {
            Optional<FeeJpaEntity> entity = feeJpaRepository.findByOrderId(orderId.getValue());
            return entity.map(feePersistenceMapper::entityToDomain);
        } catch (Exception e) {
            log.error("Tìm phí giao dịch theo orderId thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm phí giao dịch theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các phí giao dịch
     */
    @Override
    public List<Fee> findByMemberId(Long memberId) {
        log.info("Tìm phí giao dịch theo memberId, memberId = {}", memberId);

        try {
            List<FeeJpaEntity> entities = feeJpaRepository.findByMemberId(memberId);
            return entities.stream()
                    .map(feePersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm phí giao dịch theo memberId thất bại", e);
            throw e;
        }
    }

    /**
     * Tìm phí giao dịch theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Danh sách các phí giao dịch
     */
    @Override
    public List<Fee> findByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        log.info("Tìm phí giao dịch theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

        try {
            List<FeeJpaEntity> entities = feeJpaRepository.findByMemberIdAndSymbol(memberId, symbol.getValue());
            return entities.stream()
                    .map(feePersistenceMapper::entityToDomain)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Tìm phí giao dịch theo memberId và symbol thất bại", e);
            throw e;
        }
    }

    /**
     * Lưu phí giao dịch với xử lý ngoại lệ và thử lại
     * @param fee Phí giao dịch cần lưu
     * @return Phí giao dịch đã được lưu
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Fee save(Fee fee) {
        try {
            log.debug("Lưu phí giao dịch, id = {}, orderId = {}, memberId = {}, fee = {}",
                    fee.getId() != null ? fee.getId().getValue() : "null",
                    fee.getOrderId() != null ? fee.getOrderId().getValue() : "null",
                    fee.getMemberId(),
                    fee.getFee() != null ? fee.getFee().getValue() : "null");

            if (fee == null) {
                throw new IllegalArgumentException("Fee không được để trống");
            }

            FeeJpaEntity entity = feePersistenceMapper.domainToEntity(fee);
            FeeJpaEntity savedEntity = feeJpaRepository.save(entity);

            log.debug("Đã lưu phí giao dịch thành công, id = {}", savedEntity.getId());

            return feePersistenceMapper.entityToDomain(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu phí giao dịch, id = {}",
                    fee.getId() != null ? fee.getId().getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu phí giao dịch, id = {}",
                    fee.getId() != null ? fee.getId().getValue() : "null", e);
            throw new DatabaseException("Lỗi khi lưu phí giao dịch: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu phí giao dịch thất bại
     * @param e Ngoại lệ
     * @param fee Phí giao dịch cần lưu
     * @return Fee
     */
    @Recover
    public Fee recoverSave(Exception e, Fee fee) {
        log.error("Đã thử lại lưu phí giao dịch 3 lần nhưng thất bại, id = {}",
                fee.getId() != null ? fee.getId().getValue() : "null", e);
        throw new DatabaseException("Không thể lưu phí giao dịch sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tính tổng phí giao dịch theo memberId với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @return Tổng phí giao dịch
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money sumFeeByMemberId(Long memberId) {
        try {
            log.debug("Tính tổng phí giao dịch theo memberId, memberId = {}", memberId);

            if (memberId == null) {
                throw new IllegalArgumentException("MemberId không được để trống");
            }

            BigDecimal sum = feeJpaRepository.sumFeeByMemberId(memberId);
            Money result = sum != null ? Money.of(sum) : Money.ZERO;

            log.debug("Đã tính tổng phí giao dịch theo memberId, memberId = {}, sum = {}",
                    memberId, result.getValue());

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính tổng phí giao dịch theo memberId, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính tổng phí giao dịch theo memberId, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tính tổng phí giao dịch theo memberId: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tính tổng phí giao dịch theo memberId thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @return Money
     */
    @Recover
    public Money recoverSumFeeByMemberId(Exception e, Long memberId) {
        log.error("Đã thử lại tính tổng phí giao dịch theo memberId 3 lần nhưng thất bại, memberId = {}", memberId, e);
        return Money.ZERO;
    }

    /**
     * Tính tổng phí giao dịch theo memberId và symbol với xử lý ngoại lệ và thử lại
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Tổng phí giao dịch
     */
    @Override
    @Retryable(
            value = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money sumFeeByMemberIdAndSymbol(Long memberId, Symbol symbol) {
        try {
            log.debug("Tính tổng phí giao dịch theo memberId và symbol, memberId = {}, symbol = {}",
                    memberId, symbol.getValue());

            if (memberId == null) {
                throw new IllegalArgumentException("MemberId không được để trống");
            }

            if (symbol == null) {
                throw new IllegalArgumentException("Symbol không được để trống");
            }

            BigDecimal sum = feeJpaRepository.sumFeeByMemberIdAndSymbol(memberId, symbol.getValue());
            Money result = sum != null ? Money.of(sum) : Money.ZERO;

            log.debug("Đã tính tổng phí giao dịch theo memberId và symbol, memberId = {}, symbol = {}, sum = {}",
                    memberId, symbol.getValue(), result.getValue());

            return result;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính tổng phí giao dịch theo memberId và symbol, memberId = {}, symbol = {}",
                    memberId, symbol.getValue(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính tổng phí giao dịch theo memberId và symbol, memberId = {}, symbol = {}",
                    memberId, symbol.getValue(), e);
            throw new DatabaseException("Lỗi khi tính tổng phí giao dịch theo memberId và symbol: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tính tổng phí giao dịch theo memberId và symbol thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @return Money
     */
    @Recover
    public Money recoverSumFeeByMemberIdAndSymbol(Exception e, Long memberId, Symbol symbol) {
        log.error("Đã thử lại tính tổng phí giao dịch theo memberId và symbol 3 lần nhưng thất bại, memberId = {}, symbol = {}",
                memberId, symbol.getValue(), e);
        return Money.ZERO;
    }
}
