package com.icetea.lotus.infrastructure.persistence.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA entity cho FundingPayment
 */
@Entity
@Table(name = "contract_funding_payment")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FundingPaymentJpaEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "position_id")
    private Long positionId;
    
    @Column(name = "member_id")
    private Long memberId;
    
    private String symbol;
    
    private BigDecimal amount;
    
    private BigDecimal rate;
    
    private LocalDateTime time;
}
