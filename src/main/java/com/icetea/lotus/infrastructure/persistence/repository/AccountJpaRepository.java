package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.AccountJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * JPA repository cho AccountJpaEntity
 */
@Repository
public interface AccountJpaRepository extends JpaRepository<AccountJpaEntity, String> {
    
    /**
     * Tìm tài khoản theo ID của thành viên
     * @param memberId ID của thành viên
     * @return Optional chứa tài khoản nếu tìm thấy
     */
    Optional<AccountJpaEntity> findByMemberId(String memberId);
}
