package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.ContractSettlement;
import com.icetea.lotus.core.domain.valueobject.ContractId;
import com.icetea.lotus.core.domain.valueobject.ContractSettlementId;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.ContractSettlementJpaEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper để chuyển đổi giữa domain entity và JPA entity cho ContractSettlement
 */
@Component
public class ContractSettlementPersistenceMapper {
    
    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param contractSettlement Domain entity
     * @return JPA entity
     */
    public ContractSettlementJpaEntity domainToEntity(ContractSettlement contractSettlement) {
        if (contractSettlement == null) {
            return null;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        return ContractSettlementJpaEntity.builder()
                .id(contractSettlement.getId().getValue())
                .contractId(contractSettlement.getContractId().getValue())
                .symbol(contractSettlement.getSymbol().getValue())
                .memberId(contractSettlement.getMemberId())
                .positionId(contractSettlement.getPositionId().getValue())
                .settlementPrice(contractSettlement.getSettlementPrice().getValue())
                .pnl(contractSettlement.getPnl().getValue())
                .timestamp(contractSettlement.getTimestamp())
                .createdAt(now)
                .updatedAt(now)
                .build();
    }
    
    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public ContractSettlement entityToDomain(ContractSettlementJpaEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return ContractSettlement.builder()
                .id(ContractSettlementId.of(entity.getId()))
                .contractId(ContractId.of(entity.getContractId()))
                .symbol(Symbol.of(entity.getSymbol()))
                .memberId(entity.getMemberId())
                .positionId(PositionId.of(entity.getPositionId()))
                .settlementPrice(Money.of(entity.getSettlementPrice()))
                .pnl(Money.of(entity.getPnl()))
                .timestamp(entity.getTimestamp())
                .build();
    }
    
    /**
     * Chuyển đổi danh sách từ JPA entity sang domain entity
     * @param entities Danh sách JPA entity
     * @return Danh sách domain entity
     */
    public List<ContractSettlement> entitiesToDomains(List<ContractSettlementJpaEntity> entities) {
        if (entities == null) {
            return List.of();
        }
        
        return entities.stream()
                .map(this::entityToDomain)
                .collect(Collectors.toList());
    }
    
    /**
     * Chuyển đổi danh sách từ domain entity sang JPA entity
     * @param contractSettlements Danh sách domain entity
     * @return Danh sách JPA entity
     */
    public List<ContractSettlementJpaEntity> domainsToEntities(List<ContractSettlement> contractSettlements) {
        if (contractSettlements == null) {
            return List.of();
        }
        
        return contractSettlements.stream()
                .map(this::domainToEntity)
                .collect(Collectors.toList());
    }
}
