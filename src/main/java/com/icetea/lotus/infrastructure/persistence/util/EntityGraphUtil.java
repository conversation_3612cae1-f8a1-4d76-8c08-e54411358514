package com.icetea.lotus.infrastructure.persistence.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;

import jakarta.persistence.EntityGraph;
import jakarta.persistence.EntityManager;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Tiện ích để hỗ trợ lazy loading
 * @param <T> Kiểu dữ liệu của entity
 * @param <ID> Kiểu dữ liệu của ID
 */
@Slf4j
public class EntityGraphUtil<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> {

    private final EntityManager entityManager;
    private final Class<T> domainClass;

    /**
     * Khởi tạo EntityGraphUtil
     * @param entityInformation JpaEntityInformation
     * @param entityManager EntityManager
     */
    public EntityGraphUtil(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.entityManager = entityManager;
        this.domainClass = entityInformation.getJavaType();
    }

    /**
     * Tìm entity theo ID với entity graph
     * @param id ID
     * @param entityGraphName Tên entity graph
     * @return Optional<T>
     */
    public Optional<T> findById(ID id, String entityGraphName) {
        EntityGraph<?> entityGraph = entityManager.getEntityGraph(entityGraphName);
        Map<String, Object> properties = new HashMap<>();
        properties.put("jakarta.persistence.fetchgraph", entityGraph);

        return Optional.ofNullable(entityManager.find(domainClass, id, properties));
    }

    /**
     * Tìm tất cả entity với entity graph
     * @param entityGraphName Tên entity graph
     * @return List<T>
     */
    public List<T> findAll(String entityGraphName) {
        EntityGraph<?> entityGraph = entityManager.getEntityGraph(entityGraphName);

        return entityManager.createQuery("SELECT e FROM " + domainClass.getSimpleName() + " e", domainClass)
                .setHint("jakarta.persistence.fetchgraph", entityGraph)
                .getResultList();
    }

    /**
     * Tìm entity theo specification với entity graph
     * @param spec Specification
     * @param entityGraphName Tên entity graph
     * @return List<T>
     */
    public List<T> findAll(Specification<T> spec, String entityGraphName) {
        EntityGraph<?> entityGraph = entityManager.getEntityGraph(entityGraphName);

        return getQuery(spec, Sort.unsorted())
                .setHint("jakarta.persistence.fetchgraph", entityGraph)
                .getResultList();
    }

    /**
     * Tạo entity graph
     * @param attributeNames Tên các thuộc tính
     * @return EntityGraph<T>
     */
    public EntityGraph<T> createEntityGraph(String... attributeNames) {
        EntityGraph<T> entityGraph = entityManager.createEntityGraph(domainClass);

        for (String attributeName : attributeNames) {
            entityGraph.addAttributeNodes(attributeName);
        }

        return entityGraph;
    }
}
