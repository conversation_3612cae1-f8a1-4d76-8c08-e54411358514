package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.infrastructure.persistence.entity.ClawbackPositionJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * JPA Repository cho ClawbackPositionJpaEntity
 */
@Repository
public interface ClawbackPositionJpaRepository extends JpaRepository<ClawbackPositionJpaEntity, Long>, JpaSpecificationExecutor<ClawbackPositionJpaEntity> {
    
    /**
     * Tìm clawback position theo positionId
     * @param positionId ID của vị thế
     * @return Danh sách các clawback position
     */
    List<ClawbackPositionJpaEntity> findByPositionId(Long positionId);
    
    /**
     * Tìm clawback position theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các clawback position
     */
    List<ClawbackPositionJpaEntity> findByMemberId(Long memberId);
    
    /**
     * Tìm clawback position theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Ký hiệu của hợp đồng
     * @param limit Giới hạn số lượng
     * @return Danh sách các clawback position
     */
    @Query(value = "SELECT * FROM contract_clawback_position WHERE member_id = :memberId AND symbol = :symbol ORDER BY create_time DESC LIMIT :limit", nativeQuery = true)
    List<ClawbackPositionJpaEntity> findByMemberIdAndSymbolOrderByCreateTimeDesc(@Param("memberId") Long memberId, @Param("symbol") String symbol, @Param("limit") int limit);
    
    /**
     * Tìm clawback position theo symbol
     * @param symbol Ký hiệu của hợp đồng
     * @param limit Giới hạn số lượng
     * @return Danh sách các clawback position
     */
    @Query(value = "SELECT * FROM contract_clawback_position WHERE symbol = :symbol ORDER BY create_time DESC LIMIT :limit", nativeQuery = true)
    List<ClawbackPositionJpaEntity> findBySymbolOrderByCreateTimeDesc(@Param("symbol") String symbol, @Param("limit") int limit);
}
