package com.icetea.lotus.infrastructure.validation;

import com.icetea.lotus.core.domain.exception.ValidationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Tiện ích để kiểm tra đầu vào cho các tác vụ mạng
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NetworkInputValidator {
    
    private static final Pattern URL_PATTERN = Pattern.compile("^(https?|ftp)://[^\\s/$.?#].[^\\s]*$");
    private static final Pattern IP_PATTERN = Pattern.compile("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$");
    private static final Pattern HOSTNAME_PATTERN = Pattern.compile("^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\\-]*[a-zA-Z0-9])\\.)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9\\-]*[A-Za-z0-9])$");
    private static final Pattern PORT_PATTERN = Pattern.compile("^([0-9]{1,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$");
    
    /**
     * Kiểm tra URL
     * @param url URL
     * @throws ValidationException Nếu URL không hợp lệ
     */
    public void validateUrl(String url) {
        if (url == null || url.isEmpty()) {
            throw new ValidationException("URL không được null hoặc rỗng");
        }
        
        if (!URL_PATTERN.matcher(url).matches()) {
            throw new ValidationException("URL không hợp lệ: " + url);
        }
        
        try {
            new URL(url).toURI();
        } catch (MalformedURLException | URISyntaxException e) {
            throw new ValidationException("URL không hợp lệ: " + url);
        }
    }
    
    /**
     * Kiểm tra URI
     * @param uri URI
     * @throws ValidationException Nếu URI không hợp lệ
     */
    public void validateUri(String uri) {
        if (uri == null || uri.isEmpty()) {
            throw new ValidationException("URI không được null hoặc rỗng");
        }
        
        try {
            new URI(uri);
        } catch (URISyntaxException e) {
            throw new ValidationException("URI không hợp lệ: " + uri);
        }
    }
    
    /**
     * Kiểm tra IP
     * @param ip IP
     * @throws ValidationException Nếu IP không hợp lệ
     */
    public void validateIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            throw new ValidationException("IP không được null hoặc rỗng");
        }
        
        if (!IP_PATTERN.matcher(ip).matches()) {
            throw new ValidationException("IP không hợp lệ: " + ip);
        }
        
        String[] parts = ip.split("\\.");
        for (String part : parts) {
            int value = Integer.parseInt(part);
            if (value < 0 || value > 255) {
                throw new ValidationException("IP không hợp lệ: " + ip);
            }
        }
    }
    
    /**
     * Kiểm tra hostname
     * @param hostname Hostname
     * @throws ValidationException Nếu hostname không hợp lệ
     */
    public void validateHostname(String hostname) {
        if (hostname == null || hostname.isEmpty()) {
            throw new ValidationException("Hostname không được null hoặc rỗng");
        }
        
        if (!HOSTNAME_PATTERN.matcher(hostname).matches()) {
            throw new ValidationException("Hostname không hợp lệ: " + hostname);
        }
    }
    
    /**
     * Kiểm tra port
     * @param port Port
     * @throws ValidationException Nếu port không hợp lệ
     */
    public void validatePort(int port) {
        if (port < 0 || port > 65535) {
            throw new ValidationException("Port không hợp lệ: " + port);
        }
    }
    
    /**
     * Kiểm tra port
     * @param port Port
     * @throws ValidationException Nếu port không hợp lệ
     */
    public void validatePort(String port) {
        if (port == null || port.isEmpty()) {
            throw new ValidationException("Port không được null hoặc rỗng");
        }
        
        if (!PORT_PATTERN.matcher(port).matches()) {
            throw new ValidationException("Port không hợp lệ: " + port);
        }
        
        int portValue = Integer.parseInt(port);
        validatePort(portValue);
    }
    
    /**
     * Kiểm tra endpoint
     * @param hostname Hostname
     * @param port Port
     * @throws ValidationException Nếu endpoint không hợp lệ
     */
    public void validateEndpoint(String hostname, int port) {
        validateHostname(hostname);
        validatePort(port);
    }
    
    /**
     * Kiểm tra endpoint
     * @param endpoint Endpoint (hostname:port)
     * @throws ValidationException Nếu endpoint không hợp lệ
     */
    public void validateEndpoint(String endpoint) {
        if (endpoint == null || endpoint.isEmpty()) {
            throw new ValidationException("Endpoint không được null hoặc rỗng");
        }
        
        String[] parts = endpoint.split(":");
        if (parts.length != 2) {
            throw new ValidationException("Endpoint không hợp lệ: " + endpoint);
        }
        
        validateHostname(parts[0]);
        validatePort(parts[1]);
    }
    
    /**
     * Kiểm tra các tham số mạng
     * @param params Tham số
     * @throws ValidationException Nếu có lỗi xác thực
     */
    public void validateNetworkParams(Map<String, Object> params) {
        Map<String, String> errors = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value == null) {
                errors.put(key, "Tham số không được null");
                continue;
            }
            
            if (key.contains("url") && value instanceof String) {
                try {
                    validateUrl((String) value);
                } catch (ValidationException e) {
                    errors.put(key, e.getMessage());
                }
            } else if (key.contains("uri") && value instanceof String) {
                try {
                    validateUri((String) value);
                } catch (ValidationException e) {
                    errors.put(key, e.getMessage());
                }
            } else if (key.contains("ip") && value instanceof String) {
                try {
                    validateIp((String) value);
                } catch (ValidationException e) {
                    errors.put(key, e.getMessage());
                }
            } else if (key.contains("host") && value instanceof String) {
                try {
                    validateHostname((String) value);
                } catch (ValidationException e) {
                    errors.put(key, e.getMessage());
                }
            } else if (key.contains("port") && value instanceof Integer) {
                try {
                    validatePort((Integer) value);
                } catch (ValidationException e) {
                    errors.put(key, e.getMessage());
                }
            } else if (key.contains("port") && value instanceof String) {
                try {
                    validatePort((String) value);
                } catch (ValidationException e) {
                    errors.put(key, e.getMessage());
                }
            } else if (key.contains("endpoint") && value instanceof String) {
                try {
                    validateEndpoint((String) value);
                } catch (ValidationException e) {
                    errors.put(key, e.getMessage());
                }
            }
        }
        
        if (!errors.isEmpty()) {
            throw new ValidationException("Lỗi xác thực tham số mạng", errors);
        }
    }
}
