package com.icetea.lotus.infrastructure.validation;

import com.icetea.lotus.core.domain.exception.ValidationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.validation.Validator;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Lớp xác thực dữ liệu đầu vào sử dụng Spring Validator
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SpringInputValidator {
    
    private final Validator validator;
    
    /**
     * <PERSON><PERSON><PERSON> thực đối tượng
     * @param object Đối tượng cần xác thực
     * @param objectName Tên đối tượng
     * @throws ValidationException Nếu có lỗi xác thực
     */
    public void validate(Object object, String objectName) {
        Errors errors = new BeanPropertyBindingResult(object, objectName);
        validator.validate(object, errors);
        
        if (errors.hasErrors()) {
            Map<String, String> errorMap = errors.getFieldErrors().stream()
                    .collect(Collectors.toMap(FieldError::getField, FieldError::getDefaultMessage));
            
            String errorMessage = errorMap.entrySet().stream()
                    .map(entry -> entry.getKey() + ": " + entry.getValue())
                    .collect(Collectors.joining(", "));
            
            log.error("Lỗi xác thực dữ liệu đầu vào: {}", errorMessage);
            throw new ValidationException(errorMessage);
        }
    }
    
    /**
     * Xác thực đối tượng và trả về danh sách lỗi
     * @param object Đối tượng cần xác thực
     * @param objectName Tên đối tượng
     * @return Danh sách lỗi
     */
    public Map<String, String> validateAndGetErrors(Object object, String objectName) {
        Errors errors = new BeanPropertyBindingResult(object, objectName);
        validator.validate(object, errors);
        
        if (errors.hasErrors()) {
            Map<String, String> errorMap = errors.getFieldErrors().stream()
                    .collect(Collectors.toMap(FieldError::getField, FieldError::getDefaultMessage));
            
            log.error("Lỗi xác thực dữ liệu đầu vào: {}", errorMap);
            return errorMap;
        }
        
        return new HashMap<>();
    }
    
    /**
     * Kiểm tra xem đối tượng có hợp lệ không
     * @param object Đối tượng cần kiểm tra
     * @param objectName Tên đối tượng
     * @return true nếu hợp lệ, false nếu không hợp lệ
     */
    public boolean isValid(Object object, String objectName) {
        Errors errors = new BeanPropertyBindingResult(object, objectName);
        validator.validate(object, errors);
        return !errors.hasErrors();
    }
}
