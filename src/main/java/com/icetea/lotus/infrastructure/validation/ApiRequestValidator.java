package com.icetea.lotus.infrastructure.validation;

import com.icetea.lotus.core.domain.exception.ValidationException;
import com.icetea.lotus.infrastructure.util.InputValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.validation.Validator;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.Field;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Tiện ích để kiểm tra đầu vào cho các API
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiRequestValidator {
    
    private final Validator validator;
    private final InputValidator inputValidator;
    
    /**
     * <PERSON><PERSON><PERSON> thực đối tượng
     * @param object Đối tượng cần xác thực
     * @param objectName Tên đối tượng
     * @throws ValidationException Nếu có lỗi xác thực
     */
    public void validate(Object object, String objectName) {
        Errors errors = new BeanPropertyBindingResult(object, objectName);
        validator.validate(object, errors);
        
        if (errors.hasErrors()) {
            Map<String, String> errorMap = errors.getFieldErrors().stream()
                    .collect(Collectors.toMap(FieldError::getField, FieldError::getDefaultMessage));
            
            String errorMessage = errorMap.entrySet().stream()
                    .map(entry -> entry.getKey() + ": " + entry.getValue())
                    .collect(Collectors.joining(", "));
            
            log.error("Lỗi xác thực dữ liệu đầu vào: {}", errorMessage);
            throw new ValidationException(errorMessage, errorMap);
        }
    }
    
    /**
     * Xác thực đối tượng và trả về danh sách lỗi
     * @param object Đối tượng cần xác thực
     * @param objectName Tên đối tượng
     * @return Danh sách lỗi
     */
    public Map<String, String> validateAndGetErrors(Object object, String objectName) {
        Errors errors = new BeanPropertyBindingResult(object, objectName);
        validator.validate(object, errors);
        
        if (errors.hasErrors()) {
            Map<String, String> errorMap = errors.getFieldErrors().stream()
                    .collect(Collectors.toMap(FieldError::getField, FieldError::getDefaultMessage));
            
            log.error("Lỗi xác thực dữ liệu đầu vào: {}", errorMap);
            return errorMap;
        }
        
        return new HashMap<>();
    }
    
    /**
     * Kiểm tra xem đối tượng có hợp lệ không
     * @param object Đối tượng cần kiểm tra
     * @param objectName Tên đối tượng
     * @return true nếu hợp lệ, false nếu không hợp lệ
     */
    public boolean isValid(Object object, String objectName) {
        Errors errors = new BeanPropertyBindingResult(object, objectName);
        validator.validate(object, errors);
        return !errors.hasErrors();
    }
    
    /**
     * Xác thực tham số API
     * @param parameters Tham số
     * @param args Giá trị tham số
     * @throws ValidationException Nếu có lỗi xác thực
     */
    public void validateApiParameters(Parameter[] parameters, Object[] args) {
        Map<String, String> errors = new HashMap<>();
        
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            Object arg = args[i];
            
            // Kiểm tra tham số RequestBody
            if (parameter.isAnnotationPresent(RequestBody.class)) {
                if (arg == null) {
                    errors.put(parameter.getName(), "Request body không được null");
                } else {
                    Errors bindingErrors = new BeanPropertyBindingResult(arg, parameter.getName());
                    validator.validate(arg, bindingErrors);
                    
                    if (bindingErrors.hasErrors()) {
                        bindingErrors.getFieldErrors().forEach(error -> 
                            errors.put(error.getField(), error.getDefaultMessage())
                        );
                    }
                }
            }
            
            // Kiểm tra tham số RequestParam
            if (parameter.isAnnotationPresent(RequestParam.class)) {
                RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
                
                if (requestParam.required() && arg == null) {
                    errors.put(parameter.getName(), "Tham số này là bắt buộc");
                }
                
                if (arg instanceof String) {
                    String stringArg = (String) arg;
                    
                    // Kiểm tra SQL injection
                    if (inputValidator.containsSqlInjection(stringArg)) {
                        errors.put(parameter.getName(), "Tham số chứa các ký tự không hợp lệ");
                    }
                    
                    // Kiểm tra HTML
                    if (inputValidator.containsHtml(stringArg)) {
                        errors.put(parameter.getName(), "Tham số chứa HTML không hợp lệ");
                    }
                }
            }
        }
        
        if (!errors.isEmpty()) {
            String errorMessage = errors.entrySet().stream()
                    .map(entry -> entry.getKey() + ": " + entry.getValue())
                    .collect(Collectors.joining(", "));
            
            log.error("Lỗi xác thực tham số API: {}", errorMessage);
            throw new ValidationException(errorMessage, errors);
        }
    }
    
    /**
     * Xác thực đối tượng và làm sạch các trường String
     * @param object Đối tượng cần xác thực
     * @param objectName Tên đối tượng
     * @throws ValidationException Nếu có lỗi xác thực
     */
    public void validateAndSanitize(Object object, String objectName) {
        // Xác thực đối tượng
        validate(object, objectName);
        
        // Làm sạch các trường String
        Arrays.stream(object.getClass().getDeclaredFields())
                .filter(field -> field.getType().equals(String.class))
                .forEach(field -> sanitizeField(object, field));
    }
    
    /**
     * Làm sạch trường String
     * @param object Đối tượng
     * @param field Trường
     */
    private void sanitizeField(Object object, Field field) {
        try {
            field.setAccessible(true);
            String value = (String) field.get(object);
            
            if (value != null) {
                String sanitizedValue = inputValidator.validateAndSanitize(value);
                field.set(object, sanitizedValue);
            }
        } catch (IllegalAccessException e) {
            log.error("Lỗi khi làm sạch trường {}: {}", field.getName(), e.getMessage(), e);
        }
    }
}
