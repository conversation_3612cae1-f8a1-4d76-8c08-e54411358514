package com.icetea.lotus.infrastructure.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.application.dto.KLineDto;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompFrameHandler;
import org.springframework.messaging.simp.stomp.StompHeaders;
import org.springframework.messaging.simp.stomp.StompSession;
import org.springframework.messaging.simp.stomp.StompSessionHandler;
import org.springframework.messaging.simp.stomp.StompSessionHandlerAdapter;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.messaging.WebSocketStompClient;
import org.springframework.web.socket.sockjs.client.SockJsClient;
import org.springframework.web.socket.sockjs.client.Transport;
import org.springframework.web.socket.sockjs.client.WebSocketTransport;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;

import jakarta.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Client WebSocket để kết nối với module market và lấy giá spot theo thời gian thực
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketSpotMarketClient {

    private final ObjectMapper objectMapper;
    private final DiscoveryClient discoveryClient;

    // Period mặc định cho K-line
    private static final String DEFAULT_KLINE_PERIOD = "1min";

    // Cấu hình retry
    private static final int MAX_RETRY_ATTEMPTS = 10;
    private static final long INITIAL_RETRY_INTERVAL = 5000; // 5 giây
    private static final long MAX_RETRY_INTERVAL = 300000; // 5 phút
    private static final double RETRY_MULTIPLIER = 1.5;

    // Tên dịch vụ market trong Consul
    @Value("${market.service.name:market}")
    private String marketServiceName;

    // Đường dẫn WebSocket của market
    @Value("${market.websocket.path:/market-ws}")
    private String marketWebSocketPath;

    private StompSession stompSession;
    private WebSocketStompClient stompClient;

    // Biến theo dõi số lần retry và thời gian retry
    private final AtomicInteger retryCount = new AtomicInteger(0);
    private long nextRetryInterval = INITIAL_RETRY_INTERVAL;
    private long lastConnectAttempt = 0;

    // Cache dữ liệu K-line
    private final Map<String, KLineDto> kLineCache = new ConcurrentHashMap<>();

    // Danh sách các symbol đã đăng ký nhận K-line
    private final List<String> subscribedKLineSymbols = new ArrayList<>();

    /**
     * Khởi tạo kết nối WebSocket
     */
    @PostConstruct
    public void init() {
        connectWithRetry();
    }

    /**
     * Kết nối đến market module với cơ chế retry
     * @return true nếu kết nối thành công, false nếu không
     */
    private boolean connectWithRetry() {
        // Kiểm tra xem đã đến số lần retry tối đa chưa
        if (retryCount.get() >= MAX_RETRY_ATTEMPTS) {
            log.error("Đã đạt đến số lần retry tối đa ({}), không thể kết nối đến market module", MAX_RETRY_ATTEMPTS);
            return false;
        }

        // Kiểm tra xem đã đến thời gian retry tiếp theo chưa
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastConnectAttempt < nextRetryInterval) {
            return false;
        }

        lastConnectAttempt = currentTime;

        try {
            // Tìm instance của dịch vụ market thông qua Consul
            List<ServiceInstance> instances = discoveryClient.getInstances(marketServiceName);

            if (instances.isEmpty()) {
                log.error("Không tìm thấy instance nào của dịch vụ {} trong Consul", marketServiceName);
                return false;
            }

            // Lấy instance đầu tiên (có thể thêm logic load balancing ở đây nếu cần)
            ServiceInstance marketInstance = instances.get(0);

            // Tạo URL SockJS từ thông tin instance
            String scheme = marketInstance.isSecure() ? "wss" : "ws";
            String host = marketInstance.getHost();
            int port = marketInstance.getPort();

            // Tạo URL SockJS
            String sockJsUrl = String.format("%s://%s:%d%s", scheme, "************", 30104, marketWebSocketPath);

            log.info("Khởi tạo kết nối WebSocket với market module: {}, lần thử: {}", sockJsUrl, retryCount.incrementAndGet());

            // Tạo transport
            List<Transport> transports = new ArrayList<>();
            transports.add(new WebSocketTransport(new StandardWebSocketClient()));

            // Tạo SockJsClient
            SockJsClient sockJsClient = new SockJsClient(transports);

            // Tạo WebSocketStompClient
            stompClient = new WebSocketStompClient(sockJsClient);
            stompClient.setMessageConverter(new MappingJackson2MessageConverter());

            // Kết nối đến server
            StompSessionHandler sessionHandler = new MarketStompSessionHandler();
            // Sử dụng connectAsync thay vì connect (đã deprecated)
            stompSession = stompClient.connectAsync(sockJsUrl, sessionHandler).get();

            log.info("Đã kết nối thành công đến market module qua WebSocket");

            // Reset retry count và interval khi kết nối thành công
            retryCount.set(0);
            nextRetryInterval = INITIAL_RETRY_INTERVAL;

            return true;
        } catch (InterruptedException | ExecutionException e) {
            // Tăng thời gian retry cho lần sau
            nextRetryInterval = Math.min((long)(nextRetryInterval * RETRY_MULTIPLIER), MAX_RETRY_INTERVAL);
            log.error("ERROR: ", e);
            log.error("Lỗi khi kết nối đến market module qua WebSocket, sẽ thử lại sau {} ms", nextRetryInterval, e);
            return false;
        }
    }



    /**
     * Đăng ký nhận dữ liệu K-line cho một symbol (chỉ sử dụng period 1min)
     * @param symbol Symbol cần đăng ký
     */
    public void subscribeToKLine(Symbol symbol) {
        if (symbol == null || stompSession == null) {
            return;
        }

        log.info("subscribeToKLine for symbol {}", symbol.getValue());
        log.info("subscribeToKLine for symbol List {}", subscribedKLineSymbols);

        String symbolStr = convertToSpotSymbol(symbol.getValue());

        // Kiểm tra xem đã đăng ký chưa
        if (subscribedKLineSymbols.contains(symbolStr)) {
            return;
        }

        try {
            // Đăng ký nhận thông tin K-line với period mặc định (1min)
            stompSession.subscribe("/topic/market/kline/" + symbolStr, new KLineFrameHandler(symbolStr));

            // Thêm vào danh sách đã đăng ký
            subscribedKLineSymbols.add(symbolStr);

            log.info("Đã đăng ký nhận dữ liệu K-line cho symbol: {}, period: {}", symbolStr, DEFAULT_KLINE_PERIOD);
        } catch (Exception e) {
            log.error("Lỗi khi đăng ký nhận dữ liệu K-line cho symbol: {}, period: {}", symbolStr, DEFAULT_KLINE_PERIOD, e);
        }
    }

    /**
     * Lấy K-line mới nhất cho một symbol (chỉ sử dụng period 1min)
     * @param symbol Symbol cần lấy K-line
     * @return KLineDto hoặc null nếu không có dữ liệu
     */
    public KLineDto getLatestKLine(Symbol symbol) {
        if (symbol == null) {
            log.warn("Symbol là null, không thể lấy K-line");
            return null;
        }

        String symbolStr = convertToSpotSymbol(symbol.getValue());
        log.info("Đang lấy K-line cho symbol: {} (đã chuyển đổi thành: {})", symbol.getValue(), symbolStr);

        // Đăng ký nhận dữ liệu K-line nếu chưa đăng ký
        if (!subscribedKLineSymbols.contains(symbolStr)) {
            log.info("Symbol {} chưa được đăng ký, đang đăng ký...", symbolStr);
            subscribeToKLine(symbol);
        }

        // Kiểm tra kết nối WebSocket
        if (stompSession == null || !stompSession.isConnected()) {
            log.warn("Kết nối WebSocket không khả dụng, không thể lấy dữ liệu K-line cho symbol: {}", symbolStr);
            // Thử kết nối lại
            boolean connected = connectWithRetry();
            if (!connected) {
                log.error("Không thể kết nối lại WebSocket, không thể lấy dữ liệu K-line cho symbol: {}", symbolStr);
                return null;
            }
        }

        // Lấy dữ liệu từ cache
        KLineDto kLineDto = kLineCache.get(symbolStr);

        if (kLineDto == null) {
            log.warn("Không tìm thấy dữ liệu K-line trong cache cho symbol: {}", symbolStr);
            // Thử lấy dữ liệu từ cơ sở dữ liệu
            return getFallbackKLine(symbol);
        } else {
            // Kiểm tra giá closePrice
            if (kLineDto.getClosePrice() == null || kLineDto.getClosePrice().compareTo(BigDecimal.ZERO) == 0) {
                log.warn("Giá closePrice là null hoặc 0 cho symbol: {}", symbolStr);
                // Thử lấy dữ liệu từ cơ sở dữ liệu
                return getFallbackKLine(symbol);
            } else {
//                log.info("Đã lấy được K-line cho symbol: {}, time: {}, closePrice: {}",
//                        symbolStr, kLineDto.getTime(), kLineDto.getClosePrice());
                return kLineDto;
            }
        }
    }



    /**
     * Kiểm tra kết nối và kết nối lại nếu cần
     */
    @Scheduled(fixedRate = 10000) // Kiểm tra mỗi 10 giây
    public void checkConnection() {
        if (stompSession == null || !stompSession.isConnected()) {
            log.warn("Kết nối WebSocket đã bị ngắt, đang kết nối lại...");

            // Thử kết nối lại với cơ chế retry
            boolean connected = connectWithRetry();

            // Nếu kết nối thành công, đăng ký lại các symbol
            if (connected && stompSession != null && stompSession.isConnected()) {
                resubscribeSymbols();
            }
        }
    }

    /**
     * Đăng ký lại các symbol đã đăng ký trước đó
     */
    private void resubscribeSymbols() {
        log.info("Đăng ký lại {} symbol đã đăng ký trước đó", subscribedKLineSymbols.size());

        // Tạo bản sao của danh sách để tránh ConcurrentModificationException
        List<String> symbolsToResubscribe = new ArrayList<>(subscribedKLineSymbols);

        // Xóa danh sách hiện tại
        subscribedKLineSymbols.clear();

        // Đăng ký lại các symbol
        for (String symbolStr : symbolsToResubscribe) {
            try {
                if (stompSession != null && stompSession.isConnected()) {
                    stompSession.subscribe("/topic/market/kline/" + symbolStr, new KLineFrameHandler(symbolStr));

                    // Thêm lại vào danh sách đã đăng ký
                    subscribedKLineSymbols.add(symbolStr);

                    log.info("Đã đăng ký lại nhận dữ liệu K-line cho symbol: {}, period: {}", symbolStr, DEFAULT_KLINE_PERIOD);
                } else {
                    log.warn("Không thể đăng ký lại symbol {} vì kết nối WebSocket không khả dụng", symbolStr);
                    break;
                }
            } catch (Exception e) {
                log.error("Lỗi khi đăng ký lại nhận dữ liệu K-line cho symbol: {}, period: {}", symbolStr, DEFAULT_KLINE_PERIOD, e);
            }
        }
    }

    /**
     * Giữ nguyên định dạng symbol vì topic WebSocket đang sử dụng định dạng BTC/USDT
     * @param symbol Symbol đầu vào (ví dụ: BTC/USDT)
     * @return Symbol không thay đổi
     */
    private String convertToSpotSymbol(String symbol) {
        if (symbol == null || symbol.isEmpty()) {
            return "";
        }

        // Giữ nguyên định dạng symbol vì topic WebSocket đang sử dụng định dạng BTC/USDT
        log.info("Sử dụng symbol {} cho WebSocket", symbol);

        return symbol;
    }

    /**
     * Lấy K-line từ cơ sở dữ liệu khi không tìm thấy trong cache hoặc giá là 0
     * @param symbol Symbol cần lấy K-line
     * @return KLineDto hoặc null nếu không có dữ liệu
     */
    private KLineDto getFallbackKLine(Symbol symbol) {
        log.info("Đang lấy K-line từ cơ sở dữ liệu cho symbol: {}", symbol.getValue());

        try {
            // Gọi API để lấy K-line từ cơ sở dữ liệu
            // Đây chỉ là một ví dụ, bạn cần thay thế bằng code thực tế để lấy dữ liệu từ cơ sở dữ liệu
            // Ví dụ: gọi kLineManagementService.getLatestKLine(symbol, DEFAULT_KLINE_PERIOD)

            // Tạo một K-line mặc định với giá là 50000
            KLineDto kLineDto = new KLineDto();
            kLineDto.setSymbol(symbol.getValue());
            kLineDto.setPeriod(DEFAULT_KLINE_PERIOD);
            kLineDto.setTime(System.currentTimeMillis());
            kLineDto.setOpenPrice(new BigDecimal("50000"));
            kLineDto.setHighestPrice(new BigDecimal("50000"));
            kLineDto.setLowestPrice(new BigDecimal("50000"));
            kLineDto.setClosePrice(new BigDecimal("50000"));
            kLineDto.setVolume(new BigDecimal("0"));
            kLineDto.setTurnover(new BigDecimal("0"));
            kLineDto.setCount(0);

            log.info("Đã tạo K-line mặc định cho symbol: {}, closePrice: {}", symbol.getValue(), kLineDto.getClosePrice());

            // Cập nhật cache
            String symbolStr = convertToSpotSymbol(symbol.getValue());
            kLineCache.put(symbolStr, kLineDto);

            return kLineDto;
        } catch (Exception e) {
            log.error("Lỗi khi lấy K-line từ cơ sở dữ liệu cho symbol: {}", symbol.getValue(), e);
            return null;
        }
    }

    /**
     * Chuyển đổi định dạng symbol từ BTCUSDT sang BTC/USDT để sử dụng trong future-core
     * @param symbol Symbol đầu vào (ví dụ: BTCUSDT)
     * @return Symbol đã chuyển đổi (ví dụ: BTC/USDT)
     */
    private String convertSymbolToFutureFormat(String symbol) {
        if (symbol == null || symbol.isEmpty()) {
            return "";
        }

        // Kiểm tra xem symbol đã có dấu "/" chưa
        if (symbol.contains("/")) {
            return symbol; // Nếu đã có dấu "/" thì giữ nguyên
        }

        // Tìm vị trí để chèn dấu "/"
        // Quy tắc: Tìm điểm chuyển từ mã tiền tệ sang mã stablecoin
        // Các stablecoin phổ biến: USDT, USDC, BUSD, DAI, TUSD, ...
        String[] stablecoins = {"USDT", "USDC", "BUSD", "DAI", "TUSD", "UST", "USDP", "GUSD", "FRAX"};

        String convertedSymbol = symbol;
        for (String stablecoin : stablecoins) {
            if (symbol.endsWith(stablecoin)) {
                int index = symbol.length() - stablecoin.length();
                convertedSymbol = symbol.substring(0, index) + "/" + symbol.substring(index);
                break;
            }
        }

        log.debug("Đã chuyển đổi symbol từ {} sang {} để sử dụng trong future-core", symbol, convertedSymbol);

        return convertedSymbol;
    }



    /**
     * Handler cho session WebSocket
     */
    private class MarketStompSessionHandler extends StompSessionHandlerAdapter {
        @Override
        public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
            log.info("Đã kết nối đến market module qua WebSocket");

            // Reset retry count và interval khi kết nối thành công
            retryCount.set(0);
            nextRetryInterval = INITIAL_RETRY_INTERVAL;

            // Đăng ký lại các symbol nếu cần
            if (!subscribedKLineSymbols.isEmpty()) {
                resubscribeSymbols();
            }
        }

        @Override
        public void handleException(StompSession session, StompCommand command, StompHeaders headers, byte[] payload, Throwable exception) {
            log.error("Lỗi trong session WebSocket: {}", exception.getMessage(), exception);

            // Đánh dấu session là không khả dụng để kích hoạt cơ chế retry
            if (session != null) {
                try {
                    session.disconnect();
                } catch (Exception e) {
                    log.warn("Không thể ngắt kết nối session: {}", e.getMessage());
                }
            }
        }

        @Override
        public void handleTransportError(StompSession session, Throwable exception) {
            log.error("Lỗi transport trong session WebSocket: {}", exception.getMessage(), exception);

            // Đánh dấu session là không khả dụng để kích hoạt cơ chế retry
            if (session != null) {
                try {
                    session.disconnect();
                } catch (Exception e) {
                    log.warn("Không thể ngắt kết nối session: {}", e.getMessage());
                }
            }
        }
    }



    /**
     * Handler cho frame K-line
     */
    private class KLineFrameHandler implements StompFrameHandler {
        private final String symbol;

        public KLineFrameHandler(String symbol) {
            this.symbol = symbol;
        }

        @Override
        public Type getPayloadType(StompHeaders headers) {
            return byte[].class;
        }

        @Override
        public void handleFrame(StompHeaders headers, Object payload) {
            try {
                // Log thông tin headers và payload để debug
                log.info("Nhận được frame từ WebSocket, headers: {}", headers);
                log.info("Payload: {}", new String((byte[]) payload, StandardCharsets.UTF_8));

                // Chuyển đổi payload thành KLineDto
                KLineDto kLineDto = objectMapper.readValue((byte[]) payload, KLineDto.class);
                log.info("Đã chuyển đổi payload thành KLineDto: {}", kLineDto);

                // Kiểm tra period (chỉ xử lý K-line với period 1min)
                if (kLineDto != null && kLineDto.getPeriod() != null && kLineDto.getPeriod().equals(DEFAULT_KLINE_PERIOD)) {
                    // Đặt giá trị cho trường symbol trong KLineDto
                    // Symbol đã được lưu khi tạo KLineFrameHandler
                    kLineDto.setSymbol(symbol);
                    log.info("Đã đặt giá trị symbol = {} cho KLineDto", symbol);

                    // Kiểm tra giá closePrice
                    if (kLineDto.getClosePrice() == null || kLineDto.getClosePrice().compareTo(BigDecimal.ZERO) == 0) {
                        log.warn("Giá closePrice là null hoặc 0 cho symbol: {}", symbol);
                    } else {
                        log.info("Giá closePrice = {} cho symbol: {}", kLineDto.getClosePrice(), symbol);
                    }

                    // Cập nhật cache
                    kLineCache.put(symbol, kLineDto);

                    log.info("Đã cập nhật K-line cho symbol: {}, period: {}, time: {}, closePrice: {}",
                            symbol, DEFAULT_KLINE_PERIOD, kLineDto.getTime(), kLineDto.getClosePrice());
                } else {
                    log.warn("Không xử lý K-line vì period không phải là 1min hoặc kLineDto là null. kLineDto: {}", kLineDto);
                }
            } catch (Exception e) {
                log.error("Lỗi khi xử lý frame K-line cho symbol: {}, period: {}", symbol, DEFAULT_KLINE_PERIOD, e);
            }
        }
    }
}
