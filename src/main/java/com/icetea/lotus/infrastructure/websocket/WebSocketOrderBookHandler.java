package com.icetea.lotus.infrastructure.websocket;

import com.icetea.lotus.application.dto.OrderBookDto;
import com.icetea.lotus.application.mapper.OrderBookMapper;
import com.icetea.lotus.core.domain.entity.OrderBook;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

/**
 * Implementation của OrderBookHandler sử dụng WebSocket
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketOrderBookHandler implements OrderBookHandler {

    private final SimpMessagingTemplate messagingTemplate;
    private final OrderBookMapper orderBookMapper;

    /**
     * Xử lý sổ lệnh mới
     *
     * @param symbol    Symbol của hợp đồng
     * @param orderBook Sổ lệnh
     */
    @Override
    public void handleOrderBook(String symbol, OrderBook orderBook) {
        OrderBookDto orderBookDto = orderBookMapper.domainToDto(orderBook);

        // Gửi sổ lệnh qua WebSocket
        messagingTemplate.convertAndSend("/topic/market/orderbook/" + symbol, orderBookDto);

        // Gửi sổ lệnh dạng plate (giống như trong module market)
        messagingTemplate.convertAndSend("/topic/market/trade-plate/" + symbol, orderBookDto);

        // Gửi sổ lệnh dạng depth (giống như trong module market)
        messagingTemplate.convertAndSend("/topic/market/trade-depth/" + symbol, orderBookDto);
    }
}
