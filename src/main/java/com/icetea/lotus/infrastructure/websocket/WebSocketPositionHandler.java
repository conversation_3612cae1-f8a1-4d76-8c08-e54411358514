package com.icetea.lotus.infrastructure.websocket;

import com.icetea.lotus.application.dto.PositionDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

/**
 * Implementation của PositionHandler sử dụng WebSocket
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketPositionHandler implements PositionHandler {

    private final SimpMessagingTemplate messagingTemplate;

    /**
     * X<PERSON> lý cập nhật vị thế mới
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @param position Thông tin vị thế
     */
    @Override
    public void handlePositionUpdate(Long memberId, String symbol, PositionDto position) {
        try {
            log.debug("<PERSON><PERSON><PERSON> cập nhật vị thế qua WebSocket, memberId = {}, symbol = {}", memberId, symbol);

            // G<PERSON><PERSON> vị thế theo user-specific channel (giống như Binance user data stream)
            messagingTemplate.convertAndSendToUser(
                    memberId.toString(),
                    "/topic/position/update/" + symbol,
                    position
            );

            // Gửi vị thế theo general channel cho monitoring
            messagingTemplate.convertAndSend(
                    "/topic/position/" + symbol + "/" + memberId,
                    position
            );

            log.debug("Đã gửi cập nhật vị thế qua WebSocket thành công, memberId = {}, symbol = {}", memberId, symbol);
        } catch (Exception e) {
            log.error("Lỗi khi gửi cập nhật vị thế qua WebSocket, memberId = {}, symbol = {}", memberId, symbol, e);
        }
    }

    /**
     * Xử lý sự kiện đóng vị thế
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @param position Thông tin vị thế đã đóng
     */
    @Override
    public void handlePositionClosed(Long memberId, String symbol, PositionDto position) {
        try {
            log.debug("Gửi thông báo đóng vị thế qua WebSocket, memberId = {}, symbol = {}", memberId, symbol);

            // Đánh dấu position là CLOSED
            position.setStatus("CLOSED");

            // Gửi thông báo đóng vị thế
            messagingTemplate.convertAndSendToUser(
                    memberId.toString(),
                    "/position/closed/" + symbol,
                    position
            );

            // Gửi thông báo general
            messagingTemplate.convertAndSend(
                    "/topic/position/closed/" + symbol + "/" + memberId,
                    position
            );

            log.debug("Đã gửi thông báo đóng vị thế qua WebSocket thành công, memberId = {}, symbol = {}", memberId, symbol);
        } catch (Exception e) {
            log.error("Lỗi khi gửi thông báo đóng vị thế qua WebSocket, memberId = {}, symbol = {}", memberId, symbol, e);
        }
    }

    /**
     * Xử lý sự kiện thanh lý vị thế
     *
     * @param memberId ID của thành viên
     * @param symbol   Symbol của hợp đồng
     * @param position Thông tin vị thế bị thanh lý
     */
    @Override
    public void handlePositionLiquidated(Long memberId, String symbol, PositionDto position) {
        try {
            log.warn("Gửi thông báo thanh lý vị thế qua WebSocket, memberId = {}, symbol = {}", memberId, symbol);

            // Đánh dấu position là LIQUIDATED
            position.setStatus("LIQUIDATED");

            // Gửi thông báo thanh lý vị thế với mức độ ưu tiên cao
            messagingTemplate.convertAndSendToUser(
                    memberId.toString(),
                    "/position/liquidated/" + symbol,
                    position
            );

            // Gửi thông báo general cho monitoring
            messagingTemplate.convertAndSend(
                    "/topic/position/liquidated/" + symbol + "/" + memberId,
                    position
            );

            // Gửi thông báo tổng quát về thanh lý (không chứa thông tin nhạy cảm)
            messagingTemplate.convertAndSend(
                    "/topic/liquidation/" + symbol,
                    PositionLiquidationNotification.builder()
                            .symbol(symbol)
                            .direction(position.getDirection())
                            .volume(position.getVolume())
                            .liquidationPrice(position.getLiquidationPrice())
                            .timestamp(System.currentTimeMillis())
                            .build()
            );

            log.warn("Đã gửi thông báo thanh lý vị thế qua WebSocket thành công, memberId = {}, symbol = {}", memberId, symbol);
        } catch (Exception e) {
            log.error("Lỗi khi gửi thông báo thanh lý vị thế qua WebSocket, memberId = {}, symbol = {}", memberId, symbol, e);
        }
    }

    /**
     * DTO cho thông báo thanh lý công khai (không chứa thông tin nhạy cảm)
     */
    @lombok.Builder
    @lombok.Data
    public static class PositionLiquidationNotification {
        private String symbol;
        private com.icetea.lotus.core.domain.entity.PositionDirection direction;
        private java.math.BigDecimal volume;
        private java.math.BigDecimal liquidationPrice;
        private Long timestamp;
    }
}