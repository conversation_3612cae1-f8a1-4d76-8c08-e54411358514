package com.icetea.lotus.infrastructure.controller;

import com.icetea.lotus.application.dto.KLineDto;
import com.icetea.lotus.application.dto.OrderBookDto;
import com.icetea.lotus.application.port.input.ManageKLineUseCase;
import com.icetea.lotus.application.port.input.ManageOrderMatchingUseCase;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * Controller xử lý các yêu cầu WebSocket
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class WebSocketController {

    private final ManageKLineUseCase manageKLineUseCase;
    private final ManageOrderMatchingUseCase manageOrderMatchingUseCase;

    /**
     * Lấy K-line mới nhất
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @return KLineDto
     */
    @MessageMapping("/kline/latest/{symbol}/{period}")
    @SendTo("/topic/market/kline/{symbol}")
    public KLineDto getLatestKLine(@DestinationVariable String symbol, @DestinationVariable String period) {
        log.info("Lấy K-line mới nhất qua WebSocket, symbol = {}, period = {}", symbol, period);

        KLineDto kLineDto = manageKLineUseCase.getLatestKLine(symbol, period);

        if (kLineDto == null) {
            log.warn("Không tìm thấy K-line nào cho symbol = {}, period = {}", symbol, period);
            return null;
        }

        // Kiểm tra giá closePrice
        if (kLineDto.getClosePrice() == null || kLineDto.getClosePrice().compareTo(java.math.BigDecimal.ZERO) == 0) {
            log.warn("Giá closePrice là null hoặc 0 cho symbol = {}, period = {}", symbol, period);
        } else {
            log.info("Đã lấy được K-line cho symbol = {}, period = {}, closePrice = {}",
                    symbol, period, kLineDto.getClosePrice());
        }

        return kLineDto;
    }

    /**
     * Lấy danh sách K-line
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param limit Số lượng kết quả tối đa
     * @return Danh sách K-line
     */
    @MessageMapping("/kline/list/{symbol}/{period}/{limit}")
    @SendTo("/topic/market/kline/{symbol}")
    public List<KLineDto> getKLines(@DestinationVariable String symbol, @DestinationVariable String period, @DestinationVariable int limit) {
        log.debug("Lấy danh sách K-line qua WebSocket, symbol = {}, period = {}, limit = {}", symbol, period, limit);
        return manageKLineUseCase.getKLines(symbol, period, limit);
    }

    /**
     * Lấy danh sách K-line theo khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách K-line
     */
    @MessageMapping("/kline/range/{symbol}/{period}/{startTime}/{endTime}")
    @SendTo("/topic/market/kline/{symbol}")
    public List<KLineDto> getKLinesByTimeRange(@DestinationVariable String symbol, @DestinationVariable String period,
                                              @DestinationVariable Long startTime, @DestinationVariable Long endTime) {
        log.debug("Lấy danh sách K-line theo khoảng thời gian qua WebSocket, symbol = {}, period = {}, startTime = {}, endTime = {}",
                symbol, period, startTime, endTime);
        return manageKLineUseCase.getKLinesByTimeRange(symbol, period, startTime, endTime);
    }

    /**
     * Lấy sổ lệnh
     * @param symbol Symbol của hợp đồng
     * @return OrderBookDto
     */
    @MessageMapping("/orderbook/{symbol}")
    @SendTo("/topic/market/orderbook/{symbol}")
    public OrderBookDto getOrderBook(@DestinationVariable String symbol) {
        log.debug("Lấy sổ lệnh qua WebSocket, symbol = {}", symbol);
        return manageOrderMatchingUseCase.getOrderBook(symbol);
    }

    /**
     * Lấy sổ lệnh theo độ sâu
     * @param symbol Symbol của hợp đồng
     * @param depth Độ sâu của sổ lệnh
     * @return OrderBookDto
     */
    @MessageMapping("/orderbook/{symbol}/depth/{depth}")
    @SendTo("/topic/market/orderbook/{symbol}")
    public OrderBookDto getOrderBookWithDepth(@DestinationVariable String symbol, @DestinationVariable int depth) {
        log.debug("Lấy sổ lệnh theo độ sâu qua WebSocket, symbol = {}, depth = {}", symbol, depth);
        return manageOrderMatchingUseCase.getOrderBook(symbol, depth);
    }
}
