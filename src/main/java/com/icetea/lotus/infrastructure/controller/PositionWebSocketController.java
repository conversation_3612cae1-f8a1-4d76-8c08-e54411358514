package com.icetea.lotus.infrastructure.controller;

import com.icetea.lotus.application.dto.PositionDto;
import com.icetea.lotus.application.service.EnhancedPositionService;
import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.repository.ContractRepository;
import com.icetea.lotus.core.domain.repository.PositionRepository;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SubscribeMapping;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * WebSocket Controller cho Position updates
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class PositionWebSocketController {

    private final PositionRepository positionRepository;
    private final ContractRepository contractRepository;
    private final EnhancedPositionService enhancedPositionService;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Subscribe to position updates for a specific symbol
     */
    @SubscribeMapping("/position/{symbol}")
    public List<PositionDto> subscribeToPosition(@DestinationVariable String symbol, Principal principal) {
        try {
            log.debug("User {} subscribed to position updates for symbol {}", principal.getName(), symbol);

            // Get user's current positions for this symbol
            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
                    Long.parseLong(principal.getName()),
                    Symbol.of(symbol),
                    PositionStatus.OPEN
            );
            List<Position> positions = positionOpt.map(List::of).orElse(List.of());

            // Convert to DTOs with enhanced information
            return positions.stream().map(position -> {
                Contract contract = contractRepository.findBySymbol(position.getSymbol());
                return contract != null ? 
                    enhancedPositionService.createEnhancedPositionDto(position, contract) :
                    null;
            }).filter(dto -> dto != null).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error subscribing to position updates for symbol {}", symbol, e);
            return List.of();
        }
    }

    /**
     * Subscribe to all position updates for user
     */
    @SubscribeMapping("/positions")
    public List<PositionDto> subscribeToAllPositions(Principal principal) {
        try {
            Long memberId = Long.parseLong(principal.getName());
            log.debug("User {} subscribed to all position updates", memberId);

            // Get all user's open positions
            List<Position> positions = positionRepository.findAllByMemberIdAndStatus(
                    memberId,
                    PositionStatus.OPEN
            );

            // Convert to DTOs with enhanced information
            return positions.stream().map(position -> {
                Contract contract = contractRepository.findBySymbol(position.getSymbol());
                return contract != null ? 
                    enhancedPositionService.createEnhancedPositionDto(position, contract) :
                    null;
            }).filter(dto -> dto != null).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error subscribing to all position updates", e);
            return List.of();
        }
    }

    /**
     * Handle position update requests
     */
    @MessageMapping("/position/update/{symbol}")
    @SendTo("/user/position/update/{symbol}")
    public PositionDto handlePositionUpdate(@DestinationVariable String symbol, Principal principal) {
        try {
            Long memberId = Long.parseLong(principal.getName());
            log.debug("Handling position update request for user {} and symbol {}", memberId, symbol);

            // Get current position
            Position position = positionRepository.findByMemberIdAndSymbolAndStatus(
                    memberId,
                    Symbol.of(symbol),
                    PositionStatus.OPEN
            ).orElse(null);

            if (position != null) {
                Contract contract = contractRepository.findBySymbol(position.getSymbol());
                if (contract != null) {
                    return enhancedPositionService.createEnhancedPositionDto(position, contract);
                }
            }

            return null;
        } catch (Exception e) {
            log.error("Error handling position update request for symbol {}", symbol, e);
            return null;
        }
    }

    /**
     * Subscribe to liquidation notifications for a symbol (public)
     */
    @SubscribeMapping("/liquidations/{symbol}")
    public String subscribeToLiquidations(@DestinationVariable String symbol) {
        log.debug("Client subscribed to liquidation notifications for symbol {}", symbol);
        return "Subscribed to liquidation notifications for " + symbol;
    }

    /**
     * Get position margin ratio for warning
     */
    @MessageMapping("/position/margin-check/{symbol}")
    public void checkMarginRatio(@DestinationVariable String symbol, Principal principal) {
        try {
            Long memberId = Long.parseLong(principal.getName());
            
            Position position = positionRepository.findByMemberIdAndSymbolAndStatus(
                    memberId,
                    Symbol.of(symbol),
                    PositionStatus.OPEN
            ).orElse(null);

            if (position != null) {
                Contract contract = contractRepository.findBySymbol(position.getSymbol());
                if (contract != null) {
                    enhancedPositionService.checkAndSendMarginCallWarning(position, contract);
                }
            }
        } catch (Exception e) {
            log.error("Error checking margin ratio for symbol {}", symbol, e);
        }
    }
}