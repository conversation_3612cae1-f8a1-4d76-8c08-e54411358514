package com.icetea.lotus.infrastructure.monitoring;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Redis Health Monitor để theo dõi sức khỏe kết nối Redis
 * và cung cấp thông tin chi tiết về lock contention
 */
@Slf4j
@Component
public class RedisHealthMonitor implements HealthIndicator {

    private final RedissonClient redissonClient;
    private final AtomicBoolean isHealthy = new AtomicBoolean(true);
    private final AtomicLong lastSuccessfulCheck = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong consecutiveFailures = new AtomicLong(0);
    private final AtomicLong totalLockTimeouts = new AtomicLong(0);
    private final AtomicLong totalLockAcquisitions = new AtomicLong(0);

    @Autowired
    public RedisHealthMonitor(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override
    public Health health() {
        Health.Builder builder = isHealthy.get() ? Health.up() : Health.down();
        
        return builder
                .withDetail("redis", "Redis connection status")
                .withDetail("lastSuccessfulCheck", lastSuccessfulCheck.get())
                .withDetail("consecutiveFailures", consecutiveFailures.get())
                .withDetail("totalLockTimeouts", totalLockTimeouts.get())
                .withDetail("totalLockAcquisitions", totalLockAcquisitions.get())
                .withDetail("lockSuccessRate", calculateLockSuccessRate())
                .build();
    }

    /**
     * Kiểm tra sức khỏe Redis định kỳ
     * Chạy mỗi 30 giây
     */
    @Scheduled(fixedRate = 30000)
    public void checkRedisHealth() {
        try {
            // Thử thực hiện một operation đơn giản
            long startTime = System.currentTimeMillis();
            redissonClient.getKeys().count();
            long responseTime = System.currentTimeMillis() - startTime;
            
            // Cập nhật trạng thái thành công
            isHealthy.set(true);
            lastSuccessfulCheck.set(System.currentTimeMillis());
            consecutiveFailures.set(0);
            
            log.debug("Redis health check thành công, response time: {}ms", responseTime);
            
            // Cảnh báo nếu response time quá cao
            if (responseTime > 1000) {
                log.warn("Redis response time cao: {}ms", responseTime);
            }
            
        } catch (Exception e) {
            long failures = consecutiveFailures.incrementAndGet();
            log.error("Redis health check thất bại lần thứ {}: {}", failures, e.getMessage());
            
            // Đánh dấu không khỏe mạnh nếu thất bại liên tiếp > 3 lần
            if (failures > 3) {
                isHealthy.set(false);
                log.error("Redis được đánh dấu không khỏe mạnh sau {} lần thất bại liên tiếp", failures);
            }
        }
    }

    /**
     * Ghi nhận lock timeout
     */
    public void recordLockTimeout() {
        totalLockTimeouts.incrementAndGet();
        log.warn("Lock timeout được ghi nhận. Tổng số lock timeouts: {}", totalLockTimeouts.get());
    }

    /**
     * Ghi nhận lock acquisition thành công
     */
    public void recordLockAcquisition() {
        totalLockAcquisitions.incrementAndGet();
    }

    /**
     * Tính tỷ lệ thành công của lock acquisition
     */
    private double calculateLockSuccessRate() {
        long acquisitions = totalLockAcquisitions.get();
        long timeouts = totalLockTimeouts.get();
        long total = acquisitions + timeouts;
        
        if (total == 0) {
            return 100.0;
        }
        
        return (double) acquisitions / total * 100.0;
    }

    /**
     * Kiểm tra xem Redis có khỏe mạnh không
     */
    public boolean isRedisHealthy() {
        return isHealthy.get();
    }

    /**
     * Lấy thống kê lock
     */
    public String getLockStatistics() {
        return String.format("Lock Statistics - Acquisitions: %d, Timeouts: %d, Success Rate: %.2f%%",
                totalLockAcquisitions.get(), totalLockTimeouts.get(), calculateLockSuccessRate());
    }

    /**
     * Reset thống kê
     */
    public void resetStatistics() {
        totalLockTimeouts.set(0);
        totalLockAcquisitions.set(0);
        log.info("Lock statistics đã được reset");
    }

    /**
     * Log thống kê định kỳ
     * Chạy mỗi 5 phút
     */
    @Scheduled(fixedRate = 300000)
    public void logStatistics() {
        if (totalLockAcquisitions.get() > 0 || totalLockTimeouts.get() > 0) {
            log.info(getLockStatistics());
        }
    }
}
