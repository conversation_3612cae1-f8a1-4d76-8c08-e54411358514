package com.icetea.lotus.infrastructure.resilience;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.cache.PriceCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Service cung cấp các phương thức fallback khi circuit breaker mở
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CircuitBreakerFallbackService {
    
    private final PriceCache priceCache;
    
    /**
     * Fallback cho việc lấy giá
     * @param symbol Symbol của hợp đồng
     * @return Money
     */
    public Money getPriceFallback(Symbol symbol) {
        log.warn("Sử dụng fallback để lấy giá cho {}", symbol);
        return priceCache.getLastPrice(symbol);
    }
    
    /**
     * Fallback cho việc lấy danh sách giao dịch gần nhất
     * @param symbol Symbol của hợp đồng
     * @return List<Trade>
     */
    public List<Trade> getRecentTradesFallback(Symbol symbol) {
        log.warn("Sử dụng fallback để lấy danh sách giao dịch gần nhất cho {}", symbol);
        List<Trade> cachedTrades = priceCache.getRecentTrades(symbol);
        return cachedTrades != null ? cachedTrades : Collections.emptyList();
    }
    
    /**
     * Fallback cho việc lấy sổ lệnh
     * @param symbol Symbol của hợp đồng
     * @return List<Order>
     */
    public List<Order> getOrderBookFallback(Symbol symbol) {
        log.warn("Sử dụng fallback để lấy sổ lệnh cho {}", symbol);
        List<Order> buyOrders = priceCache.getBuyOrders(symbol);
        List<Order> sellOrders = priceCache.getSellOrders(symbol);
        
        List<Order> result = new ArrayList<>();
        if (buyOrders != null) {
            result.addAll(buyOrders);
        }
        if (sellOrders != null) {
            result.addAll(sellOrders);
        }
        
        return result;
    }
    
    /**
     * Fallback cho việc lấy thông tin hợp đồng
     * @param symbol Symbol của hợp đồng
     * @return Contract
     */
    public Contract getContractFallback(Symbol symbol) {
        log.warn("Sử dụng fallback để lấy thông tin hợp đồng cho {}", symbol);
        return priceCache.getContract(symbol);
    }
    
    /**
     * Fallback cho việc lấy vị thế
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Position
     */
    public Position getPositionFallback(Long memberId, Symbol symbol) {
        log.warn("Sử dụng fallback để lấy vị thế cho memberId={}, symbol={}", memberId, symbol);
        // Trả về vị thế trống
        return Position.builder()
                .memberId(memberId)
                .symbol(symbol)
                .volume(BigDecimal.ZERO)
                .build();
    }
}
