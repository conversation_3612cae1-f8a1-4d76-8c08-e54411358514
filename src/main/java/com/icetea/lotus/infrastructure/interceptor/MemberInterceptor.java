//package com.icetea.lotus.infrastructure.interceptor;
//
//import com.icetea.lotus.constant.SysConstant;
//import com.icetea.lotus.entity.Member;
//import com.icetea.lotus.entity.transform.AuthMember;
//import com.icetea.lotus.event.MemberEvent;
//import com.icetea.lotus.service.MemberService;
//import lombok.extern.slf4j.Slf4j;
//import org.json.JSONException;
//import org.json.JSONObject;
//import org.springframework.beans.factory.BeanFactory;
//import org.springframework.web.context.support.WebApplicationContextUtils;
//import org.springframework.web.servlet.HandlerInterceptor;
//import org.springframework.web.servlet.ModelAndView;
//
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.servlet.http.HttpSession;
//import java.io.IOException;
//import java.io.PrintWriter;
//import java.util.Calendar;
//
///**
// * Interceptor để xác thực người dùng
// * Tái sử dụng từ core module để đồng bộ với dự án
// */
//@Slf4j
//public class MemberInterceptor implements HandlerInterceptor {
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
//            throws Exception {
//        HttpSession session = request.getSession();
//        log.info(request.getRequestURL().toString());
//        AuthMember user = (AuthMember) session.getAttribute(SysConstant.SESSION_MEMBER);
//        if (user != null) {
//            return true;
//        } else {
//            String token = request.getHeader("access-auth-token");
//            log.info("token:{}", token);
//            // Giải quyết vấn đề service không thể inject là null
//            BeanFactory factory = WebApplicationContextUtils.getRequiredWebApplicationContext(request.getServletContext());
//            MemberService memberService = (MemberService) factory.getBean("memberService");
//            MemberEvent memberEvent = (MemberEvent) factory.getBean("memberEvent");
//            Member member = memberService.loginWithToken(token, request.getRemoteAddr(), "");
//            log.info("MemberInterceptor==> member = {}", member);
//            if (member != null) {
//                Calendar calendar = Calendar.getInstance();
//                calendar.add(Calendar.HOUR_OF_DAY, 24 * 7);
//                member.setTokenExpireTime(calendar.getTime());
//                memberService.save(member);
//                memberEvent.onLoginSuccess(member, request.getRemoteAddr());
//                session.setAttribute(SysConstant.SESSION_MEMBER, AuthMember.toAuthMember(member));
//                return true;
//            } else {
//                ajaxReturn(response, 4000, "Phiên đăng nhập hiện tại của bạn đã hết hạn, vui lòng đăng nhập lại!");
//                return false;
//            }
//        }
//    }
//
//    /**
//     * Trả về kết quả ajax
//     * @param response HttpServletResponse
//     * @param code Mã lỗi
//     * @param msg Thông báo lỗi
//     * @throws IOException IOException
//     * @throws JSONException JSONException
//     */
//    public static void ajaxReturn(HttpServletResponse response, int code, String msg) throws IOException, JSONException {
//        response.setCharacterEncoding("UTF-8");
//        response.setContentType("text/json; charset=UTF-8");
//        PrintWriter out = response.getWriter();
//        JSONObject json = new JSONObject();
//        json.put("code", code);
//        json.put("message", msg);
//        out.print(json.toString());
//        out.flush();
//        out.close();
//    }
//
//    @Override
//    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
//                           ModelAndView modelAndView) throws Exception {
//    }
//
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
//            throws Exception {
//    }
//}
