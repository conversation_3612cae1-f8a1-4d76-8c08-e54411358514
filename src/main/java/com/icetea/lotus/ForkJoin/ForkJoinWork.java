package com.icetea.lotus.ForkJoin;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.RecursiveTask;


@Slf4j
public class ForkJoinWork extends RecursiveTask<Integer> {

    @Autowired
    private CoinService coinService;

    @Autowired
    private MemberWalletService memberWalletService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MemberWalletService walletService;



    /**
     * Start value
     */
    private int start;

    /**
     * End value
     */
    private int end;

    private List<Member> objectList;

    private String coinName;

    /**
     * Critical value
     */
    public static final  int critical = 10000;


    public ForkJoinWork(int start, int end,List<Member> objectList,String coinName) {
        this.start = start;
        this.end = end;
        this.objectList = objectList;
        this.coinName = coinName;
    }



    @Override
    protected Integer compute() {
        // Determine whether the split is completed
        int lenth = end - start;
        if(lenth<=critical){
            Coin coin = coinService.findOne(coinName);
            if (coin == null) {
                return null;
            }
            objectList.forEach(member -> {
                MemberWallet wallet = memberWalletService.findByCoinAndMember(coin, member);
                if (wallet == null) {
                    wallet = new MemberWallet();
                    wallet.setCoin(coin);
                    wallet.setMemberId(member.getId());
                    wallet.setBalance(new BigDecimal(0));
                    wallet.setFrozenBalance(new BigDecimal(0));
                    if (coin.getEnableRpc() == BooleanEnum.IS_TRUE) {
                        String account = "U" + member.getId();
                        // Remote RPC service URL, suffix is currency unit
                        String serviceName = "SERVICE-RPC-" + coin.getUnit();
                        log.info("=====serviceName====="+serviceName);
                        try {
                            String url = "http://" + serviceName + "/rpc/address/{account}";
                            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class, account);
                            log.info("remote call:service={},result={}", serviceName, result);
                            if (result.getStatusCode().value() == 200) {
                                MessageResult mr = result.getBody();
                                if (mr.getCode() == 0) {
                                    // Return address successfully, call persistence
                                    String address = (String) mr.getData();
                                    log.info("=====address====="+address);
                                    wallet.setAddress(address);
                                }
                            }
                            Thread.sleep(10L);
                        } catch (Exception e) {
                            log.error("call {} failed,error={}", serviceName, e.getMessage());
                            wallet.setAddress("");
                        }
                    } else {
                        wallet.setAddress("");
                    }
                    walletService.save(wallet);
                }
            });

            return objectList.size();
        }else {
            // Split starts before splitting
            // The intermediate value of the calculated two values
            int middle = (end + start)/2;
            ForkJoinWork right = new ForkJoinWork(start,middle,objectList,coinName);
            right.fork();// Split and press into thread queue
            ForkJoinWork left = new ForkJoinWork(middle+1,end,objectList,coinName);
            left.fork();// Split and press into thread queue

            // merge
            return right.join() + left.join();
        }
    }
}
