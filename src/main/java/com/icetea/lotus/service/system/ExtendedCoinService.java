package com.icetea.lotus.service.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.dto.request.TokenInfoRequest;
import com.icetea.lotus.dto.request.TokenListRequest;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;


public interface ExtendedCoinService {

    MessageResult create(Coin coin);

    MessageResult getAllCoinName();

    MessageResult getAllCoinNameAndUnit();

    MessageResult getAllCoinNameLegal();

    MessageResult update(Coin coin);

    MessageResult detail(String name);

    MessageResult pageQuery(PageModel pageModel);

    MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception; //NOSONAR

    MessageResult delete(String name);

    MessageResult setPlatformCoin(String name);

    MessageResult transfer(Admin admin, BigDecimal amount, String unit, String code);

    MessageResult page(PageModel pageModel, String unit);

    MessageResult createCoin(String coinName);

    MessageResult needCreateWallet(String coinName);

    MessageResult getKey(String phone);

    MessageResult addPartner(String coinId, long amount, long memberId);

    MessageResult getTokenListingData(PageModel pageModel, TokenListRequest tokenListRequest);

    MessageResult getTokenInfo(String networkProtocol, String contractAddress);

    MessageResult saveToken(TokenInfoRequest tokenInfoRequest);
}
