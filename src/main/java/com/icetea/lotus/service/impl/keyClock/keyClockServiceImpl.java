//package com.icetea.lotus.service.impl.keyClock;
//
//import com.icetea.lotus.config.KeycloakPropsConfig;
//import com.icetea.lotus.service.keyClockService.KeyClockService;
//import org.keycloak.admin.client.Keycloak;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Service;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.List;
//import java.util.Map;
//
//@Service
//public class keyClockServiceImpl implements KeyClockService {
//    private static final String KEYCLOAK_URL = "http://localhost:8082/realms/cex/protocol/openid-connect/token";
//    private static final String CLIENT_ID = "cex-admin";
//    private static final String CLIENT_SECRET = "EQTy1bh6LqxCT2Tl46xAZdOvxHjjCssf";
//    private static final int USER_PER_PAGE = 10;
//    private final Keycloak keycloak;
//    private final KeycloakPropsConfig keycloakPropsConfig;
//
//    public keyClockServiceImpl(Keycloak keycloak, KeycloakPropsConfig keycloakPropsConfig) {
//        this.keycloak = keycloak;
//        this.keycloakPropsConfig = keycloakPropsConfig;
//    }
//
//    @Override
//    public ResponseEntity<?> login(String username, String password) {
//        RestTemplate restTemplate = new RestTemplate();
//
//        // Headers for the request
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//
//        // Create a MultiValueMap for the form data
//        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
//        formData.add("client_id", CLIENT_ID);
//        formData.add("client_secret", CLIENT_SECRET);
//        formData.add("grant_type", "password");
//        formData.add("username", username);
//        formData.add("password", password);
//
//        // Create the HTTP entity with headers and form data
//        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(formData, headers);
//
//        try {
//            // Invoke the Keycloak token endpoint
//            ResponseEntity<Map> response = restTemplate.exchange(
//                    KEYCLOAK_URL,
//                    HttpMethod.POST,
//                    request,
//                    Map.class
//            );
//
//            // Return response (access_token and other token details)
//            return ResponseEntity.ok(response.getBody());
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid credentials or login failed");
//        }
//    }
//
//    @Override
//    public Object getCustomers(int pageNo) {
//        List result = keycloak.realm(keycloakPropsConfig.getRealm()).users()
//                .search(null, pageNo * USER_PER_PAGE, USER_PER_PAGE);
//        return result;
//    }
//}
