package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.core.Menu;
import com.icetea.lotus.entity.spot.SysRole;
import com.icetea.lotus.service.SysRoleService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedSysRoleService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * The type Extended sys role service.
 */
@Slf4j
@Service
public class ExtendedSysRoleServiceImpl extends BaseAdminController implements ExtendedSysRoleService {

    private final SysRoleService sysRoleService;

    public ExtendedSysRoleServiceImpl(BaseAdminService baseAdminService, SysRoleService sysRoleService) {
        super(baseAdminService);
        this.sysRoleService = sysRoleService;
    }

    @Override
    public MessageResult mergeRole(SysRole sysRole) {
        MessageResult result;

        sysRole = sysRoleService.save(sysRole);
        if (sysRole != null) {
            result = success("Operation is successful");
            result.setData(sysRole);
            return result;
        } else {
            return MessageResult.error(500, "Operation failed");
        }
    }

    @Override
    public MessageResult allMenu() {
        List<Menu> list = sysRoleService.toMenus(sysRoleService.getAllPermission(), 0L);
        MessageResult result = success("success");
        result.setData(list);
        return result;
    }

    @Override
    public MessageResult roleAllPermission(Long roleId) {
        List<Menu> content = sysRoleService.toMenus(sysRoleService.findOne(roleId).getPermissions(), 0L);
        MessageResult result = success();
        result.setData(content);
        return result;
    }

    @Override
    public MessageResult getAllRole(PageModel pageModel) {
        Page<SysRole> all = sysRoleService.findAll(null, pageModel);
        return success(all);
    }

    @Override
    public MessageResult deletes(Long id) {
        return sysRoleService.deletes(id);
    }
}
