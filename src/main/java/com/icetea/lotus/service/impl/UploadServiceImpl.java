package com.icetea.lotus.service.impl;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.icetea.lotus.config.AliyunConfig;
import com.icetea.lotus.config.MinioConfig;
import com.icetea.lotus.config.S3Config;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.UploadService;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.GeneratorUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.UploadFileUtil;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.errors.MinioException;
import io.minio.http.Method;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

/**
 * The type Upload service.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UploadServiceImpl extends BaseController implements UploadService {
    private final MinioClient minioClient;
    private final MinioConfig minioConfig;
    private final LocaleMessageSourceService sourceService;

    private String savePath = "data/upload/{:cate}/{yyyy}{mm}{dd}/{time}{rand:6}";
    private String allowedFormat = ".jpg,.gif,.png";
    private long maxAllowedSize = 1024 * 10000;

    @Autowired
    private AliyunConfig aliyunConfig;

    @Autowired
    private S3Config s3Config;

    @Value("${oss.name}")
    private String ossName;

    @Override
    public MessageResult uploadOssImage(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException {
        log.info(String.valueOf(request.getSession().getServletContext().getResource("/")));
        response.setCharacterEncoding("UTF-8");
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            return MessageResult.error(500, sourceService.getMessage("FORMAT_NOT_SUPPORTED"));
        }
        if (file == null) {
            return MessageResult.error(500, sourceService.getMessage("FILE_NOT_FOUND"));
        }

        String directory = new SimpleDateFormat("yyyy/MM/dd/").format(new Date());
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf("."), fileName.length());
        String key = directory + GeneratorUtil.getUUID() + suffix;

        return doUpload(file, key);
    }

    @Override
    public MessageResult uploadLocalImage(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException {
        log.info(request.getSession().getServletContext().getResource("/").toString());
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html; charset=UTF-8");
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            return MessageResult.error(500, sourceService.getMessage("FORMAT_NOT_SUPPORTED"));
        }
        Assert.isTrue(file != null, sourceService.getMessage("NOT_FIND_FILE"));
        // Verify file type
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf("."), fileName.length());
        if (!allowedFormat.contains(suffix.trim().toLowerCase())) {
            return MessageResult.error(sourceService.getMessage("FORMAT_NOT_SUPPORT"));
        }
        String result = UploadFileUtil.uploadFile(file, fileName);
        if (result != null) {
            MessageResult mr = new MessageResult(0, sourceService.getMessage("UPLOAD_SUCCESS"));
            mr.setData(result);
            return mr;
        } else {
            MessageResult mr = new MessageResult(0, sourceService.getMessage("FAILED_TO_WRITE"));
            mr.setData(result);
            return mr;
        }
    }

    @Override
    public MessageResult uploadOssApp(HttpServletRequest request, HttpServletResponse response, MultipartFile file) throws IOException {
        log.info(String.valueOf(request.getSession().getServletContext().getResource("/")));
        response.setCharacterEncoding("UTF-8");
        if (!"POST".equalsIgnoreCase(request.getMethod())) {
            return MessageResult.error(500, sourceService.getMessage("FORMAT_NOT_SUPPORTED"));
        }
        if (file == null) {
            return MessageResult.error(500, sourceService.getMessage("FILE_NOT_FOUND"));
        }

        String directory = "appdownload/";
        String fileName = file.getOriginalFilename();
// String suffix = fileName.substring(fileName.lastIndexOf("."), fileName.length());
        String key = directory + fileName;

        return doUpload(file, key);
    }

    @Override
    public MessageResult base64UpLoad(String base64Data) {
        MessageResult result = new MessageResult();
        try {
            log.debug("Upload file data:" + base64Data);
            String dataPrix = "";
            String data = "";

            log.debug("Make judgments on data");
            if (base64Data == null || "".equals(base64Data)) {
                throw new Exception("Upload failed, upload image data is empty");
            } else {
                String[] d = base64Data.split("base64,");
                if (d != null && d.length == 2) {
                    dataPrix = d[0];
                    data = d[1];
                } else {
                    throw new Exception("Upload failed, data is illegal");
                }
            }

            log.debug("Parse the data and get file name and stream data");
            String suffix = "";
            if ("data:image/jpeg;".equalsIgnoreCase(dataPrix)) {// data:image/jpeg; base64, base64 encoded jpeg image data
                suffix = ".jpg";
            } else if ("data:image/x-icon;".equalsIgnoreCase(dataPrix)) {// data:image/x-icon; base64, base64 encoded icon image data
                suffix = ".ico";
            } else if ("data:image/gif;".equalsIgnoreCase(dataPrix)) {// data:image/gif; base64, base64 encoded gif image data
                suffix = ".gif";
            } else if ("data:image/png;".equalsIgnoreCase(dataPrix)) {// data:image/png; base64, base64 encoded png image data
                suffix = ".png";
            } else {
                throw new Exception("The uploaded image format is illegal");
            }
            String directory = new SimpleDateFormat("yyyy/MM/dd/").format(new Date());
            String key = directory + GeneratorUtil.getUUID() + suffix;

            // Because of the jar problem of BASE64Decoder, here is the toolkit provided by the spring framework.
            byte[] bs = Base64.getDecoder().decode(data);
            return doUpload(key, bs);
        } catch (Exception e) {
            log.debug("Upload failed," + e.getMessage());
            result.setCode(500);
            result.setMessage("Upload failed," + e.getMessage());
        }
        return result;
    }


    private MessageResult doUpload(MultipartFile file, String key) {
//        if ("s3".equals(ossName)) {
//            return s3Upload(file, key);
//        } else  {
//            return ossUpload(file, key);
//        }
        return minioUpload(file, key);

    }

    private MessageResult doUpload(String key, byte[] bs) {
        if ("s3".equals(ossName)) {
            return s3Upload(key, bs);
        } else {
            return ossUpload(key, bs);
        }
    }

    private String s3Upload(MultipartFile file, String key) {
        String[] split = s3Config.getRegionsName().split("-");
        String regionName = "";
        for (String s : split) {
            regionName = regionName + s.toUpperCase() + "_";
        }
        regionName = regionName.substring(0, regionName.length() - 1);
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(s3Config.getAccessKeyId(), s3Config.getAccessKeySecret());
        AmazonS3 s3 = AmazonS3ClientBuilder.standard().withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                .withRegion(Regions.valueOf(regionName)).build();

        PutObjectRequest putRequest = null;
        try {
            putRequest = new PutObjectRequest(s3Config.getBucketName(), key, FileUtil.multipartFileToFile(file));
// AccessControlList acl = new AccessControlList();
// acl.grantPermission(GroupGrantee.AllUsers, Permission.Read);
// putRequest.setAccessControlList(acl);
            s3.putObject(putRequest);
            String uri = s3Config.toUrl(key);
            MessageResult mr = new MessageResult(0, sourceService.getMessage("UPLOAD_SUCCESS"));
            mr.setData(uri);
            System.out.println("Uploaded successfully...");
            return mr.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, sourceService.getMessage("REQUEST_FAILED")).toString();
        }

    }

    private MessageResult s3Upload(String key, byte[] bs) {
        String[] split = s3Config.getRegionsName().split("-");
        String regionName = "";
        for (String s : split) {
            regionName = regionName + s.toUpperCase() + "_";
        }
        regionName = regionName.substring(0, regionName.length() - 1);
        BasicAWSCredentials awsCreds = new BasicAWSCredentials(s3Config.getAccessKeyId(), s3Config.getAccessKeySecret());
        AmazonS3 s3 = AmazonS3ClientBuilder.standard().withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                .withRegion(Regions.valueOf(regionName)).build();

        PutObjectRequest putRequest = null;
        try {
            InputStream is = new ByteArrayInputStream(bs);
            putRequest = new PutObjectRequest(s3Config.getBucketName(), key, is, new ObjectMetadata());
// AccessControlList acl = new AccessControlList();
// acl.grantPermission(GroupGrantee.AllUsers, Permission.Read);
// putRequest.setAccessControlList(acl);
            s3.putObject(putRequest);
            String uri = s3Config.toUrl(key);
            MessageResult mr = new MessageResult(0, sourceService.getMessage("UPLOAD_SUCCESS"));
            mr.setData(uri);
            System.out.println("Uploaded successfully...");
            return mr;
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, sourceService.getMessage("REQUEST_FAILED"));
        }

    }

    private String ossUpload(MultipartFile file, String key) {
        OSSClient ossClient = new OSSClient(aliyunConfig.getOssEndpoint(), aliyunConfig.getAccessKeyId(), aliyunConfig.getAccessKeySecret());
        try {
            System.out.println(key);
            ossClient.putObject(aliyunConfig.getOssBucketName(), key, file.getInputStream());
            String uri = aliyunConfig.toUrl(key);
            MessageResult mr = new MessageResult(0, sourceService.getMessage("SUCCESS"));
            mr.setData(uri);
            return mr.toString();
        } catch (OSSException oe) {
            return MessageResult.error(500, oe.getErrorMessage()).toString();
        } catch (ClientException ce) {
            System.out.println("Error Message:" + ce.getMessage());
            return MessageResult.error(500, ce.getErrorMessage()).toString();
        } catch (Throwable e) {
            e.printStackTrace();
            return MessageResult.error(500, sourceService.getMessage("REQUEST_FAILED")).toString();
        } finally {
            ossClient.shutdown();
        }
    }

    private MessageResult ossUpload(String key, byte[] bs) {
        OSSClient ossClient = new OSSClient(aliyunConfig.getOssEndpoint(), aliyunConfig.getAccessKeyId(), aliyunConfig.getAccessKeySecret());
        try {
            // Use the tool class provided by apache to operate the flow
            InputStream is = new ByteArrayInputStream(bs);
            // FileUtils.writeByteArrayToFile(new File(Global.getConfig(UPLOAD_FILE_PAHT), tempFileName), bs);
            ossClient.putObject(aliyunConfig.getOssBucketName(), key, is);
            String uri = aliyunConfig.toUrl(key);
            MessageResult mr = new MessageResult(0, "Upload successfully");
            mr.setData(uri);
            log.debug("Upload successfully, key:{}", key);
            return mr;
        } catch (Exception ee) {
            return MessageResult.error(500, sourceService.getMessage("REQUEST_FAILED"));
        } finally {
            ossClient.shutdown();
        }
    }

    private MessageResult minioUpload(MultipartFile file, String key) {
        log.debug("Uploading file to MinIO: bucket={}, key={}", minioConfig.getBucketName(), key);

        if (file.isEmpty()) {
            log.warn("Upload failed: Empty file");
            return MessageResult.error(400, sourceService.getMessage("FILE_EMPTY"));
        }

        try {
            InputStream inputStream = file.getInputStream();
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(key)
                    .stream(inputStream, file.getSize(), -1) // -1 indicates unknown size
                    .contentType(file.getContentType() != null ? file.getContentType() : "application/octet-stream")
                    .build();

            // Perform upload
            minioClient.putObject(putObjectArgs);

            // Tạo URL tạm thời để FE truy cập
            String fileUrl = minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder().method(Method.GET)
                            .bucket(minioConfig.getBucketName())
                            .object(key)
                            .expiry(60 * 10) // 10 phút
                            .build());

            log.info("File uploaded successfully: {}", fileUrl);

            MessageResult result = new MessageResult(0, sourceService.getMessage("UPLOAD_SUCCESS"));
            result.setData(fileUrl);
            return result;

        } catch (MinioException e) {
            log.error("MinIO error during upload: {}", e.getMessage());
            return MessageResult.error(500, sourceService.getMessage("MINIO_UPLOAD_FAILED") + ": " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during upload: {}", e.getMessage());
            return MessageResult.error(500, sourceService.getMessage("REQUEST_FAILED"));
        }
    }

}
