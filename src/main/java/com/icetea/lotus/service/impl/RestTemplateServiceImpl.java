package com.icetea.lotus.service.impl;

import com.icetea.lotus.entity.spot.CoinThumb;
import com.icetea.lotus.service.RestTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class RestTemplateServiceImpl implements RestTemplateService {

    private final RestTemplate restTemplate;

    @Override
    public List<CoinThumb> restTemplateToGetCoinThumb() {
        log.info("Start restTemplateToGetCoinThumb");
        String serviceName = "/market/symbol-thumb";
        String url = "http://market" + serviceName;

        try {
            ResponseEntity<List<CoinThumb>> result = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<List<CoinThumb>>() {
                    }
            );

            if (result.getStatusCode().value() == 200) {
                return result.getBody();
            }
        } catch (Exception e) {
            log.error("Call {} failed, error={}", serviceName, e.getMessage(), e);
        }

        log.info("End restTemplateToGetCoinThumb");
        return new ArrayList<>();
    }
}
