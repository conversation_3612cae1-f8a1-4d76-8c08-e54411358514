package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.constants.MethodTokenConstants;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.response.TokenInfoResponse;
import com.icetea.lotus.dto.response.TokenListingResponse;
import com.icetea.lotus.dto.request.TokenInfoRequest;
import com.icetea.lotus.dto.request.TokenListRequest;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.Coinext;
import com.icetea.lotus.entity.spot.Coinprotocol;
import com.icetea.lotus.entity.spot.HotTransferRecord;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.entity.spot.MemberTransaction;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.entity.spot.QHotTransferRecord;
import com.icetea.lotus.pagination.Criteria;
import com.icetea.lotus.pagination.Restrictions;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinextService;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.HotTransferRecordService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedCoinService;
import com.icetea.lotus.service.system.Web3Service;
import com.icetea.lotus.util.FileUtil;
import com.icetea.lotus.util.JDBCUtils;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.datatypes.generated.Uint8;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.util.Assert.notNull;

/**
 * The type Extended coin service.
 */
@Slf4j
@Service
public class ExtendedCoinServiceImpl extends BaseAdminController implements ExtendedCoinService {

    private final HotTransferRecordService hotTransferRecordService;

    private final CoinService coinService;

    private final CoinextService coinextService;

    private final CoinprotocolService coinprotocolService;

    private final MemberWalletService memberWalletService;

    private final RestTemplate restTemplate;

    @SuppressWarnings("rawtypes")
    private final RedisTemplate redisTemplate;

    private final MemberWalletService walletService;

    private final MemberService memberService;

    private final LocaleMessageSourceService messageSource;

    private final MemberTransactionService memberTransactionService;

    private final JDBCUtils jdbcUtils;

    private final Web3Service web3Service;

    @Value("${cex-services.market:market}")
    private String marketServiceName;

    private static final String MESSAGE_COIN_NAME_NOT_EXIST = "COIN_NAME_NOT_EXIST";
    private static final String SERVICE_RPC_URL = "serviceRpcUrl";

    public ExtendedCoinServiceImpl(BaseAdminService baseAdminService, HotTransferRecordService hotTransferRecordService, CoinService coinService, CoinextService coinextService, CoinprotocolService coinprotocolService, MemberWalletService memberWalletService, RestTemplate restTemplate, @SuppressWarnings("rawtypes") RedisTemplate redisTemplate, MemberWalletService walletService, MemberService memberService, LocaleMessageSourceService messageSource, MemberTransactionService memberTransactionService, JDBCUtils jdbcUtils, Web3Service web3Service) {
        super(baseAdminService);
        this.hotTransferRecordService = hotTransferRecordService;
        this.coinService = coinService;
        this.coinextService = coinextService;
        this.coinprotocolService = coinprotocolService;
        this.memberWalletService = memberWalletService;
        this.restTemplate = restTemplate;
        this.redisTemplate = redisTemplate;
        this.walletService = walletService;
        this.memberService = memberService;
        this.messageSource = messageSource;
        this.memberTransactionService = memberTransactionService;
        this.jdbcUtils = jdbcUtils;
        this.web3Service = web3Service;
    }

    @Override
    public MessageResult create(Coin coin) {
        Coin one = coinService.findOne(coin.getName());
        if (one != null) {
            return error(messageSource.getMessage("COIN_NAME_EXIST"));
        }
        coin.setWithdrawThreshold(BigDecimal.ZERO);
        coin.setCanAutoWithdraw(BooleanEnum.IS_FALSE);
        coin.setMinWithdrawAmount(BigDecimal.ZERO);
        coin.setMaxWithdrawAmount(BigDecimal.ZERO);
        coin.setMinTxFee(0.00);
        coin.setMaxTxFee(0.00);
        coin.setMinRechargeAmount(BigDecimal.ZERO);
        coin.setCanWithdraw(BooleanEnum.IS_FALSE);
        coin.setEnableRpc(BooleanEnum.IS_FALSE);
        coin.setCanRecharge(BooleanEnum.IS_FALSE);
        coin.setDepositAddress("0");
        coin.setAccountType(0);
        coinService.save(coin);

        jdbcUtils.synchronization2MemberRegisterWallet(null, coin.getName());

        return success();
    }

    @Override
    public MessageResult getAllCoinName() {
        List<String> list = coinService.getAllCoinName();
        return success(list);
    }

    @Override
    public MessageResult getAllCoinNameAndUnit() {
        List<CoinDTO> list = coinService.getAllCoinNameAndUnit();
        return success(list);
    }

    @Override
    public MessageResult getAllCoinNameLegal() {
        List<String> list = coinService.getAllCoinNameLegal();
        return success(list);
    }

    @Override
    public MessageResult update(Coin coin) {
        Coin one = coinService.findOne(coin.getName());
        notNull(one, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        coinService.save(coin);
        return success();
    }

    @Override
    public MessageResult detail(String name) {
        Coin coin = coinService.findOne(name);
        notNull(coin, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        return success(coin);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel) {
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("name");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<Coin> pageResult = coinService.findAll((Predicate) null, pageModel.getPageable());
        for (Coin coin : pageResult.getContent()) {
            if (coin.getEnableRpc().getOrdinal() == 1) {
                coin.setAllBalance(memberWalletService.getAllBalance(coin.getName()));
                log.info(coin.getAllBalance() + "==============");
                if (coin.getAccountType() == 1) {
                    coin.setHotAllBalance(memberWalletService.getAllBalance(coin.getName()));
                    coin.setBlockHeight(0L);
                } else {
                    String url = SERVICE_RPC_URL + coin.getUnit() + "/rpc/balance";
                    coin.setHotAllBalance(getRPCWalletBalance(url, coin.getUnit()));

                    String url2 = SERVICE_RPC_URL + coin.getUnit() + "/rpc/height";
                    coin.setBlockHeight(getRPCBlockHeight(url2));
                }
            }
        }
        return success(pageResult);
    }

    private BigDecimal getRPCWalletBalance(String url, String unit) {
        try {
            // String url = "http://" + serviceName + "/rpc/address/{account}"
            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class);
            log.info("getRPCWalletBalance: result={}", result);
            if (result.getStatusCode().value() == 200) {
                MessageResult mr = result.getBody();
                if (mr.getCode() == 0) {
                    String balance = mr.getData().toString();
                    BigDecimal bigDecimal = new BigDecimal(balance);
                    log.info(unit + messageSource.getMessage("HOT_WALLET_BALANCE"), bigDecimal);
                    return bigDecimal;
                }
            }
        } catch (IllegalStateException e) {
            log.error("getRPCWalletBalance IllegalStateException={}", e);
            return new BigDecimal("0");
        } catch (Exception e) {
            log.error("getRPCWalletBalance Exception={}", e);
            return new BigDecimal("0");
        }
        return new BigDecimal("0");
    }

    private Long getRPCBlockHeight(String url) {
        try {
            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class);
            log.info("getRPCBlockHeight: result={}", result);
            if (result.getStatusCode().value() == 200) {
                MessageResult mr = result.getBody();
                if (mr.getCode() == 0) {
                    String height = mr.getData().toString();
                    return Long.valueOf(height);
                }
            }

        } catch (Exception e) {
            log.error("getRPCBlockHeight Exception={}", e.toString());
            return 0L;
        }
        return 0L;
    }


    @Override
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Coin> all = coinService.findAll();
        return new FileUtil<Coin>(messageSource).exportExcel(request, response, all, "coin");
    }

    @Override
    public MessageResult delete(String name) {
        Coin coin = coinService.findOne(name);
        notNull(coin, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        coinService.deleteOne(name);
        return success();
    }

    @Override
    public MessageResult setPlatformCoin(String name) {
        Coin coin = coinService.findOne(name);
        notNull(coin, messageSource.getMessage(MESSAGE_COIN_NAME_NOT_EXIST));
        coinService.setPlatformCoin(coin);
        return success();
    }

    @Override
    public MessageResult transfer(Admin admin, BigDecimal amount, String unit, String code) {
        Assert.notNull(admin, "The session has expired, please log in again");

        String key = SysConstant.ADMIN_COIN_TRANSFER_COLD_PREFIX + admin.getMobilePhone();

        @SuppressWarnings("rawtypes")
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object object = valueOperations.get(key + "_PASS");

        if (object == null) {
            MessageResult checkCode = checkCode(code, key);
            if (checkCode.getCode() != 0) {
                return checkCode;
            }
        }
        Coin coin = coinService.findByUnit(unit);
        String urlBalance = SERVICE_RPC_URL + coin.getUnit() + "/rpc/balance";
        BigDecimal balance = getRPCWalletBalance(urlBalance, coin.getUnit());
        log.info("balance:-------{}", balance);
        if (amount.compareTo(balance) > 0) {
            return error(messageSource.getMessage("HOT_WALLET_BALANCE_POOL"));
        }
        String url = SERVICE_RPC_URL + coin.getUnit() + "/rpc/transfer?address={1}&amount={2}&fee={3}";
        MessageResult result = restTemplate.getForObject(url,
                MessageResult.class, coin.getColdWalletAddress(), amount, coin.getMinerFee());
        log.info("result = {}", result);
        if (result.getCode() == 0 && result.getData() != null) {
            HotTransferRecord hotTransferRecord = new HotTransferRecord();
            hotTransferRecord.setAdminId(admin.getId());
            hotTransferRecord.setAdminName(admin.getUsername());
            hotTransferRecord.setAmount(amount);
            hotTransferRecord.setBalance(balance.subtract(amount));
            hotTransferRecord.setMinerFee(coin.getMinerFee() == null ? BigDecimal.ZERO : coin.getMinerFee());
            hotTransferRecord.setUnit(unit.toUpperCase());
            hotTransferRecord.setColdAddress(coin.getColdWalletAddress());
            hotTransferRecord.setTransactionNumber(result.getData().toString());
            hotTransferRecordService.save(hotTransferRecord);
            return success(hotTransferRecord);
        }
        return error(messageSource.getMessage("REQUEST_FAILED"));
    }

    @Override
    public MessageResult page(PageModel pageModel, String unit) {
        List<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (StringUtils.isNotBlank(unit)) {
            booleanExpressions.add(QHotTransferRecord.hotTransferRecord.unit.eq(unit));
        }
        Page<HotTransferRecord> page = hotTransferRecordService.findAll(PredicateUtils.getPredicate(booleanExpressions), pageModel);
        return success(page);
    }

    @Override
    public MessageResult createCoin(String coinName) {
        Coin coin = coinService.findOne(coinName);
        if (coin == null) {
            return MessageResult.error("Currency configuration does not exist");
        }
        jdbcUtils.synchronization2MemberRegisterWallet(null, coin.getName());
        // To re-enable wallet creation for all members, uncomment the following line:
        // createWalletsForAllMembers(coin)
        return success();
    }

    /**
     * Creates wallets for all members for the specified coin if they do not already exist.
     *
     * @param coin the coin for which wallets should be created for all members
     */
    @SuppressWarnings("unused")
    private void createWalletsForAllMembers(Coin coin) {
        List<Member> members = memberService.findAll();
        for (Member member : members) {
            createWalletForMemberIfAbsent(coin, member);
        }
    }

    /**
     * Creates a wallet for the given member and coin if it does not already exist.
     */
    private void createWalletForMemberIfAbsent(Coin coin, Member member) {
        MemberWallet wallet = memberWalletService.findByCoinAndMember(coin, member);
        if (wallet == null) {
            wallet = new MemberWallet();
            wallet.setCoin(coin);
            wallet.setMemberId(member.getId());
            wallet.setBalance(BigDecimal.ZERO);
            wallet.setFrozenBalance(BigDecimal.ZERO);
            setWalletAddress(wallet, coin, member);
            walletService.save(wallet);
        }
    }

    /**
     * Sets the wallet address for the given wallet, coin, and member.
     * If RPC is enabled, attempts to fetch the address from the remote service.
     * Otherwise, sets the address to an empty string.
     * This version avoids nested try/catch blocks for better readability.
     */
    private void setWalletAddress(MemberWallet wallet, Coin coin, Member member) {
        if (coin.getEnableRpc() == BooleanEnum.IS_TRUE) {
            String account = "U" + member.getId();
            String serviceName = "SERVICE-RPC-" + coin.getUnit();
            try {
                String url = "http://" + serviceName + "/rpc/address/{account}";
                ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class, account);
                log.info("remote call:service={},result={}", serviceName, result);
                if (result.getStatusCode().value() == 200) {
                    MessageResult mr = result.getBody();
                    if (mr != null && mr.getCode() == 0) {
                        String address = (String) mr.getData();
                        wallet.setAddress(address);
                        sleepWithInterruptHandling(member.getId(), coin.getUnit());
                        return;
                    }
                }
            } catch (Exception e) {
                log.error("call {} failed,error={}", serviceName, e.getMessage());
            }
            wallet.setAddress("");
        } else {
            wallet.setAddress("");
        }
    }

    /**
     * Sleeps for 10 milliseconds and handles InterruptedException properly.
     * Extracted to avoid nested try/catch blocks in setWalletAddress.
     */
    private void sleepWithInterruptHandling(Long memberId, String coinUnit) {
        try {
            Thread.sleep(10L);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            log.warn("Thread interrupted while setting wallet address for memberId={}, coin={}", memberId, coinUnit, ie);
        }
    }

    @Override
    public MessageResult needCreateWallet(String coinName) {
        Coin coin = coinService.findOne(coinName);
        if (coin == null) {
            return MessageResult.error("Currency configuration does not exist");
        }
        MessageResult result = success("", false);
        List<Member> list = memberService.findAll();
        for (Member member : list) {
            MemberWallet wallet = memberWalletService.findByCoinAndMember(coin, member);
            if (wallet == null) {
                result = success(true);
                return result;
            }
        }
        return result;
    }

    @Override
    public MessageResult getKey(String phone) {
        String key = SysConstant.ADMIN_COIN_TRANSFER_COLD_PREFIX + phone + "_PASS";
        @SuppressWarnings("rawtypes")
        ValueOperations valueOperations = redisTemplate.opsForValue();
        Object object = valueOperations.get(key);
        if (object == null) {
            return error(messageSource.getMessage("NEED_CODE"));
        }
        return success(messageSource.getMessage("NO_NEED_CODE"), object);
    }

    @Override
    public MessageResult addPartner(String coinId, long amount, long memberId) {
        BigDecimal init = BigDecimal.ZERO;
        MemberTransaction memberTransaction = new MemberTransaction();
        memberTransaction.setMemberId(memberId);
        memberTransaction.setAmount(BigDecimal.valueOf(amount));
        memberTransaction.setCreateTime(new Date());
        memberTransaction.setFee(init);
        memberTransaction.setFlag(0);
        memberTransaction.setSymbol("BHB");
        memberTransaction.setType(TransactionType.RECHARGE);
        memberTransaction.setRealFee("0");
        memberTransaction.setDiscountFee("0");

        MemberTransaction result = memberTransactionService.save(memberTransaction);

        int resultWallet = walletService.updateByMemberIdAndCoinId(memberId, "BHB", BigDecimal.valueOf(amount));
        if (resultWallet == 1) {
            log.info("The balance is modified successfully -- memberID:" + memberId + "amount:" + amount);
        } else {
            log.info("The balance is modified successfully");
        }
        if (result != null) {


            log.info("Adding the database successfully --memberID:" + memberId + "amount:" + amount);
        } else {
            log.info("Failed to add database");
        }

        return success();
    }

    /**
     * Retrieves a paginated list of token listings based on the provided filter criteria.
     * <p>
     * Queries the database for coins matching the filters in {@code tokenListRequest}, fetches current price and 24h volume
     * from the market service, and transforms the results into {@code TokenListingDTO} objects. Returns the data along with
     * pagination details in a {@code MessageResult}.
     *
     * @param pageModel        the pagination model containing page number and size
     * @param tokenListRequest the filter criteria for token listing (name, symbol, market type, status)
     * @return MessageResult containing a paginated list of token listings and related metadata, or an error message if the operation fails
     */
    @Override
    public MessageResult getTokenListingData(PageModel pageModel, TokenListRequest tokenListRequest) {
        try {
            // Set default sorting if not provided
            if (pageModel.getProperty() == null) {
                List<String> list = new ArrayList<>();
                list.add("createTime");
                List<Sort.Direction> directions = new ArrayList<>();
                directions.add(Sort.Direction.DESC);
                pageModel.setProperty(list);
                pageModel.setDirection(directions);
            }

            // Build query criteria
            Criteria<Coin> criteria = buildTokenListCriteria(tokenListRequest);
            Page<Coin> pageResult = coinService.findAll(criteria, pageModel.getPageable());

            // Transform coins to token listing format
            List<TokenListingResponse> tokenListings = new ArrayList<>();

            // Get all coin thumbs from market service using the extracted method
            List<CoinThumb> coinThumbs = fetchCoinThumbsFromMarketService();

            // Build a map for quick lookup: symbol -> CoinThumb
            Map<String, CoinThumb> coinThumbMap = new HashMap<>();
            for (CoinThumb coinThumb : coinThumbs) {
                coinThumbMap.put(coinThumb.getSymbol(), coinThumb);
            }

            for (Coin coin : pageResult.getContent()) {
                // Get current price and 24h volume from market service
                BigDecimal currentPrice = BigDecimal.ZERO;
                BigDecimal volume24h = BigDecimal.ZERO;

                String symbolKey = coin.getUnit() + "/USDT";
                CoinThumb coinThumb = coinThumbMap.get(symbolKey);
                if (coinThumb != null) {
                    currentPrice = coinThumb.getClose();
                    volume24h = coinThumb.getVolume();
                }

                // Determine status based on coin properties
                String status;

                if (coin.getStatus() != null && coin.getStatus() == CommonStatus.NORMAL) {
                    status = "Active";
                } else {
                    status = "Delisted";
                }

                // TODO Check if trading is available in spot and future //NOSONAR
                TokenListingResponse tokenData = TokenListingResponse.builder()
                        .tokenName(coin.getName())
                        .symbol(coin.getUnit())
                        .listingTime(coin.getCreateTime())
                        .price(currentPrice)
                        .volume24h(volume24h)
                        .status(status)
                        .spotTradingStatus(false) // Default to false, can be changed later
                        .futureTradingStatus(false) // Default to false, can be changed later
                        .build();

                tokenListings.add(tokenData);
            }

            // Create response with pagination info
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("content", tokenListings);
            result.put("pageNumber", pageResult.getNumber());
            result.put("pageSize", pageResult.getSize());
            result.put("totalElements", pageResult.getTotalElements());
            result.put("totalPages", pageResult.getTotalPages());
            result.put("last", pageResult.isLast());

            return success(result);
        } catch (Exception e) {
            log.error("Error fetching token listing data", e);
            return error("Failed to fetch token listing data");
        }
    }

    /**
     * Builds a Criteria object for querying Coin entities based on the provided TokenListRequest filters.
     * <p>
     * Adds conditions for name (partial match), symbol (partial match), and status (mapped to CommonStatus).
     * Market type filtering is not implemented.
     *
     * @param tokenListRequest the filter parameters for token listing
     * @return Criteria<Coin> with applied filters
     */
    private Criteria<Coin> buildTokenListCriteria(TokenListRequest tokenListRequest) {
        Criteria<Coin> criteria = new Criteria<>();

        if (StringUtils.isNotBlank(tokenListRequest.getName())) {
            criteria.add(Restrictions.like("name", "%" + tokenListRequest.getName() + "%", false));
        }
        if (StringUtils.isNotBlank(tokenListRequest.getSymbol())) {
            criteria.add(Restrictions.like("unit", "%" + tokenListRequest.getSymbol() + "%", false));
        }
        // TODO: Search by Market Type if needed //NOSONAR

        if (tokenListRequest.getStatus() != null) {
            int status = tokenListRequest.getStatus();
            criteria.add(Restrictions.eq("status", status == 0 ? CommonStatus.NORMAL : CommonStatus.ILLEGAL, false));
        }

        return criteria;
    }

    /**
     * Fetches coin thumbs from the market service.
     *
     * @return List of CoinThumb or null if failed
     */
    private List<CoinThumb> fetchCoinThumbsFromMarketService() {
        try {
            String marketUrl = "http://" + marketServiceName + "/market/symbol-thumb";
            ResponseEntity<List<CoinThumb>> result = restTemplate.exchange(
                    marketUrl,
                    org.springframework.http.HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<List<CoinThumb>>() {
                    }
            );
            return result.getBody();
        } catch (Exception e) {
            log.error("Failed to get coin thumbs from market service", e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves token information from a smart contract on the specified network.
     *
     * @param networkProtocol the network protocol (e.g., "1" for Ethereum)
     * @param contractAddress the contract address of the token
     * @return MessageResult containing token information or an error message
     */
    @Override
    public MessageResult getTokenInfo(String networkProtocol, String contractAddress) {
        try {
            // Retrieve the coin protocol from database for the specified network
            Coinprotocol coinprotocol = coinprotocolService.findByProtocol(Integer.valueOf(networkProtocol));
            if (coinprotocol == null) {
                return error(messageSource.getMessage("UNSUPPORTED_NETWORK"));
            }

            // Initialize Web3j instance for the specified network
            Web3j web3j = Web3j.build(new HttpService(coinprotocol.getRpcserver()));

            // Query the smart contract for token name, symbol, decimals, and total supply
            String name = getTokenInfo(web3j, contractAddress, MethodTokenConstants.METHOD_NAME);
            String symbol = getTokenInfo(web3j, contractAddress, MethodTokenConstants.METHOD_SYMBOL);
            int decimals = Integer.parseInt(getTokenInfo(web3j, contractAddress, MethodTokenConstants.METHOD_DECIMALS));
            BigDecimal totalSupplyRaw = new BigDecimal(getTokenInfo(web3j, contractAddress, MethodTokenConstants.METHOD_TOTAL_SUPPLY));
            // Adjust total supply by dividing by 10^decimals to get the human-readable value
            BigDecimal totalSupply = totalSupplyRaw.divide(BigDecimal.TEN.pow(decimals));

            // Build and return a successful response with the token info
            return success(TokenInfoResponse.builder()
                    .networkProtocol(networkProtocol)
                    .contractAddress(contractAddress)
                    .name(name)
                    .symbol(symbol)
                    .totalSupply(totalSupply)
                    .decimals(decimals)
                    .build()
            );

        } catch (Exception e) {
            log.error("Error fetching token info", e);
            return error(messageSource.getMessage("FAILED_TO_FETCH_TOKEN_INFO"));
        }
    }

    /**
     * Calls a specified ERC-20 contract method (name, symbol, decimals, or totalSupply) using Web3j,
     * decodes the result, and returns it as a string.
     *
     * @param web3j      the Web3j instance for blockchain interaction
     * @param contract   the contract address to query
     * @param methodName the ERC-20 method name ("name", "symbol", "decimals", or "totalSupply")
     * @return the decoded method result as a string, or a default value if decoding fails
     * @throws IOException              if the contract call fails
     * @throws IllegalArgumentException if the method name is unknown
     */
    private String getTokenInfo(Web3j web3j, String contract, String methodName) throws IOException, IllegalArgumentException {
        Function function = getFunction(methodName);

        String value = web3Service.callFunction(web3j, contract, function);
        @SuppressWarnings("rawtypes")
        List<Type> decoded = FunctionReturnDecoder.decode(value, function.getOutputParameters());

        return switch (methodName) {
            case MethodTokenConstants.METHOD_NAME, MethodTokenConstants.METHOD_SYMBOL ->
                    decoded.isEmpty() ? "unknown" : decoded.get(0).getValue().toString();
            case MethodTokenConstants.METHOD_DECIMALS ->
                    (decoded.isEmpty() ? "0" : ((Uint8) decoded.get(0)).getValue().intValue()).toString();
            case MethodTokenConstants.METHOD_TOTAL_SUPPLY ->
                    (decoded.isEmpty() ? BigInteger.ZERO : ((Uint256) decoded.get(0)).getValue()).toString();
            default ->
                    throw new IllegalArgumentException(messageSource.getMessage("UNKNOWN_METHOD_NAME", new Object[]{methodName}));
        };
    }

    /**
     * Constructs a Web3j Function object for the specified ERC-20 method name.
     * <p>
     * Supports "name", "symbol", "decimals", and "totalSupply" methods, mapping each to the appropriate output type.
     * Throws IllegalArgumentException for unsupported method names.
     *
     * @param methodName the ERC-20 method name ("name", "symbol", "decimals", or "totalSupply")
     * @return a Function object configured for the specified method
     * @throws IllegalArgumentException if the method name is unknown
     */
    private Function getFunction(String methodName) throws IllegalArgumentException {
        TypeReference<?> typeReference = switch (methodName) {
            case MethodTokenConstants.METHOD_NAME, MethodTokenConstants.METHOD_SYMBOL ->
                    new TypeReference<Utf8String>() {
                    };
            case MethodTokenConstants.METHOD_DECIMALS -> new TypeReference<Uint8>() {
            };
            case MethodTokenConstants.METHOD_TOTAL_SUPPLY -> new TypeReference<Uint256>() {
            };
            default ->
                    throw new IllegalArgumentException(messageSource.getMessage("UNKNOWN_METHOD_NAME", new Object[]{methodName}));
        };
        return new Function(
                methodName,
                Collections.emptyList(),
                List.of(typeReference)
        );
    }

    /**
     * Saves a new token to the database based on the provided token information.
     * <p>
     * Validates the network protocol and checks for existing tokens with the same contract address.
     * If valid and not existing, creates new Coin and Coinext records with default values and persists them.
     * Returns a success or error MessageResult based on the operation outcome.
     *
     * @param tokenInfoRequest the token information to be saved
     * @return MessageResult indicating success or failure of the save operation
     */
    @Override
    public MessageResult saveToken(TokenInfoRequest tokenInfoRequest) {
        try {
            // Fetch token info from the blockchain
            TokenInfoResponse tokenInfo = (TokenInfoResponse) getTokenInfo(tokenInfoRequest.getNetworkProtocol(), tokenInfoRequest.getContractAddress()).getData();
            if (tokenInfo == null) {
                return error(messageSource.getMessage("FAILED_TO_FETCH_TOKEN_INFO"));
            }

            // Retrieve the coin protocol from database for the specified network
            Coinprotocol coinprotocol = coinprotocolService.findByProtocol(Integer.valueOf(tokenInfoRequest.getNetworkProtocol()));
            if (coinprotocol == null) {
                return error(messageSource.getMessage("UNSUPPORTED_NETWORK"));
            }

            // Check if the token already exists in the database
            Coinext coinext = coinextService.findFirstByCoinnameAndProtocol(tokenInfo.getName(), coinprotocol.getProtocol());
            if (coinext != null) {
                return error(messageSource.getMessage("THIS_COIN_HAS_EXISTED"));
            }

            // Save token info to database
            Coin newCoin = new Coin();
            newCoin.setName(tokenInfo.getName()); // primary key
            newCoin.setUnit(tokenInfo.getSymbol());
            newCoin.setTotalSupply(tokenInfo.getTotalSupply());
            newCoin.setIconUrl(tokenInfoRequest.getTokenImage());
            newCoin.setDescription(tokenInfoRequest.getTokenDescription());
            newCoin.setCirculationSupply(tokenInfoRequest.getCirculationSupply());
            newCoin.setMaxSupply(tokenInfoRequest.getMaxSupply());

            // Set other fields to default values
            newCoin.setWithdrawThreshold(BigDecimal.ZERO);
            newCoin.setCanAutoWithdraw(BooleanEnum.IS_FALSE);
            newCoin.setMinWithdrawAmount(BigDecimal.ZERO);
            newCoin.setMaxWithdrawAmount(BigDecimal.ZERO);
            newCoin.setMinTxFee(0.00);
            newCoin.setMaxTxFee(0.00);
            newCoin.setMinRechargeAmount(BigDecimal.ZERO);
            newCoin.setCanWithdraw(BooleanEnum.IS_FALSE);
            newCoin.setEnableRpc(BooleanEnum.IS_FALSE);
            newCoin.setCanRecharge(BooleanEnum.IS_FALSE);
            newCoin.setAccountType(0);
            newCoin.setCoinScale(0);
            coinService.save(newCoin);

            Coinext newCoinext = new Coinext();
            newCoinext.setCoinname(tokenInfo.getName()); // foreign key
            newCoinext.setProtocol(coinprotocol.getProtocol()); // foreign key

            newCoinext.setProtocolname(coinprotocol.getProtocolname());
            newCoinext.setExt(tokenInfoRequest.getContractAddress());
            newCoinext.setDecimals(tokenInfo.getDecimals());
            newCoinext.setStatus(1);

            // Set other fields to default values
            newCoinext.setCoinid(0);
            newCoinext.setWithdrawfee(0.00);
            newCoinext.setMinwithdrawfee(0.00);
            newCoinext.setIswithdraw(true);
            newCoinext.setIsrecharge(true);
            newCoinext.setIsautowithdraw(true);
            newCoinext.setMinwithdraw(BigDecimal.ZERO);
            newCoinext.setMaxwithdraw(BigDecimal.ZERO);
            newCoinext.setMinrecharge(BigDecimal.ZERO);
            newCoinext.setConfirms(0);
            coinextService.save(newCoinext);

            return success();
        } catch (Exception e) {
            log.error("Error saving token info", e);
            return MessageResult.error(messageSource.getMessage("FAILED_TO_SAVE_TOKEN_INFO"));
        }
    }
}
