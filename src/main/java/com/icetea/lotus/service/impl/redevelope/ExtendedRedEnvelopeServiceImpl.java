package com.icetea.lotus.service.impl.redevelope;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.RedEnvelope;
import com.icetea.lotus.entity.spot.RedEnvelopeDetail;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.RedEnvelopeDetailService;
import com.icetea.lotus.service.RedEnvelopeService;
import com.icetea.lotus.service.redenvelope.ExtendedRedEnvelopeService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.GeneratorUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Extended red envelope service.
 */
@Service
@RequiredArgsConstructor
public class ExtendedRedEnvelopeServiceImpl extends BaseController implements ExtendedRedEnvelopeService {

    private final RedEnvelopeService redEnveloperService;

    private final RedEnvelopeDetailService redEnveloperDetailService;

    private final LocaleMessageSourceService messageSource;

    private final CoinService coinService;

    @Override
    public MessageResult envelopeList(PageModel pageModel) {

        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("createTime");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<RedEnvelope> all = redEnveloperService.findAll(null, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult envelopeDetail(Long id) {
        RedEnvelope redEnvelope = redEnveloperService.findOne(id);
        Assert.notNull(redEnvelope, "validate id!");
        return success(redEnvelope);
    }

    @Override
    public MessageResult envelopeDetailList(Long envelopeId, Integer pageNo, Integer pageSize) {
        Page<RedEnvelopeDetail> detailList = redEnveloperDetailService.findByEnvelope(envelopeId, pageNo, pageSize);

        return success(detailList);
    }

    @Override
    public MessageResult addRedEnvelope(RedEnvelope redEnvelope) {
        // Check if the currency exists
        Coin coin = coinService.findByUnit(redEnvelope.getUnit());
        Assert.notNull(coin, "Invalid currency!");

        // Generate red envelope number
        SimpleDateFormat f = new SimpleDateFormat("MMddHHmmss");
        redEnvelope.setEnvelopeNo(f.format(new Date()) + GeneratorUtil.getNonceString(5).toUpperCase());

        redEnvelope.setMemberId(1L); // Users issued by the platform with fixed 1
        redEnvelope.setPlateform(1); // The fixed platform issuance is 1 (platform red envelope)
        redEnvelope.setState(0);
        redEnvelope.setReceiveAmount(BigDecimal.ZERO);
        redEnvelope.setReceiveCount(0);

        redEnvelope.setCreateTime(DateUtil.getCurrentDate());
        redEnvelope = redEnveloperService.save(redEnvelope);
        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), redEnvelope);
    }

    @Override
    public MessageResult modifyRedEnvelope(RedEnvelope envelope) {
        RedEnvelope redEnvelope = redEnveloperService.findOne(envelope.getId());
        notNull(redEnvelope, "Validate Red Envelope!");

        if(envelope.getType() != null) redEnvelope.setType(envelope.getType());
        if(envelope.getInvite() != null) redEnvelope.setInvite(envelope.getInvite());
        if(envelope.getUnit() != null) {
            // Check if the currency exists
            Coin coin = coinService.findByUnit(redEnvelope.getUnit());
            Assert.notNull(coin, "Invalid currency!");
            redEnvelope.setUnit(envelope.getUnit());
        }
        if(envelope.getMaxRand() != null) redEnvelope.setMaxRand(envelope.getMaxRand());
        if(envelope.getTotalAmount() != null) redEnvelope.setTotalAmount(envelope.getTotalAmount());
        if(envelope.getCount() != null) redEnvelope.setCount(envelope.getCount());
        if(envelope.getLogoImage() != null) redEnvelope.setLogoImage(envelope.getLogoImage());
        if(envelope.getBgImage() != null) redEnvelope.setBgImage(envelope.getBgImage());
        if(envelope.getName() != null) redEnvelope.setName(envelope.getName());
        if(envelope.getDetail() != null) redEnvelope.setDetail(envelope.getDetail());
        if(envelope.getExpiredHours() != null) redEnvelope.setExpiredHours(envelope.getExpiredHours());
        if(envelope.getState() != null) redEnvelope.setState(envelope.getState());

        redEnvelope = redEnveloperService.save(redEnvelope);

        return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), redEnvelope);
    }
}
