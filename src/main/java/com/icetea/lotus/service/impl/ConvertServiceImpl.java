package com.icetea.lotus.service.impl;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.ConvertCoin;
import com.icetea.lotus.entity.spot.ConvertOrder;
import com.icetea.lotus.entity.spot.QConvertOrder;
import com.icetea.lotus.model.screen.ConvertOrderScreen;
import com.icetea.lotus.service.ConvertCoinService;
import com.icetea.lotus.service.ConvertOrderService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.convert.ConvertService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.util.PredicateUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.util.Assert.notNull;

/**
 * The type Convert service.
 */
@Service
@RequiredArgsConstructor
public class ConvertServiceImpl extends BaseController implements ConvertService {

    private final ConvertCoinService convertCoinService;

    private final LocaleMessageSourceService messageSource;

    private final ConvertOrderService convertOrderService;

    @Override
    public MessageResult create(ConvertCoin convertCoin) {
        ConvertCoin one = convertCoinService.findByCoinUnit(convertCoin.getCoinUnit());
        if (one != null) {
            return error(messageSource.getMessage("COIN_NAME_EXIST"));
        }
        convertCoin.setUpdateTime(DateUtil.getCurrentDate());
        convertCoin.setCreateTime(DateUtil.getCurrentDate());
        convertCoinService.save(convertCoin);

        return success();
    }

    @Override
    public MessageResult update(ConvertCoin convertCoin, Admin admin, BindingResult bindingResult) {
        Assert.notNull(admin, messageSource.getMessage("DATA_EXPIRED_LOGIN_AGAIN"));

        notNull(convertCoin.getCoinUnit(), "validate Coin.Unit!");
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        ConvertCoin one = convertCoinService.findByCoinUnit(convertCoin.getCoinUnit());
        notNull(one, "validate coin.name!");
        convertCoin.setUpdateTime(DateUtil.getCurrentDate());
        convertCoinService.save(convertCoin);
        return success();
    }

    @Override
    public MessageResult detail(String coinUnit) {
        ConvertCoin convertCoin = convertCoinService.findByCoinUnit(coinUnit);
        notNull(convertCoin, "validate Coin.Unit!");
        return success(convertCoin);
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel) {
        if (pageModel.getProperty() == null) {
            List<String> list = new ArrayList<>();
            list.add("createTime");
            List<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setProperty(list);
            pageModel.setDirection(directions);
        }
        Page<ConvertCoin> pageResult = convertCoinService.findAll(null, pageModel.getPageable());
        return success(pageResult);
    }

    @Override
    public MessageResult pageQueryOrder(PageModel pageModel, ConvertOrderScreen screen) {
        if (pageModel.getDirection() == null && pageModel.getProperty() == null) {
            ArrayList<Sort.Direction> directions = new ArrayList<>();
            directions.add(Sort.Direction.DESC);
            pageModel.setDirection(directions);
            List<String> property = new ArrayList<>();
            property.add("createTime");
            pageModel.setProperty(property);
        }
        List<BooleanExpression> booleanExpressions = new ArrayList<>();

        if (screen.getMemberId() != null) {
            booleanExpressions.add(QConvertOrder.convertOrder.memberId.eq(screen.getMemberId()));
        }
        String fromUnit = screen.getFromUnit();
        if (!StringUtils.isBlank(fromUnit)) {
            booleanExpressions.add(QConvertOrder.convertOrder.fromUnit.eq(fromUnit));
        }
        String toUnit = screen.getToUnit();
        if (!StringUtils.isBlank(toUnit)) {
            booleanExpressions.add(QConvertOrder.convertOrder.toUnit.eq(toUnit));
        }
        if (screen.getStartTime() != null) {
            booleanExpressions.add(QConvertOrder.convertOrder.createTime.goe(screen.getStartTime()));
        }
        if (screen.getEndTime() != null) {
            booleanExpressions.add(QConvertOrder.convertOrder.createTime.loe(screen.getEndTime()));
        }
        Predicate predicate = PredicateUtils.getPredicate(booleanExpressions);
        Page<ConvertOrder> pageListMapResult = convertOrderService.findAll(predicate, pageModel.getPageable());
        return success(pageListMapResult);
    }
}
