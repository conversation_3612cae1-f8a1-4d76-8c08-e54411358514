package com.icetea.lotus.service.impl.activity;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.spot.MemberSignRecord;
import com.icetea.lotus.model.screen.MemberSignRecordScreen;
import com.icetea.lotus.model.vo.MemberSignRecordVO;
import com.icetea.lotus.service.MemberSignRecordService;
import com.icetea.lotus.service.activity.MemberSignRecordedService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * The type Member sign record service.
 */
@Service
@RequiredArgsConstructor
public class MemberSignRecordServiceImpl extends BaseController implements MemberSignRecordedService {

    private final MemberSignRecordService service;

    @Override
    public MessageResult pageQuery(MemberSignRecordScreen screen, PageModel pageModel) {
        Page<MemberSignRecord> source = service.findAllScreen(screen, pageModel);
        Page<MemberSignRecordVO> page = source.map(MemberSignRecordVO::getMemberSignRecordVO);
        return success(page);
    }
}
