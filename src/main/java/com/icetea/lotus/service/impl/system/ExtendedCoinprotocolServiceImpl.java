package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Coinprotocol;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedCoinprotocolService;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * The type Extended coinprotocol service.
 */
@Slf4j
@Service
public class ExtendedCoinprotocolServiceImpl extends BaseAdminController implements ExtendedCoinprotocolService {

    private final CoinprotocolService coinprotocolService;
    private final RedisTemplate<String, String> redisTemplate;

    public ExtendedCoinprotocolServiceImpl(BaseAdminService baseAdminService, CoinprotocolService coinprotocolService, RedisTemplate<String, String> redisTemplate) {
        super(baseAdminService);
        this.coinprotocolService = coinprotocolService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public MessageResult pageQuery(PageModel pageModel) {
        BooleanExpression predicate = null;

        Page<Coinprotocol> all = coinprotocolService.findAll(predicate, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult merge(Coinprotocol coinprotocol) {
        MessageResult result;
        // Check if the query exists
        Coinprotocol one = coinprotocolService.findByProtocol(coinprotocol.getProtocol());
        if (coinprotocol.getId() != null) {
            if (one != null && !one.getId().equals(coinprotocol.getId())) {
                result = error("The current protocol already exists");
                return result;
            }
        } else if (one != null) {
            result = error("The current protocol already exists");
            return result;
        }

        // Delete the redis cache
        redisTemplate.delete("coinprotocol");

        coinprotocol = coinprotocolService.save(coinprotocol);

        result = success("Operation is successful");
        result.setData(coinprotocol);
        return result;
    }

    /**
     * Retrieves all coin protocols.
     *
     * @return MessageResult containing the list of all coin protocols
     */
    @Override
    public MessageResult list() {
        try {
            return success(coinprotocolService.list());
        } catch (Exception e) {
            log.error("Failed to get coin protocols", e);
            return MessageResult.error("Failed to get coin protocols");
        }
    }
}
