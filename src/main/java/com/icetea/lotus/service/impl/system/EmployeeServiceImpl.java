package com.icetea.lotus.service.impl.system;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constant.FirstTimeLoginStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.core.Convert;
import com.icetea.lotus.core.Encrypt;
import com.icetea.lotus.core.Menu;
import com.icetea.lotus.dto.KeycloakUserDTO;
import com.icetea.lotus.dto.request.LoginRequest;
import com.icetea.lotus.dto.response.KeycloakLoginResponse;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Department;
import com.icetea.lotus.entity.spot.QAdmin;
import com.icetea.lotus.entity.spot.SysRole;
import com.icetea.lotus.service.AdminService;
import com.icetea.lotus.service.DepartmentService;
import com.icetea.lotus.service.EmployeeService;
import com.icetea.lotus.service.KeycloakService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.SysPermissionService;
import com.icetea.lotus.service.SysRoleService;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import freemarker.template.Configuration;
import freemarker.template.Template;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import jakarta.mail.internet.MimeMessage;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeServiceImpl extends BaseController implements EmployeeService {
    @Value("${spark.system.md5.key}")
    private String md5Key;

    private final SysRoleService sysRoleService;

    private final AdminService adminService;

    private final DepartmentService departmentService;
    private final ObjectMapper objectMapper;
    private final LocaleMessageSourceService messageSourceService;
    @Resource
    private SysPermissionService sysPermissionService;

    private final JavaMailSender javaMailSender;

    private final KeycloakService keycloakService;


    @Value("${spring.mail.username}")
    private String from;
    @Value("${spark.system.host}")
    private String host;
    @Value("${spark.system.name}")
    private String company;

    @Value("${spark.system.admins}")
    private String admins;

    private static final String LOGIN_FAILED = "Login failed";


    /**
     * Login for admin
     *
     * @param loginRequest request data
     * @return messsage
     */
    @Override
    public MessageResult adminLogin(LoginRequest loginRequest) {
        String username = loginRequest.getUsername();
        String password = loginRequest.getPassword();

        // check request
        MessageResult result = checkLoginRequest(username, password);
        if (result.getCode() != 0) {
            return result;
        }

        try {
            // check data in db
            Admin admin = adminService.findAdminByUsername(username).orElse(null);
            if (admin == null) {
                return error("Username or password is not correct");
            }

            // check password
            String adminPassword = admin.getPassword();
            String passwordRequest = Encrypt.MD5(password + md5Key);
            boolean checkPasswordMatch = passwordRequest.equalsIgnoreCase(adminPassword);
            if (!(checkPasswordMatch)) {
                return error("Username or password is not correct");
            }

            // call to key cloak to login
            var keycloakResponse = keycloakService.login(username, password).getBody();
            if (!(keycloakResponse instanceof Map<?, ?>)) {
                return error(LOGIN_FAILED);
            }

            Map<String, Object> resultMap = new HashMap<>();

            List<Menu> list;
            if ("root".equalsIgnoreCase(admin.getUsername())) {
                list = sysRoleService.toMenus(sysPermissionService.findAll(), 0L);
            } else {
                list = sysRoleService.toMenus(sysRoleService.getPermissions(admin.getRoleId()), 0L);
            }

            resultMap.put("authToken", keycloakResponse);
            resultMap.put("permissions", list);
            resultMap.put("admin", admin);
            // check last login
            if (admin.getFirstTimeLoginStatus().equals(FirstTimeLoginStatus.NEW)) {
                return success("first time login. Please change password first", resultMap.get("authToken"));
            }
            return success("Login successful", resultMap);
        } catch (Exception e) {
            log.error(LOGIN_FAILED, e);
            return error("Failed to send or save the verification code on your phone");
        }

    }

    private MessageResult checkLoginRequest(String username, String password) {
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            return error("Username or password is empty");
        }
        return success();
    }

    @Override
    public MessageResult doLogin(String username, String password, String phone, String code, boolean rememberMe, HttpServletRequest request) {

        return success("Login successful", null);

    }

    @Override
    public CompletableFuture<MessageResult> sendEmailMsg(String email, String msg, String subject) {
        try {
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = null;
            helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(from);
            helper.setTo(email);
            helper.setSubject(company + "-" + subject);
            Map<String, Object> model = new HashMap<>(16);
            model.put("msg", msg);
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_26);
            cfg.setClassForTemplateLoading(this.getClass(), "/templates");
            Template template = cfg.getTemplate("simpleMessage.ftl");
            String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
            helper.setText(html, true);

            // Send an email
            javaMailSender.send(mimeMessage);
            log.info("send email for {},content:{}", email, html);
            return CompletableFuture.completedFuture(success());
        } catch (Exception e) {
            return CompletableFuture.completedFuture(error("send email error"));
        }

    }

    @Override
    public MessageResult valiatePhoneCode(HttpServletRequest request) {
        String username = Convert.strToStr(request(request, "username"), "");
        String password = Convert.strToStr(request(request, "password"), "");
        String captcha = Convert.strToStr(request(request, "captcha"), "");
        if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
            return error("User name or Password cannot be empty");
        }
        HttpSession session = request.getSession();
        request.getSession().setAttribute("test", "123456789");
        log.info("QQ:" + request.getSession().getAttribute("test"));
        if (StringUtils.isBlank(captcha)) {
            return error("The verification code cannot be empty");
        }
        String adminLogin = "ADMIN_LOGIN";
        log.info("Verification code Client:" + captcha);

        String sss = (String) session.getAttribute("CAPTCHA_" + adminLogin);
        log.info("Verification code Session:" + sss);
        password = Encrypt.MD5(password + md5Key);
        log.info("==================>" + password);
        Admin admin = adminService.login(username, password);
        if (admin == null) {
            return error("Username or password does not exist");
        } else {
            try {
                request.getSession().setAttribute("username", username);
                request.getSession().setAttribute("password", password);
                request.getSession().setAttribute("phone", admin.getMobilePhone());

                return success("", admin.getMobilePhone());
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            return error("Send or save mobile phone verification code failed");
        }

    }

    @Override
    public MessageResult logout(RestTemplate restTemplate, String refreshToken) {
        ResponseEntity<String> response = keycloakService.logout(refreshToken);
        if (response.getStatusCode().is2xxSuccessful()) {
            // Clear local security context
            SecurityContextHolder.clearContext();
            log.info("Successfully logged out in Keycloak");
            return success("Logout successful");

        } else {
            return error("Logout failed");
        }
    }

    @Override
    public MessageResult addAdmin(Admin admin, Long departmentId) {
        Assert.notNull(departmentId, "Please select a department");
        Department department = departmentService.findOne(departmentId);
        admin.setDepartment(department);
        String password;
        if (admin.getId() != null) {
            Admin admin1 = adminService.findOne(admin.getId());
            admin.setLastLoginIp(admin1.getLastLoginIp());
            admin.setLastLoginTime(admin1.getLastLoginTime());
            // Change password if the password is not null
            if (StringUtils.isNotBlank(admin.getPassword())) {
                password = Encrypt.MD5(admin.getPassword() + md5Key);
            } else {
                password = admin1.getPassword();
            }
        } else {
            // Here is a new addition
            Admin a = adminService.findByUsername(admin.getUsername());
            if (a != null) {
                return error("The username already exists!");
            }
            if (StringUtils.isBlank(admin.getPassword())) {
                return error("The password cannot be empty");
            }
            password = Encrypt.MD5(admin.getPassword() + md5Key);
        }
        // Create a new user in Keycloak
        try {
            KeycloakUserDTO keycloakAdmin = new KeycloakUserDTO();
            keycloakAdmin.setUsername(admin.getUsername());
            keycloakAdmin.setEmail(admin.getEmail());
            if (admin.getRealName() != null) {
                keycloakAdmin.setFirstName(admin.getRealName());
                keycloakAdmin.setLastName(admin.getRealName());
            }
            keycloakAdmin.setPassword(admin.getPassword());
            keycloakAdmin.setMobilePhone(admin.getMobilePhone());
            String keycloakUserId = keycloakService.createKeycloakUserAndGetId(keycloakAdmin);
            keycloakService.assignRoleToUser(keycloakUserId, "admin");
        } catch (Exception e) {
            return error("Failed to create user in Keycloak");
        }
        admin.setPassword(password);
        adminService.saveAdmin(admin);
        return success("The operation was successful");

    }

    @Override
    public MessageResult findAllAdminUser(PageModel pageModel, String searchKey) {
        BooleanExpression predicate = QAdmin.admin.username.ne("root");
        if (StringUtils.isNotBlank(searchKey)) {
            predicate.and(QAdmin.admin.email.like(searchKey)
                    .or(QAdmin.admin.realName.like(searchKey))
                    .or(QAdmin.admin.mobilePhone.like(searchKey))
                    .or(QAdmin.admin.username.like(searchKey)));
        }
        Page<Admin> all = adminService.findAll(predicate, pageModel.getPageable());
        for (Admin admin : all.getContent()) {
            SysRole role = sysRoleService.findOne(admin.getRoleId());
            admin.setRoleName(role.getRole());
        }
        return success(all);

    }

    @Override
    public MessageResult updatePassword(Long id, String lastPassword, String newPassword) {
        LocalDateTime localDateTime = LocalDateTime.now();
        Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        if (id == null) {
            return error("Admin id must not be null");
        }
        if (lastPassword.isBlank()) {
            return error("Last password cannot be empty");
        }
        if (newPassword.isBlank()) {
            return error("New password cannot be empty");
        }
        Admin admin = adminService.findOne(id);
        if (admin == null) {
            return error("Admin not found");
        }
        lastPassword = Encrypt.MD5(lastPassword + md5Key);
        if (!(lastPassword.equalsIgnoreCase(admin.getPassword()))) {
            return error("Wrong password");
        }
        admin.setPassword(Encrypt.MD5(newPassword + md5Key));
        admin.setLastLoginTime(date);
        admin.setFirstTimeLoginStatus(FirstTimeLoginStatus.ACTIVE);
        adminService.save(admin);

        //update in keycloak
        KeycloakUserDTO keycloakAdmin = new KeycloakUserDTO();
        keycloakAdmin.setUsername(admin.getUsername());
        keycloakAdmin.setEmail(admin.getEmail());
        if (admin.getRealName() != null) {
            keycloakAdmin.setFirstName(admin.getRealName());
            keycloakAdmin.setLastName(admin.getRealName());
        }
        keycloakAdmin.setPassword(newPassword);
        keycloakAdmin.setMobilePhone(admin.getMobilePhone());

        // get user
        String memberId = keycloakService.findUserIdByUsername(admin.getUsername());
        if (memberId == null) {
            return error("Admin not found on keycloak");
        }

        keycloakService.updateKeycloakUser(memberId, keycloakAdmin);
        return MessageResult.success("The password was changed");
    }

    @Override
    public MessageResult resetPassword(Long id) {
        Assert.notNull(id, "admin id cannot be null");
        Admin admin = adminService.findOne(id);
        admin.setPassword(Encrypt.MD5("123456" + md5Key));
        adminService.save(admin);
        return MessageResult.success("The password is reset successfully, and the default password is 123456");

    }

    @Override
    public MessageResult adminDetail(Long id) {
        if (id == null) {
            return error("id cannot be null");
        }
        try {
            Map<String, Object> map = adminService.findAdminDetail(id);
            MessageResult result = success();
            result.setData(map);
            return result;
        } catch (Exception e) {
            return error("Admin not found");
        }
    }

    @Override
    public MessageResult deletes(Long[] ids) {
        adminService.deletes(ids);
        return MessageResult.success("The batch deletion is successful");

    }

    @Override
    public MessageResult refreshToken(String refreshToken) {
        if (refreshToken == null || refreshToken.isEmpty()) {
            return error("Refresh token cannot be empty");
        }
        Map<String, Object> keycloakResponse = (Map<String, Object>) keycloakService.refreshToken(refreshToken).getBody();
        if (ObjectUtils.isEmpty(keycloakResponse)) {
            return error(LOGIN_FAILED);
        }

        KeycloakLoginResponse keycloakLoginResponse = objectMapper.convertValue(keycloakResponse, KeycloakLoginResponse.class);
        log.info("loginInfo==> KeycloakLoginResponse = {}", keycloakLoginResponse);
        return success(keycloakLoginResponse);

    }
}
