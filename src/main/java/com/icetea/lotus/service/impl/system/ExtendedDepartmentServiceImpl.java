package com.icetea.lotus.service.impl.system;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.Department;
import com.icetea.lotus.service.DepartmentService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedDepartmentService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * The type Extended department service.
 */
@Slf4j
@Service
public class ExtendedDepartmentServiceImpl extends BaseAdminController implements ExtendedDepartmentService {

    private final DepartmentService departmentService;

    public ExtendedDepartmentServiceImpl(BaseAdminService baseAdminService, DepartmentService departmentService) {
        super(baseAdminService);
        this.departmentService = departmentService;
    }

    @Override
    public MessageResult save(Department department) {
        if (department.getId() != null) {
            department.setCreateTime(departmentService.findOne(department.getId()).getCreateTime());
        }
        departmentService.save(department);
        return success();
    }

    @Override
    public MessageResult detail(Long departmentId) {
        Department department = departmentService.getDepartmentDetail(departmentId);
        return success(department);
    }


    @Override
    public MessageResult allDepartmrnt(PageModel pageModel) {
        Page<Department> all = departmentService.findAll(null, pageModel.getPageable());
        return success(all);
    }

    @Override
    public MessageResult deletes(Long id) {
        return departmentService.deletes(id);
    }
}
