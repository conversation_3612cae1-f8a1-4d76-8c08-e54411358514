package com.icetea.lotus.service.finance;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.RechargeScreen;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

public interface ExtendedRechargeService {
    MessageResult coinList();

    MessageResult protocolList();

    MessageResult pageQuery(PageModel pageModel, RechargeScreen rechargeScreen, HttpServletResponse response) throws IOException;
}
