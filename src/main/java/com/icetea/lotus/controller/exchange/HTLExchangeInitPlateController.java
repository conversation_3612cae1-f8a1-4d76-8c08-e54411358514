package com.icetea.lotus.controller.exchange;

import com.icetea.lotus.entity.spot.InitPlate;
import com.icetea.lotus.service.exchange.HTLExchangeInitPlateService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("HTL_plate")
@Slf4j
@RequiredArgsConstructor
public class HTLExchangeInitPlateController {

    private final HTLExchangeInitPlateService htlExchangeInitPlateService;

    /**
     * Query the list of all HTL exchange initial plate records.
     *
     * @return MessageResult containing the list of HTL exchange initial plates.
     * @throws Exception if any error occurs during the query.
     */
    @PreAuthorize("hasRole('exchange:htl-init-plate:query')")
    @PostMapping("query")
    public MessageResult queryExchangeInitPlate()throws Exception{
        return htlExchangeInitPlateService.queryExchangeInitPlate();
    }

    /**
     * Retrieve details of a specific HTL exchange initial plate by ID.
     *
     * @param id The ID of the HTL initial plate record to retrieve.
     * @return MessageResult containing detailed information of the HTL initial plate.
     * @throws Exception if any error occurs during the retrieval.
     */
    @PreAuthorize("hasRole('exchange:htl-init-plate:detail')")
    @GetMapping("detail/{id}")
    public MessageResult queryDetailExchangeInitPlate(@PathVariable("id")long id)throws Exception{
        return htlExchangeInitPlateService.queryDetailExchangeInitPlate(id);
    }

    /**
     * Delete a specific HTL exchange initial plate record by ID.
     *
     * @param id The ID of the HTL initial plate record to delete.
     * @return MessageResult indicating the result of the delete operation.
     * @throws Exception if any error occurs during deletion.
     */
    @PreAuthorize("hasRole('exchange:htl-init-plate:delete')")
    @GetMapping("delete/{id}")
    public MessageResult deleteExchangeInitPlate(@PathVariable("id")long id)throws Exception{
        return htlExchangeInitPlateService.deleteExchangeInitPlate(id);
    }

    /**
     * Update an existing HTL exchange initial plate record.
     *
     * @param initPlate The InitPlate object containing updated values.
     * @return MessageResult indicating the result of the update operation.
     * @throws Exception if any error occurs during the update.
     */
    @PreAuthorize("hasRole('exchange:htl-init-plate:update')")
    @PostMapping("update")
    public MessageResult updateExchangeInitPlate(InitPlate initPlate)throws Exception{
        return htlExchangeInitPlateService.updateExchangeInitPlate(initPlate);
    }

}
