package com.icetea.lotus.controller.exchange;

import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.exchange.ExtendedExchangeOrderService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/exchange/exchange-order-Mine-Detail")
@Slf4j
public class ExchangeOrderMineDetailController extends BaseAdminController{

    private final ExtendedExchangeOrderService extendedExchangeOrderService;

    public ExchangeOrderMineDetailController(BaseAdminService baseAdminService, ExtendedExchangeOrderService extendedExchangeOrderService) {
        super(baseAdminService);
        this.extendedExchangeOrderService = extendedExchangeOrderService;
    }

    /**
     * Retrieve all available trading pair symbols.
     *
     * @return MessageResult containing the list of all trading pair symbols.
     */
    @RequestMapping(value = "/symbol",method = RequestMethod.GET)
    public MessageResult findAllSymbol(){
        return extendedExchangeOrderService.findAllSymbol();

    }

    /**
     * Paginated search for user's exchange order history using Elasticsearch with optional filters.
     *
     * @param memberId (Optional) The ID of the member.
     * @param phone (Optional) The phone number associated with the member.
     * @param exchangeCoin (Optional) The trading pair symbol (e.g., BTC/USDT).
     * @param startTime (Optional) Filter orders starting from this time (format: yyyy-MM-dd HH:mm:ss).
     * @param endTime (Optional) Filter orders up to this time (format: yyyy-MM-dd HH:mm:ss).
     * @param page The page number for pagination.
     * @param pageSize The number of records per page.
     * @return MessageResult containing the filtered and paginated list of exchange orders.
     */
    @RequestMapping(value = "/list-es-Page",method = RequestMethod.POST)
    public MessageResult getExchangeOrderMineListByEs(@RequestParam(value = "memberId",required = false)Long memberId,
                                                      @RequestParam(value = "phone",required = false)String phone,
                                                      @RequestParam(value = "exchangeCoin",required = false)String exchangeCoin,
                                                      @RequestParam(value = "startTime",required = false)String startTime,
                                                      @RequestParam(value = "endTime",required = false)String endTime,
                                                      @RequestParam(value = "page") int page,
                                                      @RequestParam(value = "pageSize") int pageSize){
        return extendedExchangeOrderService.getExchangeOrderMineListByEs(memberId, phone, exchangeCoin, startTime, endTime, page, pageSize);

    }

}
