package com.icetea.lotus.controller.exchange;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.model.screen.ExchangeOrderScreen;
import com.icetea.lotus.model.screen.ExchangeTradeScreen;
import com.icetea.lotus.service.exchange.ExtendedExchangeOrderService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;


@RestController
@RequestMapping("exchange/exchange-order")
@RequiredArgsConstructor
public class ExchangeOrderController {

    private final ExtendedExchangeOrderService extendedExchangeOrderService;


    /**
     * Retrieve all exchange orders.
     *
     * @return MessageResult containing the list of all exchange orders.
     */
    @PreAuthorize("hasRole('exchange:exchange-order:all')")
    @PostMapping("all")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Find all exchangeOrders")
    public MessageResult all() {
        return extendedExchangeOrderService.all();
    }

    /**
     * Get the details of a specific exchange order by ID.
     *
     * @param id The ID of the exchange order.
     * @return MessageResult containing the details of the specified exchange order.
     */
    @PreAuthorize("hasRole('exchange:exchange-order:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "exchangeOrder details")
    public MessageResult detail(String id) {
        return extendedExchangeOrderService.detail(id);

    }

    /**
     * Paginated search for exchange orders with optional filtering.
     *
     * @param pageModel The pagination and sorting information.
     * @param screen    The filter criteria for exchange orders.
     * @param response  The HTTP response used for writing additional data if needed.
     * @return MessageResult containing the filtered and paginated list of exchange orders.
     * @throws IOException if there is an error writing to the response.
     */
    @PreAuthorize("hasRole('exchange:exchange-order:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Pagination search for exchangeOrder")
    public MessageResult page(
            PageModel pageModel,
            ExchangeOrderScreen screen,
            HttpServletResponse response) throws IOException {
        return extendedExchangeOrderService.page(pageModel, screen, response);

    }

    /**
     * Get entrust (trade) details for exchange orders based on filter criteria.
     *
     * @param screen    The filter criteria for trades.
     * @param pageModel The pagination and sorting information.
     * @return MessageResult containing paginated entrust (trade) details.
     */
    @PreAuthorize("hasRole('exchange:exchange-order:entrust-details')")
    @PostMapping("entrust-details")
    public MessageResult entrustDetails(ExchangeTradeScreen screen, PageModel pageModel) {
       /* ExchangeOrder
        StringBuilder headSql = new StringBuilder("select orderId as IF(a.direction=0,buyOrderId,sellOrderId)");

        StringBuilder headCount = new StringBuilder("select count(*) ");*/
        return null;
    }

    /**
     * Export exchange orders to an Excel file based on filter criteria.
     *
     * @param memberId  The ID of the member.
     * @param type      The type of the exchange order.
     * @param symbol    The trading pair symbol (e.g., BTC/USDT).
     * @param status    The status of the exchange order.
     * @param direction The order direction (buy or sell).
     * @param request   The HTTP servlet request.
     * @param response  The HTTP servlet response used to write the Excel file.
     * @return MessageResult indicating the success or failure of the export operation.
     * @throws Exception if an error occurs during export.
     */
    @PreAuthorize("hasRole('exchange:exchange-order:out-excel')")
    @GetMapping("out-excel")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Export exchangeOrder Excel")
    public MessageResult outExcel(
            @RequestParam(value = "memberId") Long memberId,
            @RequestParam(value = "type") ExchangeOrderType type,
            @RequestParam(value = "symbol") String symbol,
            @RequestParam(value = "status") ExchangeOrderStatus status,
            @RequestParam(value = "direction") ExchangeOrderDirection direction,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return extendedExchangeOrderService.outExcel(memberId, type, symbol, status, direction, request, response);

    }


    /**
     * Cancel a specific exchange order by order ID.
     *
     * @param orderId The ID of the order to cancel.
     * @return MessageResult indicating success or failure of the cancellation.
     */
    @PreAuthorize("hasRole('exchange:exchange-order:cancel')")
    @PostMapping("cancel")
    @AccessLog(module = AdminModule.EXCHANGE, operation = "Cancel the commission")
    public MessageResult cancelOrder(String orderId) {

        return extendedExchangeOrderService.cancelOrder(orderId);

    }
}
