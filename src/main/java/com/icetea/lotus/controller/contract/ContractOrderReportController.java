//package com.icetea.lotus.controller.contract;
//
//import com.icetea.lotus.annotation.AccessLog;
//import com.icetea.lotus.constant.AdminModule;
//import com.icetea.lotus.entity.ContractOrderReport;
//import com.icetea.lotus.service.ContractOrderReportService;
//import com.icetea.lotus.util.MessageResult;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.format.annotation.DateTimeFormat;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//
///**
// * Controller quản lý báo cáo giao dịch hợp đồng
// */
//@Slf4j
//@RestController
//@RequestMapping("/contract/order/report")
//public class ContractOrderReportController {
//
//    @Autowired
//    private ContractOrderReportService contractOrderReportService;
//
//    /**
//     * Lấy báo cáo giao dịch theo orderId
//     *
//     * @param orderId ID của lệnh
//     * @return MessageResult
//     */
//    @PostMapping("/by-order-id")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy báo cáo giao dịch theo orderId")
//    public MessageResult getReportByOrderId(@RequestParam String orderId) {
//        log.info("Lấy báo cáo giao dịch theo orderId, orderId = {}", orderId);
//
//        try {
//            ContractOrderReport report = contractOrderReportService.getReportByOrderId(orderId);
//            if (report == null) {
//                return MessageResult.error("Không tìm thấy báo cáo giao dịch");
//            }
//            return MessageResult.success("Lấy báo cáo giao dịch thành công").setData(report);
//        } catch (Exception e) {
//            log.error("Lấy báo cáo giao dịch theo orderId thất bại, orderId = {}", orderId, e);
//            return MessageResult.error("Lấy báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy danh sách báo cáo giao dịch theo memberId
//     *
//     * @param memberId ID của thành viên
//     * @return MessageResult
//     */
//    @PostMapping("/by-member-id")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy danh sách báo cáo giao dịch theo memberId")
//    public MessageResult getReportsByMemberId(@RequestParam Long memberId) {
//        log.info("Lấy danh sách báo cáo giao dịch theo memberId, memberId = {}", memberId);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsByMemberId(memberId);
//            return MessageResult.success("Lấy danh sách báo cáo giao dịch thành công").setData(reports);
//        } catch (Exception e) {
//            log.error("Lấy danh sách báo cáo giao dịch theo memberId thất bại, memberId = {}", memberId, e);
//            return MessageResult.error("Lấy danh sách báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy danh sách báo cáo giao dịch theo symbol
//     *
//     * @param symbol Ký hiệu của hợp đồng
//     * @return MessageResult
//     */
//    @PostMapping("/by-symbol")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy danh sách báo cáo giao dịch theo symbol")
//    public MessageResult getReportsBySymbol(@RequestParam String symbol) {
//        log.info("Lấy danh sách báo cáo giao dịch theo symbol, symbol = {}", symbol);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsBySymbol(symbol);
//            return MessageResult.success("Lấy danh sách báo cáo giao dịch thành công").setData(reports);
//        } catch (Exception e) {
//            log.error("Lấy danh sách báo cáo giao dịch theo symbol thất bại, symbol = {}", symbol, e);
//            return MessageResult.error("Lấy danh sách báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy danh sách báo cáo giao dịch theo memberId và symbol
//     *
//     * @param memberId ID của thành viên
//     * @param symbol   Ký hiệu của hợp đồng
//     * @return MessageResult
//     */
//    @PostMapping("/by-member-id-and-symbol")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy danh sách báo cáo giao dịch theo memberId và symbol")
//    public MessageResult getReportsByMemberIdAndSymbol(
//            @RequestParam Long memberId,
//            @RequestParam String symbol) {
//        log.info("Lấy danh sách báo cáo giao dịch theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsByMemberIdAndSymbol(memberId, symbol);
//            return MessageResult.success("Lấy danh sách báo cáo giao dịch thành công").setData(reports);
//        } catch (Exception e) {
//            log.error("Lấy danh sách báo cáo giao dịch theo memberId và symbol thất bại, memberId = {}, symbol = {}", memberId, symbol, e);
//            return MessageResult.error("Lấy danh sách báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy danh sách báo cáo giao dịch theo khoảng thời gian hoàn thành lệnh
//     *
//     * @param startTime Thời gian bắt đầu
//     * @param endTime   Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/by-complete-time")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy danh sách báo cáo giao dịch theo khoảng thời gian hoàn thành lệnh")
//    public MessageResult getReportsByCompleteTime(
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy danh sách báo cáo giao dịch theo khoảng thời gian hoàn thành lệnh, startTime = {}, endTime = {}", startTime, endTime);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsByCompleteTimeBetween(startTime, endTime);
//            return MessageResult.success("Lấy danh sách báo cáo giao dịch thành công").setData(reports);
//        } catch (Exception e) {
//            log.error("Lấy danh sách báo cáo giao dịch theo khoảng thời gian hoàn thành lệnh thất bại, startTime = {}, endTime = {}", startTime, endTime, e);
//            return MessageResult.error("Lấy danh sách báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy danh sách báo cáo giao dịch theo memberId và khoảng thời gian hoàn thành lệnh
//     *
//     * @param memberId  ID của thành viên
//     * @param startTime Thời gian bắt đầu
//     * @param endTime   Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/by-member-id-and-complete-time")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy danh sách báo cáo giao dịch theo memberId và khoảng thời gian hoàn thành lệnh")
//    public MessageResult getReportsByMemberIdAndCompleteTime(
//            @RequestParam Long memberId,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy danh sách báo cáo giao dịch theo memberId và khoảng thời gian hoàn thành lệnh, memberId = {}, startTime = {}, endTime = {}", memberId, startTime, endTime);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsByMemberIdAndCompleteTimeBetween(memberId, startTime, endTime);
//            return MessageResult.success("Lấy danh sách báo cáo giao dịch thành công").setData(reports);
//        } catch (Exception e) {
//            log.error("Lấy danh sách báo cáo giao dịch theo memberId và khoảng thời gian hoàn thành lệnh thất bại, memberId = {}, startTime = {}, endTime = {}", memberId, startTime, endTime, e);
//            return MessageResult.error("Lấy danh sách báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy danh sách báo cáo giao dịch theo symbol và khoảng thời gian hoàn thành lệnh
//     *
//     * @param symbol    Ký hiệu của hợp đồng
//     * @param startTime Thời gian bắt đầu
//     * @param endTime   Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/by-symbol-and-complete-time")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy danh sách báo cáo giao dịch theo symbol và khoảng thời gian hoàn thành lệnh")
//    public MessageResult getReportsBySymbolAndCompleteTime(
//            @RequestParam String symbol,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy danh sách báo cáo giao dịch theo symbol và khoảng thời gian hoàn thành lệnh, symbol = {}, startTime = {}, endTime = {}", symbol, startTime, endTime);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsBySymbolAndCompleteTimeBetween(symbol, startTime, endTime);
//            return MessageResult.success("Lấy danh sách báo cáo giao dịch thành công").setData(reports);
//        } catch (Exception e) {
//            log.error("Lấy danh sách báo cáo giao dịch theo symbol và khoảng thời gian hoàn thành lệnh thất bại, symbol = {}, startTime = {}, endTime = {}", symbol, startTime, endTime, e);
//            return MessageResult.error("Lấy danh sách báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy danh sách báo cáo giao dịch theo memberId, symbol và khoảng thời gian hoàn thành lệnh
//     *
//     * @param memberId  ID của thành viên
//     * @param symbol    Ký hiệu của hợp đồng
//     * @param startTime Thời gian bắt đầu
//     * @param endTime   Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/by-member-id-and-symbol-and-complete-time")
//    @PreAuthorize("hasRole('contract:order:report')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy danh sách báo cáo giao dịch theo memberId, symbol và khoảng thời gian hoàn thành lệnh")
//    public MessageResult getReportsByMemberIdAndSymbolAndCompleteTime(
//            @RequestParam Long memberId,
//            @RequestParam String symbol,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy danh sách báo cáo giao dịch theo memberId, symbol và khoảng thời gian hoàn thành lệnh, memberId = {}, symbol = {}, startTime = {}, endTime = {}", memberId, symbol, startTime, endTime);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsByMemberIdAndSymbolAndCompleteTimeBetween(memberId, symbol, startTime, endTime);
//            return MessageResult.success("Lấy danh sách báo cáo giao dịch thành công").setData(reports);
//        } catch (Exception e) {
//            log.error("Lấy danh sách báo cáo giao dịch theo memberId, symbol và khoảng thời gian hoàn thành lệnh thất bại, memberId = {}, symbol = {}, startTime = {}, endTime = {}", memberId, symbol, startTime, endTime, e);
//            return MessageResult.error("Lấy danh sách báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy báo cáo tổng hợp theo khoảng thời gian hoàn thành lệnh
//     *
//     * @param startTime Thời gian bắt đầu
//     * @param endTime   Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/summary")
//    @PreAuthorize("hasRole('contract:order:report:summary')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy báo cáo tổng hợp theo khoảng thời gian hoàn thành lệnh")
//    public MessageResult getSummaryReport(
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy báo cáo tổng hợp theo khoảng thời gian hoàn thành lệnh, startTime = {}, endTime = {}", startTime, endTime);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsByCompleteTimeBetween(startTime, endTime);
//            Map<String, Object> summary = contractOrderReportService.createSummaryReport(reports);
//            return MessageResult.success("Lấy báo cáo tổng hợp thành công").setData(summary);
//        } catch (Exception e) {
//            log.error("Lấy báo cáo tổng hợp theo khoảng thời gian hoàn thành lệnh thất bại, startTime = {}, endTime = {}", startTime, endTime, e);
//            return MessageResult.error("Lấy báo cáo tổng hợp thất bại");
//        }
//    }
//
//    /**
//     * Lấy báo cáo tổng hợp theo memberId và khoảng thời gian hoàn thành lệnh
//     *
//     * @param memberId  ID của thành viên
//     * @param startTime Thời gian bắt đầu
//     * @param endTime   Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/summary-by-member-id")
//    @PreAuthorize("hasRole('contract:order:report:summary')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy báo cáo tổng hợp theo memberId và khoảng thời gian hoàn thành lệnh")
//    public MessageResult getSummaryReportByMemberId(
//            @RequestParam Long memberId,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy báo cáo tổng hợp theo memberId và khoảng thời gian hoàn thành lệnh, memberId = {}, startTime = {}, endTime = {}", memberId, startTime, endTime);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsByMemberIdAndCompleteTimeBetween(memberId, startTime, endTime);
//            Map<String, Object> summary = contractOrderReportService.createSummaryReport(reports);
//            return MessageResult.success("Lấy báo cáo tổng hợp thành công").setData(summary);
//        } catch (Exception e) {
//            log.error("Lấy báo cáo tổng hợp theo memberId và khoảng thời gian hoàn thành lệnh thất bại, memberId = {}, startTime = {}, endTime = {}", memberId, startTime, endTime, e);
//            return MessageResult.error("Lấy báo cáo tổng hợp thất bại");
//        }
//    }
//
//    /**
//     * Lấy báo cáo tổng hợp theo symbol và khoảng thời gian hoàn thành lệnh
//     *
//     * @param symbol    Ký hiệu của hợp đồng
//     * @param startTime Thời gian bắt đầu
//     * @param endTime   Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/summary-by-symbol")
//    @PreAuthorize("hasRole('contract:order:report:summary')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy báo cáo tổng hợp theo symbol và khoảng thời gian hoàn thành lệnh")
//    public MessageResult getSummaryReportBySymbol(
//            @RequestParam String symbol,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy báo cáo tổng hợp theo symbol và khoảng thời gian hoàn thành lệnh, symbol = {}, startTime = {}, endTime = {}", symbol, startTime, endTime);
//
//        try {
//            List<ContractOrderReport> reports = contractOrderReportService.getReportsBySymbolAndCompleteTimeBetween(symbol, startTime, endTime);
//            Map<String, Object> summary = contractOrderReportService.createSummaryReport(reports);
//            return MessageResult.success("Lấy báo cáo tổng hợp thành công").setData(summary);
//        } catch (Exception e) {
//            log.error("Lấy báo cáo tổng hợp theo symbol và khoảng thời gian hoàn thành lệnh thất bại, symbol = {}, startTime = {}, endTime = {}", symbol, startTime, endTime, e);
//            return MessageResult.error("Lấy báo cáo tổng hợp thất bại");
//        }
//    }
//
//    /**
//     * Tạo báo cáo giao dịch cho một lệnh
//     *
//     * @param orderId ID của lệnh
//     * @return MessageResult
//     */
//    @PostMapping("/create")
//    @PreAuthorize("hasRole('contract:order:report:create')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Tạo báo cáo giao dịch cho một lệnh")
//    public MessageResult createReportForOrder(@RequestParam String orderId) {
//        log.info("Tạo báo cáo giao dịch cho một lệnh, orderId = {}", orderId);
//
//        try {
//            ContractOrderReport report = contractOrderReportService.createReportForOrder(orderId);
//            if (report == null) {
//                return MessageResult.error("Tạo báo cáo giao dịch thất bại");
//            }
//            return MessageResult.success("Tạo báo cáo giao dịch thành công").setData(report);
//        } catch (Exception e) {
//            log.error("Tạo báo cáo giao dịch cho một lệnh thất bại, orderId = {}", orderId, e);
//            return MessageResult.error("Tạo báo cáo giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Tạo báo cáo giao dịch cho tất cả lệnh đã hoàn thành
//     *
//     * @return MessageResult
//     */
//    @PostMapping("/create-all")
//    @PreAuthorize("hasRole('contract:order:report:create')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Tạo báo cáo giao dịch cho tất cả lệnh đã hoàn thành")
//    public MessageResult createReportsForCompletedOrders() {
//        log.info("Tạo báo cáo giao dịch cho tất cả lệnh đã hoàn thành");
//
//        try {
//            contractOrderReportService.createReportsForCompletedOrders();
//            return MessageResult.success("Tạo báo cáo giao dịch thành công");
//        } catch (Exception e) {
//            log.error("Tạo báo cáo giao dịch cho tất cả lệnh đã hoàn thành thất bại", e);
//            return MessageResult.error("Tạo báo cáo giao dịch thất bại");
//        }
//    }
//}
