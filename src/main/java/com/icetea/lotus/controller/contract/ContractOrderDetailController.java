//package com.icetea.lotus.controller.contract;
//
//import com.icetea.lotus.annotation.AccessLog;
//import com.icetea.lotus.constant.AdminModule;
//import com.icetea.lotus.entity.ContractOrderDetail;
//import com.icetea.lotus.entity.ContractOrderDetailAggregation;
//import com.icetea.lotus.service.ContractOrderDetailAggregationService;
//import com.icetea.lotus.service.ContractOrderDetailService;
//import com.icetea.lotus.util.DateUtil;
//import com.icetea.lotus.util.MessageResult;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.Page;
//import org.springframework.format.annotation.DateTimeFormat;
//import org.springframework.web.bind.annotation.*;
//
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * Controller quản lý chi tiết lệnh giao dịch hợp đồng
// */
//@Slf4j
//@RestController
//@RequestMapping("/contract/order/detail")
//public class ContractOrderDetailController {
//
//    @Autowired
//    private ContractOrderDetailService contractOrderDetailService;
//
//    @Autowired
//    private ContractOrderDetailAggregationService contractOrderDetailAggregationService;
//
//    /**
//     * Lấy chi tiết lệnh theo orderId
//     * @param orderId ID của lệnh
//     * @return MessageResult
//     */
//    @PostMapping("/by-order-id")
//    @PreAuthorize("hasRole('contract:order:detail')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy chi tiết lệnh theo orderId")
//    public MessageResult getOrderDetailsByOrderId(@RequestParam String orderId) {
//        log.info("Lấy chi tiết lệnh theo orderId, orderId = {}", orderId);
//
//        try {
//            List<ContractOrderDetail> details = contractOrderDetailService.findAllByOrderId(orderId);
//            return MessageResult.success("Lấy chi tiết lệnh thành công").setData(details);
//        } catch (Exception e) {
//            log.error("Lấy chi tiết lệnh theo orderId thất bại, orderId = {}", orderId, e);
//            return MessageResult.error("Lấy chi tiết lệnh thất bại");
//        }
//    }
//
//    /**
//     * Lấy chi tiết lệnh theo khoảng thời gian
//     * @param startTime Thời gian bắt đầu
//     * @param endTime Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/by-time")
//    @PreAuthorize("hasRole('contract:order:detail')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy chi tiết lệnh theo khoảng thời gian")
//    public MessageResult getOrderDetailsByTime(
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy chi tiết lệnh theo khoảng thời gian, startTime = {}, endTime = {}", startTime, endTime);
//
//        try {
//            List<ContractOrderDetail> details = contractOrderDetailService.findAllByTimeBetween(startTime.getTime(), endTime.getTime());
//            return MessageResult.success("Lấy chi tiết lệnh thành công").setData(details);
//        } catch (Exception e) {
//            log.error("Lấy chi tiết lệnh theo khoảng thời gian thất bại, startTime = {}, endTime = {}", startTime, endTime, e);
//            return MessageResult.error("Lấy chi tiết lệnh thất bại");
//        }
//    }
//
//    /**
//     * Tính tổng phí giao dịch theo orderId
//     * @param orderId ID của lệnh
//     * @return MessageResult
//     */
//    @PostMapping("/total-fee")
//    @PreAuthorize("hasRole('contract:order:detail')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Tính tổng phí giao dịch theo orderId")
//    public MessageResult getTotalFeeByOrderId(@RequestParam String orderId) {
//        log.info("Tính tổng phí giao dịch theo orderId, orderId = {}", orderId);
//
//        try {
//            BigDecimal totalFee = contractOrderDetailService.calculateTotalFeeByOrderId(orderId);
//            Map<String, BigDecimal> result = new HashMap<>();
//            result.put("totalFee", totalFee);
//            return MessageResult.success("Tính tổng phí giao dịch thành công").setData(result);
//        } catch (Exception e) {
//            log.error("Tính tổng phí giao dịch theo orderId thất bại, orderId = {}", orderId, e);
//            return MessageResult.error("Tính tổng phí giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Tính tổng khối lượng giao dịch theo orderId
//     * @param orderId ID của lệnh
//     * @return MessageResult
//     */
//    @PostMapping("/total-amount")
//    @PreAuthorize("hasRole('contract:order:detail')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Tính tổng khối lượng giao dịch theo orderId")
//    public MessageResult getTotalAmountByOrderId(@RequestParam String orderId) {
//        log.info("Tính tổng khối lượng giao dịch theo orderId, orderId = {}", orderId);
//
//        try {
//            BigDecimal totalAmount = contractOrderDetailService.calculateTotalAmountByOrderId(orderId);
//            Map<String, BigDecimal> result = new HashMap<>();
//            result.put("totalAmount", totalAmount);
//            return MessageResult.success("Tính tổng khối lượng giao dịch thành công").setData(result);
//        } catch (Exception e) {
//            log.error("Tính tổng khối lượng giao dịch theo orderId thất bại, orderId = {}", orderId, e);
//            return MessageResult.error("Tính tổng khối lượng giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Tính tổng giá trị giao dịch theo orderId
//     * @param orderId ID của lệnh
//     * @return MessageResult
//     */
//    @PostMapping("/total-turnover")
//    @PreAuthorize("hasRole('contract:order:detail')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Tính tổng giá trị giao dịch theo orderId")
//    public MessageResult getTotalTurnoverByOrderId(@RequestParam String orderId) {
//        log.info("Tính tổng giá trị giao dịch theo orderId, orderId = {}", orderId);
//
//        try {
//            BigDecimal totalTurnover = contractOrderDetailService.calculateTotalTurnoverByOrderId(orderId);
//            Map<String, BigDecimal> result = new HashMap<>();
//            result.put("totalTurnover", totalTurnover);
//            return MessageResult.success("Tính tổng giá trị giao dịch thành công").setData(result);
//        } catch (Exception e) {
//            log.error("Tính tổng giá trị giao dịch theo orderId thất bại, orderId = {}", orderId, e);
//            return MessageResult.error("Tính tổng giá trị giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy thống kê giao dịch theo khoảng thời gian
//     * @param startTime Thời gian bắt đầu
//     * @param endTime Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/statistics")
//    @PreAuthorize("hasRole('contract:order:statistics')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy thống kê giao dịch theo khoảng thời gian")
//    public MessageResult getStatistics(
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy thống kê giao dịch theo khoảng thời gian, startTime = {}, endTime = {}", startTime, endTime);
//
//        try {
//            Map<String, Object> statistics = contractOrderDetailAggregationService.getStatistics(startTime.getTime(), endTime.getTime());
//            return MessageResult.success("Lấy thống kê giao dịch thành công").setData(statistics);
//        } catch (Exception e) {
//            log.error("Lấy thống kê giao dịch theo khoảng thời gian thất bại, startTime = {}, endTime = {}", startTime, endTime, e);
//            return MessageResult.error("Lấy thống kê giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy thống kê giao dịch theo symbol và khoảng thời gian
//     * @param symbol Ký hiệu của hợp đồng
//     * @param startTime Thời gian bắt đầu
//     * @param endTime Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/statistics-by-symbol")
//    @PreAuthorize("hasRole('contract:order:statistics')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy thống kê giao dịch theo symbol và khoảng thời gian")
//    public MessageResult getStatisticsBySymbol(
//            @RequestParam String symbol,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy thống kê giao dịch theo symbol và khoảng thời gian, symbol = {}, startTime = {}, endTime = {}", symbol, startTime, endTime);
//
//        try {
//            Map<String, Object> statistics = contractOrderDetailAggregationService.getStatisticsBySymbol(symbol, startTime.getTime(), endTime.getTime());
//            return MessageResult.success("Lấy thống kê giao dịch thành công").setData(statistics);
//        } catch (Exception e) {
//            log.error("Lấy thống kê giao dịch theo symbol và khoảng thời gian thất bại, symbol = {}, startTime = {}, endTime = {}", symbol, startTime, endTime, e);
//            return MessageResult.error("Lấy thống kê giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy thống kê giao dịch theo trang
//     * @param pageNo Số trang
//     * @param pageSize Kích thước trang
//     * @param startTime Thời gian bắt đầu
//     * @param endTime Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/statistics-by-page")
//    @PreAuthorize("hasRole('contract:order:statistics')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy thống kê giao dịch theo trang")
//    public MessageResult getStatisticsByPage(
//            @RequestParam(defaultValue = "0") int pageNo,
//            @RequestParam(defaultValue = "10") int pageSize,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy thống kê giao dịch theo trang, pageNo = {}, pageSize = {}, startTime = {}, endTime = {}", pageNo, pageSize, startTime, endTime);
//
//        try {
//            Page<ContractOrderDetailAggregation> page = contractOrderDetailAggregationService.getStatisticsByPage(pageNo, pageSize, startTime.getTime(), endTime.getTime());
//            return MessageResult.success("Lấy thống kê giao dịch thành công").setData(page);
//        } catch (Exception e) {
//            log.error("Lấy thống kê giao dịch theo trang thất bại, pageNo = {}, pageSize = {}, startTime = {}, endTime = {}", pageNo, pageSize, startTime, endTime, e);
//            return MessageResult.error("Lấy thống kê giao dịch thất bại");
//        }
//    }
//
//    /**
//     * Lấy thống kê phí giao dịch theo khoảng thời gian
//     * @param startTime Thời gian bắt đầu
//     * @param endTime Thời gian kết thúc
//     * @return MessageResult
//     */
//    @PostMapping("/fee-statistics")
//    @PreAuthorize("hasRole('contract:order:fee-statistics')")
//    @AccessLog(module = AdminModule.CONTRACT, operation = "Lấy thống kê phí giao dịch theo khoảng thời gian")
//    public MessageResult getFeeStatistics(
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
//            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
//        log.info("Lấy thống kê phí giao dịch theo khoảng thời gian, startTime = {}, endTime = {}", startTime, endTime);
//
//        try {
//            List<Map<String, Object>> statistics = contractOrderDetailAggregationService.getFeeStatistics(startTime.getTime(), endTime.getTime());
//            return MessageResult.success("Lấy thống kê phí giao dịch thành công").setData(statistics);
//        } catch (Exception e) {
//            log.error("Lấy thống kê phí giao dịch theo khoảng thời gian thất bại, startTime = {}, endTime = {}", startTime, endTime, e);
//            return MessageResult.error("Lấy thống kê phí giao dịch thất bại");
//        }
//    }
//}
