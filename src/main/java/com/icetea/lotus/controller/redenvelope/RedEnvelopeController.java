package com.icetea.lotus.controller.redenvelope;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.RedEnvelope;
import com.icetea.lotus.service.redenvelope.ExtendedRedEnvelopeService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.math.BigDecimal;



@RestController
@RequestMapping("/envelope")
@RequiredArgsConstructor
public class RedEnvelopeController {

	private final ExtendedRedEnvelopeService extendedRedEnvelopeService;


    /**
     * Red envelope pagination list
     *
     * @param pageModel the page model
     * @return message result
     */
    @PreAuthorize("hasRole('envelope:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.REDENVELOPE, operation = "View red envelope list on pages RedEnvelopeController")
    public MessageResult envelopeList(PageModel pageModel) {
		return extendedRedEnvelopeService.envelopeList(pageModel);
    }

    /**
     * Red envelope details
     *
     * @param id the id
     * @return message result
     */
    @PreAuthorize("hasRole('envelope:detail')")
	@GetMapping("{id}/detail")
    @AccessLog(module = AdminModule.REDENVELOPE, operation = "View red envelope details RedEnvelopeController")
    public MessageResult envelopeDetail(@PathVariable Long id) {
		return extendedRedEnvelopeService.envelopeDetail(id);
    }

    /**
     * Receive details page
     *
     * @param envelopeId the envelope id
     * @param pageNo     the page no
     * @param pageSize   the page size
     * @return message result
     */
    @PreAuthorize("hasRole('envelope:receive-detail')")
    @PostMapping("receive-detail")
    @AccessLog(module = AdminModule.REDENVELOPE, operation = "View red envelope collection details RedEnvelopeController")
    public MessageResult envelopeDetailList(@RequestParam(value = "envelopeId", defaultValue = "0") Long envelopeId,
    		@RequestParam(value = "pageNo", defaultValue = "0") Integer pageNo,
    		@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
		return extendedRedEnvelopeService.envelopeDetailList(envelopeId, pageNo, pageSize);
    }

    /**
     * Create a new red envelope
     *
     * @param redEnvelope the red envelope
     * @return message result
     */
    @PreAuthorize("hasRole('envelope:add')")
    @PostMapping("add")
    @AccessLog(module = AdminModule.REDENVELOPE, operation = "Added red envelope information RedEnvelopeController")
    public MessageResult addRedEnvelope(
            @Valid RedEnvelope redEnvelope) {

		return extendedRedEnvelopeService.addRedEnvelope(redEnvelope);
    }

    /**
     * Modify the red envelope
     *
     * @param id           the id
     * @param type         the type
     * @param invite       the invite
     * @param unit         the unit
     * @param maxRand      the max rand
     * @param totalAmount  the total amount
     * @param count        the count
     * @param logoImage    the logo image
     * @param bgImage      the bg image
     * @param name         the name
     * @param detail       the detail
     * @param expiredHours the expired hours
     * @param state        the state
     * @return message result
     */
    @PreAuthorize("hasRole('envelope:modify')")
    @PostMapping("modify")
    @AccessLog(module = AdminModule.REDENVELOPE, operation = "Added red envelope information RedEnvelopeController")
    public MessageResult modifyRedEnvelope(
    		@RequestParam("id") Long id,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "invite", required = false) Integer invite,
            @RequestParam(value = "unit", required = false) String unit,
            @RequestParam(value = "maxRand", required = false) BigDecimal maxRand,
            @RequestParam(value = "totalAmount", required = false) BigDecimal totalAmount,
            @RequestParam(value = "count", required = false) Integer count,
            @RequestParam(value = "logoImage", required = false) String logoImage,
            @RequestParam(value = "bgImage", required = false) String bgImage,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "detail", required = false) String detail,
            @RequestParam(value = "expiredHours", required = false) Integer expiredHours,
            @RequestParam(value = "state", required = false) Integer state) {

		return extendedRedEnvelopeService.modifyRedEnvelope(id, type, invite, unit, maxRand, totalAmount, count, logoImage, bgImage, name, detail, expiredHours, state);
    }
}
