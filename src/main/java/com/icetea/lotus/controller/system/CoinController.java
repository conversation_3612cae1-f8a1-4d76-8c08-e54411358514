package com.icetea.lotus.controller.system;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.request.TokenInfoRequest;
import com.icetea.lotus.dto.request.TokenListRequest;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.system.ExtendedCoinService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

import javax.validation.Valid;
import java.math.BigDecimal;

import static org.springframework.util.Assert.notNull;


/**
 * The type Coin controller.
 */
@RestController
@RequestMapping("/system/coin")
@Slf4j
public class CoinController extends BaseAdminController {

    private final ExtendedCoinService extendedCoinService;

    private final LocaleMessageSourceService messageSource;

    public CoinController(BaseAdminService baseAdminService, ExtendedCoinService extendedCoinService, LocaleMessageSourceService messageSource) {
        super(baseAdminService);
        this.extendedCoinService = extendedCoinService;
        this.messageSource = messageSource;
    }

    /**
     * Create message result.
     *
     * @param coin          the coin
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coin:create')")
    @PostMapping("create")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create a background currency Coin")
    public MessageResult create(@Valid Coin coin, BindingResult bindingResult) {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }

        return extendedCoinService.create(coin);
    }

    /**
     * Gets all coin name.
     *
     * @return the all coin name
     */
    @PostMapping("all-name")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Find all coin names")
    public MessageResult getAllCoinName() {
        return extendedCoinService.getAllCoinName();
    }

    /**
     * Gets all coin name and unit.
     *
     * @return the all coin name and unit
     */
    @PostMapping("all-name-and-unit")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Find all coin names and units")
    public MessageResult getAllCoinNameAndUnit() {
        return extendedCoinService.getAllCoinNameAndUnit();
    }

    /**
     * Gets all coin name legal.
     *
     * @return the all coin name legal
     */
    @PostMapping("all-name/legal")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Find all coin names")
    public MessageResult getAllCoinNameLegal() {
        return extendedCoinService.getAllCoinNameLegal();
    }

    /**
     * Update message result.
     *
     * @param coin          the coin
     * @param admin         the admin
     * @param code          the code
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coin:update')")
    @PostMapping("update")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Update the background currency Coin")
    public MessageResult update(
            @Valid Coin coin,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin,
            String code,
            BindingResult bindingResult) {

        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        Assert.notNull(admin, messageSource.getMessage("DATA_EXPIRED_LOGIN_AGAIN"));
//        MessageResult checkCode = checkCode(code, SysConstant.ADMIN_COIN_REVISE_PHONE_PREFIX + admin.getMobilePhone()); //NOSONAR
//        if (checkCode.getCode() != 0) { //NOSONAR
//            return checkCode; //NOSONAR
//        } //NOSONAR

        notNull(coin.getName(), "validate coin.name!");

        return extendedCoinService.update(coin);

    }

    /**
     * Detail message result.
     *
     * @param name the name
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coin:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Background Currency Coin Details")
    public MessageResult detail(@RequestParam("name") String name) {
        return extendedCoinService.detail(name);
    }

    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coin:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Pagination search for background currency Coin")
    public MessageResult pageQuery(PageModel pageModel) {

        return extendedCoinService.pageQuery(pageModel);
    }


    /**
     * Out excel message result.
     *
     * @param request  the request
     * @param response the response
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('system:coin:out-excel')")
    @GetMapping("outExcel")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Export background currency Coin Excel")
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {

        return extendedCoinService.outExcel(request, response);
    }

    /**
     * Delete message result.
     *
     * @param name the name
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coin:delete-by-name')")
    @PostMapping("delete/{name}")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Delete the background currency Coin")
    public MessageResult delete(@PathVariable("name") String name) {

        return extendedCoinService.delete(name);
    }

    /**
     * Sets platform coin.
     *
     * @param name the name
     * @return the platform coin
     */
    @PreAuthorize("hasRole('system:coin:set-platform')")
    @PostMapping("set/platform")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Set up platform coins")
    public MessageResult setPlatformCoin(@RequestParam("name") String name) {

        return extendedCoinService.setPlatformCoin(name);
    }


    /**
     * Transfer to the cold wallet and deduct miner fees Coin.minerFee
     *
     * @param admin  Manual operator
     * @param amount Transfers
     * @param unit   Transfer currency unit
     * @param code   Verification code
     * @return message result
     */
    @PreAuthorize("hasRole('system:coin:transfer')")
    @PostMapping("transfer")
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    @AccessLog(module = AdminModule.SYSTEM, operation = "Transfer money to cold wallet")
    public MessageResult transfer(@SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin,
                                  @RequestParam("amount") BigDecimal amount,
                                  @RequestParam("unit") String unit,
                                  @RequestParam(value = "code", defaultValue = "") String code) {

        return extendedCoinService.transfer(admin, amount, unit, code);

    }

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param unit      the unit
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coin:hot-transfer-record:page-query')")
    @PostMapping("/hot-transfer-record/page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Hot wallet transfer to cold wallet record pagination query")
    public MessageResult page(PageModel pageModel, String unit) {
        return extendedCoinService.page(pageModel, unit);
    }


    /**
     * Add new currency wallet record for everyone
     * 1. Batch insert using JDBC
     * 2. The wallet address is not obtained by default, and the user can obtain it independently when recharged.
     *
     * @param coinName the coin name
     * @return message result
     */
    @PreAuthorize("hasRole('system:coin:newwallet')")
    @PostMapping("create-member-wallet")
    public MessageResult createCoin(String coinName) {
        return extendedCoinService.createCoin(coinName);

    }

    /**
     * Need create wallet message result.
     *
     * @param coinName the coin name
     * @return the message result
     */
    @PostMapping("need-create-wallet")
    public MessageResult needCreateWallet(String coinName) {
        return extendedCoinService.needCreateWallet(coinName);
    }

    /**
     * Gets key.
     *
     * @param phone the phone
     * @return the key
     */
    @GetMapping("get-no-check-key")
    public MessageResult getKey(String phone) {
        return extendedCoinService.getKey(phone);
    }


    /**
     * Add partner message result.
     *
     * @param coinId   the coin id
     * @param amount   the amount
     * @param memberId the member id
     * @return the message result
     */
    @PreAuthorize("hasRole('system:coin:addPartner')")
    @PostMapping(value = "add-partner")
    public MessageResult addPartner(@RequestParam("coinId") String coinId, @RequestParam("amount") long amount, @RequestParam("memberId") long memberId) {
        return extendedCoinService.addPartner(coinId, amount, memberId);
    }

    @GetMapping("token-list")
    public MessageResult getTokenListingData(PageModel pageModel,
                                             TokenListRequest tokenListRequest) {
        return extendedCoinService.getTokenListingData(pageModel, tokenListRequest);
    }

    @GetMapping("token-info")
    public MessageResult getTokenInfo(@RequestParam() String networkProtocol,
                                      @RequestParam() String contractAddress) {
        return extendedCoinService.getTokenInfo(networkProtocol, contractAddress);
    }

    @PostMapping("save-token")
    public MessageResult saveToken(@RequestBody @Validated TokenInfoRequest tokenInfoRequest) {
        return extendedCoinService.saveToken(tokenInfoRequest);
    }


}
