package com.icetea.lotus.controller.code;

import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.code.SmsProviderService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

/**
 * The type Sms provider controller.
 */
@RestController
@Slf4j
@RequestMapping("/code/sms-provider")
@RequiredArgsConstructor
public class SmsProviderController {

    private final SmsProviderService smsProviderService;

    /**
     * Currency management Modify currency information mobile verification code
     *
     * @param admin the admin
     * @return message result
     */
    @RequestMapping(value = "/system/coin-revise", method = RequestMethod.POST)
    public MessageResult sendReviseCode(@SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return smsProviderService.sendReviseCode(admin);
    }

    /**
     * Coin Management Coin Settings Mobile Verification Code
     *
     * @param admin the admin
     * @return message result
     */
    @RequestMapping("/exchange-coin-set")
    public MessageResult sendExchangeCoinSet(@SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return smsProviderService.sendExchangeCoinSet(admin);
    }

    /**
     * Transfer to cold wallet mobile verification code
     *
     * @param admin the admin
     * @return message result
     */
    @RequestMapping("/transfer-cold-wallet")
    public MessageResult sendTransfer(@SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return smsProviderService.sendTransfer(admin);
    }

    /**
     * Backstage login Mobile verification code
     *
     * @param phone the phone
     * @return message result
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public MessageResult send(String phone) {
        return smsProviderService.send(phone);
    }


}
