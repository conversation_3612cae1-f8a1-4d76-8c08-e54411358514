package com.icetea.lotus.controller.otc;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.AdvertiseControlStatus;
import com.icetea.lotus.constant.AdvertiseType;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.AdvertiseScreen;
import com.icetea.lotus.service.otc.AdminAdvertiseService;
import com.icetea.lotus.util.MessageResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
 * The type Admin advertise controller.
 *
 * <AUTHOR> @description Backend Advertising Web Layer
 * @date 2019 /1/3 9:42
 */
@RestController
@RequestMapping("/otc/advertise")
@RequiredArgsConstructor
public class AdminAdvertiseController {

    private final AdminAdvertiseService adminAdvertiseService;

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:advertise:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.OTC, operation = "Backstage Advertise Details")
    public MessageResult detail(Long id) {
        return adminAdvertiseService.detail(id);
    }

    /**
     * Statue message result.
     *
     * @param ids    the ids
     * @param status the status
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:advertise:alter-status')")
    @PostMapping("alter-status")
    @AccessLog(module = AdminModule.OTC, operation = "Modify the Advertise Status of Backend Advertise")
    public MessageResult statue(
            @RequestParam(value = "ids") Long[] ids,
            @RequestParam(value = "status") AdvertiseControlStatus status) {
        return adminAdvertiseService.statue(ids, status);
    }

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:advertise:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.OTC, operation = "Pagination Search Backend Advertise")
    public MessageResult page(PageModel pageModel, AdvertiseScreen screen) {
        return adminAdvertiseService.page(pageModel, screen);
    }

    /**
     * Out excel message result.
     *
     * @param startTime     the start time
     * @param endTime       the end time
     * @param advertiseType the advertise type
     * @param realName      the real name
     * @param request       the request
     * @param response      the response
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('otc:advertise:out-excel')")
    @GetMapping("out-excel")
    @AccessLog(module = AdminModule.OTC, operation = "Export background ads Advertise Excel")
    public MessageResult outExcel(
            @RequestParam(value = "startTime", required = false) Date startTime,
            @RequestParam(value = "endTime", required = false) Date endTime,
            @RequestParam(value = "advertiseType", required = false) AdvertiseType advertiseType,
            @RequestParam(value = "realName", required = false) String realName,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return adminAdvertiseService.outExcel(startTime, endTime, advertiseType, realName, request, response);

    }

    // Obtaining conditions
    private List<BooleanExpression> getBooleanExpressionList(
            Date startTime, Date endTime, AdvertiseType advertiseType, String realName) {
        return adminAdvertiseService.getBooleanExpressionList(startTime, endTime, advertiseType, realName);
    }





}
