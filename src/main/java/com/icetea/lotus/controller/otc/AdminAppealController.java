package com.icetea.lotus.controller.otc;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.*;
import com.icetea.lotus.exception.InformationExpiredException;
import com.icetea.lotus.model.screen.AppealScreen;
import com.icetea.lotus.service.otc.AdminAppealService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * The type Admin appeal controller.
 */
@Slf4j
@RestController
@RequestMapping("/otc/appeal")
@RequiredArgsConstructor
public class AdminAppealController {

    private final AdminAppealService adminAppealService;


    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:appeal:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.OTC, operation = "Pagination Find Backend Appeal")
    public MessageResult pageQuery(
            PageModel pageModel,
            AppealScreen screen) {
        return adminAppealService.pageQuery(pageModel, screen);
    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:appeal:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.OTC, operation = "Details of backend appeal Appeal")
    public MessageResult detail(
            @RequestParam(value = "id") Long id) {
        return adminAppealService.detail(id);
    }


    /**
     * Complaint Processed Cancel Order
     *
     * @param appealId the appeal id
     * @param orderSn  the order sn
     * @param banned   the banned
     * @return message result
     * @throws InformationExpiredException the information expired exception
     */
    @PreAuthorize("hasRole('otc:appeal:cancel-order')")
    @RequestMapping(value = "cancel-order")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult cancelOrder(long appealId, String orderSn, @RequestParam(value = "banned", defaultValue = "false") boolean banned) throws InformationExpiredException {
        return adminAppealService.cancelOrder(appealId, orderSn, banned);

    }


    /**
     * Appeal processing Order release (coins release)
     *
     * @param appealId the appeal id
     * @param orderSn  the order sn
     * @param banned   the banned
     * @return message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('otc:appeal:release-coin')")
    @RequestMapping(value = "release-coin")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult confirmRelease(long appealId, String orderSn, @RequestParam(value = "banned", defaultValue = "false") boolean banned) throws Exception {
        return adminAppealService.confirmRelease(appealId, orderSn, banned);

    }

}
