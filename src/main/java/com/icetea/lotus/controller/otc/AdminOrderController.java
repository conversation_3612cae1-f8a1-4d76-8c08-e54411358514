package com.icetea.lotus.controller.otc;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.OrderStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.model.screen.OrderScreen;
import com.icetea.lotus.service.otc.AdminOrderService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;

/**
 * The type Admin order controller.
 *
 * <AUTHOR> @description Franchise transaction order
 * @date 2019 /1/8 15:41
 */
@RestController
@RequestMapping("/otc/order")
@RequiredArgsConstructor
public class AdminOrderController extends BaseController {

    private final AdminOrderService adminOrderService;

    /**
     * All message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:order:all')")
    @PostMapping("all")
    @AccessLog(module = AdminModule.OTC, operation = "All fiat currency trading orders Order")
    public MessageResult all() {
        return adminOrderService.all();
    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:order:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.OTC, operation = "Fiat currency trading order Order details")
    public MessageResult detail(Long id) {
        return adminOrderService.detail(id);
    }

    /**
     * Status message result.
     *
     * @param id     the id
     * @param status the status
     * @return the message result
     */
// Modify order status
    @PreAuthorize("hasRole('otc:order:alert-status')")
    @PatchMapping("{id}/alert-status")
    @AccessLog(module = AdminModule.OTC, operation = "Modify fiat currency trading order Order")
    public MessageResult status(
            @PathVariable("id") Long id,
            @RequestParam("status") OrderStatus status) {
        return adminOrderService.status(id, status);
    }


    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PreAuthorize("hasAnyRole('otc:order:page-query','finance:otc:order:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.OTC, operation = "Paging Search for Franchise Transaction Order")
    public MessageResult page(
            PageModel pageModel,
            OrderScreen screen) {
        return adminOrderService.page(pageModel, screen);
    }


    /**
     * Gets order num.
     *
     * @return the order num
     */
    @PreAuthorize("hasRole('otc:order:get-order-num')")
    @PostMapping("get-order-num")
    @AccessLog(module = AdminModule.OTC, operation = "Backend home page order total number interface")
    public MessageResult getOrderNum() {
        return adminOrderService.getOrderNum();

    }

    /**
     * Parameter fileName is the file name of the export excel file. The format is .xls. It is defined in the OutExcelInterceptor interceptor. Non-essential parameters.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @param response  the response
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('otc:order:out-excel')")
    @GetMapping("out-excel")
    @AccessLog(module = AdminModule.OTC, operation = "Export fiat currency trading orders Order Excel")
    public void outExcel(
            PageModel pageModel,
            OrderScreen screen,
            HttpServletResponse response
            ) throws Exception {

        adminOrderService.outExcel(pageModel, screen, response);

    }


}
