package com.icetea.lotus.controller.otc;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.OtcCoin;
import com.icetea.lotus.service.otc.AdminOtcCoinService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import static org.springframework.util.Assert.isNull;
import static org.springframework.util.Assert.notNull;

/**
 * The type Admin otc coin controller.
 *
 * <AUTHOR> @description otc currency
 * @date 2019 /1/11 13:35
 */
@RestController
@RequestMapping("/otc/otc-coin")
@RequiredArgsConstructor
public class AdminOtcCoinController{

    private final AdminOtcCoinService adminOtcCoinService;

    /**
     * Create message result.
     *
     * @param otcCoin       the otc coin
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:otc-coin:create')")
    @PostMapping("create")
    @AccessLog(module = AdminModule.OTC, operation = "Create otc currency otcCoin")
    public MessageResult create(@Valid OtcCoin otcCoin, BindingResult bindingResult) {
        isNull(otcCoin.getId(), "validate otcCoin.id!");
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }

        return adminOtcCoinService.create(otcCoin);
    }

    /**
     * All message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:otc-coin:all')")
    @PostMapping("all")
    @AccessLog(module = AdminModule.OTC, operation = "All otccurrencies otcCoin")
    public MessageResult all() {
        return adminOtcCoinService.all();
    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:otc-coin:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.OTC, operation = "otccurrency otcCoin details")
    public MessageResult detail(@RequestParam("id") Long id) {
        return adminOtcCoinService.detail(id);
    }

    /**
     * Update message result.
     *
     * @param otcCoin       the otc coin
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:otc-coin:update')")
    @PostMapping("update")
    @AccessLog(module = AdminModule.OTC, operation = "Update otc currency otcCoin")
    public MessageResult update(@Valid OtcCoin otcCoin, BindingResult bindingResult) {
        notNull(otcCoin.getId(), "validate otcCoin.id!");
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return adminOtcCoinService.update(otcCoin);

    }

    /**
     * Deletes message result.
     *
     * @param ids the ids
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:otc-coin:deletes')")
    @PostMapping("deletes")
    @AccessLog(module = AdminModule.OTC, operation = "otccurrency otcCoin Delete")
    public MessageResult deletes(
            @RequestParam(value = "ids") Long[] ids) {
        return adminOtcCoinService.deletes(ids);
    }

    /**
     * Member statistics message result.
     *
     * @param id     the id
     * @param jyRate the jy rate
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:otc-coin:alter-jy-rate')")
    @PostMapping("alter-jy-rate")
    @AccessLog(module = AdminModule.OTC, operation = "Modify the otc currency otcCoin transaction rate")
    public MessageResult memberStatistics(
            @RequestParam("id") Long id,
            @RequestParam("jyRate") BigDecimal jyRate) {
        return adminOtcCoinService.memberStatistics(id, jyRate);

    }

    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
    @PreAuthorize("hasRole('otc:otc-coin:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.OTC, operation = "Pagination search otc currency otcCoin")
    public MessageResult pageQuery(PageModel pageModel) {
        return adminOtcCoinService.pageQuery(pageModel);

    }

    /**
     * Out excel message result.
     *
     * @param request  the request
     * @param response the response
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('otc:otc-coin:out-excel')")
    @GetMapping("out-excel")
    @AccessLog(module = AdminModule.OTC, operation = "Export otc currency otcCoin Excel")
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return adminOtcCoinService.outExcel(request, response);

    }

    /**
     * Get all otc coin units message result.
     *
     * @return the message result
     */
    @PostMapping("all-otc-coin-units")
    public MessageResult getAllOtcCoinUnits(){
        return adminOtcCoinService.getAllOtcCoinUnits();
    }
}
