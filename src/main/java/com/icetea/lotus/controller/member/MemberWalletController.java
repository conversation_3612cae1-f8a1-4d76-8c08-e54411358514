package com.icetea.lotus.controller.member;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;

import com.icetea.lotus.entity.*;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.model.screen.MemberWalletScreen;
import com.icetea.lotus.service.member.ExtendedMemberWalletService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;


/**
 * The type Member wallet controller.
 */
@RestController
@RequestMapping("member/member-wallet")
@Slf4j
@RequiredArgsConstructor
public class MemberWalletController {

    private final ExtendedMemberWalletService extendedMemberWalletService;


    /**
     * Gets balance.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the balance
     */
    @PreAuthorize("hasRole('member:member-wallet:balance')")
    @PostMapping("balance")
    @AccessLog(module = AdminModule.MEMBER, operation = "Balance Management")
    public MessageResult getBalance(
            PageModel pageModel,
            MemberWalletScreen screen) {
        return extendedMemberWalletService.getBalance(pageModel, screen);
    }

    /**
     * Recharge message result.
     *
     * @param admin  the admin
     * @param unit   the unit
     * @param uid    the uid
     * @param amount the amount
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-wallet:recharge')")
    @PostMapping("recharge")
    @AccessLog(module = AdminModule.MEMBER, operation = "Coin recharge management")
    public MessageResult recharge(
    		@SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin,
            @RequestParam("unit") String unit,
            @RequestParam("uid") Long uid,
            @RequestParam("amount") BigDecimal amount) {
        return extendedMemberWalletService.recharge(admin, unit, uid, amount);
    }


    /**
     * Reset address message result.
     *
     * @param unit the unit
     * @param uid  the uid
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-wallet:reset-address')")
    @PostMapping("reset-address")
    @AccessLog(module = AdminModule.MEMBER, operation = "Reset the wallet address")
    public MessageResult resetAddress(String unit, long uid) {
        return extendedMemberWalletService.resetAddress(unit, uid);
    }

    /**
     * Lock wallet message result.
     *
     * @param uid  the uid
     * @param unit the unit
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-wallet:lock-wallet')")
    @PostMapping("lock-wallet")
    @AccessLog(module = AdminModule.MEMBER, operation = "Lock the wallet")
    public MessageResult lockWallet(Long uid, String unit) {
        return extendedMemberWalletService.lockWallet(uid, unit);

    }

    /**
     * Unlock wallet message result.
     *
     * @param uid  the uid
     * @param unit the unit
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-wallet:unlock-wallet')")
    @PostMapping("unlock-wallet")
    @AccessLog(module = AdminModule.MEMBER, operation = "Unlock the wallet")
    public MessageResult unlockWallet(Long uid, String unit) {
        return extendedMemberWalletService.unlockWallet(uid, unit);

    }
}
