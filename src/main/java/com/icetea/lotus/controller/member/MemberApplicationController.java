package com.icetea.lotus.controller.member;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.MemberApplicationScreen;
import com.icetea.lotus.service.member.ExtendedMemberApplicationService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("member/member-application")
@RequiredArgsConstructor
public class MemberApplicationController {

    private final ExtendedMemberApplicationService extendedMemberApplicationService;


    /**
     * All message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-application:all')")
    @PostMapping("all")
    @AccessLog(module = AdminModule.MEMBER, operation = "All Member MemberApplication Certification Information")
    public MessageResult all() {
        return extendedMemberApplicationService.all();

    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-application:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.MEMBER, operation = "Member MemberApplication certification information details")
    public MessageResult detail(@RequestParam("id") Long id) {
        return extendedMemberApplicationService.detail(id);

    }

    /**
     * Query page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-application:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.MEMBER, operation = "Find Member MemberApplication authentication information on pages")
    public MessageResult queryPage(PageModel pageModel, MemberApplicationScreen screen) {
        return extendedMemberApplicationService.queryPage(pageModel, screen);

    }

    /**
     * Pass message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-application:pass')")
    @PatchMapping("{id}/pass")
    @AccessLog(module = AdminModule.MEMBER, operation = "Member MemberApplication certification passes the review")
    public MessageResult pass(@PathVariable("id") Long id) {
        return extendedMemberApplicationService.pass(id);

    }

    /**
     * No pass message result.
     *
     * @param id           the id
     * @param rejectReason the reject reason
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-application:no-pass')")
    @PatchMapping("{id}/no-pass")
    @AccessLog(module = AdminModule.MEMBER, operation = "Member MemberApplication certification does not pass the audit")
    public MessageResult noPass(
            @PathVariable("id") Long id,
            @RequestParam(value = "rejectReason", required = false) String rejectReason) {
        return extendedMemberApplicationService.noPass(id, rejectReason);

    }
    

}
