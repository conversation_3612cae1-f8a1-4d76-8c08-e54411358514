package com.icetea.lotus.controller.member;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.LegalWalletRechargeScreen;
import com.icetea.lotus.service.member.ExtendedLegalWalletRechargeService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("legal-wallet-recharge")
@RequiredArgsConstructor
public class LegalWalletRechargeController {

    private final ExtendedLegalWalletRechargeService extendedLegalWalletRechargeService;

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @GetMapping("page")
    public MessageResult page(
            PageModel pageModel,
            LegalWalletRechargeScreen screen) {
        return extendedLegalWalletRechargeService.page(pageModel, screen);
    }

    /**
     * Id message result.
     *
     * @param id the id
     * @return the message result
     */
    @GetMapping("{id}")
    public MessageResult id(@PathVariable("id") Long id) {
        return extendedLegalWalletRechargeService.id(id);
    }

    /**
     * Pass message result.
     *
     * @param id the id
     * @return the message result
     */
    @PatchMapping("{id}/pass")
    public MessageResult pass(@PathVariable("id") Long id) {
        return extendedLegalWalletRechargeService.pass(id);

    }

    /**
     * No pass message result.
     *
     * @param id the id
     * @return the message result
     */
    @PatchMapping("{id}/no-pass")
    public MessageResult noPass(@PathVariable("id") Long id) {
        return extendedLegalWalletRechargeService.noPass(id);
    }


}
