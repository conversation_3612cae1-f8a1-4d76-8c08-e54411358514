package com.icetea.lotus.controller.member;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.screen.LegalWalletWithdrawScreen;
import com.icetea.lotus.service.member.ExtendedLegalWalletWithdrawService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;



@RestController
@RequestMapping("legal-wallet-withdraw")
@RequiredArgsConstructor
public class LegalWalletWithdrawController {

    private final ExtendedLegalWalletWithdrawService extendedLegalWalletWithdrawService;

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @GetMapping("page")
    public MessageResult page(
            PageModel pageModel,
            LegalWalletWithdrawScreen screen) {

        return extendedLegalWalletWithdrawService.page(pageModel, screen);

    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @GetMapping("{id}")
    public MessageResult detail(@PathVariable("id") Long id) {
        return extendedLegalWalletWithdrawService.detail(id);

    }

    /**
     * Pass message result.
     *
     * @param id the id
     * @return the message result
     */
// Review passed
    @PatchMapping("{id}/pass")
    public MessageResult pass(@PathVariable("id") Long id) {
        return extendedLegalWalletWithdrawService.pass(id);

    }

    /**
     * No pass message result.
     *
     * @param id the id
     * @return the message result
     */
// Failed to pass the review
    @PatchMapping("{id}/no-pass")
    public MessageResult noPass(@PathVariable("id") Long id) {
        return extendedLegalWalletWithdrawService.noPass(id);

    }

    /**
     * Remit message result.
     *
     * @param id                the id
     * @param paymentInstrument the payment instrument
     * @return the message result
     */
// Confirm the payment and upload the payment voucher
    @PatchMapping("{id}/remit")
    public MessageResult remit(
            @PathVariable("id") Long id,
            @RequestParam("paymentInstrument") String paymentInstrument) {

        return extendedLegalWalletWithdrawService.remit(id, paymentInstrument);

    }

}
