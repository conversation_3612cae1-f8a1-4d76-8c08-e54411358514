package com.icetea.lotus.controller.member;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.*;
import com.icetea.lotus.entity.spot.Member;
import com.icetea.lotus.model.screen.MemberScreen;
import com.icetea.lotus.service.member.ExtendedMemberService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * The type Member controller.
 */
@RestController
@RequestMapping("/member")
@Slf4j
@RequiredArgsConstructor
public class MemberController {

    private final ExtendedMemberService extendedMemberService;

    /**
     * All message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('member:all')")
    @PostMapping("all")
    @AccessLog(module = AdminModule.MEMBER, operation = "All Members")
    public MessageResult all() {
        return extendedMemberService.all();

    }

    /**
     * Detail message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.MEMBER, operation = "Member Member details")
    public MessageResult detail(@RequestParam("id") Long id) {
        return extendedMemberService.detail(id);
    }

    /**
     * Delete message result.
     *
     * @param id the id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:delete')")
    @PostMapping("delete")
    @AccessLog(module = AdminModule.MEMBER, operation = "Delete Member Member")
    public MessageResult delete(@RequestParam(value = "id") Long id) {
        return extendedMemberService.delete(id);

    }

    /**
     * Update message result.
     *
     * @param member the member
     * @return the message result
     */
    @PreAuthorize("hasRole('member:update')")
    @PostMapping(value = "update")
    @AccessLog(module = AdminModule.MEMBER, operation = "Update Member Member")
    public MessageResult update(Member member) {
        return extendedMemberService.update(member);

    }

    /**
     * Audit business message result.
     *
     * @param id     the id
     * @param status the status
     * @param detail the detail
     * @return the message result
     */
    @PreAuthorize("hasRole('member:audit-business')")
    @PatchMapping("{id}/audit-business")
    @AccessLog(module = AdminModule.MEMBER, operation = "Member Member Certified Merchant")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult auditBusiness(
            @PathVariable("id") Long id,
            @RequestParam("status") CertifiedBusinessStatus status,
            @RequestParam("detail") String detail) {
        return extendedMemberService.auditBusiness(id, status, detail);

    }

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PreAuthorize("hasRole('member:page-query')")
    @PostMapping("page-query")
    @ResponseBody
    @AccessLog(module = AdminModule.MEMBER, operation = "Find Members on pages")
    public MessageResult page(
            PageModel pageModel,
            MemberScreen screen) {
        return extendedMemberService.page(pageModel, screen);

    }

   /* @PreAuthorize("hasRole('member:audit-business')")
    @PatchMapping("{id}/cancel-business")
    @AccessLog(module = AdminModule.MEMBER, operation = "会员Member取消认证商家")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult cancelBusiness(
            @PathVariable("id") Long id,
            @RequestParam("status") CertifiedBusinessStatus status) {
        Member member = memberService.findOne(id);
        notNull(member, "validate id!");
        //确认是申请取消认证状态
        isTrue(member.getCertifiedBusinessStatus() == CANCEL_AUTH, "validate member certifiedBusinessStatus!");
        //确认传入certifiedBusinessStatus值正确
        isTrue(status == NOT_CERTIFIED || status == VERIFIED, "validate certifiedBusinessStatus!");
        // member.setCertifiedBusinessApplyTime(new Date());//time
        //查询状态为申请取消认证的申请记录
        List<BusinessAuthApply> businessAuthApplyList=businessAuthApplyService.findByMemberAndCertifiedBusinessStatus(member,CANCEL_AUTH);
        if (status == VERIFIED) {
            //不允许取消
            member.setCertifiedBusinessStatus(VERIFIED);//状态改回已认证
            if(businessAuthApplyList!=null&&businessAuthApplyList.size()>0){
                businessAuthApplyList.get(0).setCertifiedBusinessStatus(VERIFIED);
            }
        } else {
            //取消认证的申请通过
            member.setCertifiedBusinessStatus(NOT_CERTIFIED);//未认证
            member.setMemberLevel(MemberLevelEnum.REALNAME);
            if(businessAuthApplyList!=null&&businessAuthApplyList.size()>0){
                businessAuthApplyList.get(0).setCertifiedBusinessStatus(NOT_CERTIFIED);
            }
            //商家认证时收取的保证金退回
            List<DepositRecord> depositRecordList=depositRecordService.findByMemberAndStatus(member,DepositStatusEnum.PAY);
            if(depositRecordList!=null&&depositRecordList.size()>0){
                BigDecimal deposit=BigDecimal.ZERO;
                for(DepositRecord depositRecord:depositRecordList){
                    depositRecord.setStatus(DepositStatusEnum.GET_BACK);
                    deposit=deposit.add(depositRecord.getAmount());
                }
                if(businessAuthApplyList!=null&&businessAuthApplyList.size()>0){
                    BusinessAuthApply businessAuthApply=businessAuthApplyList.get(0);
                    MemberWallet memberWallet=memberWalletService.findByCoinUnitAndMemberId(businessAuthApply.getBusinessAuthDeposit().getCoin().getUnit(),member.getId());
                    memberWallet.setBalance(memberWallet.getBalance().add(deposit));
                }
            }
        }
        memberService.save(member);
        return success();
    }*/

    /**
     * Gets business auth apply.
     *
     * @param id     the id
     * @param status the status
     * @return the business auth apply
     */
    @PreAuthorize("hasRole('member:audit-business')")
    @GetMapping("{id}/business-auth-detail")
    @AccessLog(module = AdminModule.MEMBER, operation = "Query Member Member Application Information")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult getBusinessAuthApply(@PathVariable("id") Long id,
                                              @RequestParam("status") CertifiedBusinessStatus status) {
        return extendedMemberService.getBusinessAuthApply(id, status);

    }


    /**
     * Out excel message result.
     *
     * @param startTime the start time
     * @param endTime   the end time
     * @param account   the account
     * @param request   the request
     * @param response  the response
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('member:out-excel')")
    @GetMapping("out-excel")
    @AccessLog(module = AdminModule.MEMBER, operation = "Export Member Member Excel")
    public MessageResult outExcel(
            @RequestParam(value = "startTime", required = false) Date startTime,
            @RequestParam(value = "endTime", required = false) Date endTime,
            @RequestParam(value = "account", required = false) String account,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return extendedMemberService.outExcel(startTime, endTime, account, request, response);

    }


    /**
     * Publish advertise message result.
     *
     * @param memberId the member id
     * @param status   the status
     * @return the message result
     */
    @PreAuthorize("hasRole('member:alter-publish-advertisement-status')")
    @PostMapping("alter-publish-advertisement-status")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Disable/unban advertising")
    public MessageResult publishAdvertise(@RequestParam("memberId") Long memberId,
                                          @RequestParam("status") BooleanEnum status) {
        return extendedMemberService.publishAdvertise(memberId, status);

    }

    /**
     * Ban message result.
     *
     * @param status   the status
     * @param memberId the member id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:alter-status')")
    @PostMapping("alter-status")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Disable/unblock member accounts")
    public MessageResult ban(@RequestParam("status") CommonStatus status,
                             @RequestParam("memberId") Long memberId) {
        return extendedMemberService.ban(status, memberId);

    }

    /**
     * Alter transaction status message result.
     *
     * @param status   the status
     * @param memberId the member id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:alter-transaction-status')")
    @PostMapping("alter-transaction-status")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Disable/unblock member accounts")
    public MessageResult alterTransactionStatus(
            @RequestParam("status") BooleanEnum status,
            @RequestParam("memberId") Long memberId) {
        return extendedMemberService.alterTransactionStatus(status, memberId);

    }


    /**
     * Alter super partner message result.
     *
     * @param superPartner the super partner
     * @param memberId     the member id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:alter-member-superpartner')")
    @PostMapping("alter-member-superpartner")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Modify user level")
    public MessageResult alterSuperPartner(
            @RequestParam("superPartner") String superPartner,
            @RequestParam("memberId") Long memberId) {
        return extendedMemberService.alterSuperPartner(superPartner, memberId);

    }


    /**
     * Page super partner message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PreAuthorize("hasRole('member:page-query-super')")
    @PostMapping("page-query-super")
    @ResponseBody
    @AccessLog(module = AdminModule.MEMBER, operation = "Find Members on pages")
    public MessageResult pageSuperPartner(
            PageModel pageModel,
            MemberScreen screen) {
        return extendedMemberService.pageSuperPartner(pageModel, screen);

    }


    /**
     * Page super member message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @param userId    the user id
     * @return the message result
     */
    @PreAuthorize("hasRole('member:supermember-page-query')")
    @PostMapping(value = "/supermember-page-query")
    @ResponseBody
    @Transactional(rollbackFor = Exception.class)
    public MessageResult pageSuperMember(
            PageModel pageModel,
            MemberScreen screen,
            Long userId) {
        return extendedMemberService.pageSuperMember(pageModel, screen, userId);

    }

    /**
     * Sets inviter.
     *
     * @param id        the id
     * @param inviterId the inviter id
     * @return the inviter
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('member:set-inviter')")
    @PostMapping("setInviter")
    @AccessLog(module = AdminModule.MEMBER, operation = "Set up an invitation")
    public MessageResult setInviter(
            @RequestParam(value = "id") Long id,
            @RequestParam(value = "inviterId") Long inviterId) throws Exception {
        return extendedMemberService.setInviter(id, inviterId);

    }
}
