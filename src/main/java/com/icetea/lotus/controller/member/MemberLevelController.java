package com.icetea.lotus.controller.member;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.entity.spot.MemberLevel;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.member.ExtendedMemberLevelService;
import com.icetea.lotus.util.BindingResultUtil;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * The type Member level controller.
 */
@RestController
@Slf4j
@RequestMapping("member/member-level")
public class MemberLevelController extends BaseAdminController {

    private final ExtendedMemberLevelService extendedMemberLevelService;

    public MemberLevelController(BaseAdminService baseAdminService, ExtendedMemberLevelService extendedMemberLevelService) {
        super(baseAdminService);
        this.extendedMemberLevelService = extendedMemberLevelService;
    }

    /**
     * Find all message result.
     *
     * @return the message result
     */
    @PreAuthorize("hasRole('member:member-level:all')")
    @PostMapping("all")
    @AccessLog(module = AdminModule.MEMBER, operation = "All member levels MemberLevel")
    public MessageResult findAll() {
        return extendedMemberLevelService.findAll();
    }

    /**
     * Update message result.
     *
     * @param memberLevel   the member level
     * @param bindingResult the binding result
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('member:member-level:update')")
    @PostMapping("update")
    @AccessLog(module = AdminModule.MEMBER, operation = "Update Member Level MemberLevel")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult update(@Valid MemberLevel memberLevel, BindingResult bindingResult) throws Exception {
        MessageResult result = BindingResultUtil.validate(bindingResult);
        if (result != null) {
            return result;
        }
        return extendedMemberLevelService.update(memberLevel);
    }

}
