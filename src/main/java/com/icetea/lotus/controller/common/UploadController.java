package com.icetea.lotus.controller.common;


import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.service.common.UploadService;
import com.icetea.lotus.util.MessageResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


/**
 * The type Upload controller.
 */
@RestController
@RequestMapping("/common/upload")
@RequiredArgsConstructor
public class UploadController extends BaseController {
    private final UploadService uploadService;

    /**
     * Upload oss image string.
     *
     * @param request  the request
     * @param response the response
     * @param file     the file
     * @return the string
     * @throws IOException the io exception
     */
    @PostMapping(value = "/oss/image")
    @AccessLog(module = AdminModule.COMMON, operation = "Upload oss pictures")
    public MessageResult uploadOssImage(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam("file") MultipartFile file) throws IOException {
        return uploadService.uploadOssImage(request, response, file);
    }

    /**
     * Upload local image string.
     *
     * @param request  the request
     * @param response the response
     * @param file     the file
     * @return the string
     * @throws IOException the io exception
     */
    @PostMapping(value = "/local/image")
    @AccessLog(module = AdminModule.COMMON, operation = "Upload local pictures")
    public MessageResult uploadLocalImage(HttpServletRequest request, HttpServletResponse response,
                                          @RequestParam("file") MultipartFile file) throws IOException {
        return uploadService.uploadLocalImage(request, response, file);
    }


    /**
     * Upload oss app string.
     *
     * @param request  the request
     * @param response the response
     * @param file     the file
     * @return the string
     * @throws IOException the io exception
     */
    @PostMapping(value = "/oss/app")
    @AccessLog(module = AdminModule.COMMON, operation = "Upload oss package")
    public MessageResult uploadOssApp(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam("file") MultipartFile file) throws IOException {
        return uploadService.uploadOssApp(request, response, file);
    }

    /**
     * Base 64 up load message result.
     *
     * @param base64Data the base 64 data
     * @return the message result
     */
    @PreAuthorize("hasRole('common:upload:oss:base64')")
    @PostMapping(value = "/oss/base64")
    @AccessLog(module = AdminModule.COMMON, operation = "base64 upload oss")
    public MessageResult base64UpLoad(@RequestParam String base64Data) {
        return uploadService.base64UpLoad(base64Data);
    }


}
