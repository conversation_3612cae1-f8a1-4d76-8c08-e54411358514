package com.icetea.lotus.controller.authno;

import com.icetea.lotus.service.authno.AuthExchangeCoinService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * The type Auth exchange coin controller.
 */
@RestController
@RequestMapping("noauth/exchange-coin")
@RequiredArgsConstructor
public class AuthExchangeCoinController {
    private final AuthExchangeCoinService authExchangeCoinService;


    /**
     * Detail ExchangeCoin
     *
     * @param symbol :
     * @param sign   :
     * @return data message result
     */
    @PostMapping("detail")
    public MessageResult detail(
            @RequestParam(value = "symbol") String symbol,
            @RequestParam(value = "sign") String sign) {
        return authExchangeCoinService.detail(symbol, sign);
    }

    /**
     * Update price in ExchangeCoin
     *
     * @param symbol       :
     * @param maxBuyPrice  :
     * @param minSellPrice :
     * @param sign         :
     * @return data message result
     */
    @PostMapping("modify-limit")
    public MessageResult modifyLimit(
            @RequestParam(value = "symbol") String symbol,
            @RequestParam(value = "maxBuyPrice") BigDecimal maxBuyPrice,
            @RequestParam(value = "minSellPrice") BigDecimal minSellPrice,
            @RequestParam(value = "sign") String sign) {
        return authExchangeCoinService.modifyLimit(symbol, maxBuyPrice, minSellPrice, sign);
    }
}
