package com.icetea.lotus.controller.convert;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.entity.spot.ConvertCoin;
import com.icetea.lotus.service.convert.ConvertService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;

import javax.validation.Valid;

/**
 * The type Convert coin controller.
 *
 * <AUTHOR> @description Backend Currency Web
 * @date 2021 /12/29 15:01
 */
@RestController
@RequestMapping("/convert/coin")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ConvertCoinController {

    private final ConvertService convertService;


    /**
     * Create message result.
     *
     * @param convertCoin the convert coin
     * @return the message result
     */
    @PreAuthorize("hasRole('convert:coin:create')")
    @PostMapping("create")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Create a flash currency Coin")
    public MessageResult create(@Valid ConvertCoin convertCoin) {
        return convertService.create(convertCoin);
    }


    /**
     * Update message result.
     *
     * @param convertCoin   the convert coin
     * @param admin         the admin
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('convert:coin:update')")
    @PostMapping("update")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Update flash currency Coin")
    public MessageResult update(
            @Valid ConvertCoin convertCoin,
            @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin,
            BindingResult bindingResult) {
        return convertService.update(convertCoin, admin, bindingResult);
    }

    /**
     * Detail message result.
     *
     * @param coinUnit the coin unit
     * @return the message result
     */
    @PreAuthorize("hasRole('convert:coin:detail')")
    @PostMapping("detail")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Details of backend flash redeem Coin")
    public MessageResult detail(@RequestParam("coinUnit") String coinUnit) {
        return convertService.detail(coinUnit);
    }

    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @return the message result
     */
    @PreAuthorize("hasRole('convert:coin:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.SYSTEM, operation = "Pagination Find Flash Currency Coin")
    public MessageResult pageQuery(PageModel pageModel) {
        return convertService.pageQuery(pageModel);
    }

// @PostMapping("all-coin-unit")
// @AccessLog(module = AdminModule.SYSTEM, operation = "Find convertcoin's unit")
// public MessageResult getAllCoinUnit() { //NOSONAR
// List<String> list = convertCoinService.getAllCoinUnit(); //NOSONAR
// return MessageResult.getSuccessInstance(messageSource.getMessage("SUCCESS"), list); //NOSONAR
// } //NOSONAR

}
