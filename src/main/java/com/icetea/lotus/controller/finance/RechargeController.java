package com.icetea.lotus.controller.finance;
import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.controller.common.BaseAdminController;
import com.icetea.lotus.dto.request.DepositScreenRequest;
import com.icetea.lotus.dto.response.DepositRecordResponse;
import com.icetea.lotus.service.common.BaseAdminService;
import com.icetea.lotus.service.finance.ExtendedRechargeService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;

/**
 * Recharge management
 */
@Slf4j
@RestController
@RequestMapping("/finance/recharge")
public class RechargeController extends BaseAdminController {

    private final ExtendedRechargeService extendedRechargeService;

    public RechargeController(BaseAdminService baseAdminService, ExtendedRechargeService extendedRechargeService) {
        super(baseAdminService);
        this.extendedRechargeService = extendedRechargeService;
    }

    /**
     * Retrieves the list of currencies available in the recharge records.
     *
     * @return MessageResult containing the list of available currencies for recharge.
     */
    @PreAuthorize("hasRole('finance:recharge:coin-list')")
    @GetMapping("/coin-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency list in the recharge record")
    public MessageResult coinList() {
        return extendedRechargeService.coinList();

    }

    /**
     * Retrieves the list of currency protocols associated with recharge records.
     *
     * @return MessageResult containing the list of currency protocols for recharge.
     */
    @PreAuthorize("hasRole('finance:recharge:protocol-list')")
    @GetMapping("/protocol-list")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the currency protocol list in the recharge record")
    public MessageResult protocolList() {
        return extendedRechargeService.protocolList();

    }

    /**
     * Retrieves a paginated list of recharge records with the given filter criteria.
     *
     * @param depositScreenRequest The filter criteria for querying deposit records (e.g., currency, member ID).
     * @return MessageResult containing the paginated list of deposit records based on the filter criteria.
     * @throws IOException If an error occurs while writing the response.
     */
//    @PreAuthorize("hasRole('finance:recharge:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.FINANCE, operation = "Get the recharge record")
    public Page<DepositRecordResponse> pageQuery(@RequestBody DepositScreenRequest depositScreenRequest) throws IOException {
        return extendedRechargeService.pageQuery(depositScreenRequest);
    }

    /**
     * Retrieves a list of deposit transaction's status
     *
     * @return MessageResult contains list of deposit transaction's status
     */
    @GetMapping("/transaction-statuses")
    public MessageResult getListStatus() {
        return extendedRechargeService.getStatusList();
    }
}

