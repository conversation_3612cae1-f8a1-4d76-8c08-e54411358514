package com.icetea.lotus.controller.cms;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysAdvertiseLocation;
import com.icetea.lotus.entity.spot.SysAdvertise;
import com.icetea.lotus.model.screen.SysAdvertiseScreen;
import com.icetea.lotus.service.cms.AdvertiseService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * The type Advertise controller.
 *
 * <AUTHOR>
 * @Description System Advertising
 * @date 2019 /1/6 15:03
 */
@Slf4j
@RestController
@RequestMapping("/cms/system-advertise")
@RequiredArgsConstructor
public class AdvertiseController {
    private final AdvertiseService advertiseService;


    /**
     * Find one message result.
     *
     * @param sysAdvertise  the sys advertise
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('cms:system-advertise:create')")
    @PostMapping("/create")
    @AccessLog(module = AdminModule.CMS, operation = "Create system ads")
    public MessageResult findOne(@Valid SysAdvertise sysAdvertise, BindingResult bindingResult) {
        return advertiseService.findOne(sysAdvertise, bindingResult);
    }

    /**
     * Get all data from SysAdvertise
     *
     * @return message result
     */
    @PreAuthorize("hasRole('cms:system-advertise:all')")
    @PostMapping("/all")
    @AccessLog(module = AdminModule.CMS, operation = "All system ads")
    public MessageResult all() {
        return advertiseService.all();
    }

    /**
     * Get detail of SysAdvertise by SerialNumber
     *
     * @param serialNumber the serial number
     * @return message result
     */
    @PreAuthorize("hasRole('cms:system-advertise:detail')")
    @PostMapping("/detail")
    @AccessLog(module = AdminModule.CMS, operation = "System advertising details")
    public MessageResult findOne(@RequestParam(value = "serialNumber") String serialNumber) {
        return advertiseService.findOne(serialNumber);
    }

    /**
     * Update message result.
     *
     * @param sysAdvertise  the sys advertise
     * @param bindingResult the binding result
     * @return the message result
     */
    @PreAuthorize("hasRole('cms:system-advertise:update')")
    @PostMapping("/update")
    @AccessLog(module = AdminModule.CMS, operation = "Update system ads")
    public MessageResult update(@Valid SysAdvertise sysAdvertise, BindingResult bindingResult) {
        return advertiseService.update(sysAdvertise, bindingResult);
    }


    /**
     * Delete by id
     *
     * @param ids the ids
     * @return message result
     */
    @PreAuthorize("hasRole('cms:system-advertise:deletes')")
    @PostMapping("/deletes")
    @AccessLog(module = AdminModule.CMS, operation = "Batch delete system ads")
    public MessageResult delete(@RequestParam(value = "ids") String[] ids) {
        return advertiseService.delete(ids);
    }

    /**
     * get data page of  SysAdvertise
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return data message result
     */
    @PreAuthorize("hasRole('cms:system-advertise:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.CMS, operation = "Pagination query system advertising")
    public MessageResult pageQuery(PageModel pageModel, SysAdvertiseScreen screen) {
        return advertiseService.pageQuery(pageModel, screen);
    }


    /**
     * To top message result.
     *
     * @param serialNum the serial num
     * @return the message result
     */
    @PreAuthorize("hasRole('cms:system-advertise:top')")
    @PostMapping("top")
    @AccessLog(module = AdminModule.CMS, operation = "Advertising top")
    public MessageResult toTop(@RequestParam("serialNum") String serialNum) {
        return advertiseService.toTop(serialNum);
    }


    /**
     * Out excel message result.
     *
     * @param serialNumber         the serial number
     * @param sysAdvertiseLocation the sys advertise location
     * @param status               the status
     * @param request              the request
     * @param response             the response
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('cms:system-advertise:out-excel')")
    @GetMapping("/out-excel")
    @AccessLog(module = AdminModule.CMS, operation = "Export system ads Excel")
    public MessageResult outExcel(
            @RequestParam(value = "serialNumber", required = false) String serialNumber,
            @RequestParam(value = "sysAdvertiseLocation", required = false) SysAdvertiseLocation sysAdvertiseLocation,
            @RequestParam(value = "status", required = false) CommonStatus status,
            HttpServletRequest request, HttpServletResponse response) throws Exception {
        return advertiseService.outExcel(serialNumber, sysAdvertiseLocation, status, request, response);
    }

}
