package com.icetea.lotus.controller.cms;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.entity.spot.SysHelp;
import com.icetea.lotus.service.cms.HelpService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * The type Help controller.
 *
 * <AUTHOR>
 * @Description background help web
 * @date 2019 /1/9 10:11
 */
@RestController
@RequestMapping("/cms/system-help")
@RequiredArgsConstructor
public class HelpController {

    private final HelpService helpService;


    /**
     * Create new Sys-help
     *
     * @param sysHelp       : SysHelp data
     * @param bindingResult : binding and check value
     * @return : data
     */
    @PreAuthorize("hasRole('cms:system-help:create')")
    @PostMapping("/create")
    @AccessLog(module = AdminModule.CMS, operation = "Create System Help")
    public MessageResult create(@Valid SysHelp sysHelp, BindingResult bindingResult) {
        return helpService.create(sysHelp, bindingResult);
    }

    /**
     * Get all data of SysHelp
     *
     * @return : List data
     */
    @PreAuthorize("hasRole('cms:system-help:all')")
    @PostMapping("/all")
    @AccessLog(module = AdminModule.CMS, operation = "Find all system help")
    public MessageResult all() {
        return helpService.all();
    }

    /**
     * Increase Sort and set top status by id
     *
     * @param id : id of SysHelp
     * @return : message
     */
    @PreAuthorize("hasRole('cms:system-help:top')")
    @PostMapping("top")
    @AccessLog(module = AdminModule.CMS, operation = "System help top")
    public MessageResult toTop(@RequestParam("id") long id) {
        return helpService.toTop(id);
    }

    /**
     * System helps to untab
     *
     * @param id : id of SysHelp
     * @return : message
     */
    @PreAuthorize("hasRole('cms:system-help:down')")
    @PostMapping("down")
    @AccessLog(module = AdminModule.CMS, operation = "System helps to untab")
    public MessageResult toDown(@RequestParam("id") long id) {
        return helpService.toDown(id);
    }

    /**
     * Get detail by id
     *
     * @param id : SysHelp id
     * @return : data
     */
    @PreAuthorize("hasRole('cms:system-help:detail')")
    @PostMapping("/detail")
    @AccessLog(module = AdminModule.CMS, operation = "System Help Details")
    public MessageResult detail(@RequestParam(value = "id") Long id) {
        return helpService.detail(id);
    }

    /**
     * Update SysHelp field
     *
     * @param sysHelp       : SysHelp data
     * @param bindingResult : binding
     * @return : message
     */
    @PreAuthorize("hasRole('cms:system-help:update')")
    @PostMapping("/update")
    @AccessLog(module = AdminModule.CMS, operation = "Update system help")
    public MessageResult update(@Valid SysHelp sysHelp, BindingResult bindingResult) {
        return helpService.update(sysHelp, bindingResult);
    }

    /**
     * Delete by id
     *
     * @param ids : list id
     * @return : message
     */
    @PreAuthorize("hasRole('cms:system-help:deletes')")
    @PostMapping("/deletes")
    @AccessLog(module = AdminModule.CMS, operation = "Delete System Help")
    public MessageResult deleteOne(@RequestParam("ids") Long[] ids) {
        return helpService.deleteOne(ids);
    }

    /**
     * get pageNo of data
     *
     * @param pageModel : page
     * @return : data
     */
    @PreAuthorize("hasRole('cms:system-help:page-query')")
    @PostMapping("/page-query")
    @AccessLog(module = AdminModule.CMS, operation = "Pagination query system help")
    public MessageResult pageQuery(PageModel pageModel) {
        return helpService.pageQuery(pageModel);
    }

    /**
     * Out excel message result.
     *
     * @param request  the request
     * @param response the response
     * @return the message result
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('cms:system-help:out-excel')")
    @GetMapping("/out-excel")
    @AccessLog(module = AdminModule.CMS, operation = "Export system help Excel")
    public MessageResult outExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return helpService.outExcel(request, response);
    }
}
