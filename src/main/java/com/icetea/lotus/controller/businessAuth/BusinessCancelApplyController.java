package com.icetea.lotus.controller.businessAuth;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.service.businessAuth.BusinessCancelAppliedService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


/**
 * The type Business cancel apply controller.
 */
@RestController
@RequestMapping("business/cancel-apply")
@RequiredArgsConstructor
public class BusinessCancelApplyController extends BaseController {

    private final BusinessCancelAppliedService businessCancelApplyService;

    /**
     * Page query message result.
     *
     * @param pageModel the page model
     * @param account   the account
     * @param status    the status
     * @param startDate the start date
     * @param endDate   the end date
     * @return the message result
     */
    @PostMapping("page-query")
    @PreAuthorize("hasRole('business:cancel-apply:page-query')")
    public MessageResult pageQuery(
            PageModel pageModel,
            @RequestParam(value = "account", required = false) String account,
            @RequestParam(value = "status", required = false) CertifiedBusinessStatus status,
            @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date startDate,
            @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd") Date endDate) {
        return businessCancelApplyService.pageQuery(pageModel, account, status, startDate, endDate);
    }

    /**
     * Refund audit interface
     *
     * @param id      the id
     * @param success By: IS_TRUE
     * @param reason  Reasons for failure to pass the review
     * @return message result
     */
    @PreAuthorize("hasRole('business:cancel-apply:check')")
    @PostMapping("check")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult pass(
            @RequestParam(value = "id") Long id,
            @RequestParam(value = "success") BooleanEnum success,
            @RequestParam(value = "reason", defaultValue = "") String reason) {
        return businessCancelApplyService.pass(id, success, reason);
    }

    /**
     * Detail message result.
     *
     * @param id :businessCancelApply id
     * @return message result
     */
    @PostMapping("detail")
    @PreAuthorize("hasRole('business:cancel-apply:detail')")
    public MessageResult detail(@RequestParam(value = "id") Long id) {
        return businessCancelApplyService.detail(id);
    }

    /**
     * Gets search status.
     *
     * @return the search status
     */
    @PostMapping("get-search-status")
    public MessageResult getSearchStatus() {
        return businessCancelApplyService.getSearchStatus();
    }
}
