package com.icetea.lotus.controller.businessAuth;

import com.icetea.lotus.constant.CertifiedBusinessStatus;
import com.icetea.lotus.constant.CommonStatus;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.businessAuth.BusinessAuthService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;


/**
 * Merchant certification available margin types
 *
 * <AUTHOR>
 * @date 2019 /5/5
 */
@RestController
@RequestMapping("business-auth")
@Slf4j
@RequiredArgsConstructor
public class BusinessAuthController {
    private final BusinessAuthService businessAuthService;


    /**
     * Get data
     *
     * @param pageModel the page model
     * @param status    the status
     * @return data all
     */
    @PreAuthorize("hasRole('business:auth:deposit:page')")
    @GetMapping("page")
    public MessageResult getAll(PageModel pageModel, CommonStatus status) {
        return businessAuthService.getAll(pageModel, status);
    }

    /**
     * Create message result.
     *
     * @param admin    the admin
     * @param amount   the amount
     * @param coinUnit the coin unit
     * @return the message result
     */
    @PreAuthorize("hasRole('business:auth:deposit:create')")
    @PostMapping("create")
    public MessageResult create(@SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin,
                                @RequestParam("amount") Double amount,
                                @RequestParam("coinUnit") String coinUnit) {
        return businessAuthService.create(admin, amount, coinUnit);
    }

    /**
     * Loi
     *
     * @param id the id
     * @return message result
     */
    @PreAuthorize("hasRole('business-auth:apply:detail')")
    @PostMapping("apply/detail")
    public MessageResult detail(@RequestParam("id") Long id) {
        return businessAuthService.detail(id);
    }

    /**
     * Update value in BusinessAuthDeposit
     *
     * @param id     the id
     * @param amount the amount
     * @param status the status
     * @return message message result
     */
    @PreAuthorize("hasRole('business:auth:deposit:update')")
    @PatchMapping("update")
    public MessageResult update(
            @RequestParam("id") Long id,
            @RequestParam("amount") Double amount,
            @RequestParam("status") CommonStatus status) {
        return businessAuthService.update(id, amount, status);
    }

    /**
     * Get data from BusinessAuthApply
     *
     * @param pageModel the page model
     * @param status    the status
     * @param account   the account
     * @return data message result
     */
    @PostMapping("apply/page-query")
    @PreAuthorize("hasRole('business-auth:apply:page-query')")
    public MessageResult page(
            PageModel pageModel,
            @RequestParam(value = "status", required = false) CertifiedBusinessStatus status,
            @RequestParam(value = "account", defaultValue = "") String account) {

        return businessAuthService.page(pageModel, status, account);
    }

    /**
     * Get all status
     *
     * @return list status
     */
    @PostMapping("get-search-status")
    public MessageResult getSearchStatus() {
        return businessAuthService.getSearchStatus();
    }
}
