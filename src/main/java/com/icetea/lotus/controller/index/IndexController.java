package com.icetea.lotus.controller.index;


import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.service.index.IndexService;
import com.icetea.lotus.util.MessageResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.*;

@RestController("index")
@RequestMapping("index/statistics")
@RequiredArgsConstructor
public class IndexController {

    private final IndexService indexService;

    /**
     * Retrieves member statistics for the specified date range.
     *
     * @param startDate The start date of the statistics period.
     * @param endDate The end date of the statistics period.
     * @return MessageResult containing the member statistics for the specified date range.
     */
    @PostMapping("member-statistics-info")
    @AccessLog(module = AdminModule.INDEX,operation = "Home Member Information Statistics")
    public MessageResult getYestodayStatisticsInfo(
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date startDate,
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date endDate){

        return indexService.getYestodayStatisticsInfo(startDate, endDate);

    }

    /**
     * Retrieves a chart representing member statistics for the specified date range.
     *
     * @param startDate The start date of the statistics period.
     * @param endDate The end date of the statistics period.
     * @return MessageResult containing the chart data for the specified date range.
     */
    @PostMapping("member-statistics-chart")
    @AccessLog(module = AdminModule.INDEX,operation = "Home Member Information Statistics")
    public MessageResult getMemberStatisticsChart(
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date startDate,
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date endDate
    ){
        return indexService.getMemberStatisticsChart(startDate, endDate);

    }

    /**
     * Retrieves statistics for fiat currency transactions for the specified date range.
     *
     * @param startDate The start date of the transaction period.
     * @param endDate The end date of the transaction period.
     * @param unit The unit for the transaction statistics (e.g., USD, EUR).
     * @return MessageResult containing the fiat currency transaction statistics.
     */
    @PostMapping("otc-statistics-turnover")
    @AccessLog(module = AdminModule.INDEX,operation = "Statistics of fiat currency transaction information")
    public MessageResult otcStatistics(
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date startDate,
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date endDate,
            String unit
    ){

        return indexService.otcStatistics(startDate, endDate, unit);

    }

    /**
     * Retrieves exchange statistics, including trading volume, transaction volume, and processing fees, for the specified date range.
     *
     * @param startDate The start date of the trading period.
     * @param endDate The end date of the trading period.
     * @param unit The unit for the statistics (e.g., USD, EUR).
     * @return MessageResult containing the exchange statistics for the specified date range.
     */
    @PostMapping("exchange-statistics-turnover")
    @AccessLog(module = AdminModule.INDEX,operation = "Home page currency trading volume/transaction volume/process fee Total")
    public MessageResult exchangeStatistics(
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date startDate,
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date endDate,
            String unit
    ) {
        return indexService.exchangeStatistics(startDate, endDate, unit);

    }

    /**
     * Retrieves a chart representing fiat currency trading volume for the specified date range.
     *
     * @param startDate The start date of the transaction period.
     * @param endDate The end date of the transaction period.
     * @param units The units for the trading statistics (e.g., USD, EUR).
     * @return MessageResult containing the chart data for the fiat currency trading volume.
     */
    @PostMapping("/otc-statistics-num-chart")
    @AccessLog(module = AdminModule.INDEX,operation = "Statistics of fiat currency trading volume")
    public MessageResult otcNumChart(
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date startDate,
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date endDate,
            String[] units/*,
            TransactionTypeEnum type*/){

        // Assert.isTrue(type==TransactionTypeEnum.OTC_NUM || type==TransactionTypeEnum.OTC_MONEY ,"This interface is a fiat currency statistics, and the type can only be 0 (transaction volume) or 1 (transaction volume)");

        return indexService.otcNumChart(startDate, endDate, units);

    }

    /**
     * Retrieves a chart representing exchange statistics (distinguished by transaction pairs) for the specified date range.
     *
     * @param startDate The start date of the transaction period.
     * @param endDate The end date of the transaction period.
     * @param baseSymbol The base symbol for the exchange pair (e.g., BTC, ETH).
     * @param coinSymbols The coin symbols for the exchange pair (e.g., USDT, EUR).
     * @return MessageResult containing the chart data for the exchange statistics.
     */
    @PostMapping("exchange-statistics-turnover-chart")
    @AccessLog(module = AdminModule.INDEX,operation = "Coin transaction statistics chart (distinguished by transaction pairs)")
    public MessageResult exchangeNumStatistics(
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date startDate,
            @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")Date endDate,
            String baseSymbol,
            String[] coinSymbols){
        return indexService.exchangeNumStatistics(startDate, endDate, baseSymbol, coinSymbols);

    }

    /**
     * Retrieves general information about affairs.
     *
     * @return MessageResult containing the affairs data.
     */
    @GetMapping("affairs")
    public MessageResult affairs(){
        return indexService.affairs();
    }

    /**
     * Retrieves all exchange coin information.
     *
     * @return MessageResult containing all available exchange coin data.
     */
    @PostMapping("all-exchange-coin")
    public MessageResult getAllExchangeCoin(){
        return indexService.getAllExchangeCoin();
    }
}
