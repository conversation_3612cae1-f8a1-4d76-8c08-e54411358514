package com.icetea.lotus.controller.promotion;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.MemberPromotionScreen;
import com.icetea.lotus.service.promotion.ExtendedMemberPromotionService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Invitation registration reward configuration
 *
 * <AUTHOR>
@RestController
@RequestMapping("promotion/member")
@RequiredArgsConstructor
public class MemberPromotionController {

    private final ExtendedMemberPromotionService extendedMemberPromotionService;


    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PostMapping("page-query")
    @PreAuthorize("hasRole('promotion:member:page-query')")
    @AccessLog(module = AdminModule.PROMOTION,operation = "Recommended member pagination query")
    public MessageResult page(PageModel pageModel, MemberPromotionScreen screen){

        return extendedMemberPromotionService.page(pageModel, screen);
    }


    /**
     * Promotion details message result.
     *
     * @param pageModel the page model
     * @param memberId  the member id
     * @return the message result
     */
    @PreAuthorize("hasRole('promotion:member:details')")
    @PostMapping("details")
    @AccessLog(module = AdminModule.PROMOTION,operation = "Recommended Member Details")
    public MessageResult promotionDetails(PageModel pageModel,
                                          @RequestParam("memberId")Long memberId){

        return extendedMemberPromotionService.promotionDetails(pageModel, memberId);
    }

    /**
     * Out excel.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @param response  the response
     * @throws Exception the exception
     */
    @PreAuthorize("hasRole('promotion:member:out-excel')")
    @GetMapping("out-excel")
    public void outExcel(PageModel pageModel, MemberPromotionScreen screen,
                         HttpServletResponse response) throws Exception {
        extendedMemberPromotionService.outExcel(pageModel, screen, response);
    }

}
