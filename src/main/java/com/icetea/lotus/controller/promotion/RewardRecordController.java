package com.icetea.lotus.controller.promotion;

import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.model.RewardRecordScreen;
import com.icetea.lotus.service.promotion.ExtendedRewardRecordService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("promotion/reward-record")
@RequiredArgsConstructor
public class RewardRecordController {

    private final ExtendedRewardRecordService extendedRewardRecordService ;

    /**
     * Page message result.
     *
     * @param pageModel the page model
     * @param screen    the screen
     * @return the message result
     */
    @PostMapping("page-query")
    public MessageResult page(PageModel pageModel, RewardRecordScreen screen){
        return null;
    }
}
