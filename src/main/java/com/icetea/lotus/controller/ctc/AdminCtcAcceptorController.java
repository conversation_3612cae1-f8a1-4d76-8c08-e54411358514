package com.icetea.lotus.controller.ctc;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.ctc.AdminCtcAcceptorService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @Description otc acceptance
 * @date 2019/1/11 13:35
 */
@RestController
@RequestMapping("/ctc/acceptor")
@RequiredArgsConstructor
public class AdminCtcAcceptorController {

    private final AdminCtcAcceptorService adminCtcAcceptorService;


    /**
     * Pagination query
     *
     * @param pageModel :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:acceptor:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.CTC, operation = "View CTC Acceptor List AdminCtcAcceptorController")
    public MessageResult orderList(PageModel pageModel) {
        return adminCtcAcceptorService.orderList(pageModel);
    }

    /**
     * Switch status
     *
     * @param id :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:acceptor:switch')")
    @AccessLog(module = AdminModule.CTC, operation = "Mark payment and complete CTC order")
    @PostMapping("switch")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult payOrder(@RequestParam("id") Long id,
                                  @RequestParam(value = "password") String password,
                                  @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {

        return adminCtcAcceptorService.payOrder(id, password, admin);

    }
}
