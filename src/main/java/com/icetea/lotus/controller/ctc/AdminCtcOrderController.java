package com.icetea.lotus.controller.ctc;

import com.icetea.lotus.annotation.AccessLog;
import com.icetea.lotus.constant.AdminModule;
import com.icetea.lotus.constant.PageModel;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.controller.BaseController;
import com.icetea.lotus.entity.*;
import com.icetea.lotus.entity.spot.Admin;
import com.icetea.lotus.service.ctc.AdminCtcOrderService;
import com.icetea.lotus.util.MessageResult;
import org.springframework.security.access.prepost.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description otc currency
 * @date 2019/1/11 13:35
 */
@RestController
@RequestMapping("/ctc/order")
@RequiredArgsConstructor
public class AdminCtcOrderController extends BaseController {

    private final AdminCtcOrderService adminCtcOrderService;


    /**
     * Pagination query
     *
     * @param pageModel :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:order:page-query')")
    @PostMapping("page-query")
    @AccessLog(module = AdminModule.CTC, operation = "View CTC order list AdminCtcOrderController")
    public MessageResult orderList(PageModel pageModel) {
        return adminCtcOrderService.orderList(pageModel);
    }

    /**
     * Order details
     *
     * @param id :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:order:order-detail')")
    @PostMapping("order-detail")
    @AccessLog(module = AdminModule.CTC, operation = "View CTC order list AdminCtcOrderController")
    public MessageResult orderDetail(@RequestParam("id") Long id) {
        return adminCtcOrderService.orderDetail(id);

    }

    /**
     * Tag payment
     *
     * @param id :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:order:pay-order')")
    @AccessLog(module = AdminModule.CTC, operation = "Mark payment and complete CTC order")
    @PostMapping("pay-order")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult payOrder(@RequestParam("id") Long id,
                                  @RequestParam(value = "password") String password,
                                  @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return adminCtcOrderService.payOrder(id, password, admin);

    }

    /**
     * Coins released (completed)
     *
     * @param id :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:order:complete-order')")
    @AccessLog(module = AdminModule.CTC, operation = "Mark payment and complete CTC order")
    @PostMapping("complete-order")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult completeOrder(@RequestParam("id") Long id,
                                       @RequestParam(value = "password") String password,
                                       @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return adminCtcOrderService.completeOrder(id, password, admin);

    }

    /**
     * Order processing
     *
     * @param id :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:order:confirm-order')")
    @AccessLog(module = AdminModule.CTC, operation = "Confirm the order")
    @PostMapping("confirm-order")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult confirmOrder(@RequestParam("id") Long id,
                                      @RequestParam(value = "password") String password,
                                      @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return adminCtcOrderService.confirmOrder(id, password, admin);

    }

    /**
     * Forced cancellation of orders
     *
     * @param id :
     * @return :
     */
    @PreAuthorize("hasRole('ctc:order:cancel-order')")
    @PostMapping("cancel-order")
    @AccessLog(module = AdminModule.CTC, operation = "Mark payment and complete CTC order")
    @Transactional(rollbackFor = Exception.class)
    public MessageResult cancelOrder(@RequestParam("id") Long id,
                                     @RequestParam(value = "password") String password,
                                     @SessionAttribute(SysConstant.SESSION_ADMIN) Admin admin) {
        return adminCtcOrderService.cancelOrder(id, password, admin);
    }
}
