package com.bitcello.futureapi.job;

import com.bitcello.futureapi.service.PositionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
@RequiredArgsConstructor
public class OrderListJob {

    private final PositionService positionService;

    private final ConcurrentHashMap<Long, Boolean> memberJobMap = new ConcurrentHashMap<>();
}
