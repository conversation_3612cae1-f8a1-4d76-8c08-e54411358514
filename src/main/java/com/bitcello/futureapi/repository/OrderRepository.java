package com.bitcello.futureapi.repository;

import com.bitcello.futureapi.entity.Order;
import com.bitcello.futureapi.utils.enums.OrderDirection;
import com.bitcello.futureapi.utils.enums.OrderStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * JPA Repository for OrderJpaEntity
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, String>, JpaSpecificationExecutor<Order> {

    /**
     * Find all orders by member ID.
     *
     * @param memberId the ID of the member
     * @return a list of orders
     */
    Page<Order> findAllByMemberId(Long memberId, Pageable pageable);

    /**
     * Find all orders by symbol.
     *
     * @param symbol the trading symbol
     * @return a list of orders
     */
    List<Order> findAllBySymbol(String symbol);


    Page<Order> findAllBySymbol(String symbol, Pageable pageable);

    /**
     * Find all orders by member ID and symbol.
     *
     * @param memberId the ID of the member
     * @param symbol   the trading symbol
     * @return a list of orders
     */
    Page<Order> findAllByMemberIdAndSymbol(Long memberId, String symbol, Pageable pageable);

    /**
     * Find all orders by status.
     *
     * @param status the status of the order
     * @return a list of orders
     */
    List<Order> findAllByStatus(String status);

    /**
     * Find all orders by status with pagination.
     *
     * @param status   the status of the order
     * @param pageable pagination information
     * @return a page of orders
     */
    Page<Order> findAllByStatus(String status, Pageable pageable);

    /**
     * Find all orders by symbol and status.
     *
     * @param symbol the trading symbol
     * @param status the status of the order
     * @return a list of orders
     */
    List<Order> findBySymbolAndStatus(String symbol, OrderStatus status);

    /**
     * Find all orders by symbol and status with pagination.
     *
     * @param symbol   the trading symbol
     * @param status   the status of the order
     * @param pageable pagination information
     * @return a page of orders
     */
    Page<Order> findBySymbolAndStatus(String symbol, OrderStatus status, Pageable pageable);

    /**
     * Find all orders by member ID and status.
     *
     * @param memberId the ID of the member
     * @param status   the status of the order
     * @return a list of orders
     */
    List<Order> findAllByMemberIdAndStatus(Long memberId, String status);

    /**
     * Find all orders by member ID and status with pagination.
     *
     * @param memberId the ID of the member
     * @param status   the status of the order
     * @param pageable pagination information
     * @return a page of orders
     */
    Page<Order> findAllByMemberIdAndStatus(Long memberId, String status, Pageable pageable);

    /**
     * Find all orders by member ID, symbol, and status.
     *
     * @param memberId the ID of the member
     * @param symbol   the trading symbol
     * @param status   the status of the order
     * @return a list of orders
     */
    List<Order> findByMemberIdAndSymbolAndStatus(Long memberId, String symbol, String status);

    /**
     * Find all orders by member ID, symbol, and status with pagination.
     *
     * @param memberId the ID of the member
     * @param symbol   the trading symbol
     * @param status   the status of the order
     * @param pageable pagination information
     * @return a page of orders
     */
    Page<Order> findByMemberIdAndSymbolAndStatus(Long memberId, String symbol, String status, Pageable pageable);

    /**
     * Find all orders created between the specified start and end times.
     *
     * @param startTime the start time
     * @param endTime   the end time
     * @return a list of orders
     */
    List<Order> findAllByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find all orders created between the specified start and end times with pagination.
     *
     * @param startTime the start time
     * @param endTime   the end time
     * @param pageable  pagination information
     * @return a page of orders
     */
    Page<Order> findAllByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Find all orders by symbol and creation time between the specified start and end times.
     *
     * @param symbol    the trading symbol
     * @param startTime the start time
     * @param endTime   the end time
     * @return a list of orders
     */
    List<Order> findAllBySymbolAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find all orders by symbol and creation time between the specified start and end times with pagination.
     *
     * @param symbol    the trading symbol
     * @param startTime the start time
     * @param endTime   the end time
     * @param pageable  pagination information
     * @return a page of orders
     */
    Page<Order> findAllBySymbolAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Find all orders by member ID and creation time between the specified start and end times.
     *
     * @param memberId  the ID of the member
     * @param startTime the start time
     * @param endTime   the end time
     * @return a list of orders
     */
    List<Order> findAllByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find all orders by member ID and creation time between the specified start and end times with pagination.
     *
     * @param memberId  the ID of the member
     * @param startTime the start time
     * @param endTime   the end time
     * @param pageable  pagination information
     * @return a page of orders
     */
    Page<Order> findAllByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Find all orders by status and expire time before the specified time.
     *
     * @param status     the status of the order
     * @param expireTime the expire time to compare against
     * @return a list of orders
     */
    List<Order> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime);

    /**
     * Find all orders by status and expire time before the specified time with pagination.
     *
     * @param status     the status of the order
     * @param expireTime the expire time to compare against
     * @param pageable   pagination information
     * @return a page of orders
     */
    Page<Order> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime, Pageable pageable);

    /**
     * Update the status of an order.
     *
     * @param orderId the ID of the order
     * @param status  the new status of the order
     * @return the number of rows affected
     */
    @Modifying
    @Query("update Order o set o.status = :status where o.orderId = :orderId")
    int updateStatus(@Param("orderId") String orderId, @Param("status") String status);

    /**
     * Update the deal amount of an order.
     *
     * @param orderId    the ID of the order
     * @param dealVolume the volume of the deal
     * @param dealMoney  the money involved in the deal
     * @return the number of rows affected
     */
    @Modifying
    @Query("update Order o set o.dealVolume = :dealVolume, o.dealMoney = :dealMoney where o.orderId = :orderId")
    int updateDealAmount(@Param("orderId") String orderId, @Param("dealVolume") java.math.BigDecimal dealVolume, @Param("dealMoney") java.math.BigDecimal dealMoney);

    /**
     * Find all orders by symbol, direction, and status.
     *
     * @param symbol    the trading symbol
     * @param direction the order direction
     * @param status    the status of the order
     * @return a list of orders
     */
    List<Order> findAllBySymbolAndDirectionAndStatus(String symbol, OrderDirection direction, OrderStatus status);

    /**
     * Find all orders by symbol, direction, and status with pagination.
     *
     * @param symbol    the trading symbol
     * @param direction the order direction
     * @param status    the status of the order
     * @param pageable  pagination information
     * @return a page of orders
     */
    Page<Order> findAllBySymbolAndDirectionAndStatus(String symbol, OrderDirection direction, OrderStatus status, Pageable pageable);

    /**
     * Find all orders by symbol, status, and a list of types.
     *
     * @param symbol the trading symbol
     * @param status the status of the order
     * @param types  the list of order types
     * @return a list of orders
     */
    List<Order> findAllBySymbolAndStatusAndTypeIn(String symbol, OrderStatus status, List<String> types);

    /**
     * Find all orders by symbol, direction, and status, ordered by price in descending order.
     *
     * @param symbol    the trading symbol
     * @param direction the order direction
     * @param status    the status of the order
     * @return a list of orders
     */
    List<Order> findAllBySymbolAndDirectionAndStatusOrderByPriceDesc(String symbol, OrderDirection direction, OrderStatus status);

    /**
     * Find all orders by symbol, direction, and status, ordered by price in ascending order.
     *
     * @param symbol    the trading symbol
     * @param direction the order direction
     * @param status    the status of the order
     * @return a list of orders
     */
    List<Order> findAllBySymbolAndDirectionAndStatusOrderByPriceAsc(String symbol, OrderDirection direction, OrderStatus status);

    /**
     * Find all orders by status and execute time before the specified time.
     *
     * @param status      the status of the order
     * @param executeTime the execute time to compare against
     * @return a list of orders
     */
    @Query("SELECT o FROM Order o WHERE o.status = :status AND o.executeTime IS NOT NULL AND o.executeTime < :executeTime")
    List<Order> findByStatusAndExecuteTimeBefore(@Param("status") OrderStatus status, @Param("executeTime") LocalDateTime executeTime);

    /**
     * Find all orders by symbol, status, and direction with pagination.
     *
     * @param symbol    the trading symbol
     * @param status    the list of order statuses
     * @param direction the order direction
     * @param pageable  pagination information
     * @return a page of orders
     */
    Page<Order> findBySymbolAndStatusInAndDirectionOrderByPriceDesc(String symbol, List<OrderStatus> status,
                                                                    OrderDirection direction, Pageable pageable);

    /**
     * Find all orders by member ID.
     *
     * @param memberId the ID of the member
     * @return a list of orders
     */
    List<Order> findAllByMemberId(Long memberId);

    /**
     * Find all orders by member ID, symbol, and a list of statuses.
     *
     * @param memberId the ID of the member
     * @param symbol   the trading symbol
     * @param statuses the list of order statuses
     * @return a list of orders
     */
    List<Order> findAllByMemberIdAndSymbolAndStatusIn(Long memberId, String symbol, List<OrderStatus> statuses);

    /**
     * Find all orders by member ID and symbol.
     *
     * @param memberId the ID of the member
     * @param symbol   the trading symbol
     * @return a list of orders
     */
    List<Order> findAllByMemberIdAndSymbol(Long memberId, String symbol);

    /**
     * Find the first order by order ID.
     *
     * @param orderId the ID of the order
     * @return an Optional containing the Order if found, otherwise empty
     */
    Optional<Order> findFirstByOrderId(String orderId);

    /**
     * Find all orders by status.
     *
     * @param status the status of the order
     * @return a list of orders
     */
    List<Order> findAllByStatus(OrderStatus status);

    /**
     * Retrieves an order by its order ID, symbol, and member ID.
     *
     * @param orderId  the unique identifier of the order
     * @param symbol   the trading symbol associated with the order (e.g., BTCUSDT)
     * @param memberId the unique identifier of the member who owns the order
     * @return an {@link Optional} containing the order if found, or an empty {@link Optional} otherwise
     */
    Optional<Order> findByOrderIdAndSymbolAndMemberId(String orderId, String symbol, Long memberId);

    /**
     * Finds a list of orders based on the given member ID and a list of order statuses.
     *
     * @param memberId   the ID of the member whose orders are to be retrieved
     * @param statusList a list of order statuses to filter the orders
     * @return a list of orders matching the given member ID and statuses
     */
    List<Order> findByMemberIdAndStatusIn(Long memberId, List<OrderStatus> statusList);

    /**
     * Finds and returns a list of orders for a specific member ID,
     * filtered by a list of statuses and a specified symbol.
     *
     * @param memberId   the unique identifier of the member whose orders are to be retrieved
     * @param statusList the list of order statuses to filter the orders by
     * @param symbol     the symbol to filter the orders (e.g., trading pair or contract symbol)
     * @return a list of {@link Order} matching the specified criteria, or an empty list if no orders are found
     */
    List<Order> findByMemberIdAndStatusInAndSymbol(Long memberId, List<OrderStatus> statusList, String symbol);

    /**
     * Finds and retrieves a list of orders matching the given order IDs.
     *
     * @param orderIds a list of order IDs to search for
     * @return a list of orders that match the specified order IDs
     */
    List<Order> findByOrderIdIn(List<String> orderIds);
}
