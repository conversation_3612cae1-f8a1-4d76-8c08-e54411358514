package com.bitcello.futureapi.repository;

import com.bitcello.futureapi.entity.Position;
import com.bitcello.futureapi.utils.enums.PositionDirection;
import com.bitcello.futureapi.utils.enums.PositionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * JPA Repository for PositionJpaEntity
 */
@Repository
public interface PositionRepository extends JpaRepository<Position, Long>, JpaSpecificationExecutor<Position> {

    /**
     * Find a position by memberId and symbol.
     *
     * @param memberId ID of the member
     * @param symbol   Symbol of the position
     * @return Optional containing the Position if found, otherwise empty
     */
    Optional<Position> findByMemberIdAndSymbol(Long memberId, String symbol);

    /**
     * Find a position by memberId, symbol, and status.
     *
     * @param memberId ID of the member
     * @param symbol   Symbol of the position
     * @param status   Status of the position
     * @return Optional containing the Position if found, otherwise empty
     */
    Optional<Position> findByMemberIdAndSymbolAndStatus(Long memberId, String symbol, PositionStatus status);

    /**
     * Find all positions by memberId and symbol.
     *
     * @param memberId ID of the member
     * @param symbol   Symbol of the position
     * @return List of Positions matching the criteria
     */
    List<Position> findAllByMemberIdAndSymbol(Long memberId, String symbol);

    /**
     * Find a position by memberId, symbol, and direction.
     *
     * @param memberId  ID of the member
     * @param symbol    Symbol of the position
     * @param direction Direction of the position
     * @return Optional containing the Position if found, otherwise empty
     */
    Optional<Position> findByMemberIdAndSymbolAndDirection(Long memberId, String symbol, PositionDirection direction);

    /**
     * Find all positions by memberId, symbol, and direction.
     *
     * @param memberId  ID of the member
     * @param symbol    Symbol of the position
     * @param direction Direction of the position
     * @return List of Positions matching the criteria
     */
    List<Position> findAllByMemberIdAndSymbolAndDirection(Long memberId, String symbol, PositionDirection direction);

    /**
     * Find all positions by memberId.
     *
     * @param memberId ID of the member
     * @return List of Positions for the specified member
     */
    List<Position> findAllByMemberId(Long memberId);

    /**
     * Find all positions by memberId and status.
     *
     * @param memberId ID of the member
     * @param status   Status of the position
     * @return List of Positions matching the criteria
     */
    List<Position> findAllByMemberIdAndStatus(Long memberId, PositionStatus status);

    /**
     * Find positions by memberId, status, and time range.
     *
     * @param memberId  ID of the member
     * @param status    Status of the position (can be null)
     * @param startTime Start time of the range (can be null)
     * @param endTime   End time of the range (can be null)
     * @param pageable  Pagination information
     * @return Page of Positions matching the criteria
     */
    @Query("SELECT p FROM Position p WHERE p.memberId = :memberId " +
            "AND (:status IS NULL OR p.status = :status) " +
            "AND (:startTime IS NULL OR p.createTime >= :startTime) " +
            "AND (:endTime IS NULL OR p.createTime <= :endTime) " +
            "ORDER BY p.createTime DESC")
    Page<Position> findByMemberIdAndStatusAndTimeRange(
            @Param("memberId") Long memberId,
            @Param("status") PositionStatus status,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);

    /**
     * Find all positions by symbol.
     *
     * @param symbol Symbol of the position
     * @return List of Positions matching the criteria
     */
    List<Position> findAllBySymbol(String symbol);

    /**
     * Find all positions by symbol and direction.
     *
     * @param symbol    Symbol of the position
     * @param direction Direction of the position
     * @return List of Positions matching the criteria
     */
    List<Position> findAllBySymbolAndDirection(String symbol, PositionDirection direction);

    /**
     * Find all positions by symbol, direction, and status.
     *
     * @param symbol    Symbol of the position
     * @param direction Direction of the position
     * @param status    Status of the position
     * @return List of Positions matching the criteria
     */
    @Query("SELECT SUM(p.volume) FROM Position p WHERE p.symbol = :symbol AND p.direction = :direction AND p.status = :status")
    BigDecimal sumVolumeBySymbolAndDirectionAndStatus(@Param("symbol") String symbol, @Param("direction") PositionDirection direction, @Param("status") PositionStatus status);

    /**
     * Find all positions by status.
     *
     * @param status Status of the position
     * @return List of Positions matching the criteria
     */
    List<Position> findAllByStatus(PositionStatus status);

    /**
     * Find all positions by symbol and status.
     *
     * @param symbol Symbol of the position
     * @param status Status of the position
     * @return List of Positions matching the criteria
     */
    List<Position> findAllBySymbolAndStatus(String symbol, PositionStatus status);

    /**
     * Find positions with a margin ratio below a specified threshold.
     *
     * @param symbol      Symbol of the position
     * @param marginRatio Margin ratio threshold
     * @param pageable    Pagination information
     * @return List of Positions with margin ratio below the specified threshold
     */
    @Query(value = "SELECT p FROM Position p WHERE p.symbol = :symbol AND p.status = 'OPEN' " +
            "AND p.margin / (p.volume * p.openPrice) < :marginRatio ORDER BY p.margin / (p.volume * p.openPrice) ASC")
    List<Position> findRiskPositionsBySymbol(@Param("symbol") String symbol, @Param("marginRatio") BigDecimal marginRatio, Pageable pageable);
}
