package com.bitcello.futureapi.repository;

import com.bitcello.futureapi.entity.Wallet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * JPA Repository for WalletJpaEntity
 */
@Repository
public interface WalletRepository extends JpaRepository<Wallet, Long>, JpaSpecificationExecutor<Wallet> {

    /**
     * Finds a Wallet by memberId and coin.
     *
     * @param memberId the ID of the member
     * @param coin     the coin type
     * @return an Optional containing the Wallet if found, or empty if not found
     */
    Optional<Wallet> findByMemberIdAndCoin(Long memberId, String coin);

    /**
     * Finds all Wallets for a given memberId.
     *
     * @param memberId the ID of the member
     * @return a List of Wallet entities associated with the memberId
     */
    List<Wallet> findAllByMemberId(Long memberId);

    /**
     * Finds all Wallets for a given coin.
     *
     * @param coin the coin type
     * @return a List of Wallet entities associated with the coin
     */
    List<Wallet> findAllByCoin(String coin);

    /**
     * Finds all Wallets for a given memberId and coin.
     *
     * @param memberId the ID of the member
     * @param coin     the coin type
     * @return a List of Wallet entities associated with the memberId and coin
     */
    @Query("SELECT w.availableBalance FROM Wallet w WHERE w.memberId = :memberId AND w.coin = :coin")
    BigDecimal getAvailableBalance(@Param("memberId") Long memberId, @Param("coin") String coin);


    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE Wallet w SET w.balance = w.balance + :amount, w.availableBalance = w.availableBalance + :amount WHERE w.id = :walletId")
    int increaseBalance(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    /**
     * Decreases the balance of a wallet by a specified amount.
     *
     * @param walletId the ID of the wallet
     * @param amount   the amount to decrease
     * @return the number of rows affected
     */
    @Transactional
    @Modifying
    @Query("UPDATE Wallet w SET w.balance = w.balance - :amount, w.availableBalance = w.availableBalance - :amount WHERE w.id = :walletId AND w.availableBalance >= :amount")
    int decreaseBalance(@Param("walletId") long walletId, @Param("amount") BigDecimal amount);

    /**
     * Freezes a specified amount of balance in the wallet.
     *
     * @param memberId the ID of the member
     * @param coin     the coin type
     * @param amount   the amount to freeze
     */
    @Modifying
    @Transactional
    @Query("UPDATE Wallet w SET w.availableBalance = w.availableBalance - :amount, w.frozenBalance = w.frozenBalance + :amount WHERE w.memberId = :memberId AND w.coin = :coin AND w.availableBalance >= :amount")
    void freezeBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Freezes a specified amount of balance in the wallet by walletId.
     *
     * @param walletId the ID of the wallet
     * @param amount   the amount to freeze
     * @return the number of rows affected
     */
    @Transactional
    @Modifying
    @Query("UPDATE Wallet w SET w.frozenBalance = w.frozenBalance + :amount, w.availableBalance = w.availableBalance - :amount WHERE w.id = :walletId AND w.availableBalance >= :amount")
    int freezeBalance(@Param("walletId") long walletId, @Param("amount") BigDecimal amount);

    /**
     * Unfreezes a specified amount of balance in the wallet.
     *
     * @param memberId the ID of the member
     * @param coin     the coin type
     * @param amount   the amount to unfreeze
     */
    @Modifying
    @Transactional
    @Query("UPDATE Wallet w SET w.availableBalance = w.availableBalance + :amount, w.frozenBalance = w.frozenBalance - :amount WHERE w.memberId = :memberId AND w.coin = :coin AND w.frozenBalance >= :amount")
    void unfreezeBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Unfreezes a specified amount of balance in the wallet by walletId.
     *
     * @param walletId the ID of the wallet
     * @param amount   the amount to unfreeze
     * @return the number of rows affected
     */
    @Transactional
    @Modifying
    @Query("UPDATE Wallet w SET w.frozenBalance = w.frozenBalance - :amount, w.availableBalance = w.availableBalance + :amount WHERE w.id = :walletId AND w.frozenBalance >= :amount")
    int unfreezeBalance(@Param("walletId") long walletId, @Param("amount") BigDecimal amount);

    /**
     * Subtracts a specified amount from the frozen balance of a wallet.
     *
     * @param memberId the ID of the member
     * @param coin     the coin type
     * @param amount   the amount to subtract
     */
    @Modifying
    @Transactional
    @Query("UPDATE Wallet w SET w.frozenBalance = w.frozenBalance - :amount WHERE w.memberId = :memberId AND w.coin = :coin AND w.frozenBalance >= :amount")
    void subtractBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Adds a specified amount to the available and total balance of a wallet.
     *
     * @param memberId the ID of the member
     * @param coin     the coin type
     * @param amount   the amount to add
     */
    @Modifying
    @Transactional
    @Query("UPDATE Wallet w SET w.availableBalance = w.availableBalance + :amount, w.balance = w.balance + :amount WHERE w.memberId = :memberId AND w.coin = :coin")
    void addBalance(@Param("memberId") Long memberId, @Param("coin") String coin, @Param("amount") BigDecimal amount);

    /**
     * Updates the total funding fee for a wallet.
     *
     * @param walletId the ID of the wallet
     * @param fee      the fee amount to add
     * @return the number of rows affected
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE Wallet w SET w.totalFee = w.totalFee + :fee, w.updateTime = CURRENT_TIMESTAMP WHERE w.id = :walletId")
    int updateTotalFee(@Param("walletId") Long walletId, @Param("fee") BigDecimal fee);

    /**
     * Updates the total funding fee for a wallet.
     *
     * @param walletId    the ID of the wallet
     * @param realizedPnl the realized profit and loss amount to add
     * @return the number of rows affected
     */
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE Wallet w SET w.realizedPnl = w.realizedPnl + :realizedPnl, w.updateTime = CURRENT_TIMESTAMP WHERE w.id = :walletId")
    int updateRealizedPnl(@Param("walletId") Long walletId, @Param("realizedPnl") BigDecimal realizedPnl);

    /**
     * Retrieves a list of Wallet entities associated with the specified member ID and a list of coin types.
     *
     * @param memberId the ID of the member whose Wallet entities are to be retrieved
     * @param coinList the list of coin types to filter the Wallet entities by
     * @return a List of Wallet entities matching the given member ID and coin types
     */
    List<Wallet> findByMemberIdAndCoinIn(Long memberId, List<String> coinList);
}
