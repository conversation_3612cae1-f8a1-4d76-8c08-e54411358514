package com.bitcello.futureapi.repository;

import com.bitcello.futureapi.entity.Trade;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository for TradeJpaEntity
 */
@Repository
public interface TradeJpaRepository extends JpaRepository<Trade, Long>, JpaSpecificationExecutor<Trade> {

    /**
     * Find all trades by symbol.
     *
     * @param symbol the trading symbol
     * @return a list of trades
     */
    List<Trade> findAllBySymbol(String symbol);

    /**
     * Find all trades by symbol with pagination.
     *
     * @param symbol   the trading symbol
     * @param pageable pagination information
     * @return a page of trades
     */
    Page<Trade> findAllBySymbol(String symbol, Pageable pageable);

    /**
     * Find all trades by symbol and trade time range.
     *
     * @param symbol    the trading symbol
     * @param startTime the start time of the trade
     * @param endTime   the end time of the trade
     * @return a list of trades within the specified time range
     */
    List<Trade> findAllBySymbolAndTradeTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find all trades by symbol and trade time range with pagination.
     *
     * @param symbol    the trading symbol
     * @param startTime the start time of the trade
     * @param endTime   the end time of the trade
     * @param pageable  pagination information
     * @return a page of trades within the specified time range
     */
    Page<Trade> findAllBySymbolAndTradeTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Find all trades by buy member ID.
     *
     * @param buyMemberId the ID of the member who bought
     * @return a list of trades where the member is the buyer
     */
    List<Trade> findAllByBuyMemberId(Long buyMemberId);

    /**
     * Find all trades by sell member ID.
     *
     * @param sellMemberId the ID of the member who sold
     * @return a list of trades where the member is the seller
     */
    List<Trade> findAllBySellMemberId(Long sellMemberId);

    /**
     * Find all trades by buy order ID.
     *
     * @param buyOrderId the ID of the buy order
     * @return a list of trades associated with the buy order
     */
    List<Trade> findAllByBuyOrderId(String buyOrderId);

    /**
     * Find all trades by sell order ID.
     *
     * @param sellOrderId the ID of the sell order
     * @return a list of trades associated with the sell order
     */
    List<Trade> findAllBySellOrderId(String sellOrderId);

    /**
     * Find all trades by symbol, ordered by trade time in descending order with pagination.
     *
     * @param symbol   the trading symbol
     * @param pageable pagination information
     * @return a page of trades ordered by trade time in descending order
     */
    Page<Trade> findBySymbolOrderByTradeTimeDesc(String symbol, Pageable pageable);

    /**
     * Find the most recent trade by symbol, ordered by trade time in descending order.
     *
     * @param symbol the trading symbol
     * @return the most recent trade for the specified symbol
     */
    Trade findTopBySymbolOrderByTradeTimeDesc(String symbol);

    /**
     * Find all trades by buy member ID or sell member ID with pagination.
     *
     * @param buyMemberId  the ID of the member who bought
     * @param sellMemberId the ID of the member who sold
     * @param pageable     pagination information
     * @return a page of trades where the member is either the buyer or the seller
     */
    Page<Trade> findAllByBuyMemberIdOrSellMemberId(Long buyMemberId, Long sellMemberId, Pageable pageable);

    /**
     * Finds and retrieves a list of trades that are associated with any of the specified buy order IDs
     * or sell order IDs.
     *
     * @param buyOrderIds a list of buy order IDs to search for
     * @param sellOrderIds a list of sell order IDs to search for
     * @return a list of trades that match the given buy order IDs or sell order IDs
     */
    List<Trade> findByBuyOrderIdInOrSellOrderIdIn(List<String> buyOrderIds, List<String> sellOrderIds);

}
