package com.bitcello.futureapi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration class for Kafka topic names.
 * This class is used to map and manage topic configurations from the application's properties file.
 * The topic names are expected to be configured with the prefix `topic-kafka.contract`.
 * It provides properties corresponding to various Kafka topics used in the system.
 */
@Data
@Component
@ConfigurationProperties(prefix = "topic-kafka.contract")
public class KafkaTopicConfig {
    private String orderNew;
    private String orderCancel;
    private String orderCancelBroadcast;
    private String orderCancelSuccess;
    private String orderCompleted;
    private String trade;
    private String tradePlate;
    private String position;
    private String markPrice;
    private String indexPrice;
    private String fundingRate;
    private String liquidation;
    private String lastPrice;
    private String orderEvents;
    private String orderCommands;
    private String orderCancelResult;
    private String orderCancelAllResult;
}
