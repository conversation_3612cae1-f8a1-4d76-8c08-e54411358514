package com.bitcello.futureapi.config.security;

import com.bitcello.futureapi.config.KeycloakPropsConfig;
import jakarta.servlet.Filter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtTimestampValidator;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.cors.CorsConfigurationSource;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Security configuration for the application, enabling resource server capabilities
 * and configuring JWT authentication with Keycloak.
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableWebSecurity
@EnableMethodSecurity
@ConditionalOnProperty(prefix = "cex-security", name = "resource-server-enabled", havingValue = "true")
public class SecurityConfiguration {

    public static final String TOKEN_INTROSPECT = "/protocol/openid-connect/token/introspect";
    public static final String OPENID_CONNECT_CERTS = "/protocol/openid-connect/certs";
    public static final String SESSION_ATTRIBUTE_FILTER_BEAN_NAME = "sessionAttributeFilter";
    private final KeycloakPropsConfig keycloakPropsConfig;
    private final CexSecurityProperties cexSecurityProperties;
    private final ApplicationContext applicationContext;
    private final CorsConfigurationSource corsConfigurationSource;

    @Value("${spring.security.oauth2.resource-server.jwt.issuer-uri}")
    private String issuerUri;

    private final CustomAuthenticationEntryPoint customAuthenticationEntryPoint;
    private final CustomAccessDeniedHandler customAccessDeniedHandler;

    /**
     * Configures the security filter chain for the application.
     *
     * @param http the HttpSecurity object to configure
     * @return the configured SecurityFilterChain
     * @throws Exception if an error occurs during configuration
     */
    @Bean
    @SuppressWarnings("all")
    public SecurityFilterChain filterChain(@NonNull HttpSecurity http) throws Exception {

        // Configure the HttpSecurity
        return http
                .csrf(AbstractHttpConfigurer::disable)
                .cors(customizer -> customizer.configurationSource(corsConfigurationSource)) // Enable CORS with global config
                .authorizeHttpRequests(auth -> {
                            auth.requestMatchers(
                                    "/actuator/prometheus", "/actuator/**", "/actuator/health/**",
                                    "/swagger-ui", "/swagger-ui/**", "/error", "/v3/api-docs/**"
                            ).permitAll();
                            cexSecurityProperties.getPermitAllEndpoints()
                                    .forEach(endpoint -> auth.requestMatchers(endpoint).permitAll());
                            auth.anyRequest().authenticated();
                        }
                )
                .oauth2ResourceServer(oauth2 -> oauth2
                        .jwt(jwt -> jwt.jwtAuthenticationConverter(this.jwtAuthenticationConverterForKeycloak()))
                        .authenticationEntryPoint(customAuthenticationEntryPoint) // 👈 Gắn vào đây
                )
                .exceptionHandling(
                        exception -> exception.accessDeniedHandler(customAccessDeniedHandler))
                .addFilterAfter(getOptionalSessionAttributeFilter(), BearerTokenAuthenticationFilter.class)
                .build();

    }

    /**
     * Retrieves the optional session attribute filter bean from the application context.
     * If the bean is not found, a no-operation filter is returned instead.
     *
     * @return the session attribute filter or a no-op filter if not available
     */
    private Filter getOptionalSessionAttributeFilter() {
        try {
            // Attempt to fetch the sessionAttributeFilter bean
            return applicationContext.getBean(SESSION_ATTRIBUTE_FILTER_BEAN_NAME, Filter.class);
        } catch (NoSuchBeanDefinitionException e) {
            log.info("OptionalSessionAttributeFilter not found. Skipping its addition to the SecurityFilterChain.");
            return new NoOpFilter();
        }
    }

    /**
     * Configures the JWT authentication converter for Keycloak.
     *
     * @return the configured JwtAuthenticationConverter
     */
    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverterForKeycloak() {
        Converter<Jwt, Collection<GrantedAuthority>> jwtGrantedAuthoritiesConverter = jwt -> {
            Map<String, Collection<String>> realmAccess = jwt.getClaim("realm_access");
            Collection<String> roles = realmAccess.get("roles");
            return roles.stream()
                    .map(role -> new SimpleGrantedAuthority("ROLE_" + role))
                    .collect(Collectors.toList());
        };

        var jwtAuthenticationConverter = new JwtAuthenticationConverter();
        jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(jwtGrantedAuthoritiesConverter);

        return jwtAuthenticationConverter;
    }


    /**
     * Configures the JWT decoder for the OAuth2 resource server.
     *
     * @param properties the OAuth2 resource server properties
     * @return the configured JwtDecoder
     */
    @Bean
    public JwtDecoder jwtDecoder(OAuth2ResourceServerProperties properties) {
        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withJwkSetUri(
                this.issuerUri + OPENID_CONNECT_CERTS
        ).build();

        // Add custom token validator
        jwtDecoder.setJwtValidator(new DelegatingOAuth2TokenValidator<>(
                new JwtTimestampValidator(),
                jwt -> {
                    try {
                        this.isTokenActive(jwt.getTokenValue());
                        return OAuth2TokenValidatorResult.success();
                    } catch (OAuth2AuthenticationException ex) {
                        return OAuth2TokenValidatorResult.failure(ex.getError());
                    }
                }
        ));

        return jwtDecoder;
    }

    /**
     * No-op filter to use when the session attribute filter is not available.
     */
    private void isTokenActive(String token) {
        String errorCode = "invalid_token";
        try {
            if (token == null || token.isBlank()) {
                log.warn("[JWT] Token is null or empty");
                throw new OAuth2AuthenticationException(
                        new OAuth2Error(errorCode, "Token is missing", null));
            }

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("token", token);
            formData.add("client_id", keycloakPropsConfig.getResource());
            formData.add("client_secret", keycloakPropsConfig.getCredentials().getSecret());

            RestTemplate restTemplate = new RestTemplate();

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    this.issuerUri + TOKEN_INTROSPECT,
                    HttpMethod.POST,
                    new HttpEntity<>(formData, new HttpHeaders()),
                    new ParameterizedTypeReference<Map<String, Object>>() {}
            );

            Map<String, Object> body = response.getBody();
            boolean isActive = body != null && Boolean.TRUE.equals(body.get("active"));

            if (!isActive) {
                log.warn("[JWT] Token is NOT active! token={}", token);
                throw new OAuth2AuthenticationException(
                        new OAuth2Error(errorCode, "Token inactive or revoked", null)
                );
            }

            log.info("[JWT] Token is active ✅");

        } catch (OAuth2AuthenticationException e) {
            throw e; // re-throw
        } catch (Exception e) {
            log.error("[JWT] Token introspection failed ❌", e);
            throw new OAuth2AuthenticationException(
                    new OAuth2Error(errorCode, "Token introspection failed", null), e
            );
        }
    }

}
