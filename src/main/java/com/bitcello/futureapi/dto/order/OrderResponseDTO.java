package com.bitcello.futureapi.dto.order;

import com.bitcello.futureapi.utils.enums.FutureMode;
import com.bitcello.futureapi.utils.enums.OrderDirection;
import com.bitcello.futureapi.utils.enums.OrderStatus;
import com.bitcello.futureapi.utils.enums.OrderType;
import com.bitcello.futureapi.utils.enums.PositionMode;
import com.bitcello.futureapi.utils.enums.TimeInForce;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO for Order
 * Contains order-related information
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "Order information")
public class OrderResponseDTO {

    @Schema(description = "Order ID", example = "ORD123456")
    private String orderId;

    @Schema(description = "Member ID", example = "123")
    private Long memberId;

    @Schema(description = "Contract symbol", example = "BTC-USDT")
    private String symbol;

    @Schema(description = "Order direction", example = "BUY")
    private OrderDirection direction;

    @Schema(description = "Order type", example = "LIMIT")
    private OrderType type;

    @Schema(description = "Price", example = "50000.00")
    private BigDecimal price;

    @Schema(description = "Trigger price", example = "49000.00")
    private BigDecimal triggerPrice;

    @Schema(description = "Order volume", example = "0.1")
    private BigDecimal volume;

    @Schema(description = "Executed volume", example = "0.05")
    private BigDecimal dealVolume;

    @Schema(description = "Executed value", example = "2500.00")
    private BigDecimal dealMoney;

    @Schema(description = "Fee", example = "0.25")
    private BigDecimal fee;

    @Schema(description = "Order status", example = "FILLED")
    private OrderStatus status;

    @Schema(description = "Creation time", example = "2023-05-01T12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "Completion time", example = "2023-05-01T12:01:00")
    private LocalDateTime completeTime;

    @Schema(description = "Time in force", example = "GTC")
    private TimeInForce timeInForce;

    @Schema(description = "Expiration time", example = "2023-05-02T12:00:00")
    private LocalDateTime expireTime;

    @Schema(description = "Leverage", example = "10")
    private BigDecimal leverage;

    @Schema(description = "Reduce-only flag", example = "false")
    private Boolean reduceOnly;

    @Schema(description = "Callback rate", example = "0.01")
    private BigDecimal callbackRate;

    @Schema(description = "Activation price", example = "48000.00")
    private BigDecimal activationPrice;

    @Schema(description = "Post-only flag", example = "false")
    private Boolean postOnly;

    @Schema(description = "Cancel reason", example = "Canceled by user")
    private String cancelReason;

    @Schema(description = "Maximum slippage (%)", example = "0.5")
    private BigDecimal maxSlippage;

    @Schema(description = "Fill-or-Kill (FOK) flag", example = "false")
    private Boolean fillOrKill;

    @Schema(description = "Immediate-or-Cancel (IOC) flag", example = "false")
    private Boolean immediateOrCancel;

    @Schema(description = "Futures mode (e.g., ISOLATED or CROSSED)")
    private FutureMode futureMode;

    @Schema(description = "Order size")
    private BigDecimal size;

    private BigDecimal takeProfitPrice;
    private BigDecimal stopLossPrice;
    private PositionMode positionMode;
    private BigDecimal filledVolume;
    private BigDecimal averagePrice;
}
