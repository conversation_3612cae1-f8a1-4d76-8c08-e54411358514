package com.bitcello.futureapi.dto.order.cmd;

import com.bitcello.futureapi.utils.enums.FutureMode;
import com.bitcello.futureapi.utils.enums.OrderDirection;
import com.bitcello.futureapi.utils.enums.OrderType;
import com.bitcello.futureapi.utils.enums.PositionMode;
import com.bitcello.futureapi.utils.enums.TimeInForce;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO for placing an order command.
 * Contains all necessary fields to place an order in the trading system.
 */
@Setter
@Getter
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PlaceOrderCommandDTO {

    private Long memberId;
    private Long contractId;

    /**
     * Contract symbol
     */
    @NotBlank(message = "{symbol.blank}")
    @Schema(description = "Contract symbol", example = "BTC/USDT")
    private String symbol;

    /**
     * Order direction
     */
    @NotNull(message = "{order.direction.null}")
    @Schema(description = "Order direction BUY | SELL", example = "BUY")
    private OrderDirection direction;

    /**
     * Order type
     */
    @NotNull(message = "{order.type.null}")
    @Schema(description = "Order type", example = "LIMIT")
    private OrderType type;

    /**
     * Price
     */
    @Positive(message = "{price.positive}")
    @Schema(description = "Order price", example = "50000.00")
    private BigDecimal price;

    /**
     * Order size
     */
    @NotNull(message = "{amount.null}")
    @Positive(message = "{amount.positive}")
    @Schema(description = "Order size", example = "1 BTC")
    private BigDecimal size;

    /**
     * Trigger price (for conditional orders)
     */
    private BigDecimal triggerPrice;

    /**
     * Volume
     */
    @Schema(description = "Volume", example = "0.1")
    private BigDecimal volume;

    /**
     * Time-in-force
     */
    private TimeInForce timeInForce;

    /**
     * Expiration time
     */
    private LocalDateTime expireTime;

    /**
     * Leverage mode (e.g., CROSS or ISOLATED)
     */
    @NotNull(message = "{leverageMode.blank}")
    @Schema(description = "Leverage mode", example = "CROSS")
    private FutureMode futureMode;

    /**
     * Leverage value
     */
    @NotNull(message = "{leverage.null}")
    @Positive(message = "{leverage.positive}")
    private BigDecimal leverage;

    /**
     * Reduce-only flag
     */
    private Boolean reduceOnly;

    /**
     * Callback rate (used in trailing stop orders)
     */
    private BigDecimal callbackRate;

    /**
     * Post-only flag
     */
    private Boolean postOnly;

    /**
     * Maximum slippage (%)
     * If the market price fluctuates beyond this threshold, the order will not be fully executed.
     */
    private BigDecimal maxSlippage;

    /**
     * Fill-or-Kill flag (FOK)
     * If true, the order must be completely filled immediately or it will be cancelled.
     */
    private Boolean fillOrKill;

    /**
     * Immediate-or-Cancel flag (IOC)
     * If true, any part of the order not filled immediately will be cancelled.
     */
    private Boolean immediateOrCancel;

    /**
     * Take-profit price
     */
    private BigDecimal takeProfitPrice;

    /**
     * Stop-loss price
     */
    private BigDecimal stopLossPrice;

    /**
     * Position mode (e.g., ONE_WAY, HEDGE)
     */
    @NotNull(message = "{positionMode.null}")
    @Schema(description = "Position mode", example = "ONE_WAY")
    private PositionMode positionMode;

    private BigDecimal frozenMargin;
}
