package com.bitcello.futureapi.dto.position;

import com.bitcello.futureapi.utils.enums.MarginMode;
import com.bitcello.futureapi.utils.enums.PositionDirection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO for position details.
 * Contains the necessary information about a trading position.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PositionDTO {
    private Long id;
    private Long memberId;
    private String symbol;
    private PositionDirection direction;
    private BigDecimal volume;
    private BigDecimal openPrice;
    private BigDecimal closePrice;
    private BigDecimal liquidationPrice;
    private BigDecimal maintenanceMargin;
    private BigDecimal margin;
    private BigDecimal profit;
    private MarginMode marginMode;
    private BigDecimal leverage;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String remark;
    private BigDecimal unrealizedProfit;
    private BigDecimal profitRatio;
    private BigDecimal markPrice;
    private BigDecimal indexPrice;

    // Binance-like fields
    private BigDecimal entryPrice; // Same as openPrice but for clarity
    private BigDecimal breakEvenPrice;
    private BigDecimal marginRatio;
    private String status; // OPEN, CLOSED, etc.
}
