package com.bitcello.futureapi.dto.position.response;

import com.bitcello.futureapi.entity.ContractSymbol;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PositionResponseDTO {
    private ContractSymbol contractSymbol;
    private BigDecimal amount;
    private BigDecimal entryPrice;
    private BigDecimal markPrice;
    private BigDecimal liqPrice;
    private BigDecimal marginRatio;
    private BigDecimal margin;
    private BigDecimal pnl;
    private BigDecimal takeProfit;
    private BigDecimal stopLoss;
}
