package com.bitcello.futureapi.controller;

import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.constant.ErrorMessages;
import com.bitcello.futureapi.dto.base.ResponseGlobal;
import com.bitcello.futureapi.dto.order.OrderResponseDTO;
import com.bitcello.futureapi.dto.order.cmd.PlaceOrderCommandDTO;
import com.bitcello.futureapi.dto.order.request.FindOrderRequestDTO;
import com.bitcello.futureapi.model.AuthMember;
import com.bitcello.futureapi.service.PlaceOrderService;
import com.bitcello.futureapi.utils.annotations.CurrentUser;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for managing trading orders.
 * <p>
 * Provides REST APIs for:
 * <ul>
 *     <li>Placing a new order</li>
 *     <li>Canceling single/all orders</li>
 *     <li>Retrieving order(s) by ID or symbol</li>
 *     <li>Getting active orders</li>
 * </ul>
 * <p>
 * Secured via token-based authentication. All operations require a valid `AuthMember`.
 */
@RestController
@RequestMapping("/api/v1/trading")
@RequiredArgsConstructor
@Slf4j
public class OrderController {

    private final PlaceOrderService placeOrderService;
    private final LocaleMessageSourceService messageSourceService;


    /**
     * Place a new order for the authenticated user.
     *
     * @param request The order details (symbol, price, size, leverage, etc.)
     * @param member  Authenticated member (injected from a token)
     * @return Success response with created order, or error if validation fails.
     * <p>
     * Error cases:
     * - MEMBER_NOT_EXIST if a token is invalid or a user is missing
     */
    @PostMapping("/orders")
    public ResponseEntity<ResponseGlobal<OrderResponseDTO>> placeOrder(
            @Valid @RequestBody PlaceOrderCommandDTO request,
            @CurrentUser AuthMember member) {
        log.info("[Future-Order] - Received place order request: {}", request);

        if (ObjectUtils.isEmpty(member.getId())) {
            log.warn("[Future-Order]- Validation failed: missing memberId (actual: {})", member.getId());
            return ResponseEntity.badRequest()
                    .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.MEMBER_NOT_EXIST)));
        }

        // --- Inject memberId into a request ---
        request.setMemberId(member.getId());
        OrderResponseDTO result = placeOrderService.placeOrder(request);
        log.info("[Future-Order]- Order placed successfully: {}", result);

        return ResponseEntity.ok(ResponseGlobal
                .success(result, messageSourceService.getMessage(ErrorMessages.ORDER_PLACED_SUCCESSFULLY)));
    }

    /**
     * Cancels an existing order based on the provided order ID and symbol.
     * This method requires the current authenticated member's details to validate the request.
     *
     * @param member  the authenticated member requesting the order cancellation
     * @param orderId the unique identifier of the order to be canceled
     * @param symbol  the trading pair symbol associated with the order
     * @return a ResponseEntity containing a ResponseGlobal instance with a success or error message
     */
    @DeleteMapping("/cancel-order/{orderId}")
    public ResponseEntity<ResponseGlobal<String>> cancelOrder(
            @CurrentUser AuthMember member,
            @PathVariable String orderId,
            @RequestParam String symbol) {

        log.info("[Future-Cancel-Order] - Received cancel order request: orderId={}, symbol={} ", orderId, symbol);
        if (ObjectUtils.isEmpty(member.getId())) {
            log.warn("[Future-Cancel-Order]- Validation failed: missing memberId (actual: {})", member.getId());
            return ResponseEntity.badRequest()
                    .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.MEMBER_NOT_EXIST)));
        }

        placeOrderService.cancelOrder(orderId, member.getId(), symbol);
        return ResponseEntity.ok(ResponseGlobal.success(messageSourceService.getMessage(ErrorMessages.ORDER_CANCEL_SUCCESS)));
    }

    /**
     * Cancels all orders for a specific trading symbol for the authenticated user.
     * This method uses the current user's authentication details to validate the request
     * and cancels all orders associated with the specified symbol.
     *
     * @param member the authenticated user making the request
     * @param symbol the trading pair symbol for which all orders need to be canceled (e.g., "BTC/USDT")
     * @return a ResponseEntity containing a ResponseGlobal object with the count of successfully canceled orders
     */
    @DeleteMapping("/cancel-orders")
    public ResponseEntity<ResponseGlobal<String>> cancelAllOrders(
            @CurrentUser AuthMember member,
            @RequestParam String symbol) {
        log.info("[Future-Cancel-All-Order] - Received cancel order request: symbol={} ", symbol);
        if (ObjectUtils.isEmpty(member.getId())) {
            log.warn("[Future-Cancel-All-Order]- Validation failed: missing memberId (actual: {})", member.getId());
            return ResponseEntity.badRequest()
                    .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.MEMBER_NOT_EXIST)));
        }

        log.info("[Future-Cancel-All-Order] - cancelAllOrders - start - memberId: {}, symbol={}", member.getId(), symbol);
        placeOrderService.cancelAllOrdersByRequest(member.getId(), symbol);
        return ResponseEntity.ok(ResponseGlobal.success(messageSourceService.getMessage(ErrorMessages.SUCCESS)));
    }

    /**
     * Get a specific order by orderId.
     *
     * @param orderId The ID of the order
     * @return Order data if found
     * <p>
     * Error cases:
     * - 500 if fetch fails
     */
    @GetMapping("/orders/{orderId}")
    public ResponseEntity<ResponseGlobal<OrderResponseDTO>> getOrder(@PathVariable String orderId) {
        try {
            log.info("getOrder - fetching - orderId: {}", orderId);
            OrderResponseDTO order = placeOrderService.getOrder(orderId);
            return ResponseEntity.ok(ResponseGlobal.success(order));
        } catch (Exception e) {
            log.error("getOrder - failed - exception occurred", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(e.getMessage()));
        }
    }

    /**
     * Retrieves a paginated list of orders based on the provided search criteria.
     * Performs authentication using the current user's details and fetches orders for the specified member.
     *
     * @param member     the authenticated user details containing member information
     * @param requestDTO the search criteria for querying orders, including filters such as symbol
     * @return a ResponseEntity containing a paginated list of OrderResponseDTO wrapped in a ResponseGlobal object
     */
    @PostMapping("/orders/search")
    public ResponseEntity<ResponseGlobal<Page<OrderResponseDTO>>> getOrders(
            @CurrentUser AuthMember member,
            @RequestBody FindOrderRequestDTO requestDTO) {

        log.info("[Future-Order-Search] GetOrders - fetching - memberId: {}, symbol: {}",
                member.getId(), requestDTO.getSymbol());

        if (ObjectUtils.isEmpty(member.getId())) {
            log.warn("[Future-Order-Search]- GetOrders failed: missing memberId (actual: {})", member.getId());
            return ResponseEntity.badRequest().body(ResponseGlobal.error(messageSourceService.getMessage("MEMBER_NOT_EXIST")));
        }

        requestDTO.setMemberId(member.getId());
        log.info("[Future-Order-Search] - Request DTO: {}", requestDTO);
        return ResponseEntity.ok(ResponseGlobal.success(placeOrderService.findOrdersByRequest(requestDTO)));
    }

    /**
     * Get all active (open) orders for a symbol by the user.
     *
     * @param user   Authenticated user
     * @param symbol Trading pair (e.g., ETH/USDT)
     * @return List of open orders
     * <p>
     * Error cases:
     * - 500 if fetch fails
     */
    @GetMapping("/orders/member/symbol/{symbol}/active")
    public ResponseEntity<ResponseGlobal<List<OrderResponseDTO>>> getActiveOrders(
            @CurrentUser AuthMember user,
            @PathVariable String symbol) {
        try {
            log.info("getActiveOrders - fetching - memberId: {}, symbol: {}", user.getId(), symbol);
            List<OrderResponseDTO> orders = placeOrderService.getActiveOrders(user.getId(), symbol);
            return ResponseEntity.ok(ResponseGlobal.success(orders));
        } catch (Exception e) {
            log.error("getActiveOrders - failed - exception occurred", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(e.getMessage()));
        }
    }
}
