package com.bitcello.futureapi.controller;

import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.constant.ErrorMessages;
import com.bitcello.futureapi.dto.base.Page;
import com.bitcello.futureapi.dto.base.ResponseGlobal;
import com.bitcello.futureapi.dto.leverage.cmd.AdjustLeverageCommandDTO;
import com.bitcello.futureapi.dto.leverage.request.AdjustLeverageRequestDTO;
import com.bitcello.futureapi.dto.margin.cmd.AdjustMarginCommandDTO;
import com.bitcello.futureapi.dto.margin.request.AdjustMarginRequestDTO;
import com.bitcello.futureapi.dto.margin.request.ChangeMarginModeRequestDTO;
import com.bitcello.futureapi.dto.position.PositionDTO;
import com.bitcello.futureapi.dto.position.cmd.ClosePositionCommandDTO;
import com.bitcello.futureapi.dto.position.request.ClosePositionRequestDTO;
import com.bitcello.futureapi.exception.BusinessException;
import com.bitcello.futureapi.exception.DatabaseException;
import com.bitcello.futureapi.model.AuthMember;
import com.bitcello.futureapi.service.PositionService;
import com.bitcello.futureapi.utils.annotations.CurrentUser;
import com.bitcello.futureapi.utils.enums.MarginMode;
import com.bitcello.futureapi.utils.enums.PositionStatus;
import jakarta.validation.Valid;
import jakarta.validation.ValidationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Controller for position management
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/positions")
@RequiredArgsConstructor
public class PositionController {

    private final PositionService positionService;
    private final LocaleMessageSourceService messageSourceService;

    /**
     * Get position by memberId and symbol
     *
     * @param symbol Contract symbol
     * @return Position information
     */
    @GetMapping("/{symbol}")
    public ResponseEntity<ResponseGlobal<PositionDTO>> getPosition(
            @CurrentUser AuthMember user,
            @PathVariable String symbol) {
        log.info("Getting position, memberId={}, symbol={}", user.getId(), symbol);

        try {
            PositionDTO position = positionService.getPosition(user.getId(), symbol);

            if (position == null) {
                log.warn("Position not found, memberId={}, symbol={}", user.getId(), symbol);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ResponseGlobal.error("Position not found"));
            }

            return ResponseEntity.ok(ResponseGlobal.success(position));
        } catch (ValidationException e) {
            log.warn("Validation error when getting position: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ResponseGlobal.error(e.getMessage()));
        } catch (BusinessException e) {
            log.error("Business error when getting position: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(ResponseGlobal.error(e.getMessage()));
        } catch (DatabaseException e) {
            log.error("Database error when getting position: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.DATABASE_ERROR)));
        } catch (Exception e) {
            log.error("Unexpected error when getting position", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.EXCEPTION)));
        }
    }

    /**
     * Fallback for getting position
     *
     * @param user      Member information
     * @param symbol    Contract symbol
     * @param throwable Exception
     * @return Error message
     */
    public ResponseEntity<ResponseGlobal<PositionDTO>> getPositionFallback(AuthMember user, String symbol, Throwable throwable) {
        log.error("Fallback for getting position, memberId={}, symbol={}", user.getId(), symbol, throwable);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.EXCEPTION)));
    }

    /**
     * Get all positions for a member with filter parameters
     *
     * @param user      Member ID
     * @param status    Position status (OPEN, CLOSED, null for all)
     * @param startTime Start time (ISO format: yyyy-MM-ddTHH:mm:ss)
     * @param endTime   End time (ISO format: yyyy-MM-ddTHH:mm:ss)
     * @param page      Page number (starting from 0)
     * @param size      Page size
     * @return Paginated list of positions
     */
    @GetMapping()
    public ResponseEntity<ResponseGlobal<Object>> getAllPositions(
            @CurrentUser AuthMember user,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        log.info("Getting all positions, memberId={}, status={}, startTime={}, endTime={}, page={}, size={}",
                user.getId(), status, startTime, endTime, page, size);
        try {
            // If no filter parameters, use old method
            if (status == null && startTime == null && endTime == null) {
                List<PositionDTO> positions = positionService.getAllPositions(user.getId());
                return ResponseEntity.ok(ResponseGlobal.success(positions));
            } else {
                // Convert status from String to enum if provided
                PositionStatus positionStatus = null;
                if (status != null && !status.isEmpty()) {
                    positionStatus = PositionStatus.valueOf(status.toUpperCase());

                }

                // Convert startTime and endTime from String to LocalDateTime if provided
                LocalDateTime startDateTime = null;
                if (startTime != null && !startTime.isEmpty()) {
                    startDateTime = LocalDateTime.parse(startTime);

                }

                LocalDateTime endDateTime = null;
                if (endTime != null && !endTime.isEmpty()) {
                    endDateTime = LocalDateTime.parse(endTime);

                }

                // Call search method with filter parameters
                Page<PositionDTO> positionsPage = positionService.findPositions(
                        user.getId(), positionStatus, startDateTime, endDateTime, page, size);

                return ResponseEntity.ok(ResponseGlobal.success(positionsPage));
            }
        } catch (DatabaseException e) {
            log.error("Database error when getting all positions: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.DATABASE_ERROR)));
        } catch (Exception e) {
            log.error("Unexpected error when getting all positions", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(messageSourceService.getMessage(ErrorMessages.EXCEPTION)));
        }
    }

    /**
     * Fallback for getting all positions
     *
     * @param user      Member information
     * @param status    Position status
     * @param startTime Start time
     * @param endTime   End time
     * @param page      Page number
     * @param size      Page size
     * @param throwable Exception
     * @return Error message
     */
    public ResponseEntity<ResponseGlobal<Object>> getAllPositionsFallback(
            AuthMember user,
            String status,
            String startTime,
            String endTime,
            int page,
            int size,
            Throwable throwable) {
        log.error("Fallback for getting all positions, memberId={}, status={}, startTime={}, endTime={}, page={}, size={}",
                user.getId(), status, startTime, endTime, page, size, throwable);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(ResponseGlobal.error("Service temporarily unavailable, please try again later"));
    }

    /**
     * Close position
     *
     * @param request Request containing position closing information
     * @return Position information after closing
     */
    @PostMapping("/close")
    public ResponseEntity<ResponseGlobal<PositionDTO>> closePosition(
            @Valid @RequestBody ClosePositionRequestDTO request,
            @CurrentUser AuthMember member) {
        log.info("Closing position, request={}", request);

        ClosePositionCommandDTO command = ClosePositionCommandDTO.builder()
                .memberId(member.getId())
                .symbol(request.getSymbol())
                .volume(request.getVolume())
                .price(request.getPrice())
                .type(request.getType())
                .build();

        try {
            PositionDTO position = positionService.closePosition(command);
            return ResponseEntity.ok(ResponseGlobal.success(position));
        } catch (ValidationException e) {
            log.warn("Validation error when closing position: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ResponseGlobal.error(e.getMessage()));
        } catch (BusinessException e) {
            log.error("Business error when closing position: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(ResponseGlobal.error(e.getMessage()));
        } catch (DatabaseException e) {
            log.error("Database error when closing position: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ResponseGlobal.error("System error, please try again later"));
        } catch (Exception e) {
            log.error("Unexpected error when closing position", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ResponseGlobal.error("System error, please try again later"));
        }
    }

    /**
     * Fallback for closing position
     *
     * @param request   Request containing position closing information
     * @param member    Member information
     * @param throwable Exception
     * @return Error message
     */
    public ResponseEntity<ResponseGlobal<PositionDTO>> closePositionFallback(ClosePositionRequestDTO request, AuthMember member, Throwable throwable) {
        log.error("Fallback for closing position, request={}, memberId={}", request, member.getId(), throwable);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(ResponseGlobal.error("Service temporarily unavailable, please try again later"));
    }

    /**
     * Close all positions for a member
     *
     * @return Number of positions closed
     */
    @PostMapping("/close-all")
    public ResponseEntity<ResponseGlobal<Integer>> closeAllPositions(@CurrentUser AuthMember user) {
        log.info("Closing all positions, memberId={}", user.getId());
        try {
            int closedCount = positionService.closeAllPositions(user.getId());
            return ResponseEntity.ok(ResponseGlobal.success(closedCount));
        } catch (Exception e) {
            log.error("Error when closing all positions: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(e.getMessage()));
        }
    }

    /**
     * Adjust leverage for position
     *
     * @param request Request containing leverage adjustment information
     * @return Position information after adjustment
     */
    @PostMapping("/adjust-leverage")
    public ResponseEntity<ResponseGlobal<PositionDTO>> adjustLeverage(
            @Valid @RequestBody AdjustLeverageRequestDTO request,
            @CurrentUser AuthMember member) {
        log.info("Adjusting leverage, memberId={}, symbol={}, leverage={}",
                member.getId(), request.getSymbol(), request.getLeverage());

        AdjustLeverageCommandDTO command = AdjustLeverageCommandDTO.builder()
                .memberId(member.getId())
                .symbol(request.getSymbol())
                .leverage(request.getLeverage())
                .build();

        try {
            PositionDTO position = positionService.adjustLeverage(command);
            return ResponseEntity.ok(ResponseGlobal.success(position));
        } catch (Exception e) {
            log.error("Error when adjusting leverage: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(e.getMessage()));
        }
    }

    /**
     * Điều chỉnh ký quỹ cho vị thế
     *
     * @param request Request chứa thông tin điều chỉnh ký quỹ
     * @return Thông tin vị thế sau khi điều chỉnh
     */
    @PostMapping("/adjust-margin")
    public ResponseEntity<ResponseGlobal<PositionDTO>> adjustMargin(
            @Valid @RequestBody AdjustMarginRequestDTO request,
            @CurrentUser AuthMember member) {
        log.info("Adjusting margin, memberId={}, symbol={}, margin={}, isAdd={}",
                member.getId(), request.getSymbol(), request.getMargin(), request.isAdd());

        AdjustMarginCommandDTO command = AdjustMarginCommandDTO.builder()
                .memberId(member.getId())
                .symbol(request.getSymbol())
                .margin(request.getMargin())
                .isAdd(request.isAdd())
                .build();

        try {
            PositionDTO position = positionService.adjustMargin(command);
            return ResponseEntity.ok(ResponseGlobal.success(position));
        } catch (Exception e) {
            log.error("Error when adjusting margin: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(e.getMessage()));
        }
    }

    /**
     * Change margin mode for position
     *
     * @param request Request containing margin mode change information
     * @return Position information after change
     */
    @PostMapping("/change-margin-mode")
    public ResponseEntity<ResponseGlobal<PositionDTO>> changeMarginMode(
            @Valid @RequestBody ChangeMarginModeRequestDTO request,
            @CurrentUser AuthMember member) {
        log.info("Changing margin mode, memberId={}, symbol={}, marginMode={}",
                member.getId(), request.getSymbol(), request.getMarginMode());

        try {
            MarginMode marginMode = MarginMode.valueOf(request.getMarginMode());
            PositionDTO position = positionService.changeMarginMode(member.getId(), request.getSymbol(), marginMode);
            return ResponseEntity.ok(ResponseGlobal.success(position));
        } catch (Exception e) {
            log.error("Error when changing margin mode: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ResponseGlobal.error(e.getMessage()));
        }
    }

    /**
     * Calculate unrealized profit for position
     *
     * @param symbol Contract symbol
     * @return Unrealized profit
     */
    @GetMapping("/{symbol}/unrealized-profit")
    public ResponseEntity<ResponseGlobal<BigDecimal>> calculateUnrealizedProfit(
            @CurrentUser AuthMember user,
            @PathVariable String symbol) {
        log.info("Calculating unrealized profit, memberId={}, symbol={}", user.getId(), symbol);

        BigDecimal unrealizedProfit = positionService.calculateUnrealizedProfit(user.getId(), symbol);
        return ResponseEntity.ok(ResponseGlobal.success(unrealizedProfit));
    }

    /**
     * Calculate total unrealized profit for all positions of a member
     *
     * @return Total unrealized profit
     */
    @GetMapping("/total-unrealized-profit")
    public ResponseEntity<ResponseGlobal<BigDecimal>> calculateTotalUnrealizedProfit(
            @CurrentUser AuthMember user) {
        log.info("Calculating total unrealized profit, memberId={}", user.getId());

        BigDecimal totalUnrealizedProfit = positionService.calculateTotalUnrealizedProfit(user.getId());
        return ResponseEntity.ok(ResponseGlobal.success(totalUnrealizedProfit));
    }

}
