package com.bitcello.futureapi.service;

import com.bitcello.futureapi.dto.base.Page;
import com.bitcello.futureapi.dto.leverage.cmd.AdjustLeverageCommandDTO;
import com.bitcello.futureapi.dto.margin.cmd.AdjustMarginCommandDTO;
import com.bitcello.futureapi.dto.position.PositionDTO;
import com.bitcello.futureapi.dto.position.cmd.ClosePositionCommandDTO;
import com.bitcello.futureapi.entity.ContractSymbol;
import com.bitcello.futureapi.entity.Position;
import com.bitcello.futureapi.entity.Trade;
import com.bitcello.futureapi.utils.enums.MarginMode;
import com.bitcello.futureapi.utils.enums.PositionDirection;
import com.bitcello.futureapi.utils.enums.PositionStatus;
import com.bitcello.futureapi.utils.valueobject.Money;
import com.bitcello.futureapi.utils.valueobject.Symbol;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Interface for the PositionService
 * This service handles operations related to positions in a trading system.
 */
public interface PositionService {

    /**
     * Retrieves a position for a specific member and symbol.
     *
     * @param memberId The ID of the member.
     * @param symbol   The trading symbol of the position.
     * @return The PositionDTO representing the position.
     */
    PositionDTO getPosition(Long memberId, String symbol);

    /**
     * Updates the position after a trade has been executed.
     *
     * @param position The position to update.
     * @param trade    The trade that has been executed.
     * @param contract The contract symbol associated with the position.
     * @return The updated Position object.
     */
    Position updatePositionAfterTrade(Position position, Trade trade, ContractSymbol contract);

    /**
     * Retrieves all positions for a specific member.
     *
     * @param memberId The ID of the member.
     * @return A list of PositionDTOs representing all positions for the member.
     */
    List<PositionDTO> getAllPositions(Long memberId);

    /**
     * Creates a new position from a trade.
     *
     * @param trade    The trade that initiated the position.
     * @param memberId The ID of the member who owns the position.
     * @param contract The contract symbol associated with the position.
     * @return The newly created Position object.
     */
    Position createPositionFromTrade(Trade trade, Long memberId, ContractSymbol contract);

    /**
     * Calculates the liquidation price for a given position and contract.
     *
     * @param position The position for which to calculate the liquidation price.
     * @param contract The contract symbol associated with the position.
     * @return The calculated liquidation price as a BigDecimal.
     */
    BigDecimal calculateLiquidationPrice(Position position, ContractSymbol contract);

    /**
     * Checks if a position needs liquidation based on the current price and contract.
     *
     * @param position     The position to check.
     * @param currentPrice The current market price.
     * @param contract     The contract symbol associated with the position.
     * @return True if the position needs liquidation, false otherwise.
     */
    boolean needsLiquidation(Position position, BigDecimal currentPrice, ContractSymbol contract);

    /**
     * Calculates the unrealized profit for a given position based on the current price.
     *
     * @param position     The position for which to calculate unrealized profit.
     * @param currentPrice The current market price.
     * @return The unrealized profit as a BigDecimal.
     */
    BigDecimal calculateUnrealizedProfit(Position position, BigDecimal currentPrice);

    /**
     * Calculates the profit ratio for a given position based on the current price.
     *
     * @param position     The position for which to calculate profit ratio.
     * @param currentPrice The current market price.
     * @return The profit ratio as a BigDecimal.
     */
    BigDecimal calculateProfitRatio(Position position, BigDecimal currentPrice);

    /**
     * Calculates the total position value for a list of positions based on current prices.
     *
     * @param positions     The list of positions to calculate the total value for.
     * @param currentPrices A map of current prices for each symbol.
     * @return The total position value as a Money object.
     */
    Money calculateTotalPositionValue(List<Position> positions, Map<Symbol, Money> currentPrices);

    /**
     * Calculates the total unrealized profit for a list of positions based on current prices.
     *
     * @param positions     The list of positions to calculate the total unrealized profit for.
     * @param currentPrices A map of current prices for each symbol.
     * @return The total unrealized profit as a BigDecimal.
     */
    BigDecimal calculateTotalUnrealizedProfit(List<Position> positions, Map<String, BigDecimal> currentPrices);

    /**
     * Closes a position at a specified price and volume.
     *
     * @param position    The position to close.
     * @param closePrice  The price at which to close the position.
     * @param closeVolume The volume to close.
     * @return The updated Position object after closing.
     */
    Position closePosition(Position position, BigDecimal closePrice, BigDecimal closeVolume);

    /**
     * Flips a position to a new direction with specified volume and price.
     *
     * @param position     The position to flip.
     * @param newDirection The new direction for the position.
     * @param newVolume    The new volume for the flipped position.
     * @param newPrice     The new price for the flipped position.
     * @return The updated Position object after flipping.
     */
    Position flipPosition(Position position, PositionDirection newDirection, BigDecimal newVolume, BigDecimal newPrice);

    /**
     * Finds profitable positions by symbol and direction with a limit on the number of results.
     *
     * @param symbol    The trading symbol to filter positions.
     * @param direction The direction of the position (e.g., LONG, SHORT).
     * @param limit     The maximum number of positions to return.
     * @return A list of profitable positions matching the criteria.
     */
    List<Position> findProfitablePositionsBySymbolAndDirection(String symbol, PositionDirection direction, int limit);

    /**
     * Retrieves the current mark price for a given symbol.
     *
     * @param symbol The trading symbol for which to retrieve the mark price.
     * @return The current mark price as a BigDecimal.
     */
    BigDecimal getMarkPrice(Symbol symbol);

    /**
     * Calculates the unrealized profit and loss (PnL) for a given position based on the mark price.
     *
     * @param position  The position for which to calculate unrealized PnL.
     * @param markPrice The current mark price.
     * @return The unrealized PnL as a BigDecimal.
     */
    BigDecimal calculateUnrealizedPnl(Position position, BigDecimal markPrice);

    /**
     * Saves a position to the database.
     *
     * @param position The position to save.
     * @return The saved Position object.
     */
    Position save(Position position);

    /**
     * Finds positions based on member ID, status, and time range with pagination.
     *
     * @param memberId  The ID of the member whose positions to find.
     * @param status    The status of the positions to filter by.
     * @param startTime The start time for filtering positions.
     * @param endTime   The end time for filtering positions.
     * @param page      The page number for pagination.
     * @param size      The number of items per page.
     * @return A paginated list of PositionDTOs matching the criteria.
     */
    Page<PositionDTO> findPositions(Long memberId, PositionStatus status,
                                    LocalDateTime startTime, LocalDateTime endTime, int page, int size);

    /**
     * Closes a position based on the provided command.
     *
     * @param command The command containing details for closing the position.
     * @return The PositionDTO representing the closed position.
     */
    PositionDTO closePosition(ClosePositionCommandDTO command);

    /**
     * Closes all positions for a specific member.
     *
     * @param memberId The ID of the member whose positions are to be closed.
     * @return The number of positions closed.
     */
    int closeAllPositions(Long memberId);

    /**
     * Adjusts the leverage for a specific position based on the provided command.
     *
     * @param command The command containing details for adjusting the leverage.
     * @return The PositionDTO representing the position after leverage adjustment.
     */
    PositionDTO adjustLeverage(AdjustLeverageCommandDTO command);

    /**
     * Adjusts the margin for a specific position based on the provided command.
     *
     * @param command The command containing details for adjusting the margin.
     * @return The PositionDTO representing the position after margin adjustment.
     */
    PositionDTO adjustMargin(AdjustMarginCommandDTO command);

    /**
     * Changes the margin mode for a specific member and symbol.
     *
     * @param memberId   The ID of the member whose margin mode is to be changed.
     * @param symbol     The trading symbol for which to change the margin mode.
     * @param marginMode The new margin mode to set.
     * @return The updated PositionDTO with the new margin mode.
     */
    PositionDTO changeMarginMode(Long memberId, String symbol, MarginMode marginMode);

    /**
     * Calculates the unrealized profit for a specific member and symbol.
     *
     * @param memberId The ID of the member whose unrealized profit is to be calculated.
     * @param symbol   The trading symbol for which to calculate unrealized profit.
     * @return The unrealized profit as a BigDecimal.
     */
    BigDecimal calculateUnrealizedProfit(Long memberId, String symbol);

    /**
     * Calculates the total unrealized profit for a specific member.
     *
     * @param memberId The ID of the member whose total unrealized profit is to be calculated.
     * @return The total unrealized profit as a BigDecimal.
     */
    BigDecimal calculateTotalUnrealizedProfit(Long memberId);

}
