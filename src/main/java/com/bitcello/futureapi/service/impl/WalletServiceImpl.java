package com.bitcello.futureapi.service.impl;

import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.constant.ErrorMessages;
import com.bitcello.futureapi.dto.wallet.MinusAmountWalletSpotDTO;
import com.bitcello.futureapi.entity.Wallet;
import com.bitcello.futureapi.exception.BusinessException;
import com.bitcello.futureapi.exception.DatabaseException;
import com.bitcello.futureapi.exception.RestApiException;
import com.bitcello.futureapi.repository.WalletRepository;
import com.bitcello.futureapi.service.TransactionService;
import com.bitcello.futureapi.service.WalletService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Domain service implementation cho Wallet
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletServiceImpl implements WalletService {

    private final WalletRepository walletRepository;
    private final TransactionService transactionService;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    private final LocaleMessageSourceService messageSourceService;

    @Value("${topic-kafka.minus.wallet-spot}")
    private String minusWalletSpotTopic;

    /**
     * Find wallet by ID
     *
     * @param id The ID of the wallet.
     * @return Wallet
     */
    @Override
    public Wallet findById(Long id) {
        return walletRepository.findById(id).orElse(null);
    }


    /**
     * Find wallet by member ID and coin type
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return Wallet
     */
    @Override
    public Wallet findByMemberIdAndCoin(Long memberId, String coin) {
        return walletRepository.findByMemberIdAndCoin(memberId, coin)
                .orElseThrow(() -> new BusinessException(messageSourceService.getMessage(ErrorMessages.WALLET_NOT_EXIST)));
    }

    /**
     * Find or create a wallet for a member with the specified coin type.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return Wallet
     */
    @Override
    public Wallet findOrCreateWallet(Long memberId, String coin) {
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        if (wallet == null) {
            wallet = this.createWallet(memberId, coin);
        }
        if (wallet.isLocked()) {
            throw new IllegalArgumentException("wallet is locked");
        }
        return wallet;
    }

    /**
     * Find all wallets associated with a member.
     *
     * @param memberId The ID of the member.
     * @return List of Wallet
     */
    @Override
    public List<Wallet> findAllByMemberId(Long memberId) {
        return walletRepository.findAllByMemberId(memberId);
    }

    /**
     * Create a new wallet for a member with the specified coin type.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return Wallet
     */
    @Override
    public Wallet createWallet(Long memberId, String coin) {
        log.info("Tạo ví mới, memberId = {}, coin = {}", memberId, coin);

        // Checking if the wallet already exists
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        if (wallet != null) {
            log.error("Wallet already exists for this member and coin, memberId = {}, coin = {}", memberId, coin);
            throw new IllegalArgumentException("Wallet already exists for this member and coin");
        }

        // Create a new wallet
        wallet = Wallet.builder()
                .memberId(memberId)
                .coin(coin)
                .balance(BigDecimal.ZERO)
                .frozenBalance(BigDecimal.ZERO)
                .availableBalance(BigDecimal.ZERO)
                .unrealizedPnl(BigDecimal.ZERO)
                .realizedPnl(BigDecimal.ZERO)
                .usedMargin(BigDecimal.ZERO)
                .totalFee(BigDecimal.ZERO)
                .totalFundingFee(BigDecimal.ZERO)
                .isLocked(false)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // Save the wallet
        wallet = walletRepository.save(wallet);

        return wallet;
    }

    /**
     * Deposit money into the wallet
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The initial balance for the wallet.
     * @return Wallet
     */
    @Override
    @Transactional
    public Wallet deposit(Long memberId, String coin, BigDecimal amount) {
        log.info("Deposit money into the wallet, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        // Checking if the amount is valid
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("Amount Invalid, amount = {}", amount);
            throw new IllegalArgumentException("Amount Invalid");
        }

        // Find or create the wallet
        return increaseBalance(memberId, coin, amount);
    }

    /**
     * Withdraw money from the wallet
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to withdraw.
     * @return Wallet
     */
    @Override
    @Transactional
    public Wallet withdraw(Long memberId, String coin, BigDecimal amount) {
        log.info("Withdraw money from the wallet, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        // Checking if the amount is valid
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Amount Invalid");
        }

        // Find the wallet
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        if (wallet == null) {
            log.error("Wallet not found, memberId = {}, coin = {}", memberId, coin);
            throw new IllegalArgumentException("Wallet not found");
        }

        // Checking if the wallet is locked
        if (wallet.isLocked()) {
            log.error("Wallet is locked, memberId = {}, coin = {}", memberId, coin);
            throw new IllegalArgumentException("Wallet is locked");
        }

        // Checking if the available balance is sufficient
        if (wallet.getAvailableBalance().compareTo(amount) < 0) {
            log.error("Insufficient balance, availableBalance = {}, amount = {}", wallet.getAvailableBalance(), amount);
            throw new IllegalArgumentException("Insufficient balance");
        }

        // Update the wallet balance
        return decreaseBalance(memberId, coin, amount);
    }

    /**
     * Increase the balance of a wallet
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to increase the balance by.
     * @return Wallet
     */
    @Override
    public Wallet increaseBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("Increase the balance, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        // Checking if the amount is valid
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Amount Invalid");
        }

        // find or create the wallet
        Wallet wallet = this.findOrCreateWallet(memberId, coin);

        // Update the wallet balance
        int result = walletRepository.increaseBalance(wallet.getId(), amount);
        if (result <= 0) {
            log.error("Increase balance failed, walletId = {}, amount = {}", wallet.getId(), amount);
            throw new IllegalArgumentException("Increase balance failed");
        }

        // find the updated wallet
        wallet = findById(wallet.getId());

        return wallet;
    }

    /**
     * Decrease the balance of a wallet
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to decrease the balance by.
     * @return Wallet
     */
    @Override
    public Wallet decreaseBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("Decrease the balance of a wallet, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        // Checking if the amount is valid
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Amount Invalid");
        }

        // find the wallet
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        if (wallet == null) {
            throw new IllegalArgumentException("Wallet not found");
        }

        if (wallet.isLocked()) {
            throw new IllegalArgumentException("Member wallet is locked");
        }

        // Checking if the available balance is sufficient
        if (wallet.getAvailableBalance().compareTo(amount) < 0) {
            log.error("Available balance is insufficient, availableBalance = {}, amount = {}", wallet.getAvailableBalance(), amount);
            throw new IllegalArgumentException("Available balance is insufficient");
        }

        // Update the wallet balance
        int result = walletRepository.decreaseBalance(wallet.getId(), amount);
        if (result <= 0) {
            log.error("Failed to decrease balance, walletId = {}, amount = {}", wallet.getId(), amount);
            throw new IllegalArgumentException("Failed to decrease balance");
        }

        // find the updated wallet
        wallet = findById(wallet.getId());

        return wallet;
    }

    /**
     * Freeze the balance of a wallet
     *
     * @param memberId ID member
     * @param coin     The coin type.
     * @param amount   amount The amount to freeze.
     * @return Wallet
     */
    @Override
    @Transactional
    public Wallet freezeBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("[Freeze-balance] Start freezing balance | memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        // Check if the amount is valid
        if (BigDecimal.ZERO.compareTo(amount) > 0) {
            log.warn("[Freeze-balance] Invalid amount to freeze: {}", amount);
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.AMOUNT_INVALID));
        }

        // Find the wallet
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        log.debug("[Freeze-balance] Wallet found | walletId = {}, available = {}, frozen = {}",
                wallet.getId(), wallet.getAvailableBalance(), wallet.getFrozenBalance());

        // Checking if the wallet has enough balance
        if (wallet.getAvailableBalance().compareTo(amount) < 0) {
            log.warn("[Freeze-balance] Insufficient balance | walletId = {}, available = {}, required = {}",
                    wallet.getId(), wallet.getAvailableBalance(), amount);
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.WALLET_INSUFFICIENT_BALANCE));
        }

        // Subtract balance before update
        wallet.setAvailableBalance(wallet.getAvailableBalance().subtract(amount));
        log.debug("[Freeze-balance] Balance after subtraction | walletId = {}, available = {}",
                wallet.getId(), wallet.getAvailableBalance());

        // Update the wallet balance in DB
        int result = walletRepository.freezeBalance(wallet.getId(), amount);
        log.info("[Freeze-balance] Freeze balance DB update result = {}", result);
        if (result <= 0) {
            log.error("[Freeze-balance] Failed to freeze balance | walletId = {}", wallet.getId());
            throw new DatabaseException(
                    messageSourceService.getMessage(ErrorMessages.WALLET_FREEZE_BALANCE_FAILED));
        }

        // find the updated wallet
        wallet = findById(wallet.getId());
        log.info("[Freeze-balance] Freeze balance completed | walletId = {}, available = {}, frozen = {}",
                wallet.getId(), wallet.getAvailableBalance(), wallet.getFrozenBalance());

        return wallet;
    }


    /**
     * Unfreeze the balance of a wallet
     *
     * @param memberId ID member
     * @param coin     The coin type.
     * @param amount   amount The amount to freeze.
     * @return Wallet
     */
    @Override
    @Transactional
    public Wallet unfreezeBalance(Long memberId, String coin, BigDecimal amount) {
        log.info("[Unfreeze-balance] - Start Unfreeze the balance of a wallet, memberId = {}, coin = {}, amount = {}", memberId, coin, amount);

        // Checking if the amount is valid
        if (BigDecimal.ZERO.compareTo(amount) > 0) {
            log.warn("[Unfreeze-balance] Invalid amount to freeze: {}", amount);
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.AMOUNT_INVALID));
        }

        // Find the wallet
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);

        // Checking if the wallet has enough balance
        if (wallet.getFrozenBalance().compareTo(amount) < 0) {
            log.warn("[Unfreeze-balance] Insufficient frozen balance | walletId = {}, available = {}, required = {}",
                    wallet.getId(), wallet.getFrozenBalance(), amount);
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.WALLET_INSUFFICIENT_BALANCE));
        }

        // Update the wallet balance
        int result = walletRepository.unfreezeBalance(wallet.getId(), amount);

        if (result <= 0) {
            log.error("[Unfreeze-balance] Failed to unfreeze balance | walletId = {}", wallet.getId());
            throw new DatabaseException(
                    messageSourceService.getMessage(ErrorMessages.WALLET_UNFREEZE_BALANCE_FAILED));
        }
        // Find the updated wallet
        wallet = findById(wallet.getId());

        return wallet;
    }

    /**
     * Update the wallet balance
     *
     * @param memberId    The ID of the member.
     * @param coin        The coin type.
     * @param amount      The amount to update the balance by.
     * @param type        The type of transaction (e.g., deposit, withdrawal).
     * @param description A description of the transaction.
     * @return Wallet
     * @throws IllegalArgumentException if the amount is zero or if the wallet is locked.
     */
    @Override
    @Transactional
    public Wallet updateBalance(Long memberId, String coin, BigDecimal amount, String type, String description) {
        log.info("Update the wallet balance, memberId = {}, coin = {}, amount = {}, type = {}, description = {}",
                memberId, coin, amount, type, description);

        // Check if the wallet exists
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            log.error("Amount is zero, no update needed");
            return findByMemberIdAndCoin(memberId, coin);
        }

        // Check if the wallet is locked
        if (amount.compareTo(BigDecimal.ZERO) > 0) {
            return increaseBalance(memberId, coin, amount);
        }
        // Check if the wallet is locked
        else {
            try {

                return decreaseBalance(memberId, coin, amount.negate());
            } catch (IllegalArgumentException e) {
                log.error("Decrease balance failed, memberId = {}, coin = {}, amount = {}", memberId, coin, amount, e);
                // Nếu số dư không đủ, vẫn cập nhật số dư âm
                Wallet wallet = this.findOrCreateWallet(memberId, coin);

                // Cập nhật số dư
                wallet = Wallet.builder()
                        .id(wallet.getId())
                        .memberId(wallet.getMemberId())
                        .coin(wallet.getCoin())
                        .balance(wallet.getBalance().add(amount))
                        .frozenBalance(wallet.getFrozenBalance())
                        .availableBalance(wallet.getAvailableBalance().add(amount))
                        .unrealizedPnl(wallet.getUnrealizedPnl())
                        .realizedPnl(wallet.getRealizedPnl())
                        .usedMargin(wallet.getUsedMargin())
                        .totalFee(wallet.getTotalFee())
                        .totalFundingFee(wallet.getTotalFundingFee())
                        .isLocked(wallet.isLocked())
                        .createTime(wallet.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .build();

                // Lưu ví
                wallet = walletRepository.save(wallet);

                return wallet;
            }
        }
    }

    /**
     * Save a Wallet object to the database.
     *
     * @param wallet The Wallet object to save.
     * @return Wallet
     */
    @Override
    public Wallet save(Wallet wallet) {
        return walletRepository.save(wallet);
    }

    /**
     * Update the total fee for a wallet.
     *
     * @param walletId The ID of the wallet.
     * @param fee      The total fee to update.
     * @return Wallet
     * @throws IllegalArgumentException if the wallet does not exist or if the update fails.
     */
    @Override
    @Transactional
    public Wallet updateTotalFee(Long walletId, BigDecimal fee) {
        log.info("Updating total fee for wallet, walletId = {}, fee = {}", walletId, fee);
        // Find the wallet by ID
        Wallet wallet = findById(walletId);
        if (wallet == null) {
            throw new IllegalArgumentException("Wallet not found with ID: " + walletId);
        }

        // Update the total fee
        int result = walletRepository.updateTotalFee(walletId, fee);
        if (result <= 0) {
            log.error("Failed to update total fee for wallet, walletId = {}, fee = {}", walletId, fee);
            throw new IllegalArgumentException("Failed to update total fee for wallet");
        }

        // Find the updated wallet
        return findById(walletId);
    }

    /**
     * Update the total funding fee for a wallet.
     *
     * @param walletId    The ID of the wallet.
     * @param realizedPnl The total funding fee to update.
     * @return Wallet
     * @throws IllegalArgumentException if the wallet does not exist or if the update fails.
     */
    @Override
    @Transactional
    public Wallet updateRealizedPnl(Long walletId, BigDecimal realizedPnl) {
        log.info("Updating realized PnL for wallet, walletId = {}, realizedPnl = {}", walletId, realizedPnl);

        // Find the wallet by ID
        Wallet wallet = findById(walletId);
        if (wallet == null) {
            throw new IllegalArgumentException("Wallet not found with ID: " + walletId);
        }

        // Update the realized PnL
        int result = walletRepository.updateRealizedPnl(walletId, realizedPnl);
        if (result <= 0) {
            log.error("Failed to update realized PnL for wallet, walletId = {}, realizedPnl = {}", walletId, realizedPnl);
            throw new IllegalArgumentException("Failed to update realized PnL for wallet");
        }

        // Find the updated wallet
        return findById(walletId);
    }

    /**
     * Lock a wallet to prevent further transactions.
     *
     * @param memberId ID của thành viên
     * @param coin     Ký hiệu của đồng coin
     * @return boolean
     */
    @Override
    @Transactional
    public boolean lockWallet(Long memberId, String coin) {
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        if (wallet != null && !wallet.isLocked()) {
            wallet = Wallet.builder()
                    .id(wallet.getId())
                    .memberId(wallet.getMemberId())
                    .coin(wallet.getCoin())
                    .balance(wallet.getBalance())
                    .frozenBalance(wallet.getFrozenBalance())
                    .availableBalance(wallet.getAvailableBalance())
                    .unrealizedPnl(wallet.getUnrealizedPnl())
                    .realizedPnl(wallet.getRealizedPnl())
                    .usedMargin(wallet.getUsedMargin())
                    .totalFee(wallet.getTotalFee())
                    .totalFundingFee(wallet.getTotalFundingFee())
                    .isLocked(true)
                    .createTime(wallet.getCreateTime())
                    .updateTime(LocalDateTime.now())
                    .build();

            this.save(wallet);
            return true;
        } else {
            return false;
        }
    }

    /**
     * Unlock a wallet to allow further transactions.
     *
     * @param memberId ID của thành viên
     * @param coin     Ký hiệu của đồng coin
     * @return boolean
     */
    @Override
    @Transactional
    public boolean unlockWallet(Long memberId, String coin) {
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        if (wallet != null && wallet.isLocked()) {
            wallet = Wallet.builder()
                    .id(wallet.getId())
                    .memberId(wallet.getMemberId())
                    .coin(wallet.getCoin())
                    .balance(wallet.getBalance())
                    .frozenBalance(wallet.getFrozenBalance())
                    .availableBalance(wallet.getAvailableBalance())
                    .unrealizedPnl(wallet.getUnrealizedPnl())
                    .realizedPnl(wallet.getRealizedPnl())
                    .usedMargin(wallet.getUsedMargin())
                    .totalFee(wallet.getTotalFee())
                    .totalFundingFee(wallet.getTotalFundingFee())
                    .isLocked(false)
                    .createTime(wallet.getCreateTime())
                    .updateTime(LocalDateTime.now())
                    .build();

            this.save(wallet);
            return true;
        } else {
            return false;
        }
    }

    /**
     * Check if a member has enough balance in their wallet for a specific coin type.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to check against the wallet balance.
     * @return boolean
     */
    public boolean hasEnoughBalance(Long memberId, String coin, BigDecimal amount) {
        Wallet wallet = findByMemberIdAndCoin(memberId, coin);
        if (wallet == null) {
            return false;
        }
        return wallet.getAvailableBalance().compareTo(amount) >= 0;
    }

    /**
     * Check if a member has enough USDT balance in their wallet.
     *
     * @param memberId The ID of the member.
     * @param amount   The amount to check against the USDT balance.
     * @return boolean
     */
    @Override
    public boolean hasEnoughBalance(Long memberId, BigDecimal amount) {
        // Default to USDT for funding payment
        return hasEnoughBalance(memberId, "USDT", amount);
    }

    /**
     * Update the wallet balance for funding payment.
     *
     * @param memberId The ID of the member.
     * @param amount   The amount to update the balance by.
     * @return Wallet
     */
    @Override
    @Transactional
    public Wallet updateBalanceForFundingPayment(Long memberId, BigDecimal amount) {
        log.info("Updating wallet balance for funding payment, memberId = {}, amount = {}", memberId, amount);

        // Default to USDT for funding payment
        String coin = "USDT";

        // Checking if the amount is zero
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            log.info("Amount is zero, no update needed");
            return findByMemberIdAndCoin(memberId, coin);
        }

        // Find or create the wallet
        Wallet wallet;

        // Update the wallet balance based on the amount
        if (amount.compareTo(BigDecimal.ZERO) > 0) {
            // Tăng số dư
            wallet = this.increaseBalance(memberId, coin, amount);

        } else {
            try {
                // decrease balance
                wallet = this.decreaseBalance(memberId, coin, amount.negate());

            } catch (IllegalArgumentException e) {
                log.error("Giảm số dư thất bại, memberId = {}, coin = {}, amount = {}", memberId, coin, amount, e);

                // If the balance is not enough, still update the negative balance
                wallet = this.findOrCreateWallet(memberId, coin);

                // Cập nhật số dư
                wallet = Wallet.builder()
                        .id(wallet.getId())
                        .memberId(wallet.getMemberId())
                        .coin(wallet.getCoin())
                        .balance(wallet.getBalance().add(amount))
                        .frozenBalance(wallet.getFrozenBalance())
                        .availableBalance(wallet.getAvailableBalance().add(amount))
                        .unrealizedPnl(wallet.getUnrealizedPnl())
                        .realizedPnl(wallet.getRealizedPnl())
                        .usedMargin(wallet.getUsedMargin())
                        .totalFee(wallet.getTotalFee())
                        .totalFundingFee(wallet.getTotalFundingFee().add(amount.negate()))
                        .isLocked(wallet.isLocked())
                        .createTime(wallet.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .build();

                // save the wallet
                wallet = walletRepository.save(wallet);

            }
        }

        // Update the total funding fee
        wallet = Wallet.builder()
                .id(wallet.getId())
                .memberId(wallet.getMemberId())
                .coin(wallet.getCoin())
                .balance(wallet.getBalance())
                .frozenBalance(wallet.getFrozenBalance())
                .availableBalance(wallet.getAvailableBalance())
                .unrealizedPnl(wallet.getUnrealizedPnl())
                .realizedPnl(wallet.getRealizedPnl())
                .usedMargin(wallet.getUsedMargin())
                .totalFee(wallet.getTotalFee())
                .totalFundingFee(wallet.getTotalFundingFee().add(amount))
                .isLocked(wallet.isLocked())
                .createTime(wallet.getCreateTime())
                .updateTime(LocalDateTime.now())
                .build();

        // save the wallet
        wallet = walletRepository.save(wallet);

        return wallet;
    }

    /**
     * Send a message to Kafka to decrease the balance of a wallet in the spot market.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to decrease from the wallet balance.
     */
    @Override
    @SneakyThrows
    public void minusBalanceFromWalletSpot(Long memberId, String coin, BigDecimal amount) {
        MinusAmountWalletSpotDTO minusDto = MinusAmountWalletSpotDTO.builder()
                .memberId(memberId)
                .coin(coin)
                .amount(amount)
                .build();

        log.info("minusBalanceFromWalletSpot minusDto = {}", minusDto);
        log.info("send to kafka topic: {}, minusDto = {}", minusWalletSpotTopic, minusDto);
        kafkaTemplate.send(minusWalletSpotTopic, objectMapper.writeValueAsString(minusDto));
    }

    /**
     * Finds and retrieves a list of wallets corresponding to the specified member ID
     * and a list of base symbols.
     *
     * @param memberId the unique identifier of the member whose wallets are to be retrieved
     * @param baseSymbols a list of base symbols to filter the wallets
     * @return a list of wallets matching the provided member ID and base symbols
     */
    @Override
    public List<Wallet> findByMemberIdAndBaseSymbol(Long memberId, List<String> baseSymbols) {
        log.info("FindByMemberIdAndBaseSymbol: memberId={}, baseSymbols={}", memberId, baseSymbols);
        return walletRepository.findByMemberIdAndCoinIn(memberId, baseSymbols);
    }
}
