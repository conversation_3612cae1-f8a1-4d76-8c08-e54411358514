package com.bitcello.futureapi.service.impl;

import com.bitcello.futureapi.config.KafkaTopicConfig;
import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.constant.ErrorMessages;
import com.bitcello.futureapi.dto.order.OrderResponseDTO;
import com.bitcello.futureapi.dto.order.cmd.PlaceOrderCommandDTO;
import com.bitcello.futureapi.dto.order.request.FindOrderRequestDTO;
import com.bitcello.futureapi.dto.order.response.PlaceOrderResponseDTO;
import com.bitcello.futureapi.entity.ContractSymbol;
import com.bitcello.futureapi.entity.MarkPrice;
import com.bitcello.futureapi.entity.Order;
import com.bitcello.futureapi.entity.Trade;
import com.bitcello.futureapi.entity.Wallet;
import com.bitcello.futureapi.exception.BusinessException;
import com.bitcello.futureapi.exception.RestApiException;
import com.bitcello.futureapi.exception.ValidationException;
import com.bitcello.futureapi.mapper.order.OrderMapper;
import com.bitcello.futureapi.messaging.command.OrderCommand;
import com.bitcello.futureapi.messaging.producer.OrderCommandProducer;
import com.bitcello.futureapi.repository.ContractSymbolRepository;
import com.bitcello.futureapi.repository.MarkPriceRepository;
import com.bitcello.futureapi.repository.OrderRepository;
import com.bitcello.futureapi.repository.TradeJpaRepository;
import com.bitcello.futureapi.service.LeverageService;
import com.bitcello.futureapi.service.PlaceOrderService;
import com.bitcello.futureapi.service.WalletService;
import com.bitcello.futureapi.utils.MapperUtils;
import com.bitcello.futureapi.utils.enums.OrderDirection;
import com.bitcello.futureapi.utils.enums.OrderStatus;
import com.bitcello.futureapi.utils.enums.OrderType;
import com.bitcello.futureapi.utils.restrictions.Restrictions;
import com.bitcello.futureapi.utils.restrictions.SpecificationBuilder;
import com.bitcello.futureapi.utils.valueobject.Symbol;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for placing orders
 * Implements PlaceOrderUseCase
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlaceOrderServiceImpl implements PlaceOrderService {

    private final OrderRepository orderRepository;
    private final ContractSymbolRepository contractSymbolRepository;
    private final WalletService walletService;
    private final OrderMapper orderMapper;
    private final OrderCommandProducer orderCommandProducer;
    private final LeverageService leverageService;
    private final LocaleMessageSourceService messageSourceService;
    private final MapperUtils mapperUtils;
    private final MarkPriceRepository markPriceRepository;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final KafkaTopicConfig kafkaTopicConfig;
    private final ObjectMapper objectMapper;
    private final TradeJpaRepository tradeJpaRepository;

    /**
     * Places a new futures order with full validation, margin freezing, and Kafka publishing.
     * <p>
     * If the order type is MARKET, it fetches the latest mark price as the entry price.
     * This method validates the contract, calculates margin requirements,
     * checks available wallet balance, validates notional limits,
     * freezes the required balance, persists the order, and sends it to Kafka.
     * <p>
     * The method uses retry logic for temporary database or transaction failures.
     *
     * @param command DTO containing order information (symbol, size, type, leverage, etc.)
     * @return OrderResponseDTO representing the saved order details
     * @throws ValidationException if order parameters are invalid (price, size, leverage, etc.)
     * @throws RestApiException    if the wallet or contract is not found or if balance is insufficient
     */
    @Override
    @Transactional
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            noRetryFor = {EntityNotFoundException.class, ValidationException.class}
    )
    public OrderResponseDTO placeOrder(PlaceOrderCommandDTO command) {
        final Long memberId = command.getMemberId();
        final BigDecimal size = command.getSize();
        BigDecimal price = getTriggerPriceByOrderType(command);
        command.setPrice(price);
        log.info("[Future-Order] Start placing order, memberId={}, symbol={}, price={}, size={}, leverage={}",
                memberId, command.getSymbol(), price, size, command.getLeverage());

        // 1. Validate contract and command params
        ContractSymbol contractSymbol = validateAndGetContract(command);
        String quoteSymbol = contractSymbol.getQuoteSymbol();

        // 2. Calculate Volume and Required Margin (frozenBalance)
        BigDecimal volume = price.multiply(size);
        BigDecimal frozenBalance = volume.divide(command.getLeverage(), 8, RoundingMode.HALF_UP);

        // 3. Check wallet balance
        Wallet wallet = walletService.findByMemberIdAndCoin(memberId, quoteSymbol);
        if (wallet == null) {
            log.error("[Future-Order] Wallet not found, memberId={}, quoteSymbol={}", memberId, quoteSymbol);
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.WALLET_NOT_FOUND));
        }

        if (wallet.getAvailableBalance().compareTo(frozenBalance) < 0) {
            log.error("[Future-Order] Insufficient balance, memberId={}, required={}, available={}",
                    memberId, frozenBalance, wallet.getAvailableBalance());
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.INSUFFICIENT_BALANCE));
        }

        // 4. Check Notional Value
        BigDecimal maxNotionalValue = wallet.getAvailableBalance().multiply(command.getLeverage());
        if (volume.compareTo(maxNotionalValue) > 0) {
            log.error("[Future-Order] Notional value exceeds limit, memberId={}, notionalValue={}, maxAllowed={}",
                    memberId, volume, maxNotionalValue);
            throw new ValidationException(messageSourceService.getMessage(ErrorMessages.NOTIONAL_VALUE_EXCEEDS_LIMIT,
                    new Object[]{maxNotionalValue}));
        }

        // 5. Create and save orders
        command.setVolume(volume);
        command.setFrozenMargin(frozenBalance);
        Order order = createOrderEntity(command, contractSymbol);
        walletService.freezeBalance(memberId, quoteSymbol, frozenBalance);
        log.info("[Future-Order] Frozen margin: memberId={}, frozenBalance={}", memberId, frozenBalance);

        Order savedOrder = orderRepository.save(order);
        log.info("[Future-Order] Order saved: orderId={}", savedOrder.getOrderId());

        // 6. Send Kafka
        sendOrderToKafka(savedOrder);
        log.info("[Future-Order] Order sent to Kafka: orderId={}", savedOrder.getOrderId());

        // 7. Create response
        return mapperUtils.mapTo(savedOrder, OrderResponseDTO.class);
    }

    /**
     * Validates the contract symbol and all order parameters against contract rules.
     * <p>
     * Checks include price bounds (min/max), trade amount (min/max),
     * leverage range, and required trigger price for stop orders.
     * Also performs validation for take-profit and stop-loss prices.
     *
     * @param command the incoming order command to validate
     * @return the validated ContractSymbol object from the database
     * @throws ValidationException if any of the validations fail
     * @throws RestApiException    if the contract is not found
     */
    private ContractSymbol validateAndGetContract(PlaceOrderCommandDTO command) {
        log.debug("[Future-Order] Looking for contract symbol: {}", command.getSymbol());

        // 1. Validate contract symbol
        ContractSymbol contractSymbol = contractSymbolRepository.findBySymbol(command.getSymbol())
                .orElseThrow(() -> new RestApiException(messageSourceService.getMessage(ErrorMessages.CONTRACT_NOT_EXIST)));

        log.info("[Future-Order] Contract found, contractId={}, contractSymbol={}", contractSymbol.getId(), contractSymbol.getSymbol());

        command.setContractId(contractSymbol.getId());

        // 2. Validate price
        if (command.getType() != OrderType.MARKET && ObjectUtils.isEmpty(command.getPrice())) {
            log.error("[Future-Order] Price cannot be empty for non-market order, type={}", command.getType());
            throw new ValidationException(messageSourceService.getMessage(ErrorMessages.PRICE_CANNOT_BE_EMPTY));
        }

        // 3. Validate price range (only for non-market orders)
        if (command.getType() != OrderType.MARKET && ObjectUtils.isNotEmpty(command.getPrice())) {
            if (command.getPrice().compareTo(contractSymbol.getMinPrice()) < 0) {
                log.error("[Future-Order] Price too low, price={}, minPrice={}", command.getPrice(), contractSymbol.getMinPrice());
                throw new ValidationException(messageSourceService.getMessage(ErrorMessages.PRICE_TOO_LOW,
                        new Object[]{contractSymbol.getMinPrice()}));
            }

            if (command.getPrice().compareTo(contractSymbol.getMaxPrice()) > 0) {
                log.error("[Future-Order] Price too high, price={}, maxPrice={}", command.getPrice(), contractSymbol.getMaxPrice());
                throw new ValidationException(messageSourceService.getMessage(ErrorMessages.PRICE_TOO_HIGH,
                        new Object[]{contractSymbol.getMaxPrice()}));
            }
        }

        // 4. Validate amount (size)
        log.info("[Future-Order] Validating trade amount, amount={}, minAmount={}, maxAmount={}",
                command.getSize(), contractSymbol.getMinTradeAmount(), contractSymbol.getMaxTradeAmount());

        if (command.getSize().compareTo(contractSymbol.getMinTradeAmount()) < 0) {
            log.error("[Future-Order] Size too low, amount={}, minAmount={}", command.getSize(), contractSymbol.getMinTradeAmount());
            throw new ValidationException(messageSourceService.getMessage(ErrorMessages.SIZE_TOO_LOW,
                    new Object[]{contractSymbol.getMinTradeAmount()}));
        }

        if (command.getSize().compareTo(contractSymbol.getMaxTradeAmount()) > 0) {
            log.error("[Future-Order] Size too high, amount={}, maxAmount={}", command.getSize(), contractSymbol.getMaxTradeAmount());
            throw new ValidationException(messageSourceService.getMessage(ErrorMessages.SIZE_TOO_HIGH,
                    new Object[]{contractSymbol.getMaxTradeAmount()}));
        }

        // 5. Validate trigger price for stop orders
        List<OrderType> orderTypes = Arrays.asList(
                OrderType.STOP_LOSS,
                OrderType.STOP_LOSS_LIMIT
        );
        if (orderTypes.contains(command.getType())) {
            log.info("[Future-Order] Validating trigger price for stop order, type={}, triggerPrice={}", command.getType(), command.getTriggerPrice());
            if (command.getTriggerPrice() == null) {
                log.error("[Future-Order] Trigger price cannot be empty for stop order, type={}", command.getType());
                throw new ValidationException(messageSourceService.getMessage(ErrorMessages.TRIGGER_PRICE_CANNOT_BE_EMPTY));
            }
            log.info("[Future-Order] Trigger price validation passed, triggerPrice={}", command.getTriggerPrice());
        }

        // 6. Validate leverage
        log.info("[Future-Order] Validating leverage, leverage={}, minLeverage={}, maxLeverage={}",
                command.getLeverage(), contractSymbol.getLeverageMin(), contractSymbol.getLeverageMax());
        if (command.getLeverage().compareTo(contractSymbol.getLeverageMin()) < 0 ||
                command.getLeverage().compareTo(contractSymbol.getLeverageMax()) > 0) {
            log.error("[Future-Order] Leverage out of range, leverage={}, minLeverage={}, maxLeverage={}",
                    command.getLeverage(), contractSymbol.getLeverageMin(), contractSymbol.getLeverageMax());
            throw new ValidationException(messageSourceService.getMessage(ErrorMessages.LEVERAGE_OUT_OF_RANGE,
                    new Object[]{contractSymbol.getLeverageMin(), contractSymbol.getLeverageMax()}));
        }
        log.info("[Future-Order] Leverage validation passed, leverage={}", command.getLeverage());

        // 7. Validate TP and SL prices for stop orders
        validateTakeProfitAndStopLoss(command);

        return contractSymbol;
    }

    /**
     * Validates take-profit (TP) and stop-loss (SL) prices based on entry price and direction.
     * <p>
     * For BUY orders:
     * - Stop-loss must be less than entry price
     * - Take-profit must be greater than entry price
     * <p>
     * For SELL orders:
     * - Stop-loss must be greater than entry price
     * - Take-profit must be less than entry price
     *
     * @param command the order command containing TP/SL and direction info
     * @throws ValidationException if TP or SL values are logically invalid
     */
    private void validateTakeProfitAndStopLoss(PlaceOrderCommandDTO command) {
        log.info("[Future-Order] Validating TP/SL: TP={}, SL={}, type={}, direction={}",
                command.getTakeProfitPrice(), command.getStopLossPrice(), command.getType(), command.getDirection());

        BigDecimal entryPrice = getTriggerPriceByOrderType(command);

        OrderDirection direction = command.getDirection();
        BigDecimal tp = command.getTakeProfitPrice();
        BigDecimal sl = command.getStopLossPrice();

        // Validate Stop Loss
        if (sl != null) {
            log.info("[Future-Order] Validating Stop Loss: SL={}, Entry={}", command.getStopLossPrice(), entryPrice);
            if (direction == OrderDirection.BUY && sl.compareTo(entryPrice) >= 0) {
                throw new ValidationException(messageSourceService.getMessage(ErrorMessages.STOP_LOSS_PRICE_BUY_INVALID));
            }
            if (direction == OrderDirection.SELL && sl.compareTo(entryPrice) <= 0) {
                throw new ValidationException(messageSourceService.getMessage(ErrorMessages.STOP_LOSS_PRICE_SELL_INVALID));
            }
        }

        // Validate Take Profit
        if (tp != null) {
            log.info("[Future-Order] Validating Take Profit: TP={}, Entry={}", command.getTakeProfitPrice(), entryPrice);
            if (direction == OrderDirection.BUY && tp.compareTo(entryPrice) <= 0) {
                throw new ValidationException(messageSourceService.getMessage(ErrorMessages.TAKE_PROFIT_PRICE_BUY_INVALID));
            }
            if (direction == OrderDirection.SELL && tp.compareTo(entryPrice) >= 0) {
                throw new ValidationException(messageSourceService.getMessage(ErrorMessages.TAKE_PROFIT_PRICE_SELL_INVALID));
            }
        }

        log.info("[Future-Order] TP/SL validation passed for entryPrice={}", entryPrice);
    }

    /**
     * Calculates the trigger price based on the order type specified in the command.
     * For MARKET or STOP_MARKET order types, it retrieves the market price from the order book.
     *
     * @param command the PlaceOrderCommandDTO object containing order details such as type, price, and symbol
     * @return the calculated trigger price as a BigDecimal. If the mark price is not found for the symbol,
     * it returns a default last price of BigDecimal.ONEư
     */
    private BigDecimal getTriggerPriceByOrderType(PlaceOrderCommandDTO command) {
        log.info("[Get price] Start get trigger price by type order: orderType={}", command.getType());
        BigDecimal entryPrice = command.getPrice();

        // Checking orderType of order for MARKET or STOP_MARKET get market price
        List<OrderType> orderTypes = Arrays.asList(OrderType.MARKET, OrderType.STOP_MARKET);
        if (orderTypes.contains(command.getType())) {
            // implement logic to get market price from order book
            Symbol symbol = Symbol.of(command.getSymbol());
            MarkPrice markPrice = markPriceRepository.findTopBySymbolOrderByCreateTimeDesc(symbol)
                    .orElse(null);

            if (ObjectUtils.isEmpty(markPrice) || markPrice.getLastPrice() == null) {
                log.error("[Future-Order] Mark price not found for symbol={}, using default price", command.getSymbol());
                entryPrice = BigDecimal.ONE;
            } else {
                entryPrice = markPrice.getLastPrice().getValue();
            }
        }
        return entryPrice;
    }


    /**
     * Send order to OrderCommandProducer
     *
     * @param order Order
     */
    private void sendOrderToKafka(Order order) {
        try {
            // Send order to OrderCommandProducer
            orderCommandProducer.sendPlaceOrderCommand(order);

            log.info("Sent order to OrderCommandProducer, orderId={}", order.getOrderId());
        } catch (Exception e) {
            log.error("Failed to send order to OrderCommandProducer", e);
        }
    }

    /**
     * Send cancel order to OrderCommandProducer
     *
     * @param order Order
     */
    private void sendCancelOrderToKafka(Order order) {
        try {
            // Send cancel order to OrderCommandProducer
            orderCommandProducer.sendCancelOrderCommand(order);

            log.info("Sent cancel order to OrderCommandProducer, orderId={}", order.getOrderId());
        } catch (Exception e) {
            log.error("Failed to send cancel order to OrderCommandProducer", e);
        }
    }

    /**
     * Cancels a specific order based on the given order ID, member ID, and symbol.
     * Performs validations, checks status eligibility, adjusts wallet balances,
     * and sends a cancellation event to Kafka.
     *
     * @param orderId  the unique identifier of the order to be canceled
     * @param memberId the unique identifier of the member associated with the order
     * @param symbol   the contract symbol associated with the order
     * @throws ValidationException if any input parameters are invalid
     * @throws BusinessException   if the order is not found
     * @throws RestApiException    if the order has already been completed,
     *                             has an invalid status for cancellation,
     *                             or wallet has insufficient frozen balance
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            noRetryFor = {ValidationException.class, RestApiException.class}
    )
    @Transactional
    public void cancelOrder(String orderId, Long memberId, String symbol) {
        log.info("[Future-Cancel-Order] - Start cancel process, orderId={}, memberId={}, symbol={}", orderId, memberId, symbol);

        // Validate input
        if (ObjectUtils.isEmpty(orderId)) {
            log.error("[Future-Cancel-Order] - Order ID is null");
            throw new ValidationException(messageSourceService.getMessage(ErrorMessages.ORDER_ID_NOT_NULL));
        }

        if (StringUtils.isBlank(symbol)) {
            log.error("[Future-Cancel-Order] - Symbol is blank");
            throw new ValidationException(messageSourceService.getMessage(ErrorMessages.CONTRACT_SYMBOL_NOT_BLANK));
        }

        // Find order
        log.debug("[Future-Cancel-Order] - Fetching order from DB");
        Order order = orderRepository.findByOrderIdAndSymbolAndMemberId(orderId, symbol, memberId)
                .orElseThrow(() -> {
                    log.warn("[Future-Cancel-Order] - Order not found, orderId={}, memberId={}, symbol={}", orderId, memberId, symbol);
                    return new BusinessException(messageSourceService.getMessage(ErrorMessages.ORDER_NOT_EXITS));
                });

        log.info("[Future-Cancel-Order] - Order found: orderId={}, status={}, isCompleted={}", orderId, order.getStatus(), order.getIsCompleted());

        // Check if already completed
        if (Boolean.TRUE.equals(order.getIsCompleted())) {
            log.warn("[Future-Cancel-Order] - Order already completed, cannot cancel, orderId={}", orderId);
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.ORDER_ALREADY_COMPLETED));
        }

        // Check allowed statuses
        List<OrderStatus> orderStatuses = Arrays.asList(
                OrderStatus.NEW,
                OrderStatus.PARTIALLY_FILLED,
                OrderStatus.PENDING
        );

        if (!orderStatuses.contains(order.getStatus())) {
            log.warn("[Future-Cancel-Order] - Invalid status for cancellation, orderId={}, status={}", orderId, order.getStatus());
            throw new RestApiException(messageSourceService.getMessage(ErrorMessages.ORDER_INVALID_STATUS_CANCEL));
        }

        // Kafka event
        log.info("[Future-Cancel-Order] - Sending cancellation event to Kafka, orderId={}", order.getOrderId());
        sendCancelOrderToKafka(order);
        log.info("[Future-Cancel-Order] - Order cancel process completed, orderId={}", orderId);
    }


    /**
     * Cancels all active orders for a given member and symbol by sending a cancellation request.
     * Orders eligible for cancellation must not be marked as completed.
     *
     * @param memberId the unique identifier of the member whose orders are to be canceled
     * @param symbol   the symbol of the orders to be canceled; if null or blank, cancels orders irrespective of the symbol
     * @throws BusinessException if no eligible orders are found, or if an error occurs during the cancellation process
     */
    @Override
    @Transactional
    public void cancelAllOrdersByRequest(Long memberId, String symbol) {
        try {
            log.info("[Future-Cancel-All-Order] - Start cancelling process | memberId={}, symbol={}", memberId, symbol);

            List<OrderStatus> activeStatuses = Arrays.asList(
                    OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED, OrderStatus.PENDING
            );

            // Fetch all active orders
            List<Order> activeOrders = StringUtils.isNotBlank(symbol)
                    ? orderRepository.findByMemberIdAndStatusInAndSymbol(memberId, activeStatuses, symbol)
                    : orderRepository.findByMemberIdAndStatusIn(memberId, activeStatuses);

            log.info("[Future-Cancel-All-Order] - Active orders fetched = {} | memberId={}, symbol={}",
                    activeOrders.size(), memberId, symbol);

            if (activeOrders.isEmpty()) {
                log.warn("[Future-Cancel-All-Order] - No active orders found | memberId={}, symbol={}", memberId, symbol);
                throw new BusinessException(messageSourceService.getMessage(ErrorMessages.ORDER_CANCEL_FAILED));
            }

            // Filter out completed orders
            List<Order> cancelOrdersRequest = activeOrders.stream()
                    .filter(order -> Boolean.FALSE.equals(order.getIsCompleted()))
                    .toList();

            log.info("[Future-Cancel-All-Order] - Orders eligible for cancellation = {} | memberId={}",
                    cancelOrdersRequest.size(), memberId);

            if (cancelOrdersRequest.isEmpty()) {
                log.warn("[Future-Cancel-All-Order] - No eligible orders for cancellation | memberId={}", memberId);
                throw new BusinessException(messageSourceService.getMessage(ErrorMessages.ORDER_CANCEL_FAILED));
            }

            // Send cancel commands via Kafka
            OrderCommand command = OrderCommand.builder()
                    .commandId(UUID.randomUUID().toString())
                    .type(OrderCommand.OrderCommandType.CANCEL_ALL_ORDER)
                    .orderList(cancelOrdersRequest)
                    .timestamp(LocalDateTime.now())
                    .build();

            String payload = objectMapper.writeValueAsString(command);
            kafkaTemplate.send(kafkaTopicConfig.getOrderCommands(), OrderCommand.OrderCommandType.CANCEL_ALL_ORDER.name(), payload);
            log.info("[Future-Cancel-All-Order] - Completed sending cancel commands | memberId={}, totalOrders={}",
                    memberId, cancelOrdersRequest.size());

        } catch (Exception e) {
            log.error("[Future-Cancel-All-Order] - Exception occurred | memberId={}, symbol={}, error={}",
                    memberId, symbol, e.getMessage(), e);
            throw new BusinessException(messageSourceService.getMessage(ErrorMessages.ORDER_CANCEL_FAILED));
        }
    }

    /**
     * Get order information
     *
     * @param orderId Order ID
     * @return Order information
     */
    @Override
    public OrderResponseDTO getOrder(String orderId) {
        log.info("Getting order information, orderId={}", orderId);
        log.info("OrderPersistencePort implementation: {}", orderRepository.getClass().getName());

        try {
            log.info("Finding order by orderId={}", orderId);

            Optional<Order> orderOpt = orderRepository.findById(orderId);
            log.info("Order search result: {}", orderOpt.isPresent() ? "Found" : "Not found");

            if (orderOpt.isEmpty()) {
                log.warn("Order not found in cache, trying to find directly from database, orderId={}", orderId);
                // Try to refresh cache and search again

                return null;
            }

            Order order = orderOpt.get();
            log.info("Retrieved order: {}", order);

            OrderResponseDTO orderDto = orderMapper.orderToDto(order);
            log.info("Converted order to DTO: {}", orderDto);

            return orderDto;
        } catch (Exception e) {
            log.error("Failed to get order information, orderId={}", orderId, e);
            throw e;
        }
    }

    /**
     * Find orders based on request filter and pagination
     *
     * @param requestDTO request filter for finding orders
     * @return paginated list of OrderResponseDTO
     */
    @Override
    public Page<OrderResponseDTO> findOrdersByRequest(FindOrderRequestDTO requestDTO) {
        log.info("[Future-Order-Search] Start fetching orders with request: {}", requestDTO);
        final String createTime = "createTime";
        Pageable pageable = PageRequest.of(requestDTO.getPageNo(), requestDTO.getPageSize(),
                Sort.by(createTime).descending());

        SpecificationBuilder<Order> specification = new SpecificationBuilder<>();
        specification.add(Restrictions.eq("memberId", requestDTO.getMemberId(), false));
        specification.add(Restrictions.eq("symbol", requestDTO.getSymbol(), true));
        specification.add(Restrictions.in("status", requestDTO.getStatus(), true));
        if (ObjectUtils.isNotEmpty(requestDTO.getStartTime()) && ObjectUtils.isNotEmpty(requestDTO.getEndTime())) {
            specification.add(Restrictions.gte(createTime, requestDTO.getStartTime(), true));
            specification.add(Restrictions.lte(createTime, requestDTO.getEndTime(), true));
        }
        log.info("[Future-Order-Search] Adding filter: specification =  {}", specification);

        Page<Order> orderPage = orderRepository.findAll(specification, pageable);
        log.info("[Future-Order-Search] Found {} orders for request: {}", orderPage.getTotalElements(), requestDTO);

        // Map Order entities to DTOs
        Map<String, BigDecimal> finalAverageMap = calculateAveragePriceIfNeeded(requestDTO, orderPage);
        return orderPage.map(order -> {
            OrderResponseDTO dto = mapperUtils.mapTo(order, OrderResponseDTO.class);
            dto.setAveragePrice(finalAverageMap.getOrDefault(order.getOrderId(), null));
            return dto;
        });
    }

    /**
     * Calculates the average price for orders if the specified conditions are met.
     * If the request status does not contain filled or partially filled statuses or if the provided orders are empty,
     * an empty map will be returned.
     *
     * @param requestDTO the data transfer object containing the filtering criteria for orders, including their statuses
     * @param orderPage  pageable data containing the list of orders to be processed
     * @return a map where the key is the order ID and the value is the calculated average price of the associated trades.
     * Returns an empty map if the conditions for calculation are not met or if no trades are found.
     */
    private Map<String, BigDecimal> calculateAveragePriceIfNeeded(FindOrderRequestDTO requestDTO, Page<Order> orderPage) {
        List<OrderStatus> filledStatuses = Arrays.asList(OrderStatus.FILLED, OrderStatus.PARTIALLY_FILLED);

        log.debug("[Future-Search-Average-Price] Checking if calculation is required...");
        if (ObjectUtils.isEmpty(requestDTO.getStatus()) ||
                filledStatuses.stream().noneMatch(requestDTO.getStatus()::contains)) {
            log.debug("[Future-Search-Average-Price] Skipping calculation: no FILLED or PARTIALLY_FILLED status found in request.");
            return Collections.emptyMap();
        }

        List<String> orderIds = orderPage.stream()
                .map(order -> String.valueOf(order.getOrderId()))
                .toList();

        if (orderIds.isEmpty()) {
            log.debug("[Future-Search-Average-Price] No orders found after filtering. Skipping calculation.");
            return Collections.emptyMap();
        }
        log.debug("[Future-Search-Average-Price] Found {} order(s) to calculate average price for.", orderIds.size());

        // Fetch all trades related to these orders in one query
        List<Trade> trades = tradeJpaRepository.findByBuyOrderIdInOrSellOrderIdIn(orderIds, orderIds);
        log.debug("[Future-Search-Average-Price] Retrieved {} trade(s) related to given orders.", trades.size());

        // Group and calculate average price
        Map<String, BigDecimal> result = trades.stream()
                .collect(Collectors.groupingBy(
                        trade -> orderIds.contains(trade.getBuyOrderId())
                                ? trade.getBuyOrderId()
                                : trade.getSellOrderId(),
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            BigDecimal totalQuote = list.stream()
                                    .map(t -> t.getPrice().multiply(t.getVolume()))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal totalVolume = list.stream()
                                    .map(Trade::getVolume)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal avgPrice = totalVolume.compareTo(BigDecimal.ZERO) == 0
                                    ? BigDecimal.ZERO
                                    : totalQuote.divide(totalVolume, 8, RoundingMode.HALF_UP);
                            log.trace("[Future-Search-Average-Price] OrderId={} -> TotalQuote={}, TotalVolume={}, AvgPrice={}",
                                    list.get(0).getBuyOrderId() != null ? list.get(0).getBuyOrderId() : list.get(0).getSellOrderId(),
                                    totalQuote, totalVolume, avgPrice);
                            return avgPrice;
                        })
                ));

        log.info("[Future-Search-Average-Price] Average price calculation completed for {} order(s).", result.size());
        return result;
    }

    /**
     * Get active orders by memberId and symbol
     *
     * @param memberId Member ID
     * @param symbol   Contract symbol
     * @return List of orders
     */
    @Override
    public List<OrderResponseDTO> getActiveOrders(Long memberId, String symbol) {
        log.info("Getting active orders, memberId={}, symbol={}", memberId, symbol);

        try {
            List<OrderStatus> activeStatuses = Arrays.asList(OrderStatus.NEW, OrderStatus.PARTIALLY_FILLED);

            List<Order> orders = orderRepository.findAllByMemberIdAndSymbolAndStatusIn(
                    memberId, symbol, activeStatuses);

            return orders.stream()
                    .map(orderMapper::orderToDto)
                    .toList();
        } catch (Exception e) {
            log.error("Failed to get active orders", e);
            throw e;
        }
    }

    /**
     * Update order
     *
     * @param orderId Order ID
     * @param price   New price
     * @param volume  New volume
     * @return Order update result
     */
    public PlaceOrderResponseDTO updateOrder(String orderId, BigDecimal price, BigDecimal volume) {
        log.info("Update order, orderId = {}, price = {}, volume = {}", orderId, price, volume);

        try {
            // find order by ID
            Optional<Order> orderOpt = orderRepository.findById(orderId);

            if (orderOpt.isEmpty()) {
                log.warn("Order not exits, orderId = {}", orderId);
                throw new EntityNotFoundException("Order not exits: " + orderId);
            }

            Order order = orderOpt.get();

            // Check if the order belongs to the member
            if (order.getStatus() != OrderStatus.NEW && order.getStatus() != OrderStatus.PARTIALLY_FILLED) {
                log.warn("Command cannot be updated due to invalid status, orderId = {}, status = {}", orderId, order.getStatus());
                throw new ValidationException("Command cannot be updated due to invalid status: " + order.getStatus());
            }

            // Create updated order with new price and volume
            log.info("Updating order with new price = {}, volume = {}", price, volume);
            Order updatedOrder = Order.builder()
                    .orderId(order.getOrderId())
                    .memberId(order.getMemberId())
                    .symbol(order.getSymbol())
                    .direction(order.getDirection())
                    .type(order.getType())
                    .price(price)
                    .volume(volume)
                    .dealVolume(order.getDealVolume())
                    .status(order.getStatus())
                    .createTime(order.getCreateTime())
                    .executeTime(LocalDateTime.now()) // Update execute time to now
                    .build();

            // Save updated order
            Order savedOrder = orderRepository.save(updatedOrder);

            // Send update order to Kafka
            sendUpdateOrderToKafka(savedOrder);

            return PlaceOrderResponseDTO.builder()
                    .success(true)
                    .orderId(savedOrder.getOrderId())
                    .message("Update order successfully")
                    .build();
        } catch (EntityNotFoundException | ValidationException e) {
            log.warn("Update order Failed : {}", e.getMessage());
            return PlaceOrderResponseDTO.builder()
                    .success(false)
                    .message(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("Update order Failed", e);
            return PlaceOrderResponseDTO.builder()
                    .success(false)
                    .message("Update order Failed: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Send update order to OrderCommandProducer
     *
     * @param order Order
     */
    private void sendUpdateOrderToKafka(Order order) {
        try {
            log.info("Send update to OrderCommandProducer, orderId = {}", order.getOrderId());
            orderCommandProducer.sendUpdateOrderCommand(order);
        } catch (Exception e) {
            log.error("Send update to OrderCommandProducer thất bại", e);
        }
    }

    /**
     * Create a new Order entity based on the PlaceOrderCommandDTO.
     * <p>
     * This method processes the command to create a new Order entity
     * with all necessary fields set, including volume calculation.
     *
     * @param command        the PlaceOrderCommandDTO containing order details
     * @param contractSymbol the ContractSymbol associated with the order
     * @return a new Order entity ready to be saved
     */
    private Order createOrderEntity(PlaceOrderCommandDTO command, ContractSymbol contractSymbol) {
        log.debug("[Future-Order] Creating new order entity");

        Order order = Order.builder()
                .memberId(command.getMemberId())
                .contractId(contractSymbol.getId())
                .symbol(command.getSymbol())
                .coinSymbol(contractSymbol.getBaseSymbol())
                .baseSymbol(contractSymbol.getQuoteSymbol())
                .direction(command.getDirection())
                .type(command.getType())
                // Use processed effective price
                .price(command.getPrice())
                .triggerPrice(command.getTriggerPrice())
                .volume(command.getVolume())
                .dealVolume(BigDecimal.ZERO)
                .dealMoney(BigDecimal.ZERO)
                // Initial fee is 0, will be updated when order is matched
                // Fee rate information is taken from contract (takerFeeRate/makerFeeRate) when matching
                .fee(BigDecimal.ZERO)
                .status(OrderStatus.NEW)
                .createTime(LocalDateTime.now())
                .completeTime(null)
                .timeInForce(command.getTimeInForce())
                // Use processed leverage
                .leverage(command.getLeverage())
                .reduceOnly(command.getReduceOnly())
                .callbackRate(command.getCallbackRate())
                .activationPrice(null)
                .postOnly(command.getPostOnly())
                .cancelReason(null)
                .maxSlippage(command.getMaxSlippage())
                .fillOrKill(command.getFillOrKill())
                .immediateOrCancel(command.getImmediateOrCancel())
                .size(command.getSize())
                .futureMode(command.getFutureMode())
                .positionMode(command.getPositionMode())
                .stopLossPrice(command.getStopLossPrice())
                .takeProfitPrice(command.getTakeProfitPrice())
                .isCompleted(Boolean.FALSE)
                .filledVolume(BigDecimal.ZERO)
                .frozenMargin(command.getFrozenMargin())
                .build();

        log.info("[Future-Order] Order entity created successfully, orderId={}, order={}", order.getOrderId(), order);

        return order;
    }
}
