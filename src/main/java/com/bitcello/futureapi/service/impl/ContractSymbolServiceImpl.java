package com.bitcello.futureapi.service.impl;

import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.dto.contract.ContractSymbolDTO;
import com.bitcello.futureapi.entity.ContractSymbol;
import com.bitcello.futureapi.exception.BusinessException;
import com.bitcello.futureapi.exception.RestApiException;
import com.bitcello.futureapi.exception.ValidationException;
import com.bitcello.futureapi.mapper.contract.ContractSymbolDTOMapper;
import com.bitcello.futureapi.repository.ContractSymbolRepository;
import com.bitcello.futureapi.service.ContractSymbolService;
import com.bitcello.futureapi.utils.MapperUtils;
import com.bitcello.futureapi.utils.valueobject.Money;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of the {@link ContractSymbolService} interface.
 * <p>
 * This service provides core business logic for managing contract symbols,
 * including retrieval, creation, update, and deletion of contract entries.
 * </p>
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractSymbolServiceImpl implements ContractSymbolService {

    @Lazy
    private final ContractSymbolRepository contractSymbolRepository;
    private final ContractSymbolDTOMapper contractSymbolDTOMapper;
    private final LocaleMessageSourceService messageSourceService;
    private final MapperUtils mapperUtils;

    /**
     * Retrieve all contract symbols regardless of their enabled status.
     *
     * @return a list of all {@link ContractSymbolDTO}
     */
    @Override
    public List<ContractSymbolDTO> findAll() {
        List<ContractSymbolDTO> result = contractSymbolRepository.findAll().stream()
                .map(contractSymbolDTOMapper::toDto)
                .toList();
        log.info("findAll - Retrieved - {} contracts", result.size());
        return result;
    }

    /**
     * Retrieve all contract symbols that are currently enabled.
     *
     * @return a list of enabled {@link ContractSymbolDTO}
     */
    @Override
    public List<ContractSymbolDTO> findAllEnabled() {
        List<ContractSymbolDTO> result = contractSymbolRepository.findAllByEnable(Boolean.TRUE).stream()
                .map(contractSymbolDTOMapper::toDto)
                .toList();
        log.info("findAllEnabled - Retrieved - {} enabled contracts", result.size());
        return result;
    }

    /**
     * Find a contract symbol by its unique identifier.
     *
     * @param id the ID of the contract
     * @return the corresponding {@link ContractSymbolDTO}
     * @throws ValidationException if the ID is null or empty
     * @throws RestApiException if no contract is found with the given ID
     */
    @Override
    public ContractSymbolDTO findById(Long id) {
        log.info("[Find-contract-by-id] - Searching for contract with id: {}", id);
        if (ObjectUtils.isEmpty(id)) {
            log.error("[Find-contract-by-id] - Id is null");
            throw new ValidationException(messageSourceService.getMessage("CONTRACT_ID_INVALID"));
        }
        ContractSymbol contractSymbol = contractSymbolRepository.findById(id)
                .orElseThrow(() -> new RestApiException(messageSourceService.getMessage("CONTRACT_NOT_EXIST")));

        ContractSymbolDTO result = contractSymbolDTOMapper.toDto(contractSymbol);
        log.info("[Find-contract-by-symbol] - Retrieved - contract with id: {}", id);
        return result;
    }

    /**
     * Find a contract symbol by its symbol code.
     *
     * @param symbol the contract symbol string (e.g., BTCUSDT)
     * @return the corresponding {@link ContractSymbolDTO}
     * @throws ValidationException if the symbol is blank or null
     * @throws RestApiException if no contract is found with the given symbol
     */
    @Override
    public ContractSymbolDTO findBySymbol(String symbol) {
        log.info("[Find-contract-by-symbol] - Searching for contract with symbol: {}", symbol);
        if (StringUtils.isBlank(symbol)) {
            log.error("[Find-contract-by-symbol] - Symbol is blank or null");
            throw new ValidationException(messageSourceService.getMessage("CONTRACT_SYMBOL_INVALID"));
        }

        ContractSymbol contractSymbol = contractSymbolRepository.findBySymbol(symbol)
                .orElseThrow(() -> new RestApiException(messageSourceService.getMessage("CONTRACT_NOT_EXIST")));

        log.info("[Find-contract-by-symbol] - Retrieved - contract with symbol: {}", symbol);
        ContractSymbolDTO contractSymbolDTO = mapperUtils.mapTo(contractSymbol, ContractSymbolDTO.class);
        contractSymbolDTO.setMaxTradeAmount(Money.of(contractSymbol.getMaxTradeAmount()));
        contractSymbolDTO.setMinTradeAmount(Money.of(contractSymbol.getMinTradeAmount()));
        return contractSymbolDTO;
    }

    /**
     * Find an enabled contract by its symbol.
     *
     * @param symbol the contract symbol
     * @param enable the expected enabled status (should be 1)
     * @return the matching {@link ContractSymbolDTO} or null if not found
     */
    @Override
    public ContractSymbolDTO findBySymbolAndEnable(String symbol, Integer enable) {
        ContractSymbolDTO result = contractSymbolDTOMapper.toDto(contractSymbolRepository.findFirstBySymbolAndEnable(symbol, Boolean.TRUE));
        log.info("findBySymbolAndEnable - Retrieved - contract with symbol: {} and enabled status", symbol);
        return result;
    }

    /**
     * Create a new contract symbol entry.
     *
     * @param contractSymbolDto the DTO containing contract data
     * @return the newly created {@link ContractSymbolDTO}
     */
    @Override
    @Transactional
    public ContractSymbolDTO createContract(ContractSymbolDTO contractSymbolDto) {
        log.info("createContract - Creating - new contract: {}", contractSymbolDto);

        // Convert from DTO to entity
        ContractSymbol contractSymbol = contractSymbolDTOMapper.toEntity(contractSymbolDto);

        // Save contract
        ContractSymbol savedContractSymbol = contractSymbolRepository.save(contractSymbol);

        ContractSymbolDTO result = contractSymbolDTOMapper.toDto(savedContractSymbol);
        log.info("createContract - Created - contract with id: {}", result.getId());
        return result;
    }

    /**
     * Update an existing contract symbol by ID.
     *
     * @param id the ID of the contract to update
     * @param contractSymbolDto the updated DTO data
     * @return the updated {@link ContractSymbolDTO}
     * @throws BusinessException if the contract is not found
     */
    @Override
    @Transactional
    public ContractSymbolDTO updateContract(Long id, ContractSymbolDTO contractSymbolDto) {
        log.info("updateContract - Updating - contract: id = {}, contract = {}", id, contractSymbolDto);

        // Check if contract exists
        ContractSymbol contractSymbol = contractSymbolRepository.findById(id)
                .orElseThrow(() -> new BusinessException("Symbol not found"));
        contractSymbol.setBaseSymbol(contractSymbol.getBaseSymbol());
        contractSymbolDto.setId(id);
        
        // Convert from DTO to entity
        contractSymbol = contractSymbolDTOMapper.toEntity(contractSymbolDto);

        // Save contract
        ContractSymbol updatedContractSymbol = contractSymbolRepository.save(contractSymbol);

        ContractSymbolDTO result = contractSymbolDTOMapper.toDto(updatedContractSymbol);
        log.info("updateContract - Updated - contract with id: {}", result.getId());
        return result;
    }

    /**
     * Delete a contract symbol by ID.
     *
     * @param id the ID of the contract to delete
     * @throws BusinessException if the contract is not found
     */
    @Override
    @Transactional
    public void deleteContract(Long id) {
        log.info("deleteContract - Deleting - contract: id = {}", id);

        // Check if contract exists
        ContractSymbol contractSymbol = contractSymbolRepository.findById(id).orElseThrow(() -> new BusinessException("Symbol not found"));

        // Delete contract
        contractSymbolRepository.delete(contractSymbol);

        log.info("deleteContract - Deleted - contract with id: {}", id);
    }
}
