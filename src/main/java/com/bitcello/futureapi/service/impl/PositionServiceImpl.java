package com.bitcello.futureapi.service.impl;

import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.dto.base.Page;
import com.bitcello.futureapi.dto.leverage.cmd.AdjustLeverageCommandDTO;
import com.bitcello.futureapi.dto.margin.cmd.AdjustMarginCommandDTO;
import com.bitcello.futureapi.dto.position.PositionDTO;
import com.bitcello.futureapi.dto.position.cmd.ClosePositionCommandDTO;
import com.bitcello.futureapi.entity.ContractSymbol;
import com.bitcello.futureapi.entity.Order;
import com.bitcello.futureapi.entity.Position;
import com.bitcello.futureapi.entity.Trade;
import com.bitcello.futureapi.exception.BusinessException;
import com.bitcello.futureapi.exception.DatabaseException;
import com.bitcello.futureapi.exception.RestApiException;
import com.bitcello.futureapi.exception.ValidationException;
import com.bitcello.futureapi.mapper.potision.PositionMapper;
import com.bitcello.futureapi.messaging.producer.OrderCommandProducer;
import com.bitcello.futureapi.repository.ContractSymbolRepository;
import com.bitcello.futureapi.repository.PositionRepository;
import com.bitcello.futureapi.service.PositionOrderService;
import com.bitcello.futureapi.service.PositionService;
import com.bitcello.futureapi.service.PriceService;
import com.bitcello.futureapi.utils.data.PositionUtils;
import com.bitcello.futureapi.utils.data.TradeUtils;
import com.bitcello.futureapi.utils.enums.MarginMode;
import com.bitcello.futureapi.utils.enums.OrderDirection;
import com.bitcello.futureapi.utils.enums.OrderType;
import com.bitcello.futureapi.utils.enums.PositionDirection;
import com.bitcello.futureapi.utils.enums.PositionStatus;
import com.bitcello.futureapi.utils.valueobject.Money;
import com.bitcello.futureapi.utils.valueobject.Symbol;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Implementation của domain service PositionService
 * Cung cấp các chức năng liên quan đến vị thế giao dịch
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class PositionServiceImpl implements PositionService {

    private final PositionRepository positionRepository;
    private final PriceService priceService;
    private final ContractSymbolRepository contractSymbolRepository;
    private final PositionMapper positionMapper;
    private final PositionOrderService positionOrderService;
    private final OrderCommandProducer orderCommandProducer;
    private final LocaleMessageSourceService messageSourceService;

    /**
     * Get position by memberId and symbol with exception handling and retry logic
     *
     * @param memberId Member ID
     * @param symbol   Trading symbol
     * @return PositionDTO or null if not found
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public PositionDTO getPosition(Long memberId, String symbol) {
        try {
            log.debug("Get position by memberId and symbol, memberId = {}, symbol = {}", memberId, symbol);

            if (memberId == null) {
                throw new ValidationException("MemberId must not be null");
            }

            if (symbol == null || symbol.isEmpty()) {
                throw new ValidationException("Symbol must not be empty");
            }

            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbol(memberId, symbol);

            if (positionOpt.isEmpty()) {
                log.debug("Position not found, memberId = {}, symbol = {}", memberId, symbol);
                return null;
            }

            Position position = positionOpt.get();

            // Get current mark price
            BigDecimal currentPrice = priceService.getCurrentMarkPrice(symbol);

            // Get current index price
            BigDecimal indexPrice = priceService.getCurrentIndexPrice(Symbol.of(symbol));

            // Get contract info to calculate break-even price
            ContractSymbol contract = contractSymbolRepository.findBySymbol(symbol)
                    .orElseThrow(() -> new RestApiException(messageSourceService.getMessage("CONTRACT_NOT_EXIST")));

            // Calculate unrealized profit
            BigDecimal unrealizedProfit = calculateUnrealizedProfit(position, currentPrice);

            // Calculate profit ratio
            BigDecimal profitRatio = calculateProfitRatio(position, currentPrice);

            // Convert to DTO
            PositionDTO positionDto = positionMapper.toDto(position);
            positionDto.setUnrealizedProfit(unrealizedProfit);
            positionDto.setProfitRatio(profitRatio);
            positionDto.setMarkPrice(currentPrice);
            positionDto.setIndexPrice(indexPrice);

            // Calculate break-even price and margin ratio if contract is available
            if (contract != null) {
                BigDecimal breakEvenPrice = PositionUtils.calculateBreakEvenPrice(position, contract.getTakerFeeRate());
                positionDto.setBreakEvenPrice(breakEvenPrice);

                BigDecimal marginRatio = PositionUtils.calculateMarginRatio(position, currentPrice, contract.getMaintenanceMarginRate());
                positionDto.setMarginRatio(marginRatio);
            } else {
                positionDto.setBreakEvenPrice(position.getOpenPrice()); // Fallback to entry price
                positionDto.setMarginRatio(BigDecimal.ZERO);
            }

            log.debug("Successfully retrieved position, memberId = {}, symbol = {}", memberId, symbol);

            return positionDto;
        } catch (ValidationException e) {
            log.warn("Validation error when getting position: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error while getting position, memberId = {}, symbol = {}", memberId, symbol, e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error while getting position, memberId = {}, symbol = {}", memberId, symbol, e);
            throw new DatabaseException("Error while getting position: " + e.getMessage(), e);
        }
    }

    /**
     * Update the position after a new trade, with exception handling and retry logic
     *
     * @param position The current position
     * @param trade    The new trade
     * @param contract The contract information
     * @return The updated position
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position updatePositionAfterTrade(Position position, Trade trade, ContractSymbol contract) {
        try {
            log.info("updatePositionAfterTrade ==> Updating position after new trade, position = {}, trade = {}", position, trade);

            if (ObjectUtils.isEmpty(position)) {
                throw new ValidationException("Position must not be null");
            }

            if (ObjectUtils.isEmpty(trade)) {
                throw new ValidationException("Trade must not be null");
            }

            if (ObjectUtils.isEmpty(contract)) {
                throw new ValidationException("Contract must not be null");
            }

            if (!TradeUtils.involveMember(trade, position.getMemberId())) {
                throw new ValidationException("Trade does not involve this member");
            }

            // Determine trade direction for the member
            OrderDirection tradeDirection = TradeUtils.getDirectionForMember(trade, position.getMemberId());
            boolean isTradeOpeningPosition = false;
            boolean isTradeClosingPosition = false;

            // Determine if the trade is opening or closing the position
            if (position.getDirection() == PositionDirection.LONG && tradeDirection == OrderDirection.BUY) {
                isTradeOpeningPosition = true;
            } else if (position.getDirection() == PositionDirection.LONG && tradeDirection == OrderDirection.SELL) {
                isTradeClosingPosition = true;
            } else if (position.getDirection() == PositionDirection.SHORT && tradeDirection == OrderDirection.SELL) {
                isTradeOpeningPosition = true;
            } else if (position.getDirection() == PositionDirection.SHORT && tradeDirection == OrderDirection.BUY) {
                isTradeClosingPosition = true;
            }

            // Calculate the new position based on the trade type
            return getPosition(position, trade, contract, isTradeOpeningPosition, isTradeClosingPosition);
        } catch (DataAccessException | TransactionException e) {
            log.error("Error updating position after trade, position = {}, trade = {}", position, trade);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error when updating position after trade: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error when updating position after trade, position = {}, trade = {}", position, trade);
            throw new BusinessException("Error updating position: " + e.getMessage(), e);
        }
    }

    /**
     * Get the updated position based on the trade type (opening or closing)
     *
     * @param position               Current position
     * @param trade                  New trade
     * @param contract               Contract information
     * @param isTradeOpeningPosition Is the trade opening a position?
     * @param isTradeClosingPosition Is the trade closing a position?
     * @return Updated position
     */
    private Position getPosition(Position position, Trade trade, ContractSymbol contract, boolean isTradeOpeningPosition, boolean isTradeClosingPosition) {
        if (isTradeOpeningPosition) {
            // Increase position in the same direction (Binance-style: recalculate average entry price)
            BigDecimal oldValue = position.getOpenPrice().multiply(position.getVolume());
            BigDecimal newValue = trade.getPrice().multiply(trade.getVolume());
            BigDecimal totalValue = oldValue.add(newValue);
            BigDecimal newVolume = position.getVolume().add(trade.getVolume());
            BigDecimal newOpenPrice = totalValue.divide(newVolume, 8, RoundingMode.HALF_UP);

            // Update position with new average entry price
            return Position.builder()
                    .id(position.getId())
                    .memberId(position.getMemberId())
                    .symbol(position.getSymbol())
                    .direction(position.getDirection())
                    .volume(newVolume)
                    .openPrice(newOpenPrice)
                    .closePrice(null)
                    .liquidationPrice(calculateLiquidationPrice(position.getDirection(), newOpenPrice, contract))
                    .maintenanceMargin(calculateMaintenanceMargin(newOpenPrice, newVolume, contract))
                    .margin(calculateMargin(newOpenPrice, newVolume, position.getLeverage()))
                    .profit(null)
                    .marginMode(position.getMarginMode())
                    .leverage(position.getLeverage())
                    .createTime(position.getCreateTime())
                    .updateTime(LocalDateTime.now())
                    .remark(position.getRemark())
                    .status(PositionStatus.OPEN)
                    .build();
        } else if (isTradeClosingPosition) {
            // Reduce or close the position
            if (trade.getVolume().compareTo(position.getVolume()) < 0) {
                // Partial close – keep entry price
                BigDecimal newVolume = position.getVolume().subtract(trade.getVolume());

                // Calculate profit from closed portion
                BigDecimal partialProfit = calculatePartialProfit(position, trade.getPrice(), trade.getVolume());

                return Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(position.getDirection())
                        .volume(newVolume)
                        .openPrice(position.getOpenPrice()) // Keep original entry price
                        .closePrice(null)
                        .liquidationPrice(calculateLiquidationPrice(position.getDirection(), position.getOpenPrice(), contract))
                        .maintenanceMargin(calculateMaintenanceMargin(position.getOpenPrice(), newVolume, contract))
                        .margin(calculateMargin(position.getOpenPrice(), newVolume, position.getLeverage()))
                        .profit(partialProfit) // Record profit from closed portion
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.OPEN)
                        .build();
            } else if (trade.getVolume().compareTo(position.getVolume()) == 0) {
                // Full close
                BigDecimal totalProfit = calculateProfit(position, trade.getPrice());

                return Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(position.getDirection())
                        .volume(BigDecimal.ZERO)
                        .openPrice(position.getOpenPrice())
                        .closePrice(trade.getPrice())
                        .liquidationPrice(BigDecimal.ZERO)
                        .maintenanceMargin(BigDecimal.ZERO)
                        .margin(BigDecimal.ZERO)
                        .profit(totalProfit)
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.CLOSED) // Mark position as closed
                        .build();
            } else {
                // Reverse position (fully close current and open new in opposite direction)
                BigDecimal totalProfit = calculateProfit(position, trade.getPrice());
                BigDecimal newVolume = trade.getVolume().subtract(position.getVolume());
                PositionDirection newDirection = position.getDirection() == PositionDirection.LONG ?
                        PositionDirection.SHORT : PositionDirection.LONG;

                return Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(newDirection)
                        .volume(newVolume)
                        .openPrice(trade.getPrice()) // New entry price is trade price
                        .closePrice(null)
                        .liquidationPrice(calculateLiquidationPrice(newDirection, trade.getPrice(), contract))
                        .maintenanceMargin(calculateMaintenanceMargin(trade.getPrice(), newVolume, contract))
                        .margin(calculateMargin(trade.getPrice(), newVolume, position.getLeverage()))
                        .profit(totalProfit) // Record profit from closed position
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.OPEN)
                        .build();
            }
        } else {
            throw new ValidationException("Unable to determine trade type for position");
        }
    }

    /**
     * Get all positions for a member with exception handling and retry logic
     *
     * @param memberId Member ID
     * @return List of PositionDTO
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<PositionDTO> getAllPositions(Long memberId) {
        try {
            log.debug("Getting all positions for memberId = {}", memberId);

            if (memberId == null) {
                throw new ValidationException("MemberId must not be null");
            }

            List<Position> positions = positionRepository.findAllByMemberId(memberId);

            if (positions.isEmpty()) {
                log.debug("No positions found, memberId = {}", memberId);
                return List.of();
            }

            Map<String, BigDecimal> currentPrices = new HashMap<>();
            Map<String, BigDecimal> indexPrices = new HashMap<>();
            Map<String, ContractSymbol> contracts = new HashMap<>();

            for (Position position : positions) {
                String symbol = position.getSymbol();
                if (!currentPrices.containsKey(symbol)) {
                    currentPrices.put(symbol, priceService.getCurrentMarkPrice(symbol));
                    indexPrices.put(symbol, priceService.getCurrentIndexPrice(Symbol.of(symbol)));

                    Optional<ContractSymbol> contractSymbol = contractSymbolRepository.findBySymbol(symbol);
                    contractSymbol.ifPresent(contract -> contracts.put(symbol, contract));
                }
            }

            List<PositionDTO> result = positions.stream()
                    .map(position -> {
                        String symbol = position.getSymbol();
                        BigDecimal currentPrice = currentPrices.get(symbol);
                        BigDecimal indexPrice = indexPrices.get(symbol);
                        ContractSymbol contract = contracts.get(symbol);

                        BigDecimal unrealizedProfit = calculateUnrealizedProfit(position, currentPrice);
                        BigDecimal profitRatio = calculateProfitRatio(position, currentPrice);

                        PositionDTO positionDto = positionMapper.toDto(position);
                        positionDto.setUnrealizedProfit(unrealizedProfit);
                        positionDto.setProfitRatio(profitRatio);
                        positionDto.setMarkPrice(currentPrice);
                        positionDto.setIndexPrice(indexPrice);

                        if (contract != null) {
                            BigDecimal breakEvenPrice = PositionUtils.calculateBreakEvenPrice(position, contract.getTakerFeeRate());
                            positionDto.setBreakEvenPrice(breakEvenPrice);

                            BigDecimal marginRatio = PositionUtils.calculateMarginRatio(position, currentPrice, contract.getMaintenanceMarginRate());
                            positionDto.setMarginRatio(marginRatio);
                        } else {
                            log.warn("Contract not found for symbol = {}, cannot calculate break-even price", symbol);
                            positionDto.setBreakEvenPrice(position.getOpenPrice()); // Fallback to entry price
                            positionDto.setMarginRatio(BigDecimal.ZERO);
                        }

                        return positionDto;
                    })
                    .toList();

            log.debug("Successfully retrieved {} positions, memberId = {}", result.size(), memberId);

            return result;
        } catch (ValidationException e) {
            log.warn("Validation error when getting all positions: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error while getting all positions, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error while getting all positions, memberId = {}", memberId, e);
            throw new DatabaseException("Error while getting all positions: " + e.getMessage(), e);
        }
    }

    /**
     * Fallback method when updating position after trade fails after retries
     *
     * @param e        Exception
     * @param position Position
     * @param trade    Trade
     * @param contract Contract
     * @return Position
     */
    @Recover
    public Position recoverUpdatePositionAfterTrade(Exception e, Position position, Trade trade, ContractSymbol contract) {
        log.error("Retrying updatePositionAfterTrade failed 3 times, positionId = {}, tradeId = {}",
                position.getId() != null ? position.getId() : "null",
                trade.getId() != null ? trade.getId() : "null", e);
        throw new BusinessException("Failed to update position after 3 retries: " + e.getMessage(), e);
    }


    /**
     * Create a new position from a trade
     *
     * @param trade    Trade data
     * @param memberId Member ID
     * @param contract Contract
     * @return Newly created position
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position createPositionFromTrade(Trade trade, Long memberId, ContractSymbol contract) {
        try {
            log.info("createPositionFromTrade==>Creating new position from trade, trade = {}, memberId = {}",
                    trade, memberId);

            if (ObjectUtils.isEmpty(trade)) {
                throw new ValidationException("Trade must not be empty");
            }

            if (ObjectUtils.isEmpty(memberId)) {
                throw new ValidationException("MemberId must not be empty");
            }

            if (ObjectUtils.isEmpty(contract)) {
                throw new ValidationException("Contract must not be empty");
            }

            // Check if trade involves the member
            if (!TradeUtils.involveMember(trade, memberId)) {
                throw new ValidationException("Trade does not involve this member");
            }

            // Get trade direction for the member
            OrderDirection tradeDirection = TradeUtils.getDirectionForMember(trade, memberId);
            PositionDirection positionDirection = tradeDirection == OrderDirection.BUY ?
                    PositionDirection.LONG : PositionDirection.SHORT;

            // Create new position with calculations suitable for Binance futures
            BigDecimal entryPrice = trade.getPrice();
            BigDecimal volume = trade.getVolume();
            BigDecimal leverage = trade.getLeverage();

            // Get margin mode from trade instead of contract to ensure consistency
            // Trade already contains margin mode from the original Order
            MarginMode marginMode = determineMarginModeFromTrade(trade, contract);

            log.debug("Creating position from trade - leverage: {}, marginMode: {}, tradeId: {}",
                    leverage, marginMode, trade.getId());

            return Position.builder()
                    .id(null) // ID will be generated when saved to the database
                    .memberId(memberId)
                    .symbol(trade.getSymbol())
                    .direction(positionDirection)
                    .volume(volume)
                    .openPrice(entryPrice)
                    .closePrice(null)
                    .liquidationPrice(calculateLiquidationPrice(positionDirection, entryPrice, contract))
                    .maintenanceMargin(calculateMaintenanceMargin(entryPrice, volume, contract))
                    .margin(calculateMargin(entryPrice, volume, leverage))
                    .profit(null)
                    .marginMode(marginMode)
                    .leverage(leverage)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .remark(null)
                    .status(PositionStatus.OPEN) // Default status is OPEN
                    .build();
        } catch (DataAccessException | TransactionException e) {
            log.error("Error while creating position from trade, trade = {}, memberId = {}",
                    trade, memberId);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while creating position from trade: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Calculation error while creating position from trade, trade = {}, memberId = {}",
                    trade, memberId);
            throw new BusinessException("Calculation error while creating position: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error while creating position from trade, trade = {}, memberId = {}",
                    trade, memberId);
            throw new BusinessException("Error creating position: " + e.getMessage(), e);
        }
    }

    /**
     * Fallback method when position creation fails after retries
     *
     * @param e        Exception
     * @param trade    Trade
     * @param memberId Member ID
     * @param contract Contract
     * @return Position
     */
    @Recover
    public Position recoverCreatePositionFromTrade(Exception e, Trade trade, Long memberId, ContractSymbol contract) {
        log.error("Retried creating position from trade 3 times but failed, tradeId = {}, memberId = {}",
                trade.getId() != null ? trade.getId() : "null", memberId, e);
        throw new BusinessException("Unable to create position after 3 retries: " + e.getMessage(), e);
    }

    /**
     * Calculate liquidation price for a position with retry and exception handling
     *
     * @param position Position
     * @param contract Contract
     * @return Liquidation price
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal calculateLiquidationPrice(Position position, ContractSymbol contract) {
        try {
            log.debug("Calculating liquidation price for position, positionId = {}",
                    position.getId() != null ? position.getId() : "null");

            if (ObjectUtils.isEmpty(position)) {
                throw new ValidationException("Position must not be empty");
            }

            if (ObjectUtils.isEmpty(contract)) {
                throw new ValidationException("Contract must not be empty");
            }

            if (position.getVolume().compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            }

            BigDecimal liquidationPrice = PositionUtils.calculateLiquidationPrice(position, contract.getMaintenanceMarginRate());

            log.debug("Successfully calculated liquidation price, positionId = {}, liquidationPrice = {}",
                    position.getId(), liquidationPrice);

            return liquidationPrice;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error calculating liquidation price, positionId = {}",
                    position.getId() != null ? position.getId() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error calculating liquidation price: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Calculation error calculating liquidation price, positionId = {}",
                    position.getId() != null ? position.getId() : "null", e);
            throw new BusinessException("Calculation error for liquidation price: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unexpected error calculating liquidation price, positionId = {}",
                    position.getId() != null ? position.getId() : "null", e);
            throw new BusinessException("Error calculating liquidation price: " + e.getMessage(), e);
        }
    }

    /**
     * Fallback method when liquidation price calculation fails
     *
     * @param e        Exception
     * @param position Position
     * @param contract Contract
     * @return Money
     */
    @Recover
    public Money recoverCalculateLiquidationPrice(Exception e, Position position, ContractSymbol contract) {
        log.error("Retried liquidation price calculation 3 times but failed, positionId = {}",
                position.getId() != null ? position.getId() : "null", e);
        return new Money();
    }

    /**
     * Calculate liquidation price based on position direction, entry price, and contract info
     * Using Binance's exact formula
     *
     * @param direction  Position direction
     * @param entryPrice Entry price
     * @param contract   Contract
     * @return Liquidation price
     */
    private BigDecimal calculateLiquidationPrice(PositionDirection direction, BigDecimal entryPrice, ContractSymbol contract) {
        // ✅ USE correct formula from Position entity instead of wrong one
        // Create temporary position with volume = 1 for precise calculation
        BigDecimal volume = BigDecimal.ONE;
        BigDecimal leverage = contract.getLeverageMax(); // Use default leverage

        // Margin = (Entry Price × Volume) / Leverage
        BigDecimal margin = entryPrice.multiply(volume).divide(leverage, 8, RoundingMode.HALF_UP);

        // Temporary position using correct formula
        Position tempPosition = Position.builder()
                .direction(direction)
                .openPrice(entryPrice)
                .volume(volume)
                .margin(margin)
                .build();

        // Use accurate formula from Position entity
        return PositionUtils.calculateLiquidationPrice(tempPosition, contract.getMaintenanceMarginRate());
    }

    /**
     * Calculate maintenance margin based on entry price, volume, and contract
     * Volume is in USDT (notional value), not BTC
     */
    private BigDecimal calculateMaintenanceMargin(BigDecimal entryPrice, BigDecimal volume, ContractSymbol contract) {
        log.info("Calculating maintenance margin: entryPrice={}, volume={} USDT, rate={}",
                entryPrice, volume, contract.getMaintenanceMarginRate());
        // ✅ FIX: Volume is already in USDT notional value, no need to multiply by price
        // Maintenance Margin = Volume (USDT) × Maintenance Rate
        BigDecimal maintenanceMargin = volume.multiply(contract.getMaintenanceMarginRate());

        // Limit maintenance margin to not exceed DB limit (numeric(18,8))
        BigDecimal maxValue = new BigDecimal("999999999.99999999");
        if (maintenanceMargin.compareTo(maxValue) > 0) {
            log.warn("Maintenance margin exceeds DB limit, capping at max. Original: {}, Capped: {}",
                    maintenanceMargin, maxValue);
            maintenanceMargin = maxValue;
        }

        log.debug("Calculated maintenance margin: volume={} USDT, rate={}, margin={} USDT",
                volume, contract.getMaintenanceMarginRate(), maintenanceMargin);

        return maintenanceMargin;
    }

    /**
     * Calculate margin based on entry price, volume, and leverage
     * Volume is in USDT (notional value), not BTC
     */
    private BigDecimal calculateMargin(BigDecimal entryPrice, BigDecimal volume, BigDecimal leverage) {
        log.info("Calculating margin: entryPrice={}, volume={} USDT, leverage={}", entryPrice, volume, leverage);
        if (BigDecimal.ZERO.compareTo(volume) == 0 || BigDecimal.ZERO.compareTo(leverage) == 0) {
            return BigDecimal.ZERO;
        }

        // ✅ FIX: Volume is already in USDT notional value, no need to multiply by price
        // Margin = Volume (USDT) / Leverage
        BigDecimal margin = volume.divide(leverage, 8, RoundingMode.HALF_UP);

        // Limit margin to not exceed DB limit (numeric(18,8))
        BigDecimal maxValue = new BigDecimal("999999999.99999999");
        if (margin.compareTo(maxValue) > 0) {
            log.warn("Margin value exceeds DB limit, capping at max. Original: {}, Capped: {}",
                    margin, maxValue);
            margin = maxValue;
        }

        log.debug("Calculated margin: volume={} USDT, leverage={}, margin={} USDT",
                volume, leverage, margin);

        return margin;
    }

    /**
     * Calculate partial profit for a closed portion of a position
     */
    private BigDecimal calculatePartialProfit(Position position, BigDecimal closePrice, BigDecimal closedVolume) {
        BigDecimal profit;
        if (position.getDirection() == PositionDirection.LONG) {
            // Long: (ClosePrice - EntryPrice) * ClosedVolume
            profit = closePrice.subtract(position.getOpenPrice()).multiply(closedVolume);
        } else {
            // Short: (EntryPrice - ClosePrice) * ClosedVolume
            profit = position.getOpenPrice().subtract(closePrice).multiply(closedVolume);
        }
        return profit;
    }


    /**
     * Check whether a position needs to be liquidated with exception handling and retry.
     *
     * @param position     The position
     * @param currentPrice Current market price
     * @param contract     The contract
     * @return true if the position needs to be liquidated
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public boolean needsLiquidation(Position position, BigDecimal currentPrice, ContractSymbol contract) {
        try {
            log.info("Checking if position needs liquidation, position = {}, currentPrice = {}", position, currentPrice);

            if (position == null) {
                throw new ValidationException("Position must not be null");
            }

            if (currentPrice == null) {
                throw new ValidationException("CurrentPrice must not be null");
            }

            if (contract == null) {
                throw new ValidationException("Contract must not be null");
            }

            boolean needsLiquidation = PositionUtils.needsLiquidation(position, currentPrice, contract.getMaintenanceMarginRate());

            log.debug("Liquidation check result, positionId = {}, needsLiquidation = {}", position.getId(), needsLiquidation);

            return needsLiquidation;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error checking if position needs liquidation, position = {}, currentPrice = {}", position, currentPrice);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while checking if position needs liquidation: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while checking if position needs liquidation, position = {}, currentPrice = {}", position, currentPrice);
            throw new BusinessException("Error checking position liquidation: " + e.getMessage(), e);
        }
    }

    /**
     * Recovery method when checking position liquidation fails
     *
     * @param e            Exception
     * @param position     The position
     * @param currentPrice Current price
     * @param contract     The contract
     * @return false
     */
    @Recover
    public boolean recoverNeedsLiquidation(Exception e, Position position, Money currentPrice, ContractSymbol contract) {
        log.error("Retried 3 times but failed to check liquidation, positionId = {}, currentPrice = {}",
                position.getId() != null ? position.getId() : "null",
                currentPrice != null ? currentPrice.getValue() : "null", e);
        return false;
    }

    /**
     * Calculate unrealized profit for a position with exception handling and retry
     *
     * @param position     The position
     * @param currentPrice Current market price
     * @return Unrealized profit
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal calculateUnrealizedProfit(Position position, BigDecimal currentPrice) {
        try {
            log.info("Calculating unrealized profit, position = {}, currentPrice = {}", position, currentPrice);

            if (position == null) {
                throw new ValidationException("Position must not be null");
            }

            if (currentPrice == null) {
                throw new ValidationException("CurrentPrice must not be null");
            }

            BigDecimal unrealizedProfit = PositionUtils.calculateUnrealizedProfit(position, currentPrice);

            log.debug("Successfully calculated unrealized profit, positionId = {}, unrealizedProfit = {}",
                    position.getId(), unrealizedProfit);

            return unrealizedProfit;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error calculating unrealized profit,position = {}, currentPrice = {}", position, currentPrice);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while calculating unrealized profit: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Arithmetic error while calculating unrealized profit, position = {}, currentPrice = {}", position, currentPrice);
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Unexpected error while calculating unrealized profit, position = {}, currentPrice = {}", position, currentPrice);
            throw new BusinessException("Error calculating unrealized profit: " + e.getMessage(), e);
        }
    }

    /**
     * Recovery method when calculating unrealized profit fails
     *
     * @param e            Exception
     * @param position     The position
     * @param currentPrice Current market price
     * @return new Money()
     */
    @Recover
    public Money recoverCalculateUnrealizedProfit(Exception e, Position position, Money currentPrice) {
        log.error("Retried 3 times but failed to calculate unrealized profit, positionId = {}, currentPrice = {}",
                position.getId() != null ? position.getId() : "null",
                currentPrice != null ? currentPrice.getValue() : "null", e);
        return new Money();
    }

    /**
     * Calculate profit ratio for a position with exception handling and retry
     *
     * @param position     The position
     * @param currentPrice Current market price
     * @return Profit ratio
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal calculateProfitRatio(Position position, BigDecimal currentPrice) {
        try {
            log.info("Calculating profit ratio, position = {}, currentPrice = {}", position, currentPrice);

            if (position == null) {
                throw new ValidationException("Position must not be null");
            }

            if (currentPrice == null) {
                throw new ValidationException("CurrentPrice must not be null");
            }

            BigDecimal profitRatio = PositionUtils.calculateProfitRatio(position, currentPrice);

            log.debug("Successfully calculated profit ratio, positionId = {}, profitRatio = {}",
                    position.getId(), profitRatio);

            return profitRatio;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error calculating profit ratio,position = {}, currentPrice = {}", position, currentPrice);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while calculating profit ratio: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Arithmetic error while calculating profit ratio, position = {}, currentPrice = {}", position, currentPrice);
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Unexpected error while calculating profit ratio, position = {}, currentPrice = {}", position, currentPrice);
            throw new BusinessException("Error calculating profit ratio: " + e.getMessage(), e);
        }
    }

    /**
     * Recovery method when calculating profit ratio fails
     *
     * @param e            Exception
     * @param position     The position
     * @param currentPrice Current market price
     * @return BigDecimal.ZERO
     */
    @Recover
    public BigDecimal recoverCalculateProfitRatio(Exception e, Position position, Money currentPrice) {
        log.error("Retried 3 times but failed to calculate profit ratio, positionId = {}, currentPrice = {}",
                position.getId() != null ? position.getId() : "null",
                currentPrice != null ? currentPrice.getValue() : "null", e);
        return BigDecimal.ZERO;
    }

    /**
     * Calculate total position value for a member with exception handling and retry
     *
     * @param positions     List of positions
     * @param currentPrices Current prices per symbol
     * @return Total position value
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateTotalPositionValue(List<Position> positions, Map<Symbol, Money> currentPrices) {
        try {
            log.info("Calculating total position value, positions.size = {}, currentPrices.size = {}", positions, currentPrices);

            if (positions == null) {
                throw new ValidationException("Positions must not be null");
            }

            if (currentPrices == null) {
                throw new ValidationException("CurrentPrices must not be null");
            }

            Money totalValue = new Money();
            totalValue = getMoney(positions, currentPrices, totalValue);

            log.info("Successfully calculated total position value, totalValue = {}", totalValue.getValue());

            return totalValue;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error calculating total position value, positions.size = {}, currentPrices.size = {}", positions, currentPrices);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while calculating total position value", e);
            throw new BusinessException("Error calculating total position value: " + e.getMessage(), e);
        }
    }

    /**
     * Helper method to calculate total position value
     *
     * @param positions     List of positions
     * @param currentPrices Current prices per symbol
     * @param totalValue    Total value accumulator
     * @return Total position value
     */
    private static Money getMoney(List<Position> positions, Map<Symbol, Money> currentPrices, Money totalValue) {
        for (Position position : positions) {
            if (position != null && position.getSymbol() != null) {
                Money currentPrice = currentPrices.get(Symbol.of(position.getSymbol()));
                if (currentPrice != null && position.getVolume() != null && currentPrice.getValue() != null) {
                    BigDecimal positionValue = position.getVolume().multiply(currentPrice.getValue());

                    BigDecimal maxValue = new BigDecimal("9999999999.99999999");
                    if (positionValue.compareTo(maxValue) > 0) {
                        log.warn("Position value exceeds database limit, capping at maximum value. Symbol: {}, Original: {}, Capped: {}",
                                position.getSymbol(), positionValue, maxValue);
                        positionValue = maxValue;
                    }

                    totalValue = totalValue.add(Money.of(positionValue));
                }
            }
        }
        return totalValue;
    }

    /**
     * Recovery method when calculating total position value fails
     *
     * @param e             Exception
     * @param positions     List of positions
     * @param currentPrices Current prices per symbol
     * @return new Money()
     */
    @Recover
    public Money recoverCalculateTotalPositionValue(Exception e, List<Position> positions, Map<Symbol, Money> currentPrices) {
        log.error("Retried 3 times but failed to calculate total position value", e);
        return new Money();
    }

    /**
     * Calculate total unrealized profit for a member with exception handling and retry
     *
     * @param positions     List of positions
     * @param currentPrices Current prices per symbol
     * @return Total unrealized profit
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal calculateTotalUnrealizedProfit(List<Position> positions, Map<String, BigDecimal> currentPrices) {
        try {
            log.info("Calculating total unrealized profit, positions.size = {}, currentPrices.size = {}", positions, currentPrices);

            if (positions == null) {
                throw new ValidationException("Positions must not be null");
            }

            if (currentPrices == null) {
                throw new ValidationException("CurrentPrices must not be null");
            }

            BigDecimal totalProfit = BigDecimal.ZERO;

            for (Position position : positions) {
                if (position != null && position.getSymbol() != null) {
                    BigDecimal currentPrice = currentPrices.get(position.getSymbol());
                    if (currentPrice != null) {
                        totalProfit = totalProfit.add(calculateUnrealizedProfit(position, currentPrice));
                    }
                }
            }

            log.debug("Successfully calculated total unrealized profit, totalProfit = {}", totalProfit);

            return totalProfit;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error calculating total unrealized profit, positions.size = {}, currentPrices.size = {}", positions, currentPrices);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while calculating total unrealized profit: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Arithmetic error while calculating total unrealized profit", e);
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Unexpected error while calculating total unrealized profit", e);
            throw new BusinessException("Error calculating total unrealized profit: " + e.getMessage(), e);
        }
    }

    /**
     * Recovery method when calculating total unrealized profit fails
     *
     * @param e             Exception
     * @param positions     List of positions
     * @param currentPrices Current prices per symbol
     * @return new Money()
     */
    @Recover
    public Money recoverCalculateTotalUnrealizedProfit(Exception e, List<Position> positions, Map<Symbol, Money> currentPrices) {
        log.error("Retried 3 times but failed to calculate total unrealized profit", e);
        return new Money();
    }


    /**
     * Close position with exception handling and retry mechanism
     *
     * @param position    The position to close
     * @param closePrice  The closing price
     * @param closeVolume The volume to close
     * @return The updated position
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position closePosition(Position position, BigDecimal closePrice, BigDecimal closeVolume) {
        try {
            log.debug("Closing position, position = {}, closePrice = {}, closeVolume = {}",
                    position, closePrice, closeVolume);

            if (position == null) {
                throw new ValidationException("Position must not be null");
            }

            if (closePrice == null) {
                throw new ValidationException("ClosePrice must not be null");
            }

            if (closeVolume == null) {
                throw new ValidationException("CloseVolume must not be null");
            }

            if (closeVolume.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidationException("CloseVolume must be greater than 0");
            }

            if (closeVolume.compareTo(position.getVolume()) > 0) {
                throw new ValidationException("CloseVolume cannot be greater than position volume");
            }

            Position updatedPosition;

            if (closeVolume.compareTo(position.getVolume()) == 0) {
                // Close full position
                log.debug("Closing full position, positionId = {}", position.getId());

                updatedPosition = Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(position.getDirection())
                        .volume(BigDecimal.ZERO)
                        .openPrice(position.getOpenPrice())
                        .closePrice(closePrice)
                        .liquidationPrice(BigDecimal.ZERO)
                        .maintenanceMargin(BigDecimal.ZERO)
                        .margin(BigDecimal.ZERO)
                        .profit(calculateProfit(position, closePrice))
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.CLOSED)
                        .build();
            } else {
                // Partial close
                log.debug("Partially closing position, positionId = {}, closeVolume = {}",
                        position.getId(), closeVolume);

                BigDecimal remainingVolume = position.getVolume().subtract(closeVolume);

                updatedPosition = Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(position.getDirection())
                        .volume(remainingVolume)
                        .openPrice(position.getOpenPrice())
                        .closePrice(null)
                        .liquidationPrice(position.getLiquidationPrice())
                        .maintenanceMargin(position.getMaintenanceMargin().multiply(remainingVolume).divide(position.getVolume(), 8, RoundingMode.HALF_UP))
                        .margin(position.getMargin().multiply(remainingVolume).divide(position.getVolume(), 8, RoundingMode.HALF_UP))
                        .profit(null)
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.CLOSED)
                        .build();
            }

            log.debug("Position closed successfully, positionId = {}", position.getId());

            return updatedPosition;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error while closing position,  position = {}, closePrice = {}, closeVolume = {}",
                    position, closePrice, closeVolume);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error when closing position: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Calculation error when closing position, position = {}, closePrice = {}, closeVolume = {}",
                    position, closePrice, closeVolume);
            throw new BusinessException("Calculation error when closing position: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Unknown error when closing position, position = {}, closePrice = {}, closeVolume = {}",
                    position, closePrice, closeVolume);
            throw new BusinessException("Error while closing position: " + e.getMessage(), e);
        }
    }

    /**
     * Recovery method when closing position fails after retries
     *
     * @param e           Exception
     * @param position    The position to close
     * @param closePrice  The closing price
     * @param closeVolume The volume to close
     * @return Position
     */
    @Recover
    public Position recoverClosePosition(Exception e, Position position, Money closePrice, BigDecimal closeVolume) {
        log.error("Retried 3 times but failed to close position, positionId = {}, closePrice = {}, closeVolume = {}",
                position.getId() != null ? position.getId() : "null",
                closePrice != null ? closePrice.getValue() : "null",
                closeVolume, e);
        throw new BusinessException("Failed to close position after 3 retries: " + e.getMessage(), e);
    }


    /**
     * Flip position with exception handling and retry mechanism
     *
     * @param position     The position to flip
     * @param newDirection The new direction
     * @param newVolume    The new volume
     * @param newPrice     The new price
     * @return The updated position
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position flipPosition(Position position, PositionDirection newDirection, BigDecimal newVolume, BigDecimal newPrice) {
        try {
            log.debug("Flipping position, position = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                    position, newDirection, newVolume, newPrice);

            if (position == null) {
                throw new ValidationException("Position must not be null");
            }

            if (newDirection == null) {
                throw new ValidationException("NewDirection must not be null");
            }

            if (newVolume == null) {
                throw new ValidationException("NewVolume must not be null");
            }

            if (newPrice == null) {
                throw new ValidationException("NewPrice must not be null");
            }

            if (newVolume.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidationException("NewVolume must be greater than 0");
            }

            if (position.getDirection() == newDirection) {
                throw new ValidationException("New direction must be different from the current direction");
            }

            Position flippedPosition = Position.builder()
                    .id(position.getId())
                    .memberId(position.getMemberId())
                    .symbol(position.getSymbol())
                    .direction(newDirection)
                    .volume(newVolume)
                    .openPrice(newPrice)
                    .closePrice(null)
                    .liquidationPrice(null) // To be calculated after saving to database
                    .maintenanceMargin(null) // To be calculated after saving to database
                    .margin(null) // To be calculated after saving to database
                    .profit(null)
                    .marginMode(position.getMarginMode())
                    .leverage(position.getLeverage())
                    .createTime(position.getCreateTime())
                    .updateTime(LocalDateTime.now())
                    .remark(position.getRemark())
                    .build();

            log.debug("Successfully flipped position, positionId = {}", position.getId());

            return flippedPosition;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error while flipping position, position = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                    position, newDirection, newVolume, newPrice);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while flipping position: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error while flipping position,  position = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                    position, newDirection, newVolume, newPrice);
            throw new BusinessException("Error while flipping position: " + e.getMessage(), e);
        }
    }

    /**
     * Recovery method when flipping position fails after retries
     *
     * @param e            Exception
     * @param position     Position to flip
     * @param newDirection New direction
     * @param newVolume    New volume
     * @param newPrice     New price
     * @return Position
     */
    @Recover
    public Position recoverFlipPosition(Exception e, Position position, PositionDirection newDirection, BigDecimal newVolume, Money newPrice) {
        log.error("Retried 3 times but failed to flip position, positionId = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                position.getId() != null ? position.getId() : "null",
                newDirection,
                newVolume,
                newPrice != null ? newPrice.getValue() : "null", e);
        throw new BusinessException("Failed to flip position after 3 retries: " + e.getMessage(), e);
    }

    /**
     * Calculate realized profit for a position with exception handling
     *
     * @param position   Position
     * @param closePrice Closing price
     * @return Realized profit
     */
    private BigDecimal calculateProfit(Position position, BigDecimal closePrice) {
        BigDecimal profit;
        if (position.getDirection() == PositionDirection.LONG) {
            // Long: (ClosePrice - EntryPrice) * Volume
            profit = closePrice.subtract(position.getOpenPrice()).multiply(position.getVolume());
        } else {
            // Short: (EntryPrice - ClosePrice) * Volume
            profit = position.getOpenPrice().subtract(closePrice).multiply(position.getVolume());
        }

        // Cap profit to fit database precision (numeric(18,8))
        BigDecimal maxValue = new BigDecimal("9999999999.99999999");
        BigDecimal minValue = maxValue.negate();

        if (profit.compareTo(maxValue) > 0) {
            log.warn("Profit value exceeds database limit, capping at maximum value. PositionId: {}, Original: {}, Capped: {}",
                    position.getId() != null ? position.getId() : "null", profit, maxValue);
            profit = maxValue;
        } else if (profit.compareTo(minValue) < 0) {
            log.warn("Profit value exceeds database limit, capping at minimum value. PositionId: {}, Original: {}, Capped: {}",
                    position.getId() != null ? position.getId() : "null", profit, minValue);
            profit = minValue;
        }

        return profit;
    }

    /**
     * Find the most profitable positions by symbol and direction
     *
     * @param symbol    Contract symbol
     * @param direction Position direction
     * @param limit     Max number of positions
     * @return List of most profitable positions
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Position> findProfitablePositionsBySymbolAndDirection(String symbol, PositionDirection direction, int limit) {
        try {
            log.debug("Finding top profitable positions, symbol = {}, direction = {}, limit = {}", symbol, direction, limit);

            if (symbol == null) {
                throw new ValidationException("Symbol must not be null");
            }

            if (direction == null) {
                throw new ValidationException("Direction must not be null");
            }

            if (limit <= 0) {
                throw new ValidationException("Limit must be greater than 0");
            }

            BigDecimal markPrice = getMarkPrice(Symbol.of(symbol));
            List<Position> positions = positionRepository.findAllBySymbolAndDirection(symbol, direction);

            return positions.stream()
                    .filter(position -> position.getVolume().compareTo(BigDecimal.ZERO) > 0)
                    .sorted((p1, p2) -> {
                        BigDecimal profit1 = calculateUnrealizedPnl(p1, markPrice);
                        BigDecimal profit2 = calculateUnrealizedPnl(p2, markPrice);
                        return profit2.compareTo(profit1);
                    }).limit(limit).toList();
        } catch (DataAccessException | TransactionException e) {
            log.error("Error finding top profitable positions, symbol = {}, direction = {}", symbol, direction, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while finding top profitable positions: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error while finding top profitable positions, symbol = {}, direction = {}", symbol, direction, e);
            throw new BusinessException("Error finding top profitable positions: " + e.getMessage(), e);
        }
    }

    /**
     * Get the current mark price for a symbol
     *
     * @param symbol Contract symbol
     * @return Current mark price
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal getMarkPrice(Symbol symbol) {
        try {
            log.debug("Getting current mark price for symbol = {}", symbol);

            if (symbol == null) {
                throw new ValidationException("Symbol must not be null");
            }

            BigDecimal markPrice = priceService.getCurrentMarkPrice(String.valueOf(symbol));
            log.debug("Successfully retrieved mark price, symbol = {}, markPrice = {}", symbol, markPrice);

            return markPrice;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error getting mark price, symbol = {}", symbol, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while getting mark price: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error while getting mark price, symbol = {}", symbol, e);
            throw new BusinessException("Error getting mark price: " + e.getMessage(), e);
        }
    }

    /**
     * Calculate unrealized profit and loss (PnL) for a position
     *
     * @param position  Position
     * @param markPrice Current mark price
     * @return Unrealized PnL
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal calculateUnrealizedPnl(Position position, BigDecimal markPrice) {
        try {
            log.info("Calculating unrealized PnL, position = {}, markPrice = {}", position, markPrice);

            if (position == null) {
                throw new ValidationException("Position must not be null");
            }

            if (markPrice == null) {
                throw new ValidationException("MarkPrice must not be null");
            }

            return PositionUtils.calculateUnrealizedProfit(position, markPrice);

        } catch (DataAccessException | TransactionException e) {
            log.error("Error calculating unrealized PnL, position = {}, markPrice = {}", position, markPrice);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while calculating unrealized PnL: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Calculation error while calculating unrealized PnL, position = {}, markPrice = {}", position, markPrice);
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Unknown error while calculating unrealized PnL,position = {}, markPrice = {}", position, markPrice);
            throw new BusinessException("Error calculating unrealized PnL: " + e.getMessage(), e);
        }
    }

    /**
     * Save a position
     *
     * @param position Position to save
     * @return Saved position
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position save(Position position) {
        try {
            log.debug("Saving position, positionId = {}", position.getId() != null ? position.getId() : "null");

            if (position == null) {
                throw new ValidationException("Position must not be null");
            }

            Position savedPosition = positionRepository.save(position);
            log.debug("Position saved successfully, positionId = {}", savedPosition.getId());

            return savedPosition;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error while saving position, positionId = {}", position.getId() != null ? position.getId() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Validation error while saving position: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error while saving position, positionId = {}", position.getId() != null ? position.getId() : "null", e);
            throw new BusinessException("Error while saving position: " + e.getMessage(), e);
        }
    }


    /**
     * Determine margin mode from trade and contract
     *
     * @param trade    Executed trade
     * @param contract Contract information
     * @return Identified MarginMode
     */
    private MarginMode determineMarginModeFromTrade(Trade trade, ContractSymbol contract) {
        try {
            // Order entity doesn't have marginMode field yet
            // Need to add marginMode to Order entity to store from original Order
            // Currently using fallback logic to avoid UNKNOWN

            // Fallback 1: Use contract's default margin mode if available
            if (contract.getMarginMode() != null && contract.getMarginMode() != MarginMode.UNKNOWN) {
                log.debug("Using margin mode from contract: {}, tradeId: {}", contract.getMarginMode(), trade.getId());
                return contract.getMarginMode();
            }

            // Fallback 2: Use ISOLATED as default to avoid UNKNOWN
            log.debug("Using ISOLATED as default margin mode for trade: {}", trade.getId());
            return MarginMode.ISOLATED;

        } catch (Exception e) {
            log.error("Error determining margin mode from trade: {}, defaulting to ISOLATED", e.getMessage());
            return MarginMode.ISOLATED;
        }
    }

    /**
     * Find positions by member ID and status with exception handling and retry mechanism
     *
     * @param memberId  Member ID
     * @param status    Position status
     * @param startTime Start time for filtering
     * @param endTime   End time for filtering
     * @param page      Page number
     * @param size      Page size
     * @return Page of PositionDTOs
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Page<PositionDTO> findPositions(Long memberId, PositionStatus status,
                                           LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        try {
            log.debug("Searching positions, memberId = {}, status = {}, startTime = {}, endTime = {}, page = {}, size = {}",
                    memberId, status, startTime, endTime, page, size);

            if (memberId == null) {
                throw new jakarta.validation.ValidationException("MemberId cannot be null");
            }

            // Search positions by criteria
            org.springframework.data.domain.Page<Position> positionsPage = positionRepository.findByMemberIdAndStatusAndTimeRange(
                    memberId, status, startTime, endTime, PageRequest.of(page, size));

            if (positionsPage.isEmpty()) {
                log.debug("No positions found, memberId = {}", memberId);
                return Page.empty();
            }

            // Get current price for all symbols
            Map<String, BigDecimal> currentPrices = new HashMap<>();
            Map<String, BigDecimal> indexPrices = new HashMap<>();
            Map<String, ContractSymbol> contracts = new HashMap<>();

            for (Position position : positionsPage.getContent()) {
                String symbol = position.getSymbol();
                if (!currentPrices.containsKey(symbol)) {
                    currentPrices.put(symbol, priceService.getCurrentMarkPrice(symbol));
                    indexPrices.put(symbol, priceService.getCurrentIndexPrice(Symbol.of(symbol)));

                    // Get contract info
                    Optional<ContractSymbol> contractSymbol = contractSymbolRepository.findBySymbol(symbol);
                    contractSymbol.ifPresent(value -> contracts.put(symbol, value));
                }
            }

            // Convert to DTOs
            List<PositionDTO> positionDtos = positionsPage.getContent().stream()
                    .map(position -> {
                        String symbol = position.getSymbol();
                        BigDecimal currentPrice = currentPrices.get(symbol);
                        BigDecimal indexPrice = indexPrices.get(symbol);
                        ContractSymbol contract = contracts.get(symbol);

                        BigDecimal unrealizedProfit = calculateUnrealizedProfit(position, currentPrice);
                        BigDecimal profitRatio = calculateProfitRatio(position, currentPrice);

                        PositionDTO positionDto = positionMapper.toDto(position);
                        positionDto.setUnrealizedProfit(unrealizedProfit);
                        positionDto.setProfitRatio(profitRatio);
                        positionDto.setMarkPrice(currentPrice);
                        positionDto.setIndexPrice(indexPrice);

                        // Calculate break-even price and margin ratio if contract exists
                        if (contract != null) {
                            BigDecimal breakEvenPrice = PositionUtils.calculateBreakEvenPrice(position, contract.getTakerFeeRate());
                            positionDto.setBreakEvenPrice(breakEvenPrice);

                            BigDecimal marginRatio = PositionUtils.calculateMarginRatio(position, currentPrice, contract.getMaintenanceMarginRate());
                            positionDto.setMarginRatio(marginRatio);
                        } else {
                            log.warn("Contract not found for symbol = {}, cannot calculate break-even price", symbol);
                            positionDto.setBreakEvenPrice(position.getOpenPrice()); // Fallback to entry price
                            positionDto.setMarginRatio(BigDecimal.ZERO);
                        }

                        return positionDto;
                    })
                    .toList();

            // Create new Page with DTOs using custom Page class instead of PageImpl
            Page<PositionDTO> result = Page.<PositionDTO>builder()
                    .content(new ArrayList<>(positionDtos))
                    .totalElements(positionsPage.getTotalElements())
                    .totalPages(positionsPage.getTotalPages())
                    .number(positionsPage.getNumber())
                    .size(positionsPage.getSize())
                    .build();

            log.debug("Found {} positions, total: {}, memberId = {}",
                    result.getContent().size(), result.getTotalElements(), memberId);

            return result;
        } catch (jakarta.validation.ValidationException e) {
            log.warn("Validation error when searching positions: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error searching positions, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error searching positions, memberId = {}", memberId, e);
            throw new DatabaseException("Error searching positions: " + e.getMessage(), e);
        }
    }

    /**
     * Close a position with exception handling and retry mechanism
     *
     * @param command Close position command
     * @return Closed position DTO
     */
    @Override
    @Transactional
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public PositionDTO closePosition(ClosePositionCommandDTO command) {
        try {
            log.debug("Closing position, memberId = {}, symbol = {}, volume = {}, price = {}",
                    command.getMemberId(), command.getSymbol(), command.getVolume(), command.getPrice());

            if (command.getMemberId() == null) {
                throw new ValidationException("MemberId must not be null");
            }

            if (command.getSymbol() == null || command.getSymbol().isEmpty()) {
                throw new ValidationException("Symbol must not be empty");
            }

            if (command.getVolume() == null || command.getVolume().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidationException("Volume must be greater than 0");
            }

            if (command.getPrice() == null || command.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidationException("Price must be greater than 0");
            }

            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbol(command.getMemberId(), command.getSymbol());

            if (positionOpt.isEmpty()) {
                throw new EntityNotFoundException("Position not found");
            }

            Position position = positionOpt.get();

            // Validate closing volume
            if (command.getVolume().compareTo(position.getVolume()) > 0) {
                throw new ValidationException("Closing volume cannot exceed position volume");
            }

            // Step 1: Create close position order
            log.debug("Step 1: Create close position order");
            Order order;

            if (OrderType.MARKET.equals(command.getType())) {
                BigDecimal currentPrice = priceService.getCurrentMarkPrice(position.getSymbol());
                order = positionOrderService.createClosePositionOrder(position, command.getType(), currentPrice, command.getVolume());
            } else {
                order = positionOrderService.createClosePositionOrder(position, command.getType(), command.getPrice(), command.getVolume());
            }

            log.debug("Created close position order, orderId = {}", order.getOrderId());

            // Step 2: Send order to matching engine via Kafka
            log.debug("Step 2: Send order to matching engine");
            orderCommandProducer.sendPlaceOrderCommand(order);
            log.debug("Order sent to matching engine");

            // Step 3: Update position in the database
            log.debug("Step 3: Update position in database");
            Position closedPosition = closePosition(position, command.getPrice(), command.getVolume());

            log.debug("Position updated in database");

            log.info("Position closed successfully, memberId = {}, symbol = {}, volume = {}",
                    command.getMemberId(), command.getSymbol(), command.getVolume());

            return positionMapper.toDto(closedPosition);
        } catch (ValidationException | EntityNotFoundException e) {
            log.warn("Validation error while closing position: {}", e.getMessage());
            throw e;
        } catch (BusinessException e) {
            log.error("Business error while closing position: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error while closing position, memberId = {}, symbol = {}", command.getMemberId(), command.getSymbol(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error while closing position, memberId = {}, symbol = {}", command.getMemberId(), command.getSymbol(), e);
            throw new DatabaseException("Error while closing position: " + e.getMessage(), e);
        }
    }

    /**
     * Close all positions for a member with exception handling and retry mechanism
     *
     * @param memberId Member ID
     * @return Number of positions closed
     */
    @Override
    @Transactional
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int closeAllPositions(Long memberId) {
        try {
            log.debug("Closing all positions for member, memberId = {}", memberId);

            if (memberId == null) {
                throw new ValidationException("MemberId must not be null");
            }

            List<Position> positions = positionRepository.findAllByMemberId(memberId);

            if (positions.isEmpty()) {
                log.debug("No positions found to close, memberId = {}", memberId);
                return 0;
            }

            int closedCount = 0;

            for (Position position : positions) {
                BigDecimal currentPrice = priceService.getCurrentMarkPrice(position.getSymbol());

                log.debug("Step 1: Create close position order for position {}", position.getId());
                Order order = positionOrderService.createClosePositionOrder(position, OrderType.MARKET, currentPrice, position.getVolume());
                log.debug("Created close position order, orderId = {}", order.getOrderId());

                log.debug("Step 2: Send order to matching engine");
                orderCommandProducer.sendPlaceOrderCommand(order);
                log.debug("Order sent to matching engine");

                log.debug("Step 3: Update position in database");
                Position closedPosition = closePosition(position, currentPrice, position.getVolume());
                positionRepository.save(closedPosition);
                log.debug("Position updated in database");

                closedCount++;

                log.debug("Position closed successfully, memberId = {}, symbol = {}", memberId, position.getSymbol());

            }

            log.info("{} positions closed successfully, memberId = {}", closedCount, memberId);

            return closedCount;
        } catch (ValidationException e) {
            log.warn("Validation error while closing all positions: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Error closing all positions, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Unknown error closing all positions, memberId = {}", memberId, e);
            throw new DatabaseException("Error closing all positions: " + e.getMessage(), e);
        }
    }

    /**
     * Adjust leverage for a position with exception handling and retry mechanism
     *
     * @param command Adjust leverage command
     * @return Updated position DTO
     */
    @Override
    @Transactional
    public PositionDTO adjustLeverage(AdjustLeverageCommandDTO command) {
        Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbol(command.getMemberId(), command.getSymbol());

        if (positionOpt.isEmpty()) {
            throw new IllegalArgumentException("Position not found");
        }

        Position position = positionOpt.get();

        if (command.getLeverage().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Leverage must be greater than or equal to 1");
        }

        ContractSymbol contract = contractSymbolRepository.findBySymbol(position.getSymbol())
                .orElseThrow(() -> new BusinessException("Contract not found"));

        if (command.getLeverage().compareTo(contract.getLeverageMax()) > 0) {
            throw new IllegalArgumentException("Leverage cannot exceed maximum leverage");
        }

        BigDecimal newMargin = position.getOpenPrice().multiply(position.getVolume()).divide(command.getLeverage(), RoundingMode.HALF_UP);

        position.setMargin(newMargin);
        position.setLeverage(command.getLeverage());
        position.setUpdateTime(LocalDateTime.now());

        Position savedPosition = positionRepository.save(position);

        return positionMapper.toDto(savedPosition);
    }

    /**
     * Adjust margin for a position with exception handling and retry mechanism
     *
     * @param command Adjust margin command
     * @return Updated position DTO
     */
    @Override
    @Transactional
    public PositionDTO adjustMargin(AdjustMarginCommandDTO command) {
        Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbol(command.getMemberId(), command.getSymbol());

        if (positionOpt.isEmpty()) {
            throw new IllegalArgumentException("Position not found");
        }

        Position position = positionOpt.get();

        BigDecimal newMargin;
        if (command.isAdd()) {
            newMargin = position.getMargin().add(command.getMargin());
        } else {
            if (command.getMargin().compareTo(position.getMargin()) > 0) {
                throw new IllegalArgumentException("Cannot withdraw more than current margin");
            }
            newMargin = position.getMargin().subtract(command.getMargin());
        }

        BigDecimal newLeverage = position.getOpenPrice().multiply(position.getVolume()).divide(newMargin, RoundingMode.HALF_UP);

        Position newPosition = Position.builder()
                .id(position.getId())
                .memberId(position.getMemberId())
                .symbol(position.getSymbol())
                .direction(position.getDirection())
                .volume(position.getVolume())
                .openPrice(position.getOpenPrice())
                .closePrice(position.getClosePrice())
                .liquidationPrice(position.getLiquidationPrice())
                .maintenanceMargin(position.getMaintenanceMargin())
                .margin(newMargin)
                .profit(position.getProfit())
                .marginMode(position.getMarginMode())
                .leverage(newLeverage)
                .createTime(position.getCreateTime())
                .updateTime(LocalDateTime.now())
                .remark(position.getRemark())
                .build();

        Position savedPosition = positionRepository.save(newPosition);

        return positionMapper.toDto(savedPosition);
    }

    /**
     * Change margin mode for a position with exception handling and retry mechanism
     *
     * @param memberId   Member ID
     * @param symbol     Contract symbol
     * @param marginMode New margin mode
     * @return Updated position DTO
     */
    @Override
    @Transactional
    public PositionDTO changeMarginMode(Long memberId, String symbol, MarginMode marginMode) {
        Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbol(memberId, symbol);

        if (positionOpt.isEmpty()) {
            throw new IllegalArgumentException("Position not found");
        }

        Position position = positionOpt.get();

        Position newPosition = Position.builder()
                .id(position.getId())
                .memberId(position.getMemberId())
                .symbol(position.getSymbol())
                .direction(position.getDirection())
                .volume(position.getVolume())
                .openPrice(position.getOpenPrice())
                .closePrice(position.getClosePrice())
                .liquidationPrice(position.getLiquidationPrice())
                .maintenanceMargin(position.getMaintenanceMargin())
                .margin(position.getMargin())
                .profit(position.getProfit())
                .marginMode(marginMode)
                .leverage(position.getLeverage())
                .createTime(position.getCreateTime())
                .updateTime(LocalDateTime.now())
                .remark(position.getRemark())
                .build();

        Position savedPosition = positionRepository.save(newPosition);

        return positionMapper.toDto(savedPosition);
    }

    /**
     * Calculate unrealized profit for a position by member ID and symbol
     *
     * @param memberId Member ID
     * @param symbol   Contract symbol
     * @return Unrealized profit as BigDecimal
     */
    @Override
    public BigDecimal calculateUnrealizedProfit(Long memberId, String symbol) {
        Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbol(memberId, symbol);

        if (positionOpt.isEmpty()) {
            return BigDecimal.ZERO;
        }

        Position position = positionOpt.get();
        BigDecimal currentPrice = priceService.getCurrentMarkPrice(symbol);

        return PositionUtils.calculateUnrealizedProfit(position, currentPrice);
    }

    /**
     * Calculate total unrealized profit for all positions of a member
     *
     * @param memberId Member ID
     * @return Total unrealized profit as BigDecimal
     */
    @Override
    public BigDecimal calculateTotalUnrealizedProfit(Long memberId) {
        List<Position> positions = positionRepository.findAllByMemberId(memberId);

        Map<String, BigDecimal> currentPrices = new HashMap<>();
        for (Position position : positions) {
            String symbol = position.getSymbol();
            currentPrices.computeIfAbsent(symbol, priceService::getCurrentMarkPrice);
        }

        return calculateTotalUnrealizedProfit(positions, currentPrices);
    }

}
