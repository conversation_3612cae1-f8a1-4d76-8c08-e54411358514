package com.bitcello.futureapi.service;

import com.bitcello.futureapi.entity.Wallet;

import java.math.BigDecimal;
import java.util.List;

/**
 * Interface for the WalletService
 * This service handles operations related to wallets such as creating, retrieving, and updating wallet balances.
 */
public interface WalletService {

    /**
     * Finds a wallet by its ID.
     *
     * @param id The ID of the wallet.
     * @return The Wallet object if found, otherwise null.
     */
    Wallet findById(Long id);

    /**
     * Finds a wallet by member ID and coin type.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return The Wallet object if found, otherwise null.
     */
    Wallet findByMemberIdAndCoin(Long memberId, String coin);

    /**
     * Finds or creates a wallet for a member with the specified coin type.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return The Wallet object, either found or newly created.
     */
    Wallet findOrCreateWallet(Long memberId, String coin);

    /**
     * Finds all wallets associated with a member.
     *
     * @param memberId The ID of the member.
     * @return A list of Wallet objects associated with the member.
     */
    List<Wallet> findAllByMemberId(Long memberId);

    /**
     * Creates a new wallet for a member with the specified coin type.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return The newly created Wallet object.
     */
    Wallet createWallet(Long memberId, String coin);

    /**
     * Creates a new wallet for a member with the specified coin type and initial balance.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The initial balance for the wallet.
     * @return The newly created Wallet object with the initial balance set.
     */
    Wallet deposit(Long memberId, String coin, BigDecimal amount);

    /**
     * Withdraws an amount from a member's wallet.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to withdraw.
     * @return The updated Wallet object after withdrawal.
     */
    Wallet withdraw(Long memberId, String coin, BigDecimal amount);

    /**
     * Increases the balance of a member's wallet.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to increase the balance by.
     * @return The updated Wallet object after increasing the balance.
     */
    Wallet increaseBalance(Long memberId, String coin, BigDecimal amount);

    /**
     * Decreases the balance of a member's wallet.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to decrease the balance by.
     * @return The updated Wallet object after decreasing the balance.
     */
    Wallet decreaseBalance(Long memberId, String coin, BigDecimal amount);

    /**
     * Freezes a specified amount in a member's wallet.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to freeze.
     * @return The updated Wallet object after freezing the balance.
     */
    Wallet freezeBalance(Long memberId, String coin, BigDecimal amount);

    /**
     * Unfreezes a specified amount in a member's wallet.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to unfreeze.
     * @return The updated Wallet object after unfreezing the balance.
     */
    Wallet unfreezeBalance(Long memberId, String coin, BigDecimal amount);

    /**
     * Updates the balance of a member's wallet with a specific type and description.
     *
     * @param memberId    The ID of the member.
     * @param coin        The coin type.
     * @param amount      The amount to update the balance by.
     * @param type        The type of transaction (e.g., deposit, withdrawal).
     * @param description A description of the transaction.
     * @return The updated Wallet object after the balance update.
     */
    Wallet updateBalance(Long memberId, String coin, BigDecimal amount, String type, String description);

    /**
     * Saves a wallet to the database.
     *
     * @param wallet The Wallet object to save.
     * @return The saved Wallet object.
     */
    Wallet save(Wallet wallet);

    /**
     * Updates the total fee for a wallet.
     *
     * @param walletId The ID of the wallet.
     * @param fee      The total fee to update.
     * @return The updated Wallet object.
     */
    Wallet updateTotalFee(Long walletId, BigDecimal fee);

    /**
     * Updates the realized profit and loss for a wallet.
     *
     * @param walletId    The ID of the wallet.
     * @param realizedPnl The realized profit and loss to update.
     * @return The updated Wallet object.
     */
    Wallet updateRealizedPnl(Long walletId, BigDecimal realizedPnl);

    /**
     * Locks a member's wallet for a specified coin.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return true if the wallet was successfully locked, false otherwise.
     */
    boolean lockWallet(Long memberId, String coin);

    /**
     * Locks a member's wallet for a specified coin.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @return true if the wallet was successfully locked, false otherwise.
     */
    boolean unlockWallet(Long memberId, String coin);

    /**
     * Checks if a member has enough balance in their wallet for a specified coin and amount.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to check against the wallet balance.
     * @return true if the member has enough balance, false otherwise.
     */
    boolean hasEnoughBalance(Long memberId, String coin, BigDecimal amount);

    /**
     * Checks if a member has enough balance in their wallet for a specified amount.
     *
     * @param memberId The ID of the member.
     * @param amount   The amount to check against the wallet balance.
     * @return true if the member has enough balance, false otherwise.
     */
    boolean hasEnoughBalance(Long memberId, BigDecimal amount);

    /**
     * Updates the wallet balance for funding payments.
     *
     * @param memberId The ID of the member.
     * @param amount   The amount to update the balance by.
     * @return The updated Wallet object after the funding payment.
     */
    Wallet updateBalanceForFundingPayment(Long memberId, BigDecimal amount);

    /**
     * Adds a specified amount to the wallet balance for spot trading.
     *
     * @param memberId The ID of the member.
     * @param coin     The coin type.
     * @param amount   The amount to add to the wallet balance.
     */
    void minusBalanceFromWalletSpot(Long memberId, String coin, BigDecimal amount);

    /**
     * Retrieves a list of wallets for a given member ID and a list of base symbols.
     *
     * @param member The ID of the member whose wallets are to be retrieved.
     * @param baseSymbols A list of base symbols to filter the wallets by.
     * @return A list of Wallet objects that match the given member ID and base symbols.
     */
    List<Wallet> findByMemberIdAndBaseSymbol(Long member, List<String> baseSymbols);
}
