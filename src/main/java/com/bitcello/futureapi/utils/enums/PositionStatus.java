package com.bitcello.futureapi.utils.enums;

/**
 * Enum representing the status of a position in futures trading.
 * It can be OPEN, CLOSED, LIQUIDATING, LIQUIDATED, ADL, or ADL_COMPLETED.
 */
public enum PositionStatus {
    OPEN,
    CLOSED,
    LIQUIDATING,
    LIQUIDATED,
    ADL,
    ADL_COMPLETED;

    /**
     * Checks if the position status is active.
     *
     * @return true if the position status is OPEN, LIQUIDATING, or ADL; false otherwise.
     */
    public boolean isActive() {
        return this == OPEN || this == LIQUIDATING || this == ADL;
    }

    /**
     * Checks if the position status is terminated.
     *
     * @return true if the position status is CLOSED, LIQUIDATED, or ADL_COMPLETED; false otherwise.
     */
    public boolean isTerminated() {
        return this == CLOSED || this == LIQUIDATED || this == ADL_COMPLETED;
    }
}
