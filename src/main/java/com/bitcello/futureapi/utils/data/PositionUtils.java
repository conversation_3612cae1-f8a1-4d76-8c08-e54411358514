package com.bitcello.futureapi.utils.data;

import com.bitcello.futureapi.entity.Position;
import com.bitcello.futureapi.utils.enums.PositionDirection;
import com.bitcello.futureapi.utils.validators.BigDecimalValidator;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Slf4j
public class PositionUtils {

    private PositionUtils() {
        // Private constructor to prevent instantiation
    }

    public static BigDecimal calculateValue(Position position) {
        return position.getOpenPrice().multiply(position.getVolume());
    }

    public static BigDecimal calculateUnrealizedProfit(Position position, BigDecimal currentPrice) {
        // Validate input values trước khi tính toán
        BigDecimal validatedCurrentPrice = BigDecimalValidator
                .validateAndScale(currentPrice, "currentPrice");
        BigDecimal validatedOpenPrice = BigDecimalValidator
                .validateAndScale(position.getOpenPrice(), "openPrice");
        BigDecimal validatedVolume = BigDecimalValidator
                .validateAndScale(position.getVolume(), "volume");

        BigDecimal priceDiff;
        if (position.getDirection() == PositionDirection.LONG) {
            priceDiff = validatedCurrentPrice.subtract(validatedOpenPrice);
        } else {
            priceDiff = validatedOpenPrice.subtract(validatedCurrentPrice);
        }

        // Validate price difference
        priceDiff = BigDecimalValidator
                .validateAndScale(priceDiff, "priceDiff");

        // Calculate profit using USDT volume instead of BTC volume
        // Formula: PNL = (price_diff / entry_price) × position_value_in_usdt
        // Where position_value_in_usdt = entry_price × volume_btc
        BigDecimal positionValueUsdt = validatedOpenPrice.multiply(validatedVolume);
        BigDecimal profit = priceDiff.multiply(positionValueUsdt).divide(validatedOpenPrice, 8, RoundingMode.HALF_UP);

        // Validate final profit value
        profit = BigDecimalValidator
                .validateAndScale(profit, "unrealizedProfit");

        return profit;
    }

    public static BigDecimal calculateLiquidationPrice(Position position, BigDecimal maintenanceMarginRate) {
        if (position.getVolume().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal entryValue = position.getOpenPrice().multiply(position.getVolume());
        BigDecimal maintenanceMarginAmount = entryValue.multiply(maintenanceMarginRate);
        BigDecimal liquidationPriceValue;

        if (position.getDirection() == PositionDirection.LONG) {
            // LONG: Liq Price = (Entry Value - Initial Margin + Maintenance Margin) / Volume
            liquidationPriceValue = entryValue.subtract(position.getMargin()).add(maintenanceMarginAmount)
                    .divide(position.getVolume(), 8, RoundingMode.HALF_UP);
        } else {
            // SHORT: Liq Price = (Entry Value + Initial Margin - Maintenance Margin) / Volume
            liquidationPriceValue = entryValue.add(position.getMargin()).subtract(maintenanceMarginAmount)
                    .divide(position.getVolume(), 8, RoundingMode.HALF_UP);
        }

        return liquidationPriceValue.max(BigDecimal.ZERO);
    }

    public static boolean needsLiquidation(Position position, BigDecimal currentPrice, BigDecimal maintenanceMarginRate) {
        BigDecimal unrealizedProfit = calculateUnrealizedProfit(position, currentPrice);
        BigDecimal availableMargin = position.getMargin().add(unrealizedProfit);
        log.info("Available Margin: {}", availableMargin);
        BigDecimal requiredMaintenanceMargin = calculateValue(position).multiply(maintenanceMarginRate);

        return position.getMargin().compareTo(requiredMaintenanceMargin) < 0;
    }

    public static BigDecimal calculateProfitRatio(Position position, BigDecimal currentPrice) {
        BigDecimal unrealizedProfit = calculateUnrealizedProfit(position, currentPrice);
        return unrealizedProfit
                .divide(position.getMargin(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    public static BigDecimal calculateMarginRatio(Position position, BigDecimal currentPrice, BigDecimal maintenanceMarginRate) {
        BigDecimal unrealizedPnl = calculateUnrealizedProfit(position, currentPrice);
        BigDecimal walletBalance = position.getMargin().add(unrealizedPnl);

        // Maintenance Margin = Position Value * Maintenance Margin Rate
        BigDecimal positionValue = position.getOpenPrice().multiply(position.getVolume());
        BigDecimal maintenanceMargin = positionValue.multiply(maintenanceMarginRate);

        if (walletBalance.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.valueOf(100); // 100% if no balance left
        }

        // Margin Ratio = (Maintenance Margin / Wallet Balance) * 100
        return maintenanceMargin
                .divide(walletBalance, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    public static BigDecimal calculateBreakEvenPrice(Position position, BigDecimal feeRate) {
        if (position.getVolume().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal breakEvenPriceValue;
        BigDecimal totalFeeRate = feeRate.multiply(BigDecimal.valueOf(2)); // Phí mở + phí đóng

        if (position.getDirection() == PositionDirection.LONG) {
            // LONG: Break Even Price = Entry Price * (1 + 2 * Fee Rate)
            breakEvenPriceValue = position.getOpenPrice().multiply(BigDecimal.ONE.add(totalFeeRate));
        } else {
            // SHORT: Break Even Price = Entry Price * (1 - 2 * Fee Rate)
            breakEvenPriceValue = position.getOpenPrice().multiply(BigDecimal.ONE.subtract(totalFeeRate));
        }

        return breakEvenPriceValue.max(BigDecimal.ZERO);
    }

}
