package com.bitcello.futureapi.messaging.command;

import com.bitcello.futureapi.entity.Order;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Command for order-related operations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderCommand {

    /**
     * Command ID
     */
    private String commandId;

    /**
     * Command type
     */
    private OrderCommandType type;

    /**
     * Order
     */
    private Order order;

    /**
     * Command creation time
     */
    private LocalDateTime timestamp;

    /**
     * Command type
     */
    public enum OrderCommandType {
        /**
         * Place order
         */
        PLACE_ORDER,

        /**
         * Cancel order
         */
        CANCEL_ORDER,

        /**
         * Cancel all order
         */
        CANCEL_ALL_ORDER,

        /**
         * Update order
         */
        UPDATE_ORDER
    }

    private List<Order> orderList;
}
