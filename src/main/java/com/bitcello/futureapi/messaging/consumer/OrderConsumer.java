package com.bitcello.futureapi.messaging.consumer;

import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.constant.ErrorMessages;
import com.bitcello.futureapi.dto.order.request.CancelOrderInfo;
import com.bitcello.futureapi.dto.order.response.OrderCancelNotification;
import com.bitcello.futureapi.entity.Order;
import com.bitcello.futureapi.entity.Wallet;
import com.bitcello.futureapi.exception.BusinessException;
import com.bitcello.futureapi.repository.OrderRepository;
import com.bitcello.futureapi.repository.WalletRepository;
import com.bitcello.futureapi.service.WalletService;
import com.bitcello.futureapi.service.WebSocketService;
import com.bitcello.futureapi.utils.enums.OrderStatus;
import com.bitcello.futureapi.utils.enums.WebsocketStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The OrderConsumer class is responsible for consuming and processing Kafka messages related
 * to order cancellation results. It listens to specific Kafka topics, decodes the messages,
 * and performs various business operations, such as updating order statuses, releasing frozen
 * balances, and notifying users via WebSockets.
 * <p>
 * This class relies on services such as {@code LocaleMessageSourceService}, {@code WebSocketService},
 * and {@code WalletService} to handle localization, messaging, and wallet operations respectively.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderConsumer {

    private final LocaleMessageSourceService messageSourceService;
    private final WebSocketService webSocketService;
    private final ObjectMapper objectMapper;
    private final WalletService walletService;
    private final WalletRepository walletRepository;
    private final OrderRepository orderRepository;

    /**
     * Processes the result of an order cancellation message received via Kafka.
     * <p>
     * This method is triggered by a Kafka listener when a message is published to the topic
     * "${topic-kafka.contract.order-cancel-result}". It parses the received message, extracts
     * relevant details about the cancellation process, and handles different cancellation types
     * such as 'CANCEL' or 'CANCEL_ALL'. Depending on the success or failure of the operation,
     * corresponding actions are performed such as updating order status, unfreezing balances,
     * or sending notifications.
     *
     * @param records the consumed Kafka record containing the cancellation result details
     * @param ack     the acknowledgment object used for manually acknowledging message processing
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.order-cancel-result}",
            containerFactory = "kafkaListenerContainerFactory",
            groupId = "order-matching-cancel-result-group")
    public void handleOrderCancelResult(ConsumerRecord<String, String> records, Acknowledgment ack) {
        String messageValue = records.value();
        log.info("[Future-Cancel-Consumer] Received order cancel result: {}", messageValue);

        try {
            if (StringUtils.isBlank(messageValue)) {
                log.warn("[Future-Cancel-Consumer] Message is blank. Skipping processing.");
                return;
            }

            CancelOrderInfo cancelOrderInfo = objectMapper.readValue(messageValue, CancelOrderInfo.class);
            log.info("[Future-Cancel-Consumer] Parsed CancelOrderInfo: {}", cancelOrderInfo);

            if (cancelOrderInfo == null || cancelOrderInfo.getCancelResult() == null) {
                log.warn("[Future-Cancel-Consumer] CancelOrderInfo or CancelResult is null. Ignoring message.");
                return;
            }

            boolean success = cancelOrderInfo.isSuccess();
            Order order = cancelOrderInfo.getCancelResult();

            log.info("[Future-Cancel-Consumer] Start handling cancel result: type={}, orderId={}, success={}",
                    cancelOrderInfo.getType(), order.getOrderId(), success);

            if (success) {
                log.debug("[Future-Cancel-Consumer] Cancel success. Processing handleCancelSuccess...");
                handleCancelSuccess(order);
            } else {
                log.debug("[Future-Cancel-Consumer] Cancel failed. Processing handleCancelFailure...");
                handleCancelFailure(cancelOrderInfo.getErrorCode(), cancelOrderInfo.getMessage(), order);
            }

        } catch (Exception e) {
            log.error("[Future-Cancel-Consumer] Exception while processing cancel result: {}", messageValue, e);
        } finally {
            // Always acknowledge after processing, regardless of outcome
            ack.acknowledge();
            log.info("[Future-Cancel-Consumer] Message acknowledged.");
        }
    }

    /**
     * Processes the result of a "cancel all orders" request received via Kafka.
     * <p>
     * This method is triggered by a Kafka listener when a message is published
     * to the topic "${topic-kafka.contract.order-cancel-all-result}". It parses
     * the received message, extracts relevant details about the cancellation
     * process, and handles the cancellation outcome. Depending on success or failure,
     * the corresponding actions are performed, such as updating order status,
     * handling errors, or notifying relevant services.
     *
     * @param records the consumed Kafka record containing the "cancel all orders" result details
     * @param ack     the acknowledgment object used for manually confirming successful message processing
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.order-cancel-all-result}",
            containerFactory = "kafkaListenerContainerFactory",
            groupId = "order-matching-cancel-result-group")
    public void handleOrderCancelAllResult(ConsumerRecord<String, String> records, Acknowledgment ack) {
        String messageValue = records.value();
        log.info("[Future-Cancel-All-Consumer] Received order cancel result: {}", messageValue);

        if (StringUtils.isBlank(messageValue)) {
            log.warn("[Future-Cancel-All-Consumer] Message is blank. Skipping processing.");
            return;
        }

        try {
            List<CancelOrderInfo> cancelOrderInfos = objectMapper.readValue(
                    messageValue,
                    new TypeReference<>() {
                    }
            );

            Map<Boolean, List<CancelOrderInfo>> partitioned = cancelOrderInfos.stream()
                    .collect(Collectors.partitioningBy(CancelOrderInfo::isSuccess));

            List<CancelOrderInfo> cancelSuccess = partitioned.get(Boolean.TRUE);
            List<CancelOrderInfo> cancelFailure = partitioned.get(Boolean.FALSE);
            log.info("[Future-Cancel-All-Consumer] Parsed cancelOrderInfos: {}", cancelOrderInfos);

            List<Order> orderList = cancelSuccess.stream()
                    .map(CancelOrderInfo::getCancelResult)
                    .toList();
            if (orderList.isEmpty()) {
                return;
            }

            finalizeCancelAllBatch(orderList);

            Long memberId = orderList.stream().findFirst().get().getMemberId();

            if (cancelSuccess.isEmpty()) {
                // All failed
                List<String> orderIds = cancelFailure.stream().map(info -> info.getCancelResult().getOrderId()).toList();
                sendCancelNotificationToUser(String.valueOf(orderIds), memberId,
                        400, WebsocketStatus.FAILED, ErrorMessages.ORDER_CANCEL_FAILED);
            } else if (!cancelFailure.isEmpty()) {
                // Part success, part failure
                List<String> orderIds = cancelFailure.stream().map(info -> info.getCancelResult().getOrderId()).toList();
                sendCancelNotificationToUser(String.valueOf(orderIds), memberId,
                        200, WebsocketStatus.SUCCESS, ErrorMessages.ORDER_CANCEL_PARTIALLY_SUCCESS);
            } else {
                // All success
                List<String> orderIds = cancelSuccess.stream().map(info -> info.getCancelResult().getOrderId()).toList();
                sendCancelNotificationToUser(String.valueOf(orderIds), memberId,
                        200, WebsocketStatus.SUCCESS, ErrorMessages.ORDER_CANCEL_SUCCESS);
            }

        } catch (Exception e) {
            log.error("[Future-Cancel-All-Consumer] Exception while processing cancel result: {}", messageValue, e);
        } finally {
            // Always acknowledge after processing, regardless of outcome
            ack.acknowledge();
            log.info("[Future-Cancel-All-Consumer] Message acknowledged.");
        }
    }

    /**
     * Handles the successful cancellation of an order.
     * <p>
     * This method processes the successful cancellation of an order by performing
     * the following operations:
     * 1. Verifying the validity of the order object.
     * 2. Updating the order status and related fields in the database.
     * 3. Releasing any frozen margin or balance if applicable.
     * 4. Sending a cancellation success notification to the user.
     *
     * @param order the order object containing details about the canceled order.
     *              Must not be null and must contain a valid orderId.
     */
    private void handleCancelSuccess(Order order) {
        if (order == null || StringUtils.isEmpty(order.getOrderId())) {
            log.warn("[Future-Cancel-Success] Invalid order input: {}", order);
            return;
        }

        log.info("[Future-Cancel-Success] Start cancel success handling - orderId={}, symbol={}", order.getOrderId(), order.getSymbol());

        // 1. Update order status in database
        Order update = orderRepository.findById(order.getOrderId())
                .orElseThrow(() -> new BusinessException(messageSourceService.getMessage(ErrorMessages.WALLET_NOT_EXIST)));

        update.setStatus(OrderStatus.CANCELED);
        update.setIsCompleted(true);
        update.setCanceledTime(LocalDateTime.now());
        update.setCompleteTime(LocalDateTime.now());
        order.setCancelReason("User Cancel");

        orderRepository.saveAndFlush(update); // Ensure this persist update

        log.info("[Future-Cancel-Success] Order updated: orderId={}, status={}", update.getOrderId(), update.getStatus());

        // 2. Release frozen margin/balance if needed
        BigDecimal remainingSize = order.getSize().subtract(order.getFilledVolume() == null ? BigDecimal.ZERO : order.getFilledVolume());
        if (BigDecimal.ZERO.compareTo(remainingSize) <= 0) {
            BigDecimal price = order.getPrice();
            BigDecimal volume = price.multiply(remainingSize);
            BigDecimal frozenBalance = volume.divide(order.getLeverage(), 8, RoundingMode.HALF_UP);

            Wallet wallet = walletService.unfreezeBalance(order.getMemberId(), order.getBaseSymbol(), frozenBalance);

            log.info("[Future-Cancel-Success] Unfrozen balance: memberId={}, coin={}, amount={}, walletId={}",
                    order.getMemberId(), order.getBaseSymbol(), frozenBalance, wallet.getId());
        } else {
            log.info("[Future-Cancel-Success] No remaining size to unfreeze - orderId={}, size={}, filled={}",
                    order.getOrderId(), order.getSize(), order.getFilledVolume());
        }

        // 3. Send notification to a user
        sendCancelNotificationToUser(String.valueOf(order), order.getMemberId(),
                200, WebsocketStatus.SUCCESS, ErrorMessages.ORDER_CANCEL_SUCCESS);
        log.info("[Future-Cancel-Success] Cancel process completed - orderId={}", order.getOrderId());
    }

    /**
     * Handles the failure of an order cancellation process.
     * <p>
     * This method logs the failure details and notifies the user about the cancellation failure.
     *
     * @param errorCode    the error code representing the reason for the cancellation failure
     * @param errorMessage the descriptive message detailing the error that occurred
     * @param order        the order object associated with the failed cancellation process,
     *                     containing details about the order
     */
    private void handleCancelFailure(String errorCode, String errorMessage, Order order) {
        log.warn("[Future-Cancel-Failed] - Order cancel FAILED - OrderId: {}, Symbol: {}, Error: {} ({})",
                order.getOrderId(), order.getSymbol(), errorMessage, errorCode);
        sendCancelNotificationToUser(String.valueOf(order), order.getMemberId(), 400, WebsocketStatus.FAILED, errorCode);
    }


    /**
     * Sends a cancellation notification to a specified user.
     * This method constructs an {@link OrderCancelNotification} object
     * based on the provided data and sends it to the user through a WebSocket service.
     *
     * @param data            the cancellation-related data to be included in the notification
     * @param memberId        the ID of the user to whom the notification will be sent
     * @param code            the code representing the cancellation status or result
     * @param websocketStatus the status of the cancellation process (e.g., success, failure)
     * @param messageCode     the key for fetching the localized message for the notification
     */
    private void sendCancelNotificationToUser(String data, Long memberId, int code,
                                              WebsocketStatus websocketStatus, String messageCode) {
        log.info("[Future-Cancel-Websocket] Send notification - data={}, websocketStatus={}, messageCode={} ", data, websocketStatus, messageCode);
        OrderCancelNotification notification = OrderCancelNotification.builder()
                .code(code)
                .status(websocketStatus)
                .message(messageSourceService.getMessage(messageCode))
                .data(data)
                .type("ORDER_CANCEL_RESULT")
                .build();
        webSocketService.sendToUser(memberId, "/topic/order-cancel/" + memberId, notification);
    }


    /**
     * Handles the finalization process for canceling all orders in a batch.
     * <p>
     * This method performs the following operations:
     * 1. Extracts order IDs from the provided list and fetches the latest order details from the database.
     * 2. Validates that the orders exist and extracts the member ID associated with the orders.
     * 3. Processes the orders to calculate unfrozen amounts for each symbol.
     * 4. Saves the updated order information.
     * 5. Updates wallet balances if there are amounts to unfreeze.
     * 6. Logs relevant information throughout the process.
     *
     * @param orderList the list of orders involved in the cancel all request, containing details about each order.
     */
    private void finalizeCancelAllBatch(List<Order> orderList) {
        log.info("[Future-Cancel-All] Start cancel all success handling orderList.size()={}, orderList={}", orderList.size(), orderList);

        // 1. Get orderIds and fetch up-to-date orders from DB
        List<String> orderIds = orderList.stream()
                .map(Order::getOrderId)
                .toList();
        if (orderIds.isEmpty()) {
            log.warn("[Future-Cancel-All] No valid order IDs found in input list");
            return;
        }
        log.info("[Future-Cancel-All] Fetching up-to-date orders from DB for orderIds={}", orderIds);

        // 2.Fetch fresh orders from database
        List<Order> freshOrders = orderRepository.findByOrderIdIn(orderIds);
        if (freshOrders.isEmpty()) {
            log.warn("[Future-Cancel-All] No up-to-date orders found in DB for orderIds={}", orderIds);
            return;
        }

        //  3. Get memberId
        Long memberId = freshOrders.stream().findFirst().get().getMemberId();
        log.info("[Future-Cancel-All] memberId={}, totalOrders={}", memberId, freshOrders.size());

        // 4. Process orders and calculate unfrozen amounts
        Map<String, BigDecimal> unfrozenAmountsBySymbol = processOrderCancellations(freshOrders);

        // 5. Save updated orders
        orderRepository.saveAll(freshOrders);
        log.info("[Future-Cancel-All] Saved updated orders freshOrders={}", freshOrders);

        // 6. Update wallets if there are amounts to unfreeze
        if (unfrozenAmountsBySymbol.isEmpty()) {
            log.info("[Future-Cancel-All] No amounts to unfreeze for member {}", memberId);
            return;
        }

        updateWalletBalances(memberId, unfrozenAmountsBySymbol);
        log.info("[Future-Cancel-All] Successfully completed batch cancel for member {} with {} orders",
                memberId, freshOrders.size());
    }

    /**
     * Processes a list of orders, updating their status to canceled, calculating unfrozen amounts for each
     * symbol, and returning a map of total unfrozen amounts by symbol.
     * Orders that are already canceled are skipped.
     *
     * @param orders the list of orders to be processed. Each order must contain details such as status,
     *               symbol, and order ID. Null or empty values in the list are not permitted.
     * @return a map where the keys are the symbols of the orders and the values are the total unfrozen
     * amounts for those symbols. If no unfrozen amounts are calculated, an empty map is returned.
     */
    private Map<String, BigDecimal> processOrderCancellations(List<Order> orders) {
        Map<String, BigDecimal> unfrozenAmountsBySymbol = new HashMap<>();
        LocalDateTime cancelTime = LocalDateTime.now();
        int processedCount = 0;

        for (Order order : orders) {
            if (order.getStatus() == OrderStatus.CANCELED) {
                log.debug("[Future-Cancel-All] Order {} already canceled, skipping", order.getOrderId());
                continue;
            }

            // Update order status
            order.setStatus(OrderStatus.CANCELED);
            order.setIsCompleted(true);
            order.setCanceledTime(cancelTime);
            order.setCompleteTime(cancelTime);
            order.setCancelReason("User Cancel All");

            // Calculate unfrozen amount
            BigDecimal unfrozenAmount = calculateUnfrozenAmount(order);
            if (unfrozenAmount.compareTo(BigDecimal.ZERO) > 0) {
                unfrozenAmountsBySymbol.merge(order.getBaseSymbol(), unfrozenAmount, BigDecimal::add);
                log.debug("[Future-Cancel-All] Order {}: unfrozen {} {}",
                        order.getOrderId(), unfrozenAmount, order.getSymbol());
            }

            processedCount++;
        }

        log.info("[Future-Cancel-All] Processed {}/{} orders. Total unfrozen amounts by symbol: {}",
                processedCount, orders.size(), unfrozenAmountsBySymbol);

        return unfrozenAmountsBySymbol;
    }

    /**
     * Calculates the amount to unfreeze for a given order based on its remaining size and leverage.
     * This method is used to determine the unfrozen amount when an order is partially or fully canceled
     * and its funds are eligible for release. The calculation considers filled volume, remaining size,
     * order price, and leverage to compute the unfreezing amount. In case of any calculation error,
     * the method logs the issue and returns zero.
     *
     * @param order the order for which the unfrozen amount is to be calculated. It must contain
     *              all necessary fields such as size, filled volume, price, and leverage.
     *              Null values for these fields are handled where applicable.
     * @return the calculated unfrozen amount as a BigDecimal. If the calculation fails or if
     * the remaining size is zero or less, returns BigDecimal.ZERO.
     */
    private BigDecimal calculateUnfrozenAmount(Order order) {
        try {
            BigDecimal filledVolume = order.getFilledVolume() != null ? order.getFilledVolume() : BigDecimal.ZERO;
            BigDecimal remainingSize = order.getSize().subtract(filledVolume);

            if (remainingSize.compareTo(BigDecimal.ZERO) <= 0) {
                log.debug("[Future-Cancel-All] Order {} fully filled, no amount to unfreeze", order.getOrderId());
                return BigDecimal.ZERO;
            }

            BigDecimal volume = order.getPrice().multiply(remainingSize);
            BigDecimal unfrozenAmount = volume.divide(order.getLeverage(), 8, RoundingMode.HALF_UP);

            log.debug("[Future-Cancel-All] Order {}: remainingSize={}, volume={}, leverage={}, unfrozen={}",
                    order.getOrderId(), remainingSize, volume, order.getLeverage(), unfrozenAmount);

            return unfrozenAmount;

        } catch (ArithmeticException e) {
            log.error("[Future-Cancel-All] Calculation error for order {}: leverage={}",
                    order.getOrderId(), order.getLeverage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * Updates the wallet balances for a specified member based on the provided unfrozen amounts
     * for each symbol. It fetches the wallets belonging to the member for the given symbols
     * and applies the changes to the corresponding wallet balances.
     * <p>
     * Logs warnings if no wallets are found or if individual wallets are missing for specific symbols.
     * Upon successful updates, logs the count of updated wallets.
     * In case of an exception, logs the error and throws an {@link IllegalArgumentException}.
     *
     * @param memberId                the unique identifier of the member whose wallet balances need to be updated
     * @param unfrozenAmountsBySymbol a map containing the amount to be unfrozen for each symbol,
     *                                where the key represents the symbol and the value is the unfrozen amount
     */
    private void updateWalletBalances(Long memberId, Map<String, BigDecimal> unfrozenAmountsBySymbol) {
        try {
            List<String> symbols = new ArrayList<>(unfrozenAmountsBySymbol.keySet());
            List<Wallet> wallets = walletService.findByMemberIdAndBaseSymbol(memberId, symbols);

            if (wallets.isEmpty()) {
                log.warn("[Future-Cancel-All] No wallets found for member {} and symbols {}", memberId, symbols);
                return;
            }

            Map<String, Wallet> walletMap = wallets.stream()
                    .collect(Collectors.toMap(Wallet::getCoin, Function.identity()));

            int updatedWallets = 0;
            for (Map.Entry<String, BigDecimal> entry : unfrozenAmountsBySymbol.entrySet()) {
                String symbol = entry.getKey();
                BigDecimal unfreezeAmount = entry.getValue();

                Wallet wallet = walletMap.get(symbol);
                if (wallet == null) {
                    log.warn("[Future-Cancel-All] Wallet not found for symbol {} and member {}", symbol, memberId);
                    continue;
                }

                if (updateWalletBalance(wallet, unfreezeAmount)) {
                    updatedWallets++;
                }
            }

            log.info("[Future-Cancel-All] Successfully updated {}/{} wallets for member {}",
                    updatedWallets, unfrozenAmountsBySymbol.size(), memberId);

            walletRepository.saveAll(wallets);

        } catch (Exception e) {
            log.error("[Future-Cancel-All] Error updating wallet balances for member {}", memberId, e);
            throw new IllegalArgumentException("Failed to update wallet balances", e);
        }
    }

    /**
     * Updates the wallet balances by unfreezing an amount from the frozen balance and adding it to
     * the available balance. Ensures that neither balance becomes inconsistent (e.g., negative).
     * Adjusts the available balance and sets the frozen balance to zero if the unfreeze amount
     * exceeds the current frozen balance.
     *
     * @param wallet         the wallet to be updated. Must contain valid frozen and available balances.
     * @param unfreezeAmount the amount to unfreeze from the wallet's frozen balance. Must be non-null.
     * @return true if the wallet update was successful, false if an exception occurred.
     */
    private boolean updateWalletBalance(Wallet wallet, BigDecimal unfreezeAmount) {
        try {
            BigDecimal currentFrozen = wallet.getFrozenBalance();
            BigDecimal currentAvailable = wallet.getAvailableBalance();

            log.debug("[Future-Cancel-All] Wallet {} before update: frozen={}, available={}",
                    wallet.getId(), currentFrozen, currentAvailable);

            BigDecimal newFrozen = currentFrozen.subtract(unfreezeAmount);
            BigDecimal newAvailable = currentAvailable.add(unfreezeAmount);

            wallet.setFrozenBalance(newFrozen);
            wallet.setAvailableBalance(newAvailable);

            log.info("[Future-Cancel-All] Updated wallet {} ({}): frozen {} -> {}, available {} -> {}",
                    wallet.getId(), wallet.getCoin(), currentFrozen, newFrozen, currentAvailable, newAvailable);

            return true;

        } catch (Exception e) {
            log.error("[Future-Cancel-All] Error updating wallet {}", wallet.getId(), e);
            return false;
        }
    }
}
