package com.bitcello.futureapi.messaging.consumer;

import com.bitcello.futureapi.config.i18n.LocaleMessageSourceService;
import com.bitcello.futureapi.constant.ErrorMessages;
import com.bitcello.futureapi.entity.ContractSymbol;
import com.bitcello.futureapi.entity.Order;
import com.bitcello.futureapi.entity.Position;
import com.bitcello.futureapi.entity.Trade;
import com.bitcello.futureapi.entity.Wallet;
import com.bitcello.futureapi.exception.BusinessException;
import com.bitcello.futureapi.exception.ValidationException;
import com.bitcello.futureapi.messaging.OrderEvent;
import com.bitcello.futureapi.repository.ContractSymbolRepository;
import com.bitcello.futureapi.repository.PositionRepository;
import com.bitcello.futureapi.repository.TradeJpaRepository;
import com.bitcello.futureapi.service.PositionService;
import com.bitcello.futureapi.service.PriceService;
import com.bitcello.futureapi.service.WalletService;
import com.bitcello.futureapi.utils.enums.PositionDirection;
import com.bitcello.futureapi.utils.enums.PositionStatus;
import com.bitcello.futureapi.utils.valueobject.Money;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Kafka consumer that listens to order-related events (order events) from a configured Kafka topic.
 * This component handles events such as order creation, matching, and cancellation,
 * and updates the system state accordingly (e.g., positions, balances, etc.).
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderEventConsumer {

    private final ObjectMapper objectMapper;
    private final TradeJpaRepository tradeRepository;
    private final ContractSymbolRepository contractSymbolRepository;
    private final PositionRepository positionRepository;
    private final PositionService positionService;
    private final WalletService walletService;
    private final LocaleMessageSourceService messageSourceService;
    private final PriceService priceService;

    @Value("${topic-kafka.contract.order-events}")
    private String orderEventsTopic;

    /**
     * Xử lý OrderEvent
     *
     * @param message OrderEvent
     */
    @KafkaListener(
            topics = "${topic-kafka.contract.order-events}",
            containerFactory = "kafkaListenerContainerFactory",
            groupId = "contract-perpetual-futures-core-event"
    )
    @SneakyThrows
    public void handleOrderEvent(String message) {
        OrderEvent event = objectMapper.readValue(message, OrderEvent.class);

        if (event == null || event.getOrder() == null || event.getOrder().getSymbol() == null) {
            log.error("Get an invalid event from topic {}, event = {}", orderEventsTopic, event);
            return;
        }

        String symbol = event.getOrder().getSymbol();

        log.debug("Get an event from topic {}, type = {}, symbol = {}, orderId = {}",
                orderEventsTopic, event.getType(), symbol, event.getOrder().getOrderId());

        // Handle events by type
        switch (event.getType()) {
            case ORDER_PLACED:
                log.debug("Event Handling ORDER_PLACED, symbol: {}", symbol);
                // ORDER_PLACED event handling
                try {
                    handleOrderPlacedEvent(event, symbol);
                } catch (Exception e) {
                    log.error("Errors when handling ORDER_PLACED events, symbol = {}", symbol, e);
                }
                break;

            case ORDER_CANCELLED:
                log.debug("Event Handling ORDER_CANCELLED, symbol: {}", symbol);
                // The order has been canceled in the matching engine, no need to call cancelOrder again
                // Just log to track events
                log.info("The order has been successfully canceled in the matching engine, orderId: {}, symbol: {}",
                        event.getOrder().getOrderId(), symbol);
                break;

            case ORDER_UPDATED:
                log.debug("Xử lý event ORDER_UPDATED, symbol: {}", symbol);
                // Updating commands in the matching engine
                // Currently, there is no support for updating commands
                break;

            default:
                log.warn("Event type not supported: {}", event.getType());
                break;
        }
    }

    /**
     * ORDER_PLACED event handling
     *
     * @param event  OrderEvent
     * @param symbol Symbol
     */
    private void handleOrderPlacedEvent(OrderEvent event, String symbol) {
        try {
            // Get order and trade information
            Order order = event.getOrder();
            List<Trade> trades = event.getTrades();

            if (trades == null || trades.isEmpty()) {
                log.debug("No transaction is created, orderId = {}", order.getOrderId());
                return;
            }

            log.debug("Processing {} transactions for {}, symbol = {}", trades.size(), order.getOrderId(), symbol);

            // 1. Save the transaction to the database (if it hasn't been saved)
            tradeRepository.saveAll(trades);

            // Get all the contractSymbols and put them in the Map for quick lookup
            Map<String, ContractSymbol> contractSymbolMap = contractSymbolRepository.findAll()
                    .stream()
                    .collect(Collectors.toMap(ContractSymbol::getSymbol, cs -> cs));

            BigDecimal markPrice = priceService.getCurrentMarkPrice(symbol);

            // 2. Position Updates
            for (Trade trade : trades) {
                // Get a contract
                ContractSymbol contract = contractSymbolMap.get(trade.getSymbol());

                if (contract == null) {
                    log.warn("Symbol {} does not exist in contractSymbolList, ignore.", trade.getSymbol());
                    continue; // Skip this trade, proceed to other trades
                }

                // Position updates for buyers
                updatePositionForTrade(trade, trade.getBuyMemberId(), contract, PositionDirection.LONG, event.getOrder(), markPrice);

                // Position updates for sellers
                updatePositionForTrade(trade, trade.getSellMemberId(), contract, PositionDirection.SHORT, event.getOrder(), markPrice);
            }
        } catch (Exception e) {
            log.error("Error handling event ORDER_PLACED, symbol = {}", symbol, e);
            throw e;
        }
    }

    /**
     * Position updates for trading
     *
     * @param trade    Trading
     * @param memberId Member ID
     * @param contract CONTRACT
     */
    private void updatePositionForTrade(Trade trade, Long memberId, ContractSymbol contract, PositionDirection direction, Order order, BigDecimal markPrice) {
        try {
            Optional<Position> positionOpt = positionRepository.findByMemberIdAndSymbolAndStatus(
                    memberId, trade.getSymbol(), PositionStatus.OPEN);

            Position updatedPosition;
            if (positionOpt.isPresent()) {
                Position position = positionOpt.get();
                // Update position
                updatedPosition = positionService.updatePositionAfterTrade(position, trade, contract);
            } else {
                // No open positions, create new ones
                log.info("Create a new position for memberId = {}, symbol = {}, direction = {}, volume = {}",
                        memberId, trade.getSymbol(), direction, trade.getVolume());
                updatedPosition = positionService.createPositionFromTrade(trade, memberId, contract);
                log.info("New position created: id = {}, direction = {}, volume = {}, status = {}",
                        updatedPosition.getId(), updatedPosition.getDirection(),
                        updatedPosition.getVolume(), updatedPosition.getStatus());
            }

            // Calculate unrealizedPnl and ROE
            updatedPosition.setUnrealizedPnl(positionService.calculateUnrealizedPnl(updatedPosition, markPrice));
            updatedPosition.setReturnOnEquity(positionService.calculateProfitRatio(updatedPosition, markPrice));
            updatedPosition.setTakeProfitPrice(order.getTakeProfitPrice());
            updatedPosition.setStopLossPrice(order.getStopLossPrice());

            // Save a position
            Position savedPosition = positionRepository.save(updatedPosition);
            log.info("Position saved to database: id = {}, memberId = {}, symbol = {}, direction = {}, volume = {}, status = {}",
                    savedPosition.getId(), memberId, trade.getSymbol(),
                    savedPosition.getDirection(), savedPosition.getVolume(), savedPosition.getStatus());

        } catch (Exception e) {
            log.error("Error when updating a position for a trade, tradeId = {}, memberId = {}", trade.getId(), memberId, e);
            throw e;
        }
    }
}