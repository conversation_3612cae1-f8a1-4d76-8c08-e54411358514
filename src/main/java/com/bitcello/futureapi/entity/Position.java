package com.bitcello.futureapi.entity;

import com.bitcello.futureapi.utils.enums.MarginMode;
import com.bitcello.futureapi.utils.enums.PositionDirection;
import com.bitcello.futureapi.utils.enums.PositionStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Position
 */
@Entity
@Table(name = "contract_position")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Position {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "member_id", nullable = false)
    private Long memberId;

    @Column(name = "symbol", nullable = false, length = 50)
    private String symbol;

    @Enumerated(EnumType.STRING)
    @Column(name = "direction", nullable = false, length = 10)
    private PositionDirection direction;

    @Column(name = "volume", nullable = false, columnDefinition = "numeric")
    private BigDecimal volume;

    @Column(name = "size", columnDefinition = "numeric(18,8)")
    private BigDecimal size;

    @Column(name = "entry_price", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal entryPrice;

    @Column(name = "open_price", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal openPrice;

    @Column(name = "close_price", columnDefinition = "numeric(18,8)")
    private BigDecimal closePrice;

    @Column(name = "liquidation_price", columnDefinition = "numeric(18,8)")
    private BigDecimal liquidationPrice;

    @Column(name = "take_profit_price", columnDefinition = "numeric(18,8)")
    private BigDecimal takeProfitPrice;

    @Column(name = "stop_loss_price", columnDefinition = "numeric(18,8)")
    private BigDecimal stopLossPrice;

    @Column(name = "maintenance_margin", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal maintenanceMargin;

    @Column(name = "margin", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal margin;

    @Column(name = "profit", columnDefinition = "numeric(18,8)")
    private BigDecimal profit;

    @Enumerated(EnumType.STRING)
    @Column(name = "margin_mode", nullable = false, length = 20)
    private MarginMode marginMode;

    @Column(name = "leverage", nullable = false, columnDefinition = "numeric(18,8)")
    private BigDecimal leverage;

    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;

    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    @Column(name = "open_time", nullable = false)
    private LocalDateTime openTime;

    @Column(name = "close_time", nullable = false)
    private LocalDateTime closeTime;

    @Column(name = "remark", length = 255)
    private String remark;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private PositionStatus status;

    @Column(name = "unrealized_pnl", columnDefinition = "numeric(18,8)")
    private BigDecimal unrealizedPnl;

    @Column(name = "return_on_equity", columnDefinition = "numeric(18,8)")
    private BigDecimal returnOnEquity;
}
