package com.bitcello.futureapi.entity;

import com.bitcello.futureapi.utils.enums.FutureMode;
import com.bitcello.futureapi.utils.enums.OrderDirection;
import com.bitcello.futureapi.utils.enums.OrderStatus;
import com.bitcello.futureapi.utils.enums.OrderType;
import com.bitcello.futureapi.utils.enums.PositionMode;
import com.bitcello.futureapi.utils.enums.TimeInForce;
import com.bitcello.futureapi.utils.enums.TriggerType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * JPA Entity cho Order
 */
@Entity
@Table(name = "contract_order")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class Order {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String orderId;

    @Column(name = "member_id")
    private Long memberId;

    @Column(name = "contract_id")
    private Long contractId;

    private String symbol;

    @Column(name = "coin_symbol")
    private String coinSymbol;

    @Column(name = "base_symbol")
    private String baseSymbol;

    @Enumerated(EnumType.STRING)
    private OrderDirection direction;

    @Enumerated(EnumType.STRING)
    private OrderType type;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal price;

    @Column(name = "trigger_price", columnDefinition = "decimal(18,8)")
    private BigDecimal triggerPrice;

    @Enumerated(EnumType.STRING)
    @Column(name = "trigger_type")
    private TriggerType triggerType;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal volume;

    @Column(name = "deal_volume", columnDefinition = "decimal(18,8)")
    private BigDecimal dealVolume;

    @Column(name = "deal_money", columnDefinition = "decimal(18,8)")
    private BigDecimal dealMoney;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal fee;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private OrderStatus status;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "is_completed")
    private Boolean isCompleted;

    @Column(name = "complete_time")
    private LocalDateTime completeTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "time_in_force")
    private TimeInForce timeInForce;

    @Column(name = "canceled_time")
    private LocalDateTime canceledTime;

    @Column(name = "execute_time")
    private LocalDateTime executeTime;

    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    @Builder.Default
    private Boolean liquidation = false;

    @Builder.Default
    private Boolean adl = false;

    @Builder.Default
    private Boolean implied = false;

    @Column(name = "source_order_id")
    private String sourceOrderId;

    @Column(name = "oco_id")
    private String ocoId;

    @Column(name = "oco_order_no")
    private String ocoOrderNo;

    private BigDecimal leverage;

    @Column(name = "reduce_only")
    private Boolean reduceOnly;

    @Column(name = "callback_rate", columnDefinition = "decimal(18,8)")
    private BigDecimal callbackRate;

    @Column(name = "activation_price", columnDefinition = "decimal(18,8)")
    private BigDecimal activationPrice;

    @Column(name = "post_only")
    private Boolean postOnly;

    @Column(name = "cancel_reason")
    private String cancelReason;

    @Column(name = "max_slippage", columnDefinition = "decimal(18,8)")
    private BigDecimal maxSlippage;

    @Column(name = "fill_or_kill")
    private Boolean fillOrKill;

    @Column(name = "immediate_or_cancel")
    private Boolean immediateOrCancel;

    @Enumerated(EnumType.STRING)
    private FutureMode futureMode;

    @Column(columnDefinition = "decimal(18,8)")
    private BigDecimal size;

    @Column(name = "frozen_margin", columnDefinition = "decimal(18,8)")
    private BigDecimal frozenMargin;

    @Column(name = "take_profit_price", columnDefinition = "decimal(18,8)")
    private BigDecimal takeProfitPrice;

    @Column(name = "stop_loss_price", columnDefinition = "decimal(18,8)")
    private BigDecimal stopLossPrice;

    @Enumerated(EnumType.STRING)
    @Column(name = "position_mode")
    private PositionMode positionMode;

    @Column(name = "filled_volume", columnDefinition = "decimal(18,8)")
    private BigDecimal filledVolume;
}
