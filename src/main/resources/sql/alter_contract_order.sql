-- <PERSON><PERSON><PERSON><PERSON> các cột thiếu vào bảng contract_order
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS activation_price DECIMAL(18,8);
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS post_only BOOLEAN DEFAULT FALSE;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS max_slippage DECIMAL(18,8) DEFAULT 0;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS fill_or_kill BOOLEAN DEFAULT FALSE;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS immediate_or_cancel BOOLEAN DEFAULT FALSE;
ALTER TABLE contract_order ADD COLUMN IF NOT EXISTS cancel_reason VARCHAR(255);

-- Đ<PERSON><PERSON> tên cột slippage thành max_slippage nếu cần
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'contract_order'
        AND column_name = 'slippage'
    ) THEN
        ALTER TABLE contract_order RENAME COLUMN slippage TO max_slippage;
    END IF;
END $$;

-- <PERSON><PERSON><PERSON> nh<PERSON>t dữ liệu mẫu
UPDATE contract_order SET post_only = FALSE, fill_or_kill = FALSE, immediate_or_cancel = FALSE WHERE post_only IS NULL;
