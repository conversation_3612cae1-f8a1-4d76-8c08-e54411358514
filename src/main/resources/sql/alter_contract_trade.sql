-- T<PERSON><PERSON><PERSON> cột buy_order_type và sell_order_type vào bảng contract_trade
ALTER TABLE contract_trade ADD COLUMN IF NOT EXISTS buy_order_type VARCHAR(20);
ALTER TABLE contract_trade ADD COLUMN IF NOT EXISTS sell_order_type VARCHAR(20);

-- T<PERSON><PERSON><PERSON> cột buy_turnover và sell_turnover vào bảng contract_trade nếu chưa có
ALTER TABLE contract_trade ADD COLUMN IF NOT EXISTS buy_turnover DECIMAL(18,8);
ALTER TABLE contract_trade ADD COLUMN IF NOT EXISTS sell_turnover DECIMAL(18,8);

-- <PERSON><PERSON><PERSON> nh<PERSON>t dữ liệu mẫu
UPDATE contract_trade SET buy_order_type = 'LIMIT', sell_order_type = 'LIMIT' WHERE buy_order_type IS NULL;
