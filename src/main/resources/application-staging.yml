# ======================================================================
# Spring Boot Configuration for FUTURES API Service (Binance-style)
# Converted from application.properties to application.yml
# ======================================================================

# Database Config
spring:
  application:
    name: future-api
  output:
    ansi:
      enabled: always

  datasource:
    url: ${POSTGRES_URL:**********************************************}
    username: ${POSTGRES_USER:future_user}
    password: ${POSTGRES_PASSWORD:OZrB4ysfaHJkWApoTg5EAHlbIkXYhJE97mTX70pTEL1uyEw9yNYH8MA3Gk3gRPFu}
    driver-class-name: org.postgresql.Driver

    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      connection-timeout: 20000
      max-lifetime: 1800000
      pool-name: MyHikariCP

  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  #  Redis Configuration
  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:******************************************************************************************************************************************************************************************************************************

    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
      database: ${REDIS_DB:0}
      timeout: ${REDIS_TIMEOUT:5000}

  # OAuth2 / Keycloak Security
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082}/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082}/realms/cex-lotus
            token-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082}/realms/cex-lotus/protocol/openid-connect/token

  # Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:31226}
    properties:
      sasl:
        mechanism: PLAIN

    listener:
      concurrency: 9
      type: batch

    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:3}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      properties:
        # Idempotence ensures exactly-once delivery semantics
        enable.idempotence: true
        # Maximum in-flight requests per connection
        max.in.flight.requests.per.connection: 5
        # Request timeout in ms
        request.timeout.ms: 30000
        # Delivery timeout in ms
        delivery.timeout.ms: 120000
        # Linger time in ms
        linger.ms: 5
        # TCP keepalive settings
        connections.max.idle.ms: 540000

    consumer:
      client-id: ${spring.application.name}-consumer
      group-id: ${spring.application.name}-group
      enable-auto-commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      max-poll-records: 100
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        # Isolation level for reading transactional messages
        isolation.level: read_committed
        # Fetch minimum bytes
        fetch.min.bytes: 1
        # Fetch maximum wait time in ms
        fetch.max.wait.ms: 500
        # Heartbeat interval in ms
        heartbeat.interval.ms: 3000
        # Session timeout in ms
        session.timeout.ms: 30000
        # Maximum poll interval in ms
        max.poll.interval.ms: 300000
        # Trusted packages for deserialization
        spring.json.trusted.packages: "*"

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: ${server.servlet.context-path}/actuator/health
        health-check-interval: 60s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.prefer-ip-address}
        register-health-check: ${CONSUL_REGISTER_HEALTH_CHECK:true}
        # Add these properties to fix IP registration
        hostname: ${POD_IP:${spring.cloud.client.ip-address:localhost}}
        ip-address: ${POD_IP:${spring.cloud.client.ip-address:127.0.0.1}}
        prefer-agent-address: false
        deregister: true

# Kafka Topics (Messaging Channels)
topic-kafka:
  contract:
    order-new: contract-order-new
    order-cancel: contract-order-cancel
    order-cancel-broadcast: contract-order-cancel-broadcast
    order-cancel-success: contract-order-cancel-success
    order-completed: contract-order-completed
    trade: contract-trade
    trade-plate: contract-trade-plate
    position: contract-position
    mark-price: contract-mark-price
    index-price: contract-index-price
    funding-rate: contract-funding-rate
    liquidation: contract-liquidation
    last-price: contract-last-price
    order-events: contract-order-events
    order-commands: contract-order-commands
    order-cancel-result: contract-order-cancel-result
    order-cancel-all-result: contract-order-cancel-all-result

  minus:
    wallet-spot: minus-balance-wallet-spot

# Swagger (OpenAPI) Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# Keycloak Config
keycloak:
  auth-server-url: ${KEYCLOAK_ISSUER_URI:http://************:32082}
  realm: cex-lotus
  resource: cex-future
  credentials:
    secret: T63reTvMuOAr1JDVO7Jn1yRvGr0aKhOl

cex-security:
  resource-server-enabled: true
  default-principal-name: internal-service
  permit-all-endpoints:
    - "/contract-ws/**"
    - "/future/contract-ws/**"
    - "/api/v1/prices/history-kline/**"
    - "/api/v1/prices/mark/**"
    - "/api/v1/prices/last-price/**"
    - "/api/v1/prices/last-price/**"
    - "/api/v1/contracts/**"
