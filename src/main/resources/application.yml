server:
  port: ${SERVER_PORT:6010}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/admin}
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: flyway
  application:
    name: admin
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: cex-external-api-client
            client-secret: xkZbe85jveKyrsh65X6tWKTbkIgWWOGZ
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}/realms/cex-lotus
            token-uri: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}/realms/cex-lotus/protocol/openid-connect/token

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true

  jackson:
    serialization:
      indent_output: ${SPRING_JACKSON_INDENT_OUTPUT:true}
    date-format: ${SPRING_JACKSON_DATE_FORMAT:yyyy-MM-dd HH:mm:ss}
    time-zone: ${SPRING_JACKSON_TIME_ZONE:GMT+8}

  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}

  devtools:
    restart:
      enabled: ${SPRING_DEVTOOLS_RESTART_ENABLED:true}

  freemarker:
    cache: ${SPRING_FREEMARKER_CACHE:false}
    template-loader-path: ${SPRING_FREEMARKER_TEMPLATE_LOADER_PATH:classpath:/templates}

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}

management:
  context-path: /actuator
  health:
    mail:
      enabled: false
  security:
    enabled: false


endpoints:
  health:
    sensitive: false
  info:
    sensitive: false
  metrics:
    sensitive: false

aliyun:
  accessKeyId: ${ALIYUN_ACCESS_KEY_ID:LTAI5zG6W9gFE32Uw}
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET:D0RFaFT55JSU3223Daqln1T2uWGFP}
  ossEndpoint: ${ALIYUN_OSS_ENDPOINT:oss-cn-shenzhen.aliyuncs.com}
  ossBucketName: ${ALIYUN_OSS_BUCKET_NAME:bizzan}
  mail-sms:
    region: ap-southeast-1
    access-key-id: LTAI5tSqZs8e1sMSBPzt3Dkm
    access-secret: ******************************
    from-address: <EMAIL>
    from-alias: BIZZAN
    sms-sign: BIZZAN
    sms-template: SMS_199285259
    email-tag: BIZZAN

sms:
  driver: ${SMS_DRIVER:diyi}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:**********}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8CAA321133}
  sign: ${SMS_SIGN:BIZZAN}

commission:
  need:
    real-name: ${COMMISSION_NEED_REAL_NAME:1}
  promotion:
    second-level: ${COMMISSION_PROMOTION_SECOND_LEVEL:1}

spark:
  system:
    md5:
      key: ${SPARK_SYSTEM_MD5_KEY:XehGyeyrVgOV4P8Uf70REVpIw332iVNwNs}
    work-id: ${SPARK_SYSTEM_WORK_ID:1}
    data-center-id: ${SPARK_SYSTEM_DATA_CENTER_ID:1}
    host: ${SPARK_SYSTEM_HOST:smtp.126.com}
    name: ${SPARK_SYSTEM_NAME:BIZZAN}
    admins: ${SPARK_SYSTEM_ADMINS:<EMAIL>}
    admin-phones: ${SPARK_SYSTEM_ADMIN_PHONES:18000000}

access:
  key:
    id: ${ACCESS_KEY_ID:}
    secret: ${ACCESS_KEY_SECRET:}

es:
  username: ${ES_USERNAME:}
  password: ${ES_PASSWORD:}
  mine:
    index: ${ES_MINE_INDEX:}
    type: ${ES_MINE_TYPE:}
  public:
    ip: ${ES_PUBLIC_IP:}
  private:
    ip: ${ES_PRIVATE_IP:#}
  port: ${ES_PORT:9200}

google:
  host: BIZZAN.PRO

oss:
  name: oss

email:
  driver: java

# Add this under the existing configuration
logging:
  level:
    org.springframework.security: DEBUG
    org.springframework.security.web: DEBUG
    org.springframework.security.authentication: DEBUG
    org.springframework.security.access: DEBUG

cex-security:
  permit-all-endpoints:
    - "/system/employee/login"
    - "/system/employee/logout"
    - "/system/employee/refresh-token"
    - "auth/login"
    - "auth/register"
    - "/system/employee/update-password"
  resource-server-enabled: true


cex-services:
  market: "market"
  exchange: "exchange"

# keycloak with Consul Service Mesh
keycloak:
  auth-server-url: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}
  realm: cex-lotus
  resource: cex-admin
  credentials:
    secret: O6xO17JJ7W3M5jjCBNrh6Z84voYLMrUT
  # Admin client configuration for backend service
  admin-client:
    server-url: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}
    realm: cex-lotus
    client-id: admin-cli
    username: ${KEYCLOAK_ADMIN_USER:admin}
    password: ${KEYCLOAK_ADMIN_PASSWORD:admin}
    grant-type: password
