# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=contract-perpetual-futures
spring.kafka.consumer.auto-offset-reset=latest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.acks=all
spring.kafka.producer.retries=3

# Kafka Topics
topic-kafka.contract.order-new=contract-order-new
topic-kafka.contract.order-cancel=contract-order-cancel
topic-kafka.contract.order-completed=contract-order-completed
topic-kafka.contract.trade=contract-trade
topic-kafka.contract.trade-plate=contract-trade-plate
topic-kafka.contract.position=contract-position
topic-kafka.contract.mark-price=contract-mark-price
topic-kafka.contract.index-price=contract-index-price
topic-kafka.contract.funding-rate=contract-funding-rate
topic-kafka.contract.liquidation=contract-liquidation
