# Korean messages

# Common messages
common.success=ì±ê³µ
common.error=ì¤ë¥
common.warning=ê²½ê³ 
common.info=ì ë³´

# Order Matching messages
orderMatching.error.cancelOrder=ì£¼ë¬¸ ì·¨ì ì¤ë¥, ì£¼ë¬¸ ID = {0}, ì¬ë³¼ = {1}
orderMatching.error.placeOrder=ì£¼ë¬¸ ë°°ì¹ ì¤ë¥, ì£¼ë¬¸ ID = {0}, ì¬ë³¼ = {1}
orderMatching.error.cancelAllOrders=ëª¨ë  ì£¼ë¬¸ ì·¨ì ì¤ë¥, íì ID = {0}, ì¬ë³¼ = {1}
orderMatching.error.liquidatePosition=í¬ì§ì ì²­ì° ì¤ë¥, í¬ì§ì ID = {0}, ì¬ë³¼ = {1}
orderMatching.error.matchOrderFIFO=FIFO ìê³ ë¦¬ì¦ì¼ë¡ ì£¼ë¬¸ ë§¤ì¹­ ì¤ë¥, ì£¼ë¬¸ ID = {0}, ì¬ë³¼ = {1}
orderMatching.error.matchOrderProRata=Pro-rata ìê³ ë¦¬ì¦ì¼ë¡ ì£¼ë¬¸ ë§¤ì¹­ ì¤ë¥, ì£¼ë¬¸ ID = {0}, ì¬ë³¼ = {1}
orderMatching.error.matchOrderHybrid=Hybrid ìê³ ë¦¬ì¦ì¼ë¡ ì£¼ë¬¸ ë§¤ì¹­ ì¤ë¥, ì£¼ë¬¸ ID = {0}, ì¬ë³¼ = {1}
orderMatching.error.shutdown=OrderMatchingEngineService ë¦¬ìì¤ í´ì  ì¤ë¥
orderMatching.error.syncContracts=ë§¤ì¹­ ìì§ ëê¸°í ì¤í¨
orderMatching.error.initEngine=ë§¤ì¹­ ìì§ ì´ê¸°í ì¤ë¥, ì¬ë³¼ = {0}
orderMatching.error.checkTriggerOrders=í¸ë¦¬ê±° ì£¼ë¬¸ íì¸ ì¤ë¥, ì¬ë³¼ = {0}
orderMatching.error.checkLiquidations=ì²­ì° íì¸ ì¤ë¥, ì¬ë³¼ = {0}
orderMatching.error.lock.timeout=ì¬ë³¼ {0}ì ëí ì ê¸ì íëí  ì ììµëë¤
orderMatching.error.lock.interrupted=ì¬ë³¼ {0}ì ëí ì ê¸ì ê¸°ë¤ë¦¬ë ëì ì¤ë¨ëììµëë¤
orderMatching.error.contract.notFound=ì¬ë³¼ {0}ì ëí ê³ì½ì ì°¾ì ì ììµëë¤
orderMatching.error.setAlgorithm=ì¬ë³¼ {0}ì ëí ë§¤ì¹­ ìê³ ë¦¬ì¦ ì¤ì  ì¤ë¥, ìê³ ë¦¬ì¦: {1}
orderMatching.error.getOrderBook=ì¬ë³¼ {0}ì ëí ì£¼ë¬¸ì¥ ê°ì ¸ì¤ê¸° ì¤ë¥
orderMatching.info.algorithmSet=ì¬ë³¼ {0}ì ëí ë§¤ì¹­ ìê³ ë¦¬ì¦ ì¤ì  ìë£, ìê³ ë¦¬ì¦: {1}

orderMatching.warn.engineNotFound=ì¬ë³¼ {0}ì ëí ë§¤ì¹­ ìì§ì ì°¾ì ì ììµëë¤
orderMatching.warn.invalidSymbol=ì í¨íì§ ìì ì¬ë³¼
orderMatching.warn.invalidOrder=ì í¨íì§ ìì ì£¼ë¬¸
orderMatching.warn.tradingHalt=ì¬ë³¼ {0}ì ëí ê±°ëê° ì¤ì§ëììµëë¤
orderMatching.warn.enabledContractsNotFound=íì±íë ê³ì½ ëª©ë¡ì ê²ìí  ì ììµëë¤
orderMatching.warn.symbol.notOwned=ì¬ë³¼ {0}ì(ë) ì´ í¬ëì ìíì§ ììµëë¤

orderMatching.info.engineInitialized=ì¬ë³¼ {0}ì ëí ë§¤ì¹­ ìì§ì´ ì´ê¸°íëììµëë¤
orderMatching.info.engineShutdown=ì¬ë³¼ {0}ì ëí ë§¤ì¹­ ìì§ì´ ì¤ì§ëê³  ë¦¬ìì¤ê° í´ì ëììµëë¤
orderMatching.info.tradingPaused=ì¬ë³¼ {0}ì ëí ê±°ëê° ì¼ì ì¤ì§ëììµëë¤
orderMatching.info.tradingResumed=ì¬ë³¼ {0}ì ëí ê±°ëê° ì¬ê°ëììµëë¤
orderMatching.info.syncCompleted=ëê¸°íê° ìë£ëììµëë¤. ë§¤ì¹­ ìì§ ì: {0}
orderMatching.info.syncStarted=ê³ì½ê³¼ ë§¤ì¹­ ìì§ ëê¸°í ì¤
orderMatching.info.shutdownStarted=OrderMatchingEngineService ë¦¬ìì¤ í´ì  ì¤
orderMatching.info.shutdownCompleted=OrderMatchingEngineService ë¦¬ìì¤ê° í´ì ëììµëë¤
orderMatching.info.positionLiquidated=í¬ì§ì {0}ì´(ê°) ì²­ì°ëììµëë¤, ì¬ë³¼ = {1}
orderMatching.info.orderCanceled=ì£¼ë¬¸ {0}ì´(ê°) ì·¨ìëììµëë¤, ì¬ë³¼ = {1}
orderMatching.info.distributedEngineInitialized=ë¶ì° ì ê¸ ë§¤ì¹­ ìì§ì´ ì´ê¸°íëììµëë¤
orderMatching.info.optimizedEngineInitialized=ìµì íë ë§¤ì¹­ ìì§ì´ ì´ê¸°íëììµëë¤

orderMatching.debug.markPriceUpdated=ì¬ë³¼ {0}ì ëí ë§í¬ ê°ê²©ì´ ìë°ì´í¸ëììµëë¤, ë§í¬ ê°ê²© = {1}
orderMatching.debug.indexPriceUpdated=ì¬ë³¼ {0}ì ëí ì¸ë±ì¤ ê°ê²©ì´ ìë°ì´í¸ëììµëë¤, ì¸ë±ì¤ ê°ê²© = {1}
orderMatching.debug.triggerOrdersChecked=ì¬ë³¼ {0}ì ëí í¸ë¦¬ê±° ì£¼ë¬¸ì´ íì¸ëììµëë¤
orderMatching.debug.liquidationsChecked=ì¬ë³¼ {0}ì ëí ì²­ì°ì´ íì¸ëììµëë¤

# Contract Service messages
contractService.error.createContract=ê³ì½ ìì± ì¤ë¥, ì¬ë³¼ = {0}
contractService.error.updateContract=ê³ì½ ìë°ì´í¸ ì¤ë¥, ì¬ë³¼ = {0}
contractService.error.deleteContract=ê³ì½ ì­ì  ì¤ë¥, ì¬ë³¼ = {0}

contractService.warn.contractNotFound=ì¬ë³¼ {0}ì(ë¥¼) ê°ì§ ê³ì½ì ì°¾ì ì ììµëë¤

contractService.info.contractCreated=ê³ì½ì´ ìì±ëììµëë¤, ì¬ë³¼ = {0}
contractService.info.contractUpdated=ê³ì½ì´ ìë°ì´í¸ëììµëë¤, ì¬ë³¼ = {0}
contractService.info.contractDeleted=ê³ì½ì´ ì­ì ëììµëë¤, ì¬ë³¼ = {0}

# Position Service messages
positionService.error.createPosition=í¬ì§ì ìì± ì¤ë¥, íì ID = {0}, ì¬ë³¼ = {1}
positionService.error.updatePosition=í¬ì§ì ìë°ì´í¸ ì¤ë¥, í¬ì§ì ID = {0}
positionService.error.closePosition=í¬ì§ì ì¢ë£ ì¤ë¥, í¬ì§ì ID = {0}

positionService.warn.positionNotFound=ID {0}ì(ë¥¼) ê°ì§ í¬ì§ìì ì°¾ì ì ììµëë¤

positionService.info.positionCreated=í¬ì§ìì´ ìì±ëììµëë¤, íì ID = {0}, ì¬ë³¼ = {1}
positionService.info.positionUpdated=í¬ì§ìì´ ìë°ì´í¸ëììµëë¤, í¬ì§ì ID = {0}
positionService.info.positionClosed=í¬ì§ìì´ ì¢ë£ëììµëë¤, í¬ì§ì ID = {0}

# Messaging Service messages
messagingService.error.processFundingRate=íë© ë¹ì¨ ì²ë¦¬ ì¤ë¥, ì¬ë³¼ = {0}, ê° = {1}
messagingService.error.processNewOrder=ì ì£¼ë¬¸ ì²ë¦¬ ì¤ë¥
messagingService.error.processCancelOrder=ì£¼ë¬¸ ì·¨ì ì²ë¦¬ ì¤ë¥
messagingService.error.processFundingRateUpdate=íë© ë¹ì¨ ìë°ì´í¸ ì²ë¦¬ ì¤ë¥
messagingService.error.processTrade=ê±°ë ì²ë¦¬ ì¤ë¥, ì¬ë³¼ = {0}, ê° = {1}

messagingService.info.receivedFundingRate=íë© ë¹ì¨ì ìì íìµëë¤, ì¬ë³¼ = {0}, ê° = {1}
messagingService.info.receivedNewOrder=ì ì£¼ë¬¸ì ìì íìµëë¤, í í½ = {0}, í¤ = {1}
messagingService.info.receivedCancelOrder=ì£¼ë¬¸ ì·¨ìë¥¼ ìì íìµëë¤, í í½ = {0}, í¤ = {1}
messagingService.info.receivedFundingRateUpdate=íë© ë¹ì¨ ìë°ì´í¸ë¥¼ ìì íìµëë¤, í í½ = {0}, í¤ = {1}
messagingService.info.receivedTrade=ê±°ëë¥¼ ìì íìµëë¤, ì¬ë³¼ = {0}, ê° = {1}
messagingService.info.receivedLiquidation=ì²­ì°ì ìì íìµëë¤, ì¬ë³¼ = {0}, ê° = {1}
messagingService.info.markPriceFromHeader=í¤ëìì ë§í¬ ê°ê²©ì ì±ê³µì ì¼ë¡ ê°ì ¸ììµëë¤, ë§í¬ ê°ê²© = {0}
messagingService.warn.markPriceFromService=í¤ëìì ë§í¬ ê°ê²©ì ê°ì ¸ì¬ ì ììµëë¤, ìë¹ì¤ìì ë§í¬ ê°ê²©ì ì¬ì©í©ëë¤, ì¤ë¥ = {0}
messagingService.warn.useLiquidationPrice=ë§í¬ ê°ê²©ì ì°¾ì ì ììµëë¤, ì²­ì° ê°ê²©ì ì¬ì©í©ëë¤, ì¬ë³¼ = {0}
messagingService.info.processLiquidation=ë§í¬ ê°ê²© = {0}, ì¬ë³¼ = {1}, í¬ì§ì ID = {2}ë¡ ì²­ì° ì²ë¦¬ ì¤
messagingService.error.processLiquidation=ì²­ì° ì²ë¦¬ ì¤ë¥, ì¬ë³¼ = {0}, ê° = {1}

# Special Order Service messages
specialOrderService.error.processTimeOrders=ìê° ì£¼ë¬¸ ì²ë¦¬ ì¤ë¥
specialOrderService.error.processExpiredOrders=ë§ë£ë ì£¼ë¬¸ ì²ë¦¬ ì¤ë¥
specialOrderService.error.processTriggerOrders=í¸ë¦¬ê±° ì£¼ë¬¸ ì²ë¦¬ ì¤ë¥
specialOrderService.error.checkTriggerOrders=í¸ë¦¬ê±° ì£¼ë¬¸ íì¸ ì¤ë¥, ì¬ë³¼ = {0}
specialOrderService.error.processTimeOrder=ìê° ì£¼ë¬¸ ì²ë¦¬ ì¤ë¥

specialOrderService.info.processTimeOrders=ìê° ì£¼ë¬¸ ì²ë¦¬ ì¤
specialOrderService.info.processExpiredOrders=ë§ë£ë ì£¼ë¬¸ ì²ë¦¬ ì¤
specialOrderService.info.processTriggerOrders=í¸ë¦¬ê±° ì£¼ë¬¸ íì¸ ì¤

specialOrderService.debug.processTimeOrder=ìê° ì£¼ë¬¸ ì²ë¦¬ ì¤, ì£¼ë¬¸ ID = {0}

# Report Service messages
reportService.error.createReportsAsync=ìë£ë ëª¨ë  ì£¼ë¬¸ì ëí ë¹ëê¸° ë³´ê³ ì ìì± ì¤ë¥
reportService.error.processStatisticsAsync=ë¹ëê¸° íµê³ ì²ë¦¬ ì¤ë¥

reportService.info.createReportsAsync=ìë£ë ëª¨ë  ì£¼ë¬¸ì ëí ë¹ëê¸° ë³´ê³ ì ìì± ì¤
reportService.info.completedOrdersCount=ë³´ê³ ìê° ìë ìë£ë ì£¼ë¬¸ ì: {0}
reportService.info.createReportsAsyncCompleted=ìë£ë ëª¨ë  ì£¼ë¬¸ì ëí ë¹ëê¸° ë³´ê³ ì ìì± ìë£
reportService.info.processStatisticsAsync=ë¹ëê¸° íµê³ ì²ë¦¬ ì¤, ììì¼ = {0}, ì¢ë£ì¼ = {1}
reportService.info.processStatisticsAsyncCompleted=ë¹ëê¸° íµê³ ì²ë¦¬ ìë£, ììì¼ = {0}, ì¢ë£ì¼ = {1}

# Sharding Manager messages
shardingManager.error.rebalancingInterrupted=í¬ë {0}ì ëí ì¬ë³¼ ì¬ì¡°ì  ì¤ ì¤ë¨ë¨

shardingManager.warn.rebalancingLockFailed=í¬ë {0}ì ëí ì¬ë³¼ ì¬ì¡°ì ì ìí ì ê¸ì íëí  ì ìì
shardingManager.warn.no.active.pods=ì¬ì¡°ì ì ìí íì± í¬ëë¥¼ ì°¾ì ì ìì

shardingManager.info.initialized=í¬ë {0}ì ëí ì¤ë© ê´ë¦¬ì ì´ê¸°íë¨
shardingManager.info.symbolAssigned=ì¬ë³¼ {0}ì´(ê°) í¬ë {1}ì í ë¹ë¨, ì´ì  ìì ì: {2}
shardingManager.info.symbolUnassigned=ì¬ë³¼ {0}ì´(ê°) í¬ë {1}ìì í ë¹ í´ì ë¨
shardingManager.info.symbolRemoved=ì¬ë³¼ {0}ì´(ê°) í¬ë ë§¤íìì ì ê±°ë¨
shardingManager.info.rebalancingStarted=í¬ë {0}ì ëí ì¬ë³¼ ì¬ì¡°ì  ìì
shardingManager.info.rebalancingCompleted=í¬ë {0}ì ëí ì¬ë³¼ ì¬ì¡°ì  ìë£
shardingManager.info.symbolMoved=ì¬ë³¼ {0}ì´(ê°) í¬ë {1}ìì í¬ë {2}ë¡ ì´ëë¨
shardingManager.info.rebalanceStarted=í¬ë {0}ì ëí ì¬ë³¼ ì¬ì¡°ì  ìì
shardingManager.info.rebalanceCompleted=í¬ë {0}ì ëí ì¬ë³¼ ì¬ì¡°ì  ìë£

shardingManager.debug.heartbeatSent=í¬ë {0}ì ëí íí¸ë¹í¸ ì ì¡ë¨

# Reshard Executor messages
reshardExecutor.error.lockTimeout=ì¬ë³¼ {0}ì ëí ì ê¸ì íëí  ì ììµëë¤
reshardExecutor.error.lockInterrupted=ì¬ë³¼ {0}ì ëí ì ê¸ì ê¸°ë¤ë¦¬ë ëì ì¤ë¨ëììµëë¤
reshardExecutor.error.reshardFailed=ì¬ë³¼ {0}ì ëí ë¦¬ì¤ë© ì¤í¨
reshardExecutor.error.statusUpdateFailed=ì¬ë³¼ {0}ì ë¦¬ì¤ë© ìíë¥¼ {1}(ì¼)ë¡ ìë°ì´í¸íì§ ëª»íìµëë¤
reshardExecutor.error.prepareNotificationFailed=ì¬ë³¼ {0}ì ëí ì¤ë¹ ìë¦¼ ì ì¡ ì¤í¨
reshardExecutor.error.pauseOrderProcessingFailed=ì¬ë³¼ {0}ì ëí ì£¼ë¬¸ ì²ë¦¬ ì¼ì ì¤ì§ ì¤í¨
reshardExecutor.error.engineNotFound=ì¬ë³¼ {0}ì ëí ë§¤ì¹­ ìì§ì ì°¾ì ì ììµëë¤
reshardExecutor.error.snapshotCreationFailed=ì¬ë³¼ {0}ì ëí ì¤ëì· ìì± ì¤í¨
reshardExecutor.error.snapshotTransferFailed=ì¬ë³¼ {0}ì ì¤ëì·ì ì¤ë {1}ë¡ ì ì¡íì§ ëª»íìµëë¤
reshardExecutor.error.mappingUpdateFailed=ì¬ë³¼ {0}ì ë§¤íì ì¤ë {1}ë¡ ìë°ì´í¸íì§ ëª»íìµëë¤
reshardExecutor.error.completeNotificationFailed=ì¬ë³¼ {0}ì ëí ìë£ ìë¦¼ ì ì¡ ì¤í¨
reshardExecutor.error.rollbackFailed=ì¬ë³¼ {0}ì ëí ë¡¤ë°± ì¤í¨

reshardExecutor.info.executingReshard=ì¬ë³¼ {0}ì(ë¥¼) ì¤ë {1}ìì ì¤ë {2}ë¡ ë¦¬ì¤ë© ì¤í ì¤
reshardExecutor.info.statusUpdated=ì¬ë³¼ {0}ì ë¦¬ì¤ë© ìíë¥¼ {1}(ì¼)ë¡ ìë°ì´í¸íìµëë¤
reshardExecutor.info.prepareNotificationSent=ì¬ë³¼ {0}ì ëí ì¤ë¹ ìë¦¼ ì ì¡ë¨
reshardExecutor.info.orderProcessingPaused=ì¬ë³¼ {0}ì ëí ì£¼ë¬¸ ì²ë¦¬ê° ì¼ì ì¤ì§ë¨
reshardExecutor.info.snapshotCreated=ì¬ë³¼ {0}ì ëí ì¤ëì· ìì±ë¨
reshardExecutor.info.snapshotTransferred=ì¬ë³¼ {0}ì ì¤ëì·ì´ ì¤ë {1}ë¡ ì ì¡ë¨
reshardExecutor.info.mappingUpdated=ì¬ë³¼ {0}ì ë§¤íì´ ì¤ë {1}ë¡ ìë°ì´í¸ë¨
reshardExecutor.info.completeNotificationSent=ì¬ë³¼ {0}ì ëí ìë£ ìë¦¼ ì ì¡ë¨
reshardExecutor.info.rollingBack=ì¬ë³¼ {0}ì ëí ë¦¬ì¤ë© ë¡¤ë°± ì¤
reshardExecutor.info.rollbackCompleted=ì¬ë³¼ {0}ì ëí ë¡¤ë°± ìë£
reshardExecutor.info.reshardCompleted=ì¬ë³¼ {0}ì(ë¥¼) ì¤ë {1}ìì ì¤ë {2}ë¡ ë¦¬ì¤ë© ìë£

# Reshard Planner messages
reshardPlanner.info.planningReshard=ì¬ë³¼ {0}ì ëí ë¦¬ì¤ë© ê³í ì¤, ì´ì : {1}
reshardPlanner.info.reshardPlanExists=ì¬ë³¼ {0}ì(ë) ì´ë¯¸ ë¦¬ì¤ë© ê³íì´ ììµëë¤
reshardPlanner.error.currentShardNotFound=ì¬ë³¼ {0}ì ëí íì¬ ì¤ëë¥¼ ì°¾ì ì ììµëë¤
reshardPlanner.error.targetShardNotFound=ì¬ë³¼ {0}ì ëí ëì ì¤ëë¥¼ ì°¾ì ì ììµëë¤
reshardPlanner.info.sameShard=ì¬ë³¼ {0}ì(ë) ì´ë¯¸ ì¤ë {1}ì ìì¼ë¯ë¡ ë¦¬ì¤ë©ì´ íìíì§ ììµëë¤
reshardPlanner.info.reshardPlanCreated=ì¬ë³¼ {0}ì ëí ë¦¬ì¤ë© ê³íì´ ìì±ëììµëë¤ (ì¤ë {1}ìì ì¤ë {2}ë¡)
reshardPlanner.error.reshardPlanNotFound=ì¬ë³¼ {0}ì ëí ë¦¬ì¤ë© ê³íì ì°¾ì ì ììµëë¤
reshardPlanner.info.approvingReshardPlan=ì¬ë³¼ {0}ì ëí ë¦¬ì¤ë© ê³íì ì¹ì¸ ì¤ (ì¤ë {1}ìì ì¤ë {2}ë¡)
reshardPlanner.info.rejectingReshardPlan=ì¬ë³¼ {0}ì ëí ë¦¬ì¤ë© ê³íì ê±°ë¶ ì¤

# Hot Spot Detector messages
hotSpotDetector.debug.detectingHotSpots=í«ì¤í ê°ì§ ì¤
hotSpotDetector.info.hotSpotsDetected={0}ê°ì í«ì¤íì´ ê°ì§ëììµëë¤
hotSpotDetector.info.hotSpotDetails=í«ì¤í: ì¬ë³¼={0}, ì¬ê°ë={1}
hotSpotDetector.debug.hotSpotRecentlyDetected=í«ì¤í {0}ì´(ê°) ìµê·¼ì ê°ì§ëìì¼ë¯ë¡ ê±´ëëëë¤
hotSpotDetector.info.lowSeverityHotSpot=í«ì¤í {0}ì(ë) ì¬ê°ëê° ë®ì¼ë¯ë¡ ë¡ê¹ë§ í©ëë¤

# Load Imbalance Detector messages
loadImbalanceDetector.debug.detectingImbalance=ë¶í ë¶ê· í ê°ì§ ì¤
loadImbalanceDetector.info.imbalanceDetected=ì¤ë {1}ì ëí {0} ë¶ê· íì´ ê°ì§ëììµëë¤: {2}% (ìê³ê°: {3}%)
loadImbalanceDetector.info.processingImbalances={0}ê°ì ë¶ê· í ì²ë¦¬ ì¤
loadImbalanceDetector.info.shardImbalanceDetails=ì¤ë {0}ì ëí ë¶ê· í: {1}
loadImbalanceDetector.debug.imbalanceRecentlyDetected=ì¤ë {0}ì ëí ë¶ê· íì´ ìµê·¼ì ê°ì§ëìì¼ë¯ë¡ ê±´ëëëë¤

# Symbol Selector messages
symbolSelector.info.selectingSymbols=ì¤ë {0}ì ë¶íë¥¼ ì¬ì¡°ì íê¸° ìí ì¬ë³¼ ì í ì¤
symbolSelector.info.notEnoughInstances=ì¤ë {0}ì ë¶íë¥¼ ì¬ì¡°ì í  ì¸ì¤í´ì¤ê° ì¶©ë¶íì§ ììµëë¤
symbolSelector.error.cannotDetermineInstances=ì¤ë {0}ì ëí ìµê³  ë° ìµì  ë¶í ì¸ì¤í´ì¤ë¥¼ ê²°ì í  ì ììµëë¤
symbolSelector.info.loadInstancesIdentified=ìµê³  ë¶í ì¸ì¤í´ì¤ {0}ê³¼(ì) ìµì  ë¶í ì¸ì¤í´ì¤ {1}ì(ë¥¼) ìë³íìµëë¤
symbolSelector.info.noSymbolsFound=ì¸ì¤í´ì¤ {0}ì ëí ì¬ë³¼ì ì°¾ì ì ììµëë¤
symbolSelector.info.noSuitableSymbolsFound=ì´ëí  ì í©í ì¬ë³¼ì ì°¾ì ì ììµëë¤
symbolSelector.info.symbolsSelected=ì¸ì¤í´ì¤ {1}ìì ì¸ì¤í´ì¤ {2}ë¡ ì´ëí  {0}ê°ì ì¬ë³¼ì ì ííìµëë¤

# Migration Planner messages
migrationPlanner.info.planningMigration=ì¸ì¤í´ì¤ {1}ìì ì¸ì¤í´ì¤ {2}ë¡ {0}ê°ì ì¬ë³¼ ë§ì´ê·¸ë ì´ì ê³í ì¤
migrationPlanner.info.tooManyMigrations=ì´ë¯¸ {0}ê°ì ë§ì´ê·¸ë ì´ìì´ ì§í ì¤ì´ë©°, ì í {1}ì(ë¥¼) ì´ê³¼í©ëë¤
migrationPlanner.info.migrationPlanExists=ì¬ë³¼ {0}ì(ë) ì´ë¯¸ ë§ì´ê·¸ë ì´ì ê³íì´ ììµëë¤
migrationPlanner.info.migrationPlanCreated=ì¬ë³¼ {0}ì ëí ë§ì´ê·¸ë ì´ì ê³íì´ ìì±ëììµëë¤ (ì¸ì¤í´ì¤ {1}ìì ì¸ì¤í´ì¤ {2}ë¡)
migrationPlanner.error.migrationPlanNotFound=ì¬ë³¼ {0}ì ëí ë§ì´ê·¸ë ì´ì ê³íì ì°¾ì ì ììµëë¤
migrationPlanner.info.approvingMigrationPlan=ì¬ë³¼ {0}ì ëí ë§ì´ê·¸ë ì´ì ê³íì ì¹ì¸ ì¤ (ì¸ì¤í´ì¤ {1}ìì ì¸ì¤í´ì¤ {2}ë¡)
migrationPlanner.info.rejectingMigrationPlan=ì¬ë³¼ {0}ì ëí ë§ì´ê·¸ë ì´ì ê³íì ê±°ë¶ ì¤

# Migration Executor messages
migrationExecutor.info.executingMigration=ì¬ë³¼ {0}ì(ë¥¼) ì¸ì¤í´ì¤ {1}ìì ì¸ì¤í´ì¤ {2}ë¡ ë§ì´ê·¸ë ì´ì ì¤í ì¤
migrationExecutor.error.lockTimeout=ì¬ë³¼ {0}ì ëí ì ê¸ì íëí  ì ììµëë¤
migrationExecutor.error.lockInterrupted=ì¬ë³¼ {0}ì ëí ì ê¸ì ê¸°ë¤ë¦¬ë ëì ì¤ë¨ëììµëë¤
migrationExecutor.error.migrationFailed=ì¬ë³¼ {0}ì ëí ë§ì´ê·¸ë ì´ì ì¤í¨
migrationExecutor.info.statusUpdated=ì¬ë³¼ {0}ì ë§ì´ê·¸ë ì´ì ìíë¥¼ {1}(ì¼)ë¡ ìë°ì´í¸íìµëë¤
migrationExecutor.error.statusUpdateFailed=ì¬ë³¼ {0}ì ë§ì´ê·¸ë ì´ì ìíë¥¼ {1}(ì¼)ë¡ ìë°ì´í¸íì§ ëª»íìµëë¤
migrationExecutor.info.prepareNotificationSent=ì¬ë³¼ {0}ì ëí ì¤ë¹ ìë¦¼ ì ì¡ë¨
migrationExecutor.error.prepareNotificationFailed=ì¬ë³¼ {0}ì ëí ì¤ë¹ ìë¦¼ ì ì¡ ì¤í¨
migrationExecutor.info.orderProcessingPaused=ì¬ë³¼ {0}ì ëí ì£¼ë¬¸ ì²ë¦¬ê° ì¼ì ì¤ì§ë¨
migrationExecutor.error.pauseOrderProcessingFailed=ì¬ë³¼ {0}ì ëí ì£¼ë¬¸ ì²ë¦¬ ì¼ì ì¤ì§ ì¤í¨
migrationExecutor.info.snapshotCreated=ì¬ë³¼ {0}ì ëí ì¤ëì· ìì±ë¨
migrationExecutor.error.engineNotFound=ì¬ë³¼ {0}ì ëí ë§¤ì¹­ ìì§ì ì°¾ì ì ììµëë¤
migrationExecutor.error.snapshotCreationFailed=ì¬ë³¼ {0}ì ëí ì¤ëì· ìì± ì¤í¨
migrationExecutor.info.snapshotTransferred=ì¬ë³¼ {0}ì ì¤ëì·ì´ ì¸ì¤í´ì¤ {1}ë¡ ì ì¡ë¨
migrationExecutor.error.snapshotTransferFailed=ì¬ë³¼ {0}ì ì¤ëì·ì ì¸ì¤í´ì¤ {1}ë¡ ì ì¡íì§ ëª»íìµëë¤
migrationExecutor.info.mappingUpdated=ì¬ë³¼ {0}ì ë§¤íì´ ì¸ì¤í´ì¤ {1}ë¡ ìë°ì´í¸ë¨
migrationExecutor.error.mappingUpdateFailed=ì¬ë³¼ {0}ì ë§¤íì ì¸ì¤í´ì¤ {1}ë¡ ìë°ì´í¸íì§ ëª»íìµëë¤
migrationExecutor.info.completeNotificationSent=ì¬ë³¼ {0}ì ëí ìë£ ìë¦¼ ì ì¡ë¨
migrationExecutor.error.completeNotificationFailed=ì¬ë³¼ {0}ì ëí ìë£ ìë¦¼ ì ì¡ ì¤í¨
migrationExecutor.info.rollingBack=ì¬ë³¼ {0}ì ëí ë§ì´ê·¸ë ì´ì ë¡¤ë°± ì¤
migrationExecutor.info.rollbackCompleted=ì¬ë³¼ {0}ì ëí ë¡¤ë°± ìë£
migrationExecutor.error.rollbackFailed=ì¬ë³¼ {0}ì ëí ë¡¤ë°± ì¤í¨
migrationExecutor.info.migrationCompleted=ì¬ë³¼ {0}ì(ë¥¼) ì¸ì¤í´ì¤ {1}ìì ì¸ì¤í´ì¤ {2}ë¡ ë§ì´ê·¸ë ì´ì ìë£

# Settlement messages
settlement.error.scheduledFundingSettlement=ì¬ë³¼ {0}ì ëí ìì½ë íë© ì ì° ì¤ë¥
settlement.error.scheduledContractSettlement=ì¬ë³¼ {0}ì ëí ìì½ë ê³ì½ ì ì° ì¤ë¥
settlement.error.initializeFundingSettlementSchedules=íë© ì ì° ì¼ì  ì´ê¸°í ì¤ë¥
settlement.error.initializeContractSettlementSchedules=ê³ì½ ì ì° ì¼ì  ì´ê¸°í ì¤ë¥
settlement.error.calculateFundingRate=ì¬ë³¼ {0}ì ëí íë© ë¹ì¨ ê³ì° ì¤ë¥
settlement.error.settleContract=ì¬ë³¼ {0}ì ëí ê³ì½ ì ì° ì¤ë¥
settlement.error.settleFunding=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí íë© ì ì° ì¤ë¥
settlement.error.autoFundingSettlement=ì¬ë³¼ {0}ì ëí ìë íë© ì ì° ì¤ë¥
settlement.error.autoContractSettlement=ì¬ë³¼ {0}ì ëí ìë ê³ì½ ì ì° ì¤ë¥
settlement.error.autoFundingSettlementAll=ëª¨ë  ì¬ë³¼ì ëí ìë íë© ì ì° ì¤ë¥
settlement.error.autoContractSettlementAll=ëª¨ë  ì¬ë³¼ì ëí ìë ê³ì½ ì ì° ì¤ë¥
settlement.error.scheduleFundingSettlement=ì¬ë³¼ {0}, ì¼ì  {1}ì ëí íë© ì ì° ì¼ì  ì¤ì  ì¤ë¥
settlement.error.scheduleContractSettlement=ì¬ë³¼ {0}, ì¼ì  {1}ì ëí ê³ì½ ì ì° ì¼ì  ì¤ì  ì¤ë¥
settlement.error.scheduleFundingSettlementAll=ì¼ì  {0}ì¼ë¡ ëª¨ë  ì¬ë³¼ì ëí íë© ì ì° ì¼ì  ì¤ì  ì¤ë¥
settlement.error.scheduleContractSettlementAll=ì¼ì  {0}ì¼ë¡ ëª¨ë  ì¬ë³¼ì ëí ê³ì½ ì ì° ì¼ì  ì¤ì  ì¤ë¥
settlement.error.cancelFundingSettlementSchedule=ì¬ë³¼ {0}ì ëí íë© ì ì° ì¼ì  ì·¨ì ì¤ë¥
settlement.error.cancelContractSettlementSchedule=ì¬ë³¼ {0}ì ëí ê³ì½ ì ì° ì¼ì  ì·¨ì ì¤ë¥
settlement.error.cancelFundingSettlementScheduleAll=ëª¨ë  ì¬ë³¼ì ëí íë© ì ì° ì¼ì  ì·¨ì ì¤ë¥
settlement.error.cancelContractSettlementScheduleAll=ëª¨ë  ì¬ë³¼ì ëí ê³ì½ ì ì° ì¼ì  ì·¨ì ì¤ë¥
settlement.error.getNextFundingSettlementTime=ì¬ë³¼ {0}ì ëí ë¤ì íë© ì ì° ìê° ê°ì ¸ì¤ê¸° ì¤ë¥
settlement.error.fundingSettlement=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí íë© ì ì° ì¤ë¥
settlement.error.fundingSettlementRetry=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí íë© ì ì°ì 3ë² ì¬ìëíì§ë§ ì¤í¨í¨
settlement.error.contractSettlementRetry=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí ê³ì½ ì ì°ì 3ë² ì¬ìëíì§ë§ ì¤í¨í¨

settlement.warn.fundingRateZero=ì¬ë³¼ {0}ì ëí íë© ë¹ì¨ì´ 0ìëë¤
settlement.warn.settlementPriceZero=ì¬ë³¼ {0}ì ëí ì ì° ê°ê²©ì´ 0ìëë¤
settlement.warn.noPositions=ì¬ë³¼ {0}ì ëí í¬ì§ìì ì°¾ì ì ììµëë¤

settlement.info.scheduledFundingSettlementStarted=ì¬ë³¼ {0}ì ëí ìì½ë íë© ì ì° ììë¨
settlement.info.scheduledFundingSettlementCompleted=ì¬ë³¼ {0}ì ëí ìì½ë íë© ì ì° ìë£ë¨
settlement.info.scheduledContractSettlementStarted=ì¬ë³¼ {0}ì ëí ìì½ë ê³ì½ ì ì° ììë¨
settlement.info.scheduledContractSettlementCompleted=ì¬ë³¼ {0}ì ëí ìì½ë ê³ì½ ì ì° ìë£ë¨
settlement.info.initializeFundingSettlementSchedules=íë© ì ì° ì¼ì  ì´ê¸°í ì¤
settlement.info.initializeFundingSettlementSchedulesCompleted=íë© ì ì° ì¼ì  ì´ê¸°í ìë£ë¨
settlement.info.initializeContractSettlementSchedules=ê³ì½ ì ì° ì¼ì  ì´ê¸°í ì¤
settlement.info.initializeContractSettlementSchedulesCompleted=ê³ì½ ì ì° ì¼ì  ì´ê¸°í ìë£ë¨
settlement.info.autoFundingSettlementStarted=ì¬ë³¼ {0}ì ëí ìë íë© ì ì° ììë¨
settlement.info.autoFundingSettlementCompleted=ì¬ë³¼ {0}ì ëí ìë íë© ì ì° ìë£ë¨
settlement.info.autoContractSettlementStarted=ì¬ë³¼ {0}ì ëí ìë ê³ì½ ì ì° ììë¨
settlement.info.autoContractSettlementCompleted=ì¬ë³¼ {0}ì ëí ìë ê³ì½ ì ì° ìë£ë¨
settlement.info.autoFundingSettlementAllCompleted=ëª¨ë  ì¬ë³¼ì ëí ìë íë© ì ì° ìë£ë¨
settlement.info.autoContractSettlementAllCompleted=ëª¨ë  ì¬ë³¼ì ëí ìë ê³ì½ ì ì° ìë£ë¨
settlement.info.scheduleFundingSettlement=ì¬ë³¼ {0}, ë¤ì ìê° {1}ì ëí íë© ì ì° ì¼ì  ì¤ì  ì¤
settlement.info.scheduleContractSettlement=ì¬ë³¼ {0}, ë¤ì ìê° {1}ì ëí ê³ì½ ì ì° ì¼ì  ì¤ì  ì¤
settlement.info.scheduleFundingSettlementCompleted=ì¬ë³¼ {0}, ë¤ì ìê° {1}ì ëí íë© ì ì° ì¼ì  ì¤ì  ìë£ë¨
settlement.info.scheduleContractSettlementCompleted=ì¬ë³¼ {0}, ë¤ì ìê° {1}ì ëí ê³ì½ ì ì° ì¼ì  ì¤ì  ìë£ë¨
settlement.info.scheduleFundingSettlementAll=ì¼ì  {0}ì¼ë¡ ëª¨ë  ì¬ë³¼ì ëí íë© ì ì° ì¼ì  ì¤ì  ì¤
settlement.info.scheduleFundingSettlementAllCompleted=ì¼ì  {0}ì¼ë¡ ëª¨ë  ì¬ë³¼ì ëí íë© ì ì° ì¼ì  ì¤ì  ìë£ë¨, ì±ê³µ ì: {1}
settlement.info.scheduleContractSettlementAll=ì¼ì  {0}ì¼ë¡ ëª¨ë  ì¬ë³¼ì ëí ê³ì½ ì ì° ì¼ì  ì¤ì  ì¤
settlement.info.scheduleContractSettlementAllCompleted=ì¼ì  {0}ì¼ë¡ ëª¨ë  ì¬ë³¼ì ëí ê³ì½ ì ì° ì¼ì  ì¤ì  ìë£ë¨, ì±ê³µ ì: {1}
settlement.info.cancelFundingSettlementSchedule=ì¬ë³¼ {0}ì ëí íë© ì ì° ì¼ì  ì·¨ì ì¤
settlement.info.cancelFundingSettlementScheduleCompleted=ì¬ë³¼ {0}ì ëí íë© ì ì° ì¼ì  ì·¨ìë¨
settlement.info.cancelContractSettlementSchedule=ì¬ë³¼ {0}ì ëí ê³ì½ ì ì° ì¼ì  ì·¨ì ì¤
settlement.info.cancelContractSettlementScheduleCompleted=ì¬ë³¼ {0}ì ëí ê³ì½ ì ì° ì¼ì  ì·¨ìë¨
settlement.info.cancelFundingSettlementScheduleAll=ëª¨ë  ì¬ë³¼ì ëí íë© ì ì° ì¼ì  ì·¨ì ì¤
settlement.info.cancelFundingSettlementScheduleAllCompleted=ëª¨ë  ì¬ë³¼ì ëí íë© ì ì° ì¼ì  ì·¨ìë¨, ì±ê³µ ì: {0}
settlement.info.cancelContractSettlementScheduleAll=ëª¨ë  ì¬ë³¼ì ëí ê³ì½ ì ì° ì¼ì  ì·¨ì ì¤
settlement.info.cancelContractSettlementScheduleAllCompleted=ëª¨ë  ì¬ë³¼ì ëí ê³ì½ ì ì° ì¼ì  ì·¨ìë¨, ì±ê³µ ì: {0}
settlement.info.fundingSettlementStarted=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí íë© ì ì° ììë¨
settlement.info.fundingSettlementCompleted=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí íë© ì ì° ìë£ë¨
settlement.info.contractSettlementStarted=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí ê³ì½ ì ì° ììë¨
settlement.info.contractSettlementCompleted=ì¬ë³¼ {0}, íìì¤í¬í {1}ì ëí ê³ì½ ì ì° ìë£ë¨
settlement.info.autoFundingSettlementAllStarted=íìì¤í¬í {0}ì ëª¨ë  ì¬ë³¼ì ëí ìë íë© ì ì° ììë¨
settlement.info.autoContractSettlementAllStarted=íìì¤í¬í {0}ì ëª¨ë  ì¬ë³¼ì ëí ìë ê³ì½ ì ì° ììë¨

# Funding Service messages
fundingService.debug.calculateFundingRate=ì¬ë³¼ {0}, ì¸ë±ì¤ ê°ê²© {1}, ë§í¬ ê°ê²© {2}ì ëí íë© ë¹ì¨ ê³ì° ì¤
fundingService.debug.getCurrentFundingRate=ì¬ë³¼ {0}ì ëí íì¬ íë© ë¹ì¨ ê°ì ¸ì¤ë ì¤
fundingService.debug.getNextFundingTime=ì¬ë³¼ {0}ì ëí ë¤ì íë© ìê° ê°ì ¸ì¤ë ì¤

fundingService.error.calculateFundingRate=ì¬ë³¼ {0}ì ëí íë© ë¹ì¨ ê³ì° ì¤ ì¤ë¥ ë°ì
fundingService.error.getCurrentFundingRate=ì¬ë³¼ {0}ì ëí íì¬ íë© ë¹ì¨ ê°ì ¸ì¤ë ì¤ ì¤ë¥ ë°ì
fundingService.error.getNextFundingTime=ì¬ë³¼ {0}ì ëí ë¤ì íë© ìê° ê°ì ¸ì¤ë ì¤ ì¤ë¥ ë°ì

# Price Service messages
priceService.debug.getIndexPrice=ì¬ë³¼ {0}ì ëí ì¸ë±ì¤ ê°ê²© ê°ì ¸ì¤ë ì¤
priceService.debug.getMarkPrice=ì¬ë³¼ {0}ì ëí ë§í¬ ê°ê²© ê°ì ¸ì¤ë ì¤
priceService.debug.calculateIndexPrice=ì¬ë³¼ {0}ì ëí ì¸ë±ì¤ ê°ê²© ê³ì° ì¤
priceService.debug.calculateMarkPrice=ì¬ë³¼ {0}ì ëí ë§í¬ ê°ê²© ê³ì° ì¤

priceService.error.getIndexPrice=ì¬ë³¼ {0}ì ëí ì¸ë±ì¤ ê°ê²© ê°ì ¸ì¤ë ì¤ ì¤ë¥ ë°ì
priceService.error.getMarkPrice=ì¬ë³¼ {0}ì ëí ë§í¬ ê°ê²© ê°ì ¸ì¤ë ì¤ ì¤ë¥ ë°ì
priceService.error.calculateIndexPrice=ì¬ë³¼ {0}ì ëí ì¸ë±ì¤ ê°ê²© ê³ì° ì¤ ì¤ë¥ ë°ì
priceService.error.calculateMarkPrice=ì¬ë³¼ {0}ì ëí ë§í¬ ê°ê²© ê³ì° ì¤ ì¤ë¥ ë°ì
