# Default messages (English)

# Common messages
common.success=Success
common.error=Error
common.warning=Warning
common.info=Information

# Order Matching messages
orderMatching.error.cancelOrder=Error canceling order, orderId = {0}, symbol = {1}
orderMatching.error.placeOrder=Error placing order, orderId = {0}, symbol = {1}
orderMatching.error.cancelAllOrders=Error canceling all orders, memberId = {0}, symbol = {1}
orderMatching.error.liquidatePosition=Error liquidating position, positionId = {0}, symbol = {1}
orderMatching.error.matchOrderFIFO=Error matching order with FIFO algorithm, orderId = {0}, symbol = {1}
orderMatching.error.matchOrderProRata=Error matching order with Pro-rata algorithm, orderId = {0}, symbol = {1}
orderMatching.error.matchOrderHybrid=Error matching order with Hybrid algorithm, orderId = {0}, symbol = {1}
orderMatching.error.shutdown=Error releasing resources of OrderMatchingEngineService
orderMatching.error.syncContracts=Synchronizing matching engine failed
orderMatching.error.initEngine=Error initializing matching engine, symbol = {0}
orderMatching.error.checkTriggerOrders=Error checking trigger orders, symbol = {0}
orderMatching.error.checkLiquidations=Error checking liquidations, symbol = {0}
orderMatching.error.lock.timeout=Could not acquire lock for symbol: {0}
orderMatching.error.lock.interrupted=Interrupted while waiting for lock for symbol: {0}
orderMatching.error.contract.notFound=Contract not found for symbol: {0}
orderMatching.error.setAlgorithm=Error setting matching algorithm for symbol: {0}, algorithm: {1}
orderMatching.error.getOrderBook=Error getting order book for symbol: {0}
orderMatching.info.algorithmSet=Matching algorithm set for symbol: {0}, algorithm: {1}

orderMatching.warn.engineNotFound=Matching engine not found for symbol {0}
orderMatching.warn.invalidSymbol=Invalid symbol
orderMatching.warn.invalidOrder=Invalid order
orderMatching.warn.tradingHalt=Trading is halted for symbol {0}
orderMatching.warn.enabledContractsNotFound=Unable to retrieve list of enabled contracts
orderMatching.warn.symbol.notOwned=Symbol {0} is not owned by this pod

orderMatching.info.engineInitialized=Matching engine initialized for symbol {0}
orderMatching.info.engineShutdown=Matching engine stopped and resources released for symbol = {0}
orderMatching.info.tradingPaused=Trading paused for symbol {0}
orderMatching.info.tradingResumed=Trading resumed for symbol {0}
orderMatching.info.syncCompleted=Synchronization completed. Number of matching engines: {0}
orderMatching.info.syncStarted=Synchronizing matching engine with contracts
orderMatching.info.shutdownStarted=Releasing resources of OrderMatchingEngineService
orderMatching.info.shutdownCompleted=Resources of OrderMatchingEngineService released
orderMatching.info.positionLiquidated=Position liquidated {0}, symbol = {1}
orderMatching.info.orderCanceled=Order canceled {0}, symbol = {1}
orderMatching.info.distributedEngineInitialized=Distributed locking matching engine initialized
orderMatching.info.optimizedEngineInitialized=Optimized matching engine initialized

orderMatching.debug.markPriceUpdated=Mark price updated for symbol {0}, markPrice = {1}
orderMatching.debug.indexPriceUpdated=Index price updated for symbol {0}, indexPrice = {1}
orderMatching.debug.triggerOrdersChecked=Trigger orders checked for symbol {0}
orderMatching.debug.liquidationsChecked=Liquidations checked for symbol {0}

# Contract Service messages
contractService.error.createContract=Error creating contract, symbol = {0}
contractService.error.updateContract=Error updating contract, symbol = {0}
contractService.error.deleteContract=Error deleting contract, symbol = {0}

contractService.warn.contractNotFound=Contract not found with symbol {0}

contractService.info.contractCreated=Contract created, symbol = {0}
contractService.info.contractUpdated=Contract updated, symbol = {0}
contractService.info.contractDeleted=Contract deleted, symbol = {0}

# Position Service messages
positionService.error.createPosition=Error creating position, memberId = {0}, symbol = {1}
positionService.error.updatePosition=Error updating position, positionId = {0}
positionService.error.closePosition=Error closing position, positionId = {0}

positionService.warn.positionNotFound=Position not found with id {0}

positionService.info.positionCreated=Position created, memberId = {0}, symbol = {1}
positionService.info.positionUpdated=Position updated, positionId = {0}
positionService.info.positionClosed=Position closed, positionId = {0}

# Messaging Service messages
messagingService.error.processFundingRate=Error processing funding rate, symbol = {0}, value = {1}
messagingService.error.processNewOrder=Error processing new order
messagingService.error.processCancelOrder=Error processing cancel order
messagingService.error.processFundingRateUpdate=Error processing funding rate update
messagingService.error.processTrade=Error processing trade, symbol = {0}, value = {1}

messagingService.info.receivedFundingRate=Received funding rate, symbol = {0}, value = {1}
messagingService.info.receivedNewOrder=Received new order, topic = {0}, key = {1}
messagingService.info.receivedCancelOrder=Received cancel order, topic = {0}, key = {1}
messagingService.info.receivedFundingRateUpdate=Received funding rate update, topic = {0}, key = {1}
messagingService.info.receivedTrade=Received trade, symbol = {0}, value = {1}
messagingService.info.receivedLiquidation=Received liquidation, symbol = {0}, value = {1}
messagingService.info.markPriceFromHeader=Successfully got mark price from header, markPrice = {0}
messagingService.warn.markPriceFromService=Could not get mark price from header, using mark price from service, error = {0}
messagingService.warn.useLiquidationPrice=Mark price not found, using liquidation price, symbol = {0}
messagingService.info.processLiquidation=Processing liquidation with mark price = {0}, symbol = {1}, positionId = {2}
messagingService.error.processLiquidation=Error processing liquidation, symbol = {0}, value = {1}

# Special Order Service messages
specialOrderService.error.processTimeOrders=Error processing time orders
specialOrderService.error.processExpiredOrders=Error processing expired orders
specialOrderService.error.processTriggerOrders=Error processing trigger orders
specialOrderService.error.checkTriggerOrders=Error checking trigger orders, symbol = {0}
specialOrderService.error.processTimeOrder=Error processing time order

specialOrderService.info.processTimeOrders=Processing time orders
specialOrderService.info.processExpiredOrders=Processing expired orders
specialOrderService.info.processTriggerOrders=Checking trigger orders

specialOrderService.debug.processTimeOrder=Processing time order, orderId = {0}

# Report Service messages
reportService.error.createReportsAsync=Error creating reports asynchronously for all completed orders
reportService.error.processStatisticsAsync=Error processing statistics asynchronously

reportService.info.createReportsAsync=Creating reports asynchronously for all completed orders
reportService.info.completedOrdersCount=Number of completed orders without report: {0}
reportService.info.createReportsAsyncCompleted=Asynchronous report creation completed for all completed orders
reportService.info.processStatisticsAsync=Processing statistics asynchronously, startDate = {0}, endDate = {1}
reportService.info.processStatisticsAsyncCompleted=Asynchronous statistics processing completed, startDate = {0}, endDate = {1}

# Sharding Manager messages
shardingManager.error.rebalancingInterrupted=Interrupted while rebalancing symbols for pod: {0}

shardingManager.warn.rebalancingLockFailed=Could not acquire lock for rebalancing symbols for pod: {0}
shardingManager.warn.no.active.pods=No active pods found for rebalancing

shardingManager.info.initialized=Sharding manager initialized for pod: {0}
shardingManager.info.symbolAssigned=Symbol {0} assigned to pod {1}, previous owner: {2}
shardingManager.info.symbolUnassigned=Symbol {0} unassigned from pod {1}
shardingManager.info.symbolRemoved=Symbol {0} removed from pod mapping
shardingManager.info.rebalancingStarted=Starting rebalancing symbols for pod: {0}
shardingManager.info.rebalancingCompleted=Completed rebalancing symbols for pod: {0}
shardingManager.info.symbolMoved=Symbol {0} moved from pod {1} to pod {2}
shardingManager.info.rebalanceStarted=Starting rebalancing symbols for pod: {0}
shardingManager.info.rebalanceCompleted=Completed rebalancing symbols for pod: {0}

shardingManager.debug.heartbeatSent=Heartbeat sent for pod: {0}

# Reshard Executor messages
reshardExecutor.error.lockTimeout=Could not acquire lock for symbol: {0}
reshardExecutor.error.lockInterrupted=Interrupted while waiting for lock for symbol: {0}
reshardExecutor.error.reshardFailed=Resharding failed for symbol: {0}
reshardExecutor.error.statusUpdateFailed=Failed to update resharding status for symbol {0} to {1}
reshardExecutor.error.prepareNotificationFailed=Failed to send prepare notification for symbol: {0}
reshardExecutor.error.pauseOrderProcessingFailed=Failed to pause order processing for symbol: {0}
reshardExecutor.error.engineNotFound=Matching engine not found for symbol: {0}
reshardExecutor.error.snapshotCreationFailed=Failed to create snapshot for symbol: {0}
reshardExecutor.error.snapshotTransferFailed=Failed to transfer snapshot for symbol {0} to shard {1}
reshardExecutor.error.mappingUpdateFailed=Failed to update mapping for symbol {0} to shard {1}
reshardExecutor.error.completeNotificationFailed=Failed to send completion notification for symbol: {0}
reshardExecutor.error.rollbackFailed=Rollback failed for symbol: {0}

reshardExecutor.info.executingReshard=Executing resharding for symbol {0} from shard {1} to shard {2}
reshardExecutor.info.statusUpdated=Updated resharding status for symbol {0} to {1}
reshardExecutor.info.prepareNotificationSent=Prepare notification sent for symbol: {0}
reshardExecutor.info.orderProcessingPaused=Order processing paused for symbol: {0}
reshardExecutor.info.snapshotCreated=Snapshot created for symbol: {0}
reshardExecutor.info.snapshotTransferred=Snapshot transferred for symbol {0} to shard {1}
reshardExecutor.info.mappingUpdated=Mapping updated for symbol {0} to shard {1}
reshardExecutor.info.completeNotificationSent=Completion notification sent for symbol: {0}
reshardExecutor.info.rollingBack=Rolling back resharding for symbol: {0}
reshardExecutor.info.rollbackCompleted=Rollback completed for symbol: {0}
reshardExecutor.info.reshardCompleted=Resharding completed for symbol {0} from shard {1} to shard {2}

# Reshard Planner messages
reshardPlanner.info.planningReshard=Planning resharding for symbol {0} with reason {1}
reshardPlanner.info.reshardPlanExists=Symbol {0} already has a resharding plan
reshardPlanner.error.currentShardNotFound=Current shard not found for symbol {0}
reshardPlanner.error.targetShardNotFound=Target shard not found for symbol {0}
reshardPlanner.info.sameShard=Symbol {0} is already on shard {1}, no resharding needed
reshardPlanner.info.reshardPlanCreated=Resharding plan created for symbol {0} from shard {1} to shard {2}
reshardPlanner.error.reshardPlanNotFound=Resharding plan not found for symbol {0}
reshardPlanner.info.approvingReshardPlan=Approving resharding plan for symbol {0} from shard {1} to shard {2}
reshardPlanner.info.rejectingReshardPlan=Rejecting resharding plan for symbol {0}

# Hot Spot Detector messages
hotSpotDetector.debug.detectingHotSpots=Detecting hot spots
hotSpotDetector.info.hotSpotsDetected=Detected {0} hot spots
hotSpotDetector.info.hotSpotDetails=Hot spot: symbol={0}, severity={1}
hotSpotDetector.debug.hotSpotRecentlyDetected=Hot spot {0} was recently detected, skipping
hotSpotDetector.info.lowSeverityHotSpot=Hot spot {0} has low severity, just logging

# Load Imbalance Detector messages
loadImbalanceDetector.debug.detectingImbalance=Detecting load imbalance
loadImbalanceDetector.info.imbalanceDetected=Detected {0} imbalance for shard {1}: {2}% (threshold: {3}%)
loadImbalanceDetector.info.processingImbalances=Processing {0} imbalances
loadImbalanceDetector.info.shardImbalanceDetails=Imbalance for shard {0}: {1}
loadImbalanceDetector.debug.imbalanceRecentlyDetected=Imbalance for shard {0} was recently detected, skipping

# Symbol Selector messages
symbolSelector.info.selectingSymbols=Selecting symbols to rebalance load for shard {0}
symbolSelector.info.notEnoughInstances=Not enough instances in shard {0} to rebalance load
symbolSelector.error.cannotDetermineInstances=Cannot determine highest and lowest load instances for shard {0}
symbolSelector.info.loadInstancesIdentified=Identified highest load instance {0} and lowest load instance {1}
symbolSelector.info.noSymbolsFound=No symbols found for instance {0}
symbolSelector.info.noSuitableSymbolsFound=No suitable symbols found to move
symbolSelector.info.symbolsSelected=Selected {0} symbols to move from instance {1} to instance {2}

# Migration Planner messages
migrationPlanner.info.planningMigration=Planning migration of {0} symbols from instance {1} to instance {2}
migrationPlanner.info.tooManyMigrations=There are already {0} migrations in progress, exceeding limit {1}
migrationPlanner.info.migrationPlanExists=Symbol {0} already has a migration plan
migrationPlanner.info.migrationPlanCreated=Migration plan created for symbol {0} from instance {1} to instance {2}
migrationPlanner.error.migrationPlanNotFound=Migration plan not found for symbol {0}
migrationPlanner.info.approvingMigrationPlan=Approving migration plan for symbol {0} from instance {1} to instance {2}
migrationPlanner.info.rejectingMigrationPlan=Rejecting migration plan for symbol {0}

# Migration Executor messages
migrationExecutor.info.executingMigration=Executing migration for symbol {0} from instance {1} to instance {2}
migrationExecutor.error.lockTimeout=Could not acquire lock for symbol: {0}
migrationExecutor.error.lockInterrupted=Interrupted while waiting for lock for symbol: {0}
migrationExecutor.error.migrationFailed=Migration failed for symbol: {0}
migrationExecutor.info.statusUpdated=Updated migration status for symbol {0} to {1}
migrationExecutor.error.statusUpdateFailed=Failed to update migration status for symbol {0} to {1}
migrationExecutor.info.prepareNotificationSent=Prepare notification sent for symbol: {0}
migrationExecutor.error.prepareNotificationFailed=Failed to send prepare notification for symbol: {0}
migrationExecutor.info.orderProcessingPaused=Order processing paused for symbol: {0}
migrationExecutor.error.pauseOrderProcessingFailed=Failed to pause order processing for symbol: {0}
migrationExecutor.info.snapshotCreated=Snapshot created for symbol: {0}
migrationExecutor.error.engineNotFound=Matching engine not found for symbol: {0}
migrationExecutor.error.snapshotCreationFailed=Failed to create snapshot for symbol: {0}
migrationExecutor.info.snapshotTransferred=Snapshot transferred for symbol {0} to instance {1}
migrationExecutor.error.snapshotTransferFailed=Failed to transfer snapshot for symbol {0} to instance {1}
migrationExecutor.info.mappingUpdated=Mapping updated for symbol {0} to instance {1}
migrationExecutor.error.mappingUpdateFailed=Failed to update mapping for symbol {0} to instance {1}
migrationExecutor.info.completeNotificationSent=Completion notification sent for symbol: {0}
migrationExecutor.error.completeNotificationFailed=Failed to send completion notification for symbol: {0}
migrationExecutor.info.rollingBack=Rolling back migration for symbol: {0}
migrationExecutor.info.rollbackCompleted=Rollback completed for symbol: {0}
migrationExecutor.error.rollbackFailed=Rollback failed for symbol: {0}
migrationExecutor.info.migrationCompleted=Migration completed for symbol {0} from instance {1} to instance {2}

# Settlement messages
settlement.error.scheduledFundingSettlement=Error in scheduled funding settlement for symbol: {0}
settlement.error.scheduledContractSettlement=Error in scheduled contract settlement for symbol: {0}
settlement.error.initializeFundingSettlementSchedules=Error initializing funding settlement schedules
settlement.error.initializeContractSettlementSchedules=Error initializing contract settlement schedules
settlement.error.calculateFundingRate=Error calculating funding rate for symbol: {0}
settlement.error.settleContract=Error settling contract for symbol: {0}
settlement.error.autoFundingSettlement=Error in auto funding settlement for symbol: {0}
settlement.error.autoContractSettlement=Error in auto contract settlement for symbol: {0}
settlement.error.autoFundingSettlementAll=Error in auto funding settlement for all symbols
settlement.error.autoContractSettlementAll=Error in auto contract settlement for all symbols

settlement.info.scheduledFundingSettlementStarted=Scheduled funding settlement started for symbol: {0}
settlement.info.scheduledFundingSettlementCompleted=Scheduled funding settlement completed for symbol: {0}
settlement.info.scheduledContractSettlementStarted=Scheduled contract settlement started for symbol: {0}
settlement.info.scheduledContractSettlementCompleted=Scheduled contract settlement completed for symbol: {0}
settlement.info.initializeFundingSettlementSchedules=Initializing funding settlement schedules
settlement.info.initializeFundingSettlementSchedulesCompleted=Funding settlement schedules initialized
settlement.info.initializeContractSettlementSchedules=Initializing contract settlement schedules
settlement.info.initializeContractSettlementSchedulesCompleted=Contract settlement schedules initialized
settlement.info.autoFundingSettlementStarted=Auto funding settlement started for symbol: {0}
settlement.info.autoFundingSettlementCompleted=Auto funding settlement completed for symbol: {0}
settlement.info.autoContractSettlementStarted=Auto contract settlement started for symbol: {0}
settlement.info.autoContractSettlementCompleted=Auto contract settlement completed for symbol: {0}
settlement.info.autoFundingSettlementAllCompleted=Auto funding settlement completed for all symbols
settlement.info.autoContractSettlementAllCompleted=Auto contract settlement completed for all symbols
settlement.info.scheduleFundingSettlement=Scheduling funding settlement for symbol: {0}, next time: {1}
settlement.info.scheduleContractSettlement=Scheduling contract settlement for symbol: {0}, next time: {1}
settlement.info.scheduleFundingSettlementCompleted=Funding settlement scheduling completed for symbol: {0}, next time: {1}
settlement.info.scheduleContractSettlementCompleted=Contract settlement scheduling completed for symbol: {0}, next time: {1}
settlement.info.scheduleFundingSettlementAll=Scheduling funding settlement for all symbols with schedule: {0}
settlement.info.scheduleFundingSettlementAllCompleted=Funding settlement scheduling completed for all symbols with schedule: {0}, success count: {1}
settlement.info.scheduleContractSettlementAll=Scheduling contract settlement for all symbols with schedule: {0}
settlement.info.scheduleContractSettlementAllCompleted=Contract settlement scheduling completed for all symbols with schedule: {0}, success count: {1}
settlement.info.cancelFundingSettlementSchedule=Canceling funding settlement schedule for symbol: {0}
settlement.info.cancelFundingSettlementScheduleCompleted=Funding settlement schedule canceled for symbol: {0}
settlement.info.cancelContractSettlementSchedule=Canceling contract settlement schedule for symbol: {0}
settlement.info.cancelContractSettlementScheduleCompleted=Contract settlement schedule canceled for symbol: {0}
settlement.info.cancelFundingSettlementScheduleAll=Canceling funding settlement schedule for all symbols
settlement.info.cancelFundingSettlementScheduleAllCompleted=Funding settlement schedule canceled for all symbols, success count: {0}
settlement.info.cancelContractSettlementScheduleAll=Canceling contract settlement schedule for all symbols
settlement.info.cancelContractSettlementScheduleAllCompleted=Contract settlement schedule canceled for all symbols, success count: {0}
settlement.info.fundingSettlementStarted=Funding settlement started for symbol: {0}, timestamp: {1}
settlement.info.fundingSettlementCompleted=Funding settlement completed for symbol: {0}, timestamp: {1}
settlement.info.contractSettlementStarted=Contract settlement started for symbol: {0}, timestamp: {1}
settlement.info.contractSettlementCompleted=Contract settlement completed for symbol: {0}, timestamp: {1}
settlement.info.autoFundingSettlementAllStarted=Auto funding settlement started for all symbols, timestamp: {0}
settlement.info.autoContractSettlementAllStarted=Auto contract settlement started for all symbols, timestamp: {0}

settlement.error.settleFunding=Error settling funding for symbol: {0}, timestamp: {1}
settlement.error.fundingSettlement=Error in funding settlement for symbol: {0}, timestamp: {1}
settlement.error.fundingSettlementRetry=Retried funding settlement 3 times but failed for symbol: {0}, timestamp: {1}
settlement.error.contractSettlementRetry=Retried contract settlement 3 times but failed for symbol: {0}, timestamp: {1}
settlement.error.scheduleFundingSettlement=Error scheduling funding settlement for symbol: {0}, schedule: {1}
settlement.error.scheduleContractSettlement=Error scheduling contract settlement for symbol: {0}, schedule: {1}
settlement.error.scheduleFundingSettlementAll=Error scheduling funding settlement for all symbols with schedule: {0}
settlement.error.scheduleContractSettlementAll=Error scheduling contract settlement for all symbols with schedule: {0}
settlement.error.cancelFundingSettlementSchedule=Error canceling funding settlement schedule for symbol: {0}
settlement.error.cancelContractSettlementSchedule=Error canceling contract settlement schedule for symbol: {0}
settlement.error.cancelFundingSettlementScheduleAll=Error canceling funding settlement schedule for all symbols
settlement.error.cancelContractSettlementScheduleAll=Error canceling contract settlement schedule for all symbols
settlement.error.getNextFundingSettlementTime=Error getting next funding settlement time for symbol: {0}

settlement.warn.fundingRateZero=Funding rate is zero for symbol: {0}
settlement.warn.settlementPriceZero=Settlement price is zero for symbol: {0}
settlement.warn.noPositions=No positions found for symbol: {0}

# Funding Service messages
fundingService.debug.calculateFundingRate=Calculating funding rate for symbol: {0}, indexPrice: {1}, markPrice: {2}
fundingService.debug.getCurrentFundingRate=Getting current funding rate for symbol: {0}
fundingService.debug.getNextFundingTime=Getting next funding time for symbol: {0}

fundingService.error.calculateFundingRate=Error calculating funding rate for symbol: {0}
fundingService.error.getCurrentFundingRate=Error getting current funding rate for symbol: {0}
fundingService.error.getNextFundingTime=Error getting next funding time for symbol: {0}

# Price Service messages
priceService.debug.getIndexPrice=Getting index price for symbol: {0}
priceService.debug.getMarkPrice=Getting mark price for symbol: {0}
priceService.debug.calculateIndexPrice=Calculating index price for symbol: {0}
priceService.debug.calculateMarkPrice=Calculating mark price for symbol: {0}

priceService.error.getIndexPrice=Error getting index price for symbol: {0}
priceService.error.getMarkPrice=Error getting mark price for symbol: {0}
priceService.error.calculateIndexPrice=Error calculating index price for symbol: {0}
priceService.error.calculateMarkPrice=Error calculating mark price for symbol: {0}
