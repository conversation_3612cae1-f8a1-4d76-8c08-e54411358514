# Vietnamese messages

# Common messages
common.success=ThÃ nh cÃ´ng
common.error=Lá»i
common.warning=Cáº£nh bÃ¡o
common.info=ThÃ´ng tin

# Order Matching messages
orderMatching.error.cancelOrder=Lá»i khi há»§y lá»nh, orderId = {0}, symbol = {1}
orderMatching.error.placeOrder=Lá»i khi Äáº·t lá»nh, orderId = {0}, symbol = {1}
orderMatching.error.cancelAllOrders=Lá»i khi há»§y táº¥t cáº£ lá»nh, memberId = {0}, symbol = {1}
orderMatching.error.liquidatePosition=Lá»i khi thanh lÃ½ vá» tháº¿, positionId = {0}, symbol = {1}
orderMatching.error.matchOrderFIFO=Lá»i khi khá»p lá»nh FIFO, orderId = {0}, symbol = {1}
orderMatching.error.matchOrderProRata=Lá»i khi khá»p lá»nh Pro-rata, orderId = {0}, symbol = {1}
orderMatching.error.matchOrderHybrid=Lá»i khi khá»p lá»nh Hybrid, orderId = {0}, symbol = {1}
orderMatching.error.shutdown=Lá»i khi giáº£i phÃ³ng tÃ i nguyÃªn cá»§a OrderMatchingEngineService
orderMatching.error.syncContracts=Äá»ng bá» hÃ³a matching engine tháº¥t báº¡i
orderMatching.error.initEngine=Lá»i khi khá»i táº¡o matching engine, symbol = {0}
orderMatching.error.checkTriggerOrders=Lá»i khi kiá»m tra lá»nh chá», symbol = {0}
orderMatching.error.checkLiquidations=Lá»i khi kiá»m tra thanh lÃ½, symbol = {0}
orderMatching.error.lock.timeout=KhÃ´ng thá» láº¥y lock cho symbol: {0}
orderMatching.error.lock.interrupted=Bá» giÃ¡n Äoáº¡n khi chá» lock cho symbol: {0}
orderMatching.error.contract.notFound=KhÃ´ng tÃ¬m tháº¥y há»£p Äá»ng cho symbol: {0}
orderMatching.error.setAlgorithm=Lá»i khi thiáº¿t láº­p thuáº­t toÃ¡n khá»p lá»nh cho symbol: {0}, thuáº­t toÃ¡n: {1}
orderMatching.error.getOrderBook=Lá»i khi láº¥y sá» lá»nh cho symbol: {0}
orderMatching.info.algorithmSet=ÄÃ£ thiáº¿t láº­p thuáº­t toÃ¡n khá»p lá»nh cho symbol: {0}, thuáº­t toÃ¡n: {1}

orderMatching.warn.engineNotFound=KhÃ´ng tÃ¬m tháº¥y matching engine cho symbol {0}
orderMatching.warn.invalidSymbol=Symbol khÃ´ng há»£p lá»
orderMatching.warn.invalidOrder=Lá»nh khÃ´ng há»£p lá»
orderMatching.warn.tradingHalt=Giao dá»ch Äang táº¡m dá»«ng cho symbol {0}
orderMatching.warn.enabledContractsNotFound=KhÃ´ng thá» láº¥y danh sÃ¡ch há»£p Äá»ng ÄÃ£ kÃ­ch hoáº¡t
orderMatching.warn.symbol.notOwned=Symbol {0} khÃ´ng ÄÆ°á»£c gÃ¡n cho pod nÃ y

orderMatching.info.engineInitialized=ÄÃ£ khá»i táº¡o matching engine cho symbol {0}
orderMatching.info.engineShutdown=ÄÃ£ dá»«ng vÃ  giáº£i phÃ³ng tÃ i nguyÃªn cho matching engine, symbol = {0}
orderMatching.info.tradingPaused=ÄÃ£ táº¡m dá»«ng giao dá»ch cho symbol {0}
orderMatching.info.tradingResumed=ÄÃ£ tiáº¿p tá»¥c giao dá»ch cho symbol {0}
orderMatching.info.syncCompleted=Äá»ng bá» hÃ³a hoÃ n táº¥t. Sá» lÆ°á»£ng matching engine: {0}
orderMatching.info.syncStarted=Äá»ng bá» hÃ³a matching engine vá»i há»£p Äá»ng
orderMatching.info.shutdownStarted=Äang giáº£i phÃ³ng tÃ i nguyÃªn cá»§a OrderMatchingEngineService
orderMatching.info.shutdownCompleted=ÄÃ£ giáº£i phÃ³ng tÃ i nguyÃªn cá»§a OrderMatchingEngineService
orderMatching.info.positionLiquidated=ÄÃ£ thanh lÃ½ vá» tháº¿ {0}, symbol = {1}
orderMatching.info.orderCanceled=ÄÃ£ há»§y lá»nh {0}, symbol = {1}
orderMatching.info.distributedEngineInitialized=ÄÃ£ khá»i táº¡o distributed locking matching engine
orderMatching.info.optimizedEngineInitialized=ÄÃ£ khá»i táº¡o optimized matching engine

orderMatching.debug.markPriceUpdated=ÄÃ£ cáº­p nháº­t giÃ¡ ÄÃ¡nh dáº¥u cho symbol {0}, markPrice = {1}
orderMatching.debug.indexPriceUpdated=ÄÃ£ cáº­p nháº­t giÃ¡ chá» sá» cho symbol {0}, indexPrice = {1}
orderMatching.debug.triggerOrdersChecked=ÄÃ£ kiá»m tra lá»nh chá» cho symbol {0}
orderMatching.debug.liquidationsChecked=ÄÃ£ kiá»m tra thanh lÃ½ cho symbol {0}

# Contract Service messages
contractService.error.createContract=Lá»i khi táº¡o há»£p Äá»ng, symbol = {0}
contractService.error.updateContract=Lá»i khi cáº­p nháº­t há»£p Äá»ng, symbol = {0}
contractService.error.deleteContract=Lá»i khi xÃ³a há»£p Äá»ng, symbol = {0}

contractService.warn.contractNotFound=KhÃ´ng tÃ¬m tháº¥y há»£p Äá»ng vá»i symbol {0}

contractService.info.contractCreated=ÄÃ£ táº¡o há»£p Äá»ng, symbol = {0}
contractService.info.contractUpdated=ÄÃ£ cáº­p nháº­t há»£p Äá»ng, symbol = {0}
contractService.info.contractDeleted=ÄÃ£ xÃ³a há»£p Äá»ng, symbol = {0}

# Position Service messages
positionService.error.createPosition=Lá»i khi táº¡o vá» tháº¿, memberId = {0}, symbol = {1}
positionService.error.updatePosition=Lá»i khi cáº­p nháº­t vá» tháº¿, positionId = {0}
positionService.error.closePosition=Lá»i khi ÄÃ³ng vá» tháº¿, positionId = {0}

positionService.warn.positionNotFound=KhÃ´ng tÃ¬m tháº¥y vá» tháº¿ vá»i id {0}

positionService.info.positionCreated=ÄÃ£ táº¡o vá» tháº¿, memberId = {0}, symbol = {1}
positionService.info.positionUpdated=ÄÃ£ cáº­p nháº­t vá» tháº¿, positionId = {0}
positionService.info.positionClosed=ÄÃ£ ÄÃ³ng vá» tháº¿, positionId = {0}

# Messaging Service messages
messagingService.error.processFundingRate=Lá»i khi xá»­ lÃ½ funding rate, symbol = {0}, value = {1}
messagingService.error.processNewOrder=Lá»i khi xá»­ lÃ½ lá»nh má»i
messagingService.error.processCancelOrder=Lá»i khi xá»­ lÃ½ lá»nh há»§y
messagingService.error.processFundingRateUpdate=Lá»i khi xá»­ lÃ½ cáº­p nháº­t tá»· lá» tÃ i trá»£
messagingService.error.processTrade=Lá»i khi xá»­ lÃ½ giao dá»ch, symbol = {0}, value = {1}

messagingService.info.receivedFundingRate=ÄÃ£ nháº­n funding rate, symbol = {0}, value = {1}
messagingService.info.receivedNewOrder=ÄÃ£ nháº­n lá»nh má»i, topic = {0}, key = {1}
messagingService.info.receivedCancelOrder=ÄÃ£ nháº­n lá»nh há»§y, topic = {0}, key = {1}
messagingService.info.receivedFundingRateUpdate=ÄÃ£ nháº­n cáº­p nháº­t tá»· lá» tÃ i trá»£, topic = {0}, key = {1}
messagingService.info.receivedTrade=ÄÃ£ nháº­n giao dá»ch, symbol = {0}, value = {1}
messagingService.info.receivedLiquidation=ÄÃ£ nháº­n thanh lÃ½, symbol = {0}, value = {1}
messagingService.info.markPriceFromHeader=Láº¥y giÃ¡ ÄÃ¡nh dáº¥u tá»« header thÃ nh cÃ´ng, markPrice = {0}
messagingService.warn.markPriceFromService=KhÃ´ng thá» láº¥y giÃ¡ ÄÃ¡nh dáº¥u tá»« header, sá»­ dá»¥ng giÃ¡ ÄÃ¡nh dáº¥u tá»« service, error = {0}
messagingService.warn.useLiquidationPrice=KhÃ´ng tÃ¬m tháº¥y giÃ¡ ÄÃ¡nh dáº¥u, sá»­ dá»¥ng giÃ¡ thanh lÃ½, symbol = {0}
messagingService.info.processLiquidation=Xá»­ lÃ½ thanh lÃ½ vá»i giÃ¡ ÄÃ¡nh dáº¥u = {0}, symbol = {1}, positionId = {2}
messagingService.error.processLiquidation=Xá»­ lÃ½ thanh lÃ½ tháº¥t báº¡i, symbol = {0}, value = {1}

# Special Order Service messages
specialOrderService.error.processTimeOrders=Lá»i khi xá»­ lÃ½ lá»nh theo thá»i gian
specialOrderService.error.processExpiredOrders=Lá»i khi xá»­ lÃ½ lá»nh háº¿t háº¡n
specialOrderService.error.processTriggerOrders=Lá»i khi xá»­ lÃ½ lá»nh chá»
specialOrderService.error.checkTriggerOrders=Lá»i khi kiá»m tra lá»nh chá», symbol = {0}
specialOrderService.error.processTimeOrder=Lá»i khi xá»­ lÃ½ lá»nh theo thá»i gian

specialOrderService.info.processTimeOrders=Äang xá»­ lÃ½ lá»nh theo thá»i gian
specialOrderService.info.processExpiredOrders=Äang xá»­ lÃ½ lá»nh háº¿t háº¡n
specialOrderService.info.processTriggerOrders=Äang kiá»m tra lá»nh chá»

specialOrderService.debug.processTimeOrder=Äang xá»­ lÃ½ lá»nh theo thá»i gian, orderId = {0}

# Report Service messages
reportService.error.createReportsAsync=Lá»i khi táº¡o bÃ¡o cÃ¡o báº¥t Äá»ng bá» cho táº¥t cáº£ lá»nh ÄÃ£ hoÃ n thÃ nh
reportService.error.processStatisticsAsync=Lá»i khi xá»­ lÃ½ dá»¯ liá»u thá»ng kÃª báº¥t Äá»ng bá»

reportService.info.createReportsAsync=Táº¡o bÃ¡o cÃ¡o giao dá»ch báº¥t Äá»ng bá» cho táº¥t cáº£ lá»nh ÄÃ£ hoÃ n thÃ nh
reportService.info.completedOrdersCount=Sá» lÆ°á»£ng lá»nh ÄÃ£ hoÃ n thÃ nh chÆ°a cÃ³ bÃ¡o cÃ¡o: {0}
reportService.info.createReportsAsyncCompleted=ÄÃ£ táº¡o bÃ¡o cÃ¡o báº¥t Äá»ng bá» cho táº¥t cáº£ lá»nh ÄÃ£ hoÃ n thÃ nh
reportService.info.processStatisticsAsync=Äang xá»­ lÃ½ dá»¯ liá»u thá»ng kÃª báº¥t Äá»ng bá», startDate = {0}, endDate = {1}
reportService.info.processStatisticsAsyncCompleted=ÄÃ£ xá»­ lÃ½ dá»¯ liá»u thá»ng kÃª báº¥t Äá»ng bá», startDate = {0}, endDate = {1}

# Sharding Manager messages
shardingManager.error.rebalancingInterrupted=Bá» giÃ¡n Äoáº¡n khi cÃ¢n báº±ng láº¡i cÃ¡c symbol cho pod: {0}

shardingManager.warn.rebalancingLockFailed=KhÃ´ng thá» láº¥y khÃ³a Äá» cÃ¢n báº±ng láº¡i cÃ¡c symbol cho pod: {0}
shardingManager.warn.no.active.pods=KhÃ´ng tÃ¬m tháº¥y pod nÃ o Äang hoáº¡t Äá»ng Äá» cÃ¢n báº±ng láº¡i

shardingManager.info.initialized=ÄÃ£ khá»i táº¡o sharding manager cho pod: {0}
shardingManager.info.symbolAssigned=Symbol {0} ÄÃ£ ÄÆ°á»£c gÃ¡n cho pod {1}, chá»§ sá» há»¯u trÆ°á»c ÄÃ³: {2}
shardingManager.info.symbolUnassigned=Symbol {0} ÄÃ£ ÄÆ°á»£c há»§y gÃ¡n khá»i pod {1}
shardingManager.info.symbolRemoved=Symbol {0} ÄÃ£ bá» xÃ³a khá»i mapping pod
shardingManager.info.rebalancingStarted=Báº¯t Äáº§u cÃ¢n báº±ng láº¡i cÃ¡c symbol cho pod: {0}
shardingManager.info.rebalancingCompleted=ÄÃ£ hoÃ n thÃ nh cÃ¢n báº±ng láº¡i cÃ¡c symbol cho pod: {0}
shardingManager.info.symbolMoved=Symbol {0} ÄÃ£ ÄÆ°á»£c di chuyá»n tá»« pod {1} sang pod {2}
shardingManager.info.rebalanceStarted=Báº¯t Äáº§u cÃ¢n báº±ng láº¡i cÃ¡c symbol cho pod: {0}
shardingManager.info.rebalanceCompleted=ÄÃ£ hoÃ n thÃ nh cÃ¢n báº±ng láº¡i cÃ¡c symbol cho pod: {0}

shardingManager.debug.heartbeatSent=ÄÃ£ gá»­i heartbeat cho pod: {0}

# Reshard Executor messages
reshardExecutor.error.lockTimeout=KhÃ´ng thá» láº¥y khÃ³a cho symbol: {0}
reshardExecutor.error.lockInterrupted=Bá» giÃ¡n Äoáº¡n khi chá» khÃ³a cho symbol: {0}
reshardExecutor.error.reshardFailed=Re-sharding tháº¥t báº¡i cho symbol: {0}
reshardExecutor.error.statusUpdateFailed=Cáº­p nháº­t tráº¡ng thÃ¡i re-sharding tháº¥t báº¡i cho symbol {0} thÃ nh {1}
reshardExecutor.error.prepareNotificationFailed=Gá»­i thÃ´ng bÃ¡o chuáº©n bá» re-sharding tháº¥t báº¡i cho symbol: {0}
reshardExecutor.error.pauseOrderProcessingFailed=Táº¡m dá»«ng xá»­ lÃ½ lá»nh tháº¥t báº¡i cho symbol: {0}
reshardExecutor.error.engineNotFound=KhÃ´ng tÃ¬m tháº¥y matching engine cho symbol: {0}
reshardExecutor.error.snapshotCreationFailed=Táº¡o snapshot tháº¥t báº¡i cho symbol: {0}
reshardExecutor.error.snapshotTransferFailed=Chuyá»n snapshot tháº¥t báº¡i cho symbol {0} Äáº¿n shard {1}
reshardExecutor.error.mappingUpdateFailed=Cáº­p nháº­t mapping tháº¥t báº¡i cho symbol {0} sang shard {1}
reshardExecutor.error.completeNotificationFailed=Gá»­i thÃ´ng bÃ¡o hoÃ n thÃ nh re-sharding tháº¥t báº¡i cho symbol: {0}
reshardExecutor.error.rollbackFailed=Rollback re-sharding tháº¥t báº¡i cho symbol: {0}

reshardExecutor.info.executingReshard=Äang thá»±c hiá»n re-sharding cho symbol {0} tá»« shard {1} sang shard {2}
reshardExecutor.info.statusUpdated=ÄÃ£ cáº­p nháº­t tráº¡ng thÃ¡i re-sharding cho symbol {0} thÃ nh {1}
reshardExecutor.info.prepareNotificationSent=ÄÃ£ gá»­i thÃ´ng bÃ¡o chuáº©n bá» re-sharding cho symbol: {0}
reshardExecutor.info.orderProcessingPaused=ÄÃ£ táº¡m dá»«ng xá»­ lÃ½ lá»nh cho symbol: {0}
reshardExecutor.info.snapshotCreated=ÄÃ£ táº¡o snapshot cho symbol: {0}
reshardExecutor.info.snapshotTransferred=ÄÃ£ chuyá»n snapshot cho symbol {0} Äáº¿n shard {1}
reshardExecutor.info.mappingUpdated=ÄÃ£ cáº­p nháº­t mapping cho symbol {0} sang shard {1}
reshardExecutor.info.completeNotificationSent=ÄÃ£ gá»­i thÃ´ng bÃ¡o hoÃ n thÃ nh re-sharding cho symbol: {0}
reshardExecutor.info.rollingBack=Äang rollback re-sharding cho symbol: {0}
reshardExecutor.info.rollbackCompleted=ÄÃ£ hoÃ n thÃ nh rollback re-sharding cho symbol: {0}
reshardExecutor.info.reshardCompleted=ÄÃ£ hoÃ n thÃ nh re-sharding cho symbol {0} tá»« shard {1} sang shard {2}

# Reshard Planner messages
reshardPlanner.info.planningReshard=Äang lÃªn káº¿ hoáº¡ch re-sharding cho symbol {0} vá»i lÃ½ do {1}
reshardPlanner.info.reshardPlanExists=Symbol {0} ÄÃ£ cÃ³ káº¿ hoáº¡ch re-sharding
reshardPlanner.error.currentShardNotFound=KhÃ´ng tÃ¬m tháº¥y shard hiá»n táº¡i cho symbol {0}
reshardPlanner.error.targetShardNotFound=KhÃ´ng tÃ¬m tháº¥y shard ÄÃ­ch cho symbol {0}
reshardPlanner.info.sameShard=Symbol {0} ÄÃ£ á» shard {1}, khÃ´ng cáº§n re-sharding
reshardPlanner.info.reshardPlanCreated=ÄÃ£ táº¡o káº¿ hoáº¡ch re-sharding cho symbol {0} tá»« shard {1} sang shard {2}
reshardPlanner.error.reshardPlanNotFound=KhÃ´ng tÃ¬m tháº¥y káº¿ hoáº¡ch re-sharding cho symbol {0}
reshardPlanner.info.approvingReshardPlan=Äang phÃª duyá»t káº¿ hoáº¡ch re-sharding cho symbol {0} tá»« shard {1} sang shard {2}
reshardPlanner.info.rejectingReshardPlan=Äang tá»« chá»i káº¿ hoáº¡ch re-sharding cho symbol {0}

# Hot Spot Detector messages
hotSpotDetector.debug.detectingHotSpots=Äang phÃ¡t hiá»n cÃ¡c hot spot
hotSpotDetector.info.hotSpotsDetected=ÄÃ£ phÃ¡t hiá»n {0} hot spot
hotSpotDetector.info.hotSpotDetails=Hot spot: symbol={0}, severity={1}
hotSpotDetector.debug.hotSpotRecentlyDetected=Hot spot {0} ÄÃ£ ÄÆ°á»£c phÃ¡t hiá»n gáº§n ÄÃ¢y, bá» qua
hotSpotDetector.info.lowSeverityHotSpot=Hot spot {0} cÃ³ má»©c Äá» nghiÃªm trá»ng tháº¥p, chá» ghi nháº­n

# Load Imbalance Detector messages
loadImbalanceDetector.debug.detectingImbalance=Äang phÃ¡t hiá»n máº¥t cÃ¢n báº±ng táº£i
loadImbalanceDetector.info.imbalanceDetected=ÄÃ£ phÃ¡t hiá»n máº¥t cÃ¢n báº±ng {0} cho shard {1}: {2}% (ngÆ°á»¡ng: {3}%)
loadImbalanceDetector.info.processingImbalances=Äang xá»­ lÃ½ {0} máº¥t cÃ¢n báº±ng
loadImbalanceDetector.info.shardImbalanceDetails=Máº¥t cÃ¢n báº±ng cho shard {0}: {1}
loadImbalanceDetector.debug.imbalanceRecentlyDetected=Máº¥t cÃ¢n báº±ng cho shard {0} ÄÃ£ ÄÆ°á»£c phÃ¡t hiá»n gáº§n ÄÃ¢y, bá» qua

# Symbol Selector messages
symbolSelector.info.selectingSymbols=Äang lá»±a chá»n cÃ¡c symbol Äá» cÃ¢n báº±ng láº¡i táº£i cho shard {0}
symbolSelector.info.notEnoughInstances=KhÃ´ng Äá»§ instance trong shard {0} Äá» cÃ¢n báº±ng láº¡i táº£i
symbolSelector.error.cannotDetermineInstances=KhÃ´ng thá» xÃ¡c Äá»nh instance cÃ³ táº£i cao nháº¥t vÃ  tháº¥p nháº¥t cho shard {0}
symbolSelector.info.loadInstancesIdentified=ÄÃ£ xÃ¡c Äá»nh instance cÃ³ táº£i cao nháº¥t {0} vÃ  tháº¥p nháº¥t {1}
symbolSelector.info.noSymbolsFound=KhÃ´ng tÃ¬m tháº¥y symbol nÃ o cho instance {0}
symbolSelector.info.noSuitableSymbolsFound=KhÃ´ng tÃ¬m tháº¥y symbol nÃ o phÃ¹ há»£p Äá» di chuyá»n
symbolSelector.info.symbolsSelected=ÄÃ£ chá»n {0} symbol Äá» di chuyá»n tá»« instance {1} sang instance {2}

# Migration Planner messages
migrationPlanner.info.planningMigration=Äang lÃªn káº¿ hoáº¡ch di chuyá»n {0} symbol tá»« instance {1} sang instance {2}
migrationPlanner.info.tooManyMigrations=ÄÃ£ cÃ³ {0} di chuyá»n Äang diá»n ra, vÆ°á»£t quÃ¡ giá»i háº¡n {1}
migrationPlanner.info.migrationPlanExists=Symbol {0} ÄÃ£ cÃ³ káº¿ hoáº¡ch di chuyá»n
migrationPlanner.info.migrationPlanCreated=ÄÃ£ táº¡o káº¿ hoáº¡ch di chuyá»n cho symbol {0} tá»« instance {1} sang instance {2}
migrationPlanner.error.migrationPlanNotFound=KhÃ´ng tÃ¬m tháº¥y káº¿ hoáº¡ch di chuyá»n cho symbol {0}
migrationPlanner.info.approvingMigrationPlan=Äang phÃª duyá»t káº¿ hoáº¡ch di chuyá»n cho symbol {0} tá»« instance {1} sang instance {2}
migrationPlanner.info.rejectingMigrationPlan=Äang tá»« chá»i káº¿ hoáº¡ch di chuyá»n cho symbol {0}

# Migration Executor messages
migrationExecutor.info.executingMigration=Äang thá»±c hiá»n di chuyá»n cho symbol {0} tá»« instance {1} sang instance {2}
migrationExecutor.error.lockTimeout=Timeout khi láº¥y lock cho symbol {0}
migrationExecutor.error.lockInterrupted=Bá» giÃ¡n Äoáº¡n khi láº¥y lock cho symbol {0}
migrationExecutor.error.migrationFailed=Di chuyá»n tháº¥t báº¡i cho symbol {0}
migrationExecutor.info.statusUpdated=ÄÃ£ cáº­p nháº­t tráº¡ng thÃ¡i di chuyá»n cho symbol {0} thÃ nh {1}
migrationExecutor.error.statusUpdateFailed=Cáº­p nháº­t tráº¡ng thÃ¡i di chuyá»n tháº¥t báº¡i cho symbol {0} thÃ nh {1}
migrationExecutor.info.prepareNotificationSent=ÄÃ£ gá»­i thÃ´ng bÃ¡o chuáº©n bá» di chuyá»n cho symbol {0}
migrationExecutor.error.prepareNotificationFailed=Gá»­i thÃ´ng bÃ¡o chuáº©n bá» di chuyá»n tháº¥t báº¡i cho symbol {0}
migrationExecutor.info.orderProcessingPaused=ÄÃ£ táº¡m dá»«ng xá»­ lÃ½ lá»nh cho symbol {0}
migrationExecutor.error.pauseOrderProcessingFailed=Táº¡m dá»«ng xá»­ lÃ½ lá»nh tháº¥t báº¡i cho symbol {0}
migrationExecutor.info.snapshotCreated=ÄÃ£ táº¡o snapshot cho symbol {0}
migrationExecutor.error.engineNotFound=KhÃ´ng tÃ¬m tháº¥y matching engine cho symbol {0}
migrationExecutor.error.snapshotCreationFailed=Táº¡o snapshot tháº¥t báº¡i cho symbol {0}
migrationExecutor.info.snapshotTransferred=ÄÃ£ chuyá»n snapshot cho symbol {0} Äáº¿n instance {1}
migrationExecutor.error.snapshotTransferFailed=Chuyá»n snapshot tháº¥t báº¡i cho symbol {0} Äáº¿n instance {1}
migrationExecutor.info.mappingUpdated=ÄÃ£ cáº­p nháº­t mapping cho symbol {0} sang instance {1}
migrationExecutor.error.mappingUpdateFailed=Cáº­p nháº­t mapping tháº¥t báº¡i cho symbol {0} sang instance {1}
migrationExecutor.info.completeNotificationSent=ÄÃ£ gá»­i thÃ´ng bÃ¡o hoÃ n thÃ nh di chuyá»n cho symbol {0}
migrationExecutor.error.completeNotificationFailed=Gá»­i thÃ´ng bÃ¡o hoÃ n thÃ nh di chuyá»n tháº¥t báº¡i cho symbol {0}
migrationExecutor.info.rollingBack=Äang rollback di chuyá»n cho symbol {0}
migrationExecutor.info.rollbackCompleted=ÄÃ£ hoÃ n thÃ nh rollback di chuyá»n cho symbol {0}
migrationExecutor.error.rollbackFailed=Rollback di chuyá»n tháº¥t báº¡i cho symbol {0}
migrationExecutor.info.migrationCompleted=ÄÃ£ hoÃ n thÃ nh di chuyá»n cho symbol {0} tá»« instance {1} sang instance {2}

# Settlement messages
settlement.error.scheduledFundingSettlement=Lá»i trong thanh toÃ¡n tÃ i trá»£ theo lá»ch cho symbol: {0}
settlement.error.scheduledContractSettlement=Lá»i trong thanh toÃ¡n há»£p Äá»ng theo lá»ch cho symbol: {0}
settlement.error.initializeFundingSettlementSchedules=Lá»i khi khá»i táº¡o lá»ch thanh toÃ¡n tÃ i trá»£
settlement.error.initializeContractSettlementSchedules=Lá»i khi khá»i táº¡o lá»ch thanh toÃ¡n há»£p Äá»ng
settlement.error.calculateFundingRate=Lá»i khi tÃ­nh toÃ¡n tá»· lá» tÃ i trá»£ cho symbol: {0}
settlement.error.settleContract=Lá»i khi thanh toÃ¡n há»£p Äá»ng cho symbol: {0}
settlement.error.autoFundingSettlement=Lá»i trong thanh toÃ¡n tÃ i trá»£ tá»± Äá»ng cho symbol: {0}
settlement.error.autoContractSettlement=Lá»i trong thanh toÃ¡n há»£p Äá»ng tá»± Äá»ng cho symbol: {0}
settlement.error.autoFundingSettlementAll=Lá»i trong thanh toÃ¡n tÃ i trá»£ tá»± Äá»ng cho táº¥t cáº£ symbol
settlement.error.autoContractSettlementAll=Lá»i trong thanh toÃ¡n há»£p Äá»ng tá»± Äá»ng cho táº¥t cáº£ symbol

settlement.info.scheduledFundingSettlementStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n tÃ i trá»£ theo lá»ch cho symbol: {0}
settlement.info.scheduledFundingSettlementCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n tÃ i trá»£ theo lá»ch cho symbol: {0}
settlement.info.scheduledContractSettlementStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n há»£p Äá»ng theo lá»ch cho symbol: {0}
settlement.info.scheduledContractSettlementCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n há»£p Äá»ng theo lá»ch cho symbol: {0}
settlement.info.initializeFundingSettlementSchedules=Äang khá»i táº¡o lá»ch thanh toÃ¡n tÃ i trá»£
settlement.info.initializeFundingSettlementSchedulesCompleted=ÄÃ£ khá»i táº¡o lá»ch thanh toÃ¡n tÃ i trá»£
settlement.info.initializeContractSettlementSchedules=Äang khá»i táº¡o lá»ch thanh toÃ¡n há»£p Äá»ng
settlement.info.initializeContractSettlementSchedulesCompleted=ÄÃ£ khá»i táº¡o lá»ch thanh toÃ¡n há»£p Äá»ng
settlement.info.autoFundingSettlementStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n tÃ i trá»£ tá»± Äá»ng cho symbol: {0}
settlement.info.autoFundingSettlementCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n tÃ i trá»£ tá»± Äá»ng cho symbol: {0}
settlement.info.autoContractSettlementStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n há»£p Äá»ng tá»± Äá»ng cho symbol: {0}
settlement.info.autoContractSettlementCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n há»£p Äá»ng tá»± Äá»ng cho symbol: {0}
settlement.info.autoFundingSettlementAllCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n tÃ i trá»£ tá»± Äá»ng cho táº¥t cáº£ symbol
settlement.info.autoContractSettlementAllCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n há»£p Äá»ng tá»± Äá»ng cho táº¥t cáº£ symbol
settlement.info.scheduleFundingSettlement=Äang lÃªn lá»ch thanh toÃ¡n tÃ i trá»£ cho symbol: {0}, thá»i gian tiáº¿p theo: {1}
settlement.info.scheduleContractSettlement=Äang lÃªn lá»ch thanh toÃ¡n há»£p Äá»ng cho symbol: {0}, thá»i gian tiáº¿p theo: {1}
settlement.info.scheduleFundingSettlementCompleted=ÄÃ£ hoÃ n thÃ nh lÃªn lá»ch thanh toÃ¡n tÃ i trá»£ cho symbol: {0}, thá»i gian tiáº¿p theo: {1}
settlement.info.scheduleContractSettlementCompleted=ÄÃ£ hoÃ n thÃ nh lÃªn lá»ch thanh toÃ¡n há»£p Äá»ng cho symbol: {0}, thá»i gian tiáº¿p theo: {1}
settlement.info.scheduleFundingSettlementAll=Äang lÃªn lá»ch thanh toÃ¡n tÃ i trá»£ cho táº¥t cáº£ symbol vá»i lá»ch: {0}
settlement.info.scheduleFundingSettlementAllCompleted=ÄÃ£ hoÃ n thÃ nh lÃªn lá»ch thanh toÃ¡n tÃ i trá»£ cho táº¥t cáº£ symbol vá»i lá»ch: {0}, sá» lÆ°á»£ng thÃ nh cÃ´ng: {1}
settlement.info.scheduleContractSettlementAll=Äang lÃªn lá»ch thanh toÃ¡n há»£p Äá»ng cho táº¥t cáº£ symbol vá»i lá»ch: {0}
settlement.info.scheduleContractSettlementAllCompleted=ÄÃ£ hoÃ n thÃ nh lÃªn lá»ch thanh toÃ¡n há»£p Äá»ng cho táº¥t cáº£ symbol vá»i lá»ch: {0}, sá» lÆ°á»£ng thÃ nh cÃ´ng: {1}
settlement.info.cancelFundingSettlementSchedule=Äang há»§y lá»ch thanh toÃ¡n tÃ i trá»£ cho symbol: {0}
settlement.info.cancelFundingSettlementScheduleCompleted=ÄÃ£ há»§y lá»ch thanh toÃ¡n tÃ i trá»£ cho symbol: {0}
settlement.info.cancelContractSettlementSchedule=Äang há»§y lá»ch thanh toÃ¡n há»£p Äá»ng cho symbol: {0}
settlement.info.cancelContractSettlementScheduleCompleted=ÄÃ£ há»§y lá»ch thanh toÃ¡n há»£p Äá»ng cho symbol: {0}
settlement.info.cancelFundingSettlementScheduleAll=Äang há»§y lá»ch thanh toÃ¡n tÃ i trá»£ cho táº¥t cáº£ symbol
settlement.info.cancelFundingSettlementScheduleAllCompleted=ÄÃ£ há»§y lá»ch thanh toÃ¡n tÃ i trá»£ cho táº¥t cáº£ symbol, sá» lÆ°á»£ng thÃ nh cÃ´ng: {0}
settlement.info.cancelContractSettlementScheduleAll=Äang há»§y lá»ch thanh toÃ¡n há»£p Äá»ng cho táº¥t cáº£ symbol
settlement.info.cancelContractSettlementScheduleAllCompleted=ÄÃ£ há»§y lá»ch thanh toÃ¡n há»£p Äá»ng cho táº¥t cáº£ symbol, sá» lÆ°á»£ng thÃ nh cÃ´ng: {0}
settlement.info.fundingSettlementStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n tÃ i trá»£ cho symbol: {0}, thá»i Äiá»m: {1}
settlement.info.fundingSettlementCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n tÃ i trá»£ cho symbol: {0}, thá»i Äiá»m: {1}
settlement.info.contractSettlementStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n há»£p Äá»ng cho symbol: {0}, thá»i Äiá»m: {1}
settlement.info.contractSettlementCompleted=ÄÃ£ hoÃ n thÃ nh thanh toÃ¡n há»£p Äá»ng cho symbol: {0}, thá»i Äiá»m: {1}
settlement.info.autoFundingSettlementAllStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n tÃ i trá»£ tá»± Äá»ng cho táº¥t cáº£ symbol, thá»i Äiá»m: {0}
settlement.info.autoContractSettlementAllStarted=ÄÃ£ báº¯t Äáº§u thanh toÃ¡n há»£p Äá»ng tá»± Äá»ng cho táº¥t cáº£ symbol, thá»i Äiá»m: {0}

settlement.error.settleFunding=Lá»i khi thanh toÃ¡n tÃ i trá»£ cho symbol: {0}, thá»i Äiá»m: {1}
settlement.error.fundingSettlement=Lá»i trong thanh toÃ¡n tÃ i trá»£ cho symbol: {0}, thá»i Äiá»m: {1}
settlement.error.fundingSettlementRetry=ÄÃ£ thá»­ láº¡i thanh toÃ¡n tÃ i trá»£ 3 láº§n nhÆ°ng tháº¥t báº¡i cho symbol: {0}, thá»i Äiá»m: {1}
settlement.error.contractSettlementRetry=ÄÃ£ thá»­ láº¡i thanh toÃ¡n há»£p Äá»ng 3 láº§n nhÆ°ng tháº¥t báº¡i cho symbol: {0}, thá»i Äiá»m: {1}
settlement.error.scheduleFundingSettlement=Lá»i khi lÃªn lá»ch thanh toÃ¡n tÃ i trá»£ cho symbol: {0}, lá»ch: {1}
settlement.error.scheduleContractSettlement=Lá»i khi lÃªn lá»ch thanh toÃ¡n há»£p Äá»ng cho symbol: {0}, lá»ch: {1}
settlement.error.scheduleFundingSettlementAll=Lá»i khi lÃªn lá»ch thanh toÃ¡n tÃ i trá»£ cho táº¥t cáº£ symbol vá»i lá»ch: {0}
settlement.error.scheduleContractSettlementAll=Lá»i khi lÃªn lá»ch thanh toÃ¡n há»£p Äá»ng cho táº¥t cáº£ symbol vá»i lá»ch: {0}
settlement.error.cancelFundingSettlementSchedule=Lá»i khi há»§y lá»ch thanh toÃ¡n tÃ i trá»£ cho symbol: {0}
settlement.error.cancelContractSettlementSchedule=Lá»i khi há»§y lá»ch thanh toÃ¡n há»£p Äá»ng cho symbol: {0}
settlement.error.cancelFundingSettlementScheduleAll=Lá»i khi há»§y lá»ch thanh toÃ¡n tÃ i trá»£ cho táº¥t cáº£ symbol
settlement.error.cancelContractSettlementScheduleAll=Lá»i khi há»§y lá»ch thanh toÃ¡n há»£p Äá»ng cho táº¥t cáº£ symbol
settlement.error.getNextFundingSettlementTime=Lá»i khi láº¥y thá»i Äiá»m thanh toÃ¡n tÃ i trá»£ tiáº¿p theo cho symbol: {0}

settlement.warn.fundingRateZero=Tá»· lá» tÃ i trá»£ báº±ng 0 cho symbol: {0}
settlement.warn.settlementPriceZero=GiÃ¡ thanh toÃ¡n báº±ng 0 cho symbol: {0}
settlement.warn.noPositions=KhÃ´ng tÃ¬m tháº¥y vá» tháº¿ nÃ o cho symbol: {0}

# Funding Service messages
fundingService.debug.calculateFundingRate=Äang tÃ­nh toÃ¡n tá»· lá» tÃ i trá»£ cho symbol: {0}, giÃ¡ chá» sá»: {1}, giÃ¡ ÄÃ¡nh dáº¥u: {2}
fundingService.debug.getCurrentFundingRate=Äang láº¥y tá»· lá» tÃ i trá»£ hiá»n táº¡i cho symbol: {0}
fundingService.debug.getNextFundingTime=Äang láº¥y thá»i gian tÃ i trá»£ tiáº¿p theo cho symbol: {0}

fundingService.error.calculateFundingRate=Lá»i khi tÃ­nh toÃ¡n tá»· lá» tÃ i trá»£ cho symbol: {0}
fundingService.error.getCurrentFundingRate=Lá»i khi láº¥y tá»· lá» tÃ i trá»£ hiá»n táº¡i cho symbol: {0}
fundingService.error.getNextFundingTime=Lá»i khi láº¥y thá»i gian tÃ i trá»£ tiáº¿p theo cho symbol: {0}

# Price Service messages
priceService.debug.getIndexPrice=Äang láº¥y giÃ¡ chá» sá» cho symbol: {0}
priceService.debug.getMarkPrice=Äang láº¥y giÃ¡ ÄÃ¡nh dáº¥u cho symbol: {0}
priceService.debug.calculateIndexPrice=Äang tÃ­nh toÃ¡n giÃ¡ chá» sá» cho symbol: {0}
priceService.debug.calculateMarkPrice=Äang tÃ­nh toÃ¡n giÃ¡ ÄÃ¡nh dáº¥u cho symbol: {0}

priceService.error.getIndexPrice=Lá»i khi láº¥y giÃ¡ chá» sá» cho symbol: {0}
priceService.error.getMarkPrice=Lá»i khi láº¥y giÃ¡ ÄÃ¡nh dáº¥u cho symbol: {0}
priceService.error.calculateIndexPrice=Lá»i khi tÃ­nh toÃ¡n giÃ¡ chá» sá» cho symbol: {0}
priceService.error.calculateMarkPrice=Lá»i khi tÃ­nh toÃ¡n giÃ¡ ÄÃ¡nh dáº¥u cho symbol: {0}
