#!/bin/bash

# Script to update the Vault secret with the correct PostgreSQL password

# Set variables
POSTGRES_CLUSTER_NAME="bitcello-spot-cluster"
POSTGRES_USER="spot_user"
POSTGRES_NAMESPACE="postgres-operator"
VAULT_SECRET_PATH="datasource"  # Path in Vault where the secret is stored
VAULT_SECRET_KEY="SPOT_DATASOURCE_PASSWORD"  # Key in Vault for the password

# Get the PostgreSQL password from Kubernetes secret
echo "Retrieving PostgreSQL password from Kubernetes secret..."
POSTGRES_PASSWORD=$(kubectl get secret ${POSTGRES_USER}.${POSTGRES_CLUSTER_NAME}.credentials.postgresql.acid.zalan.do -n ${POSTGRES_NAMESPACE} -o jsonpath='{.data.password}' | base64 -d)

if [ -z "$POSTGRES_PASSWORD" ]; then
  echo "Error: Failed to retrieve PostgreSQL password from Kubernetes secret."
  exit 1
fi

echo "Successfully retrieved PostgreSQL password."

# Update the Vault secret with the correct password
echo "Updating Vault secret with the correct PostgreSQL password..."
echo "To update the Vault secret, run the following command:"
echo "vault kv put ${VAULT_SECRET_PATH} ${VAULT_SECRET_KEY}=${POSTGRES_PASSWORD}"

echo "Script completed. Please ensure the Vault secret is updated with the correct PostgreSQL password."

# Note: This script assumes that the user has the necessary permissions to access both Kubernetes secrets and Vault.
# If you're using a different Vault path or key, please update the VAULT_SECRET_PATH and VAULT_SECRET_KEY variables accordingly.