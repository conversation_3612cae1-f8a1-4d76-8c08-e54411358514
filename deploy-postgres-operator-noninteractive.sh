#!/bin/bash

# PostgreSQL Operator Non-Interactive Deployment Script
# This script deploys the Zalando PostgreSQL Operator to a Kubernetes cluster without user interaction
# Usage: ./deploy-postgres-operator-noninteractive.sh [--with-ui] [--with-sample-cluster]

set -e

# Parse command line arguments
WITH_UI=false
WITH_SAMPLE_CLUSTER=false

for arg in "$@"; do
  case $arg in
    --with-ui)
      WITH_UI=true
      shift
      ;;
    --with-sample-cluster)
      WITH_SAMPLE_CLUSTER=true
      shift
      ;;
    *)
      # Unknown option
      ;;
  esac
done

echo "=== PostgreSQL Operator Non-Interactive Deployment Script ==="
echo ""

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "Error: kubectl is not installed or not in the PATH"
    echo "Please install kubectl and try again"
    exit 1
fi

# Check if connected to a Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    echo "Error: Not connected to a Kubernetes cluster"
    echo "Please configure kubectl to connect to your cluster and try again"
    exit 1
fi

echo "=== Deploying PostgreSQL Operator ==="

# Step 1: Create the ConfigMap with operator configuration
echo "Creating ConfigMap..."
kubectl apply -f postgres-operator/manifests/configmap.yaml

# Step 2: Create the service account and RBAC resources
echo "Creating service account and RBAC resources..."
kubectl apply -f postgres-operator/manifests/operator-service-account-rbac.yaml

# Step 3: Deploy the operator
echo "Deploying PostgreSQL Operator..."
kubectl apply -f postgres-operator/manifests/postgres-operator.yaml

# Step 4: Create the operator service
echo "Creating operator API service..."
kubectl apply -f postgres-operator/manifests/api-service.yaml

# Wait for the operator to be ready
echo "Waiting for PostgreSQL Operator to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/postgres-operator

echo "=== PostgreSQL Operator deployed successfully ==="
echo ""

# Deploy UI if requested
if [ "$WITH_UI" = true ]; then
    echo "=== Deploying PostgreSQL Operator UI ==="
    
    # Deploy the UI
    echo "Deploying UI components..."
    kubectl apply -f postgres-operator/ui/manifests/
    
    # Wait for the UI to be ready
    echo "Waiting for PostgreSQL Operator UI to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/postgres-operator-ui
    
    echo "=== PostgreSQL Operator UI deployed successfully ==="
    
    # Get the UI service details
    UI_SERVICE=$(kubectl get svc postgres-operator-ui -o jsonpath='{.spec.type}')
    if [ "$UI_SERVICE" == "LoadBalancer" ]; then
        UI_IP=$(kubectl get svc postgres-operator-ui -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        echo "Access the UI at: http://$UI_IP:8081"
    else
        echo "To access the UI, run the following command:"
        echo "kubectl port-forward svc/postgres-operator-ui 8081:8081"
        echo "Then open your browser and navigate to: http://localhost:8081"
    fi
fi

# Create sample cluster if requested
if [ "$WITH_SAMPLE_CLUSTER" = true ]; then
    echo "=== Creating Sample PostgreSQL Cluster ==="
    
    # Create the PostgreSQL cluster
    echo "Creating PostgreSQL cluster..."
    kubectl apply -f postgres-operator/manifests/future-db_complete-postgres-without-loadbalancer.yaml
    
    # Create the NodePort service for external access
    echo "Creating NodePort service for external access..."
    kubectl apply -f postgres-operator/manifests/future-db-service-nodeport.yaml
    
    echo "=== Sample PostgreSQL Cluster creation initiated ==="
    echo "It may take a few minutes for the cluster to be fully provisioned."
    echo ""
    echo "To check the status, run:"
    echo "kubectl get postgresql"
    echo ""
    echo "To get connection details once the cluster is ready, run:"
    echo "kubectl get secret future-user.cex-future-cluster.credentials.postgresql.acid.zalan.do -o jsonpath='{.data.password}' | base64 -d"
    echo ""
    echo "Connection string format:"
    echo "*********************************************************/future_db"
fi

echo ""
echo "=== Deployment Complete ==="
echo "For more information, refer to the PostgreSQL Operator documentation:"
echo "https://postgres-operator.readthedocs.io/"