# Troubleshooting Guide: Vault PVC Stuck in Pending Status

This guide provides a systematic approach to diagnose and resolve issues with the `vault-data-pvc` that's stuck in pending status.

## 1. Verify PVC Status

First, check the current status of the PVC:

```bash
kubectl get pvc -n vault
kubectl describe pvc vault-data-pvc -n vault
```

Look for:
- Status (should be "Pending")
- Events section for error messages
- Volume binding mode

## 2. Check Storage Class Configuration

The PVC is using the `local-path` storage class:

```bash
kubectl get storageclass
kubectl describe storageclass local-path
```

Verify:
- The storage class exists
- It's properly configured for local storage
- It has the correct provisioner (usually `rancher.io/local-path`)
- Check if it's the default storage class (though this shouldn't affect provisioning)

## 3. Check Node Status and Resources

Since the Vault deployment has a nodeSelector for `bitcello-worker`:

```bash
kubectl get nodes
kubectl describe node bitcello-worker
```

Check:
- Node is in "Ready" status
- Node has enough resources (CPU, memory, storage)
- Node has any taints that might prevent scheduling
- Node conditions for any issues

## 4. Check Available Storage on Node

```bash
# Check available storage on the node
kubectl get nodes bitcello-worker -o jsonpath='{.status.allocatable.ephemeral-storage}'

# SSH to the node and check disk space
ssh bitcello-worker
df -h
```

## 5. Check for Path Provisioner Issues

For `local-path` storage class, check:

```bash
# Check if the local-path-provisioner pod is running
kubectl get pods -n kube-system | grep local-path
kubectl logs -n kube-system -l app=local-path-provisioner
```

## 6. Common Causes and Solutions

### Issue 1: Storage Class Not Found or Misconfigured

**Symptoms:**
- Events show "storageclass.storage.k8s.io "local-path" not found"

**Solution:**
- Create or correct the storage class:
```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-path
provisioner: rancher.io/local-path
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
```

### Issue 2: Node Doesn't Have Enough Disk Space

**Symptoms:**
- No specific error, but PVC remains in Pending state
- Node shows high disk usage

**Solution:**
- Clean up disk space on the node (remove old logs, container images, etc.)
- Consider using a different node with more space
- Reduce the PVC size request if appropriate

### Issue 3: Volume Binding Mode Issues

**Symptoms:**
- PVC stuck in Pending state with no pods trying to use it

**Solution:**
- If the storage class has `volumeBindingMode: WaitForFirstConsumer`, the PVC won't be bound until a pod tries to use it
- Check if the Vault pod is being scheduled correctly
- Temporarily change the binding mode to `Immediate` for testing

### Issue 4: Path Provisioner Issues

**Symptoms:**
- Events show errors related to the provisioner
- Local-path-provisioner logs show errors

**Solution:**
- Check if the local-path-provisioner is running correctly
- Verify the provisioner has the right permissions
- Restart the provisioner if necessary

### Issue 5: Node Selector Issues

**Symptoms:**
- Pod can't be scheduled to the node specified in nodeSelector

**Solution:**
- Verify the node label matches the nodeSelector
- Check if the node is available and ready
- Consider removing the nodeSelector temporarily for testing

## 7. Specific Solutions to Try

1. **Check if the namespace exists:**
```bash
kubectl get namespace vault
# If it doesn't exist:
kubectl create namespace vault
```

2. **Recreate the PVC:**
```bash
kubectl delete pvc vault-data-pvc -n vault
kubectl apply -f pvc.yaml
```

3. **Try with a different storage class:**
Modify the PVC to use a different storage class if available.

4. **Check for orphaned resources:**
```bash
kubectl get pv | grep vault-data
# Delete any orphaned PVs if found
```

5. **Verify the local-path provisioner is working:**
Create a test PVC with the same storage class in a different namespace to see if the issue is specific to the vault namespace.

## 8. K3s-Specific Troubleshooting

Since you're using K3s (based on previous context):

1. **Check K3s storage directory:**
```bash
ls -la /var/lib/rancher/k3s/storage/
```

2. **Verify local-path provisioner is included in your K3s installation:**
```bash
kubectl get pods -n kube-system | grep local-path
```

3. **Check K3s logs:**
```bash
journalctl -u k3s
```

4. **Restart K3s if necessary:**
```bash
systemctl restart k3s
```

By systematically working through these steps, you should be able to identify and resolve the issue with the pending vault-data-pvc.