# PostgreSQL Operator Comprehensive Deployment and Usage Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Prerequisites](#prerequisites)
3. [Architecture Overview](#architecture-overview)
4. [Deployment Options](#deployment-options)
   - [Interactive Deployment](#interactive-deployment)
   - [Non-Interactive Deployment](#non-interactive-deployment)
   - [Manual Deployment](#manual-deployment)
5. [Configuration Options](#configuration-options)
   - [General Settings](#general-settings)
   - [Resource Settings](#resource-settings)
   - [PostgreSQL Settings](#postgresql-settings)
   - [Connection Pooler Settings](#connection-pooler-settings)
   - [Backup Settings](#backup-settings)
6. [Creating PostgreSQL Clusters](#creating-postgresql-clusters)
   - [Sample Cluster Definition](#sample-cluster-definition)
   - [Custom Cluster Configuration](#custom-cluster-configuration)
7. [Accessing PostgreSQL Clusters](#accessing-postgresql-clusters)
   - [Within Kubernetes](#within-kubernetes)
   - [External Access](#external-access)
   - [Getting Connection Credentials](#getting-connection-credentials)
8. [PostgreSQL Operator UI](#postgresql-operator-ui)
   - [Accessing the UI](#accessing-the-ui)
   - [UI Features](#ui-features)
9. [Maintenance Operations](#maintenance-operations)
   - [Scaling Clusters](#scaling-clusters)
   - [Version Upgrades](#version-upgrades)
   - [Configuration Changes](#configuration-changes)
   - [Backup and Restore](#backup-and-restore)
10. [Troubleshooting](#troubleshooting)
    - [Common Issues](#common-issues)
    - [Checking Logs](#checking-logs)
    - [Known Limitations](#known-limitations)
11. [Undeployment](#undeployment)
    - [Interactive Undeployment](#interactive-undeployment)
    - [Non-Interactive Undeployment](#non-interactive-undeployment)
    - [Manual Undeployment](#manual-undeployment)
12. [References](#references)

## Introduction

The PostgreSQL Operator, developed by Zalando, is a Kubernetes operator that manages PostgreSQL clusters deployed to Kubernetes. It automates various tasks such as:

- Creating and deleting PostgreSQL clusters
- Resizing PostgreSQL clusters
- Managing users and databases
- Performing PostgreSQL version upgrades
- Managing PostgreSQL configuration
- Applying security updates

This guide provides comprehensive instructions for deploying, configuring, and using the PostgreSQL Operator in a Kubernetes environment.

## Prerequisites

Before deploying the PostgreSQL Operator, ensure you have the following:

- Kubernetes cluster (v1.19 or higher), K3s.
- kubectl command-line tool configured to connect to your cluster
- Access to the container registry (ghcr.io)
- Sufficient permissions to create and manage Kubernetes resources

## Architecture Overview

The PostgreSQL Operator consists of the following components:

1. **Operator Deployment**: The main controller that manages PostgreSQL clusters
2. **Operator UI** (optional): A web interface for managing PostgreSQL clusters
3. **Custom Resource Definitions (CRDs)**: Define the PostgreSQL resources
4. **RBAC Resources**: Service accounts, roles, and role bindings

When deployed, the PostgreSQL Operator watches for custom resources of kind `postgresql` and creates the necessary Kubernetes resources to run PostgreSQL clusters, including:

- StatefulSets for PostgreSQL instances
- Services for accessing PostgreSQL
- ConfigMaps for configuration
- Secrets for credentials
- PersistentVolumeClaims for data storage

## Deployment Options

There are three ways to deploy the PostgreSQL Operator:

### Interactive Deployment

The `deploy-postgres-operator.sh` script automates the deployment with interactive prompts:

```bash
# Make the script executable (if not already)
chmod +x deploy-postgres-operator.sh

# Run the deployment script
./deploy-postgres-operator.sh
```

The script will:
1. Check prerequisites
2. Deploy the PostgreSQL Operator components
3. Ask if you want to deploy the PostgreSQL Operator UI
4. Ask if you want to create a sample PostgreSQL cluster

### Non-Interactive Deployment

The `deploy-postgres-operator-noninteractive.sh` script provides a non-interactive way to deploy:

```bash
# Make the script executable (if not already)
chmod +x deploy-postgres-operator-noninteractive.sh

# Run the deployment script with options
./deploy-postgres-operator-noninteractive.sh [--with-ui] [--with-sample-cluster]
```

Options:
- `--with-ui`: Deploy the PostgreSQL Operator UI
- `--with-sample-cluster`: Create a sample PostgreSQL cluster

This is useful for automated deployments or CI/CD pipelines.

### Manual Deployment

You can also deploy the components manually:

```bash
# Create the ConfigMap with operator configuration
kubectl apply -f postgres-operator/manifests/configmap.yaml

# Create the service account and RBAC resources
kubectl apply -f postgres-operator/manifests/operator-service-account-rbac.yaml

# Deploy the operator
kubectl apply -f postgres-operator/manifests/postgres-operator.yaml

# Create the operator service
kubectl apply -f postgres-operator/manifests/api-service.yaml

# Optional: Deploy the UI
kubectl apply -f postgres-operator/ui/manifests/
```

## Configuration Options

The PostgreSQL Operator is configured via a ConfigMap (`postgres-operator/manifests/configmap.yaml`). Here are the key configuration options:

### General Settings

- `docker_image`: The Spilo (PostgreSQL) image to use (default: `ghcr.io/zalando/spilo-17:4.0-p2`)
- `watched_namespace`: The namespace to watch for PostgreSQL resources (default: `*` for all namespaces)
- `enable_crd_validation`: Enable validation of CRDs (default: `true`)
- `workers`: Number of worker threads (default: `8`)

### Resource Settings

- `default_cpu_request`/`default_cpu_limit`: Default CPU request/limit for PostgreSQL pods
- `default_memory_request`/`default_memory_limit`: Default memory request/limit for PostgreSQL pods
- `min_cpu_limit`/`min_memory_limit`: Minimum CPU/memory limits

### PostgreSQL Settings

- `super_username`: PostgreSQL superuser name (default: `postgres`)
- `replication_username`: PostgreSQL replication user name (default: `standby`)
- `enable_password_rotation`: Enable automatic password rotation (default: `false`)
- `password_rotation_interval`: Days between password rotations (default: `90`)

### Connection Pooler Settings

- `enable_pgbouncer`: Enable PgBouncer connection pooler (default: `false`)
- `connection_pooler_image`: PgBouncer image to use (default: `registry.opensource.zalan.do/acid/pgbouncer:master-32`)
- `connection_pooler_mode`: PgBouncer mode (default: `transaction`)

### Backup Settings

- `logical_backup_schedule`: Cron schedule for logical backups (default: `30 00 * * *`)
- `logical_backup_docker_image`: Backup image to use (default: `ghcr.io/zalando/postgres-operator/logical-backup:v1.14.0`)
- `logical_backup_s3_bucket`: S3 bucket for backups (default: `my-bucket-url`)

## Creating PostgreSQL Clusters

After deploying the operator, you can create PostgreSQL clusters by applying custom resources of kind `postgresql`.

### Sample Cluster Definition

The repository includes a sample PostgreSQL cluster definition:

```bash
# Create Postgres Cluster
kubectl apply -f postgres-operator/manifests/future-db_complete-postgres-without-loadbalancer.yaml

# Create Service for Postgres Cluster for external access (just for Development) 
kubectl apply -f postgres-operator/manifests/spot-db-service-nodeport.yaml
```

### Custom Cluster Configuration

You can create your own PostgreSQL cluster by defining a custom resource with the following structure:

```yaml
apiVersion: "acid.zalan.do/v1"
kind: postgresql
metadata:
  name: my-postgres-cluster
spec:
  dockerImage: ghcr.io/zalando/spilo-17:4.0-p2
  teamId: "acid"
  numberOfInstances: 3
  users:
    myapp: []  # No special permissions
    admin:
      - superuser
      - createdb
  databases:
    mydb: myapp
  postgresql:
    version: "17"
    parameters:
      shared_buffers: "1GB"
      max_connections: "500"
      port: "5432"
  volume:
    size: 10Gi
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 500m
      memory: 1Gi
```

Key configuration options:
- `name`: The name of the PostgreSQL cluster
- `dockerImage`: The Spilo (PostgreSQL) image to use
- `teamId`: The team ID for the cluster
- `numberOfInstances`: The number of PostgreSQL instances
- `users`: Application/robot users and their permissions
- `databases`: Databases and their owners
- `postgresql.version`: The PostgreSQL version
- `postgresql.parameters`: PostgreSQL configuration parameters
- `volume.size`: The size of the PostgreSQL data volume
- `resources`: CPU and memory requests/limits

## Accessing PostgreSQL Clusters

### Within Kubernetes

Applications running within the Kubernetes cluster can access PostgreSQL using the service name:

```
************************************************/database-name
```

### External Access

For development purposes, you can create a NodePort service:

```bash
kubectl apply -f postgres-operator/manifests/spot-db-service-nodeport.yaml
```

Or use port-forwarding:

```bash
# Forward the PostgreSQL port to your local machine
kubectl port-forward svc/cluster-name 5432:5432
```

### Getting Connection Credentials

The PostgreSQL operator generates random passwords for users. To retrieve the password:

```bash
# Get the secret containing the password
kubectl get secret username.cluster-name.credentials.postgresql.acid.zalan.do -o jsonpath='{.data.password}' | base64 -d
```

Connection string format:

```
************************************************/database-name
```

## PostgreSQL Operator UI

The PostgreSQL Operator UI provides a web interface for managing PostgreSQL clusters.

### Accessing the UI

After deploying the UI, you can access it by port-forwarding:

```bash
kubectl port-forward svc/postgres-operator-ui 8081:8081
```

Then open your browser and navigate to: http://localhost:8081

### UI Features

The UI provides the following features:
- View all PostgreSQL clusters
- Create new PostgreSQL clusters
- Edit existing PostgreSQL clusters
- Delete PostgreSQL clusters
- View cluster details (instances, resources, etc.)
- Manage users and databases

## Maintenance Operations

### Scaling Clusters

To scale a PostgreSQL cluster, edit the `numberOfInstances` field in the PostgreSQL custom resource:

```bash
kubectl edit postgresql/cluster-name
```

### Version Upgrades

To upgrade the PostgreSQL version, edit the `postgresql.version` field in the PostgreSQL custom resource:

```bash
kubectl edit postgresql/cluster-name
```

### Configuration Changes

To change the PostgreSQL configuration, edit the `postgresql.parameters` field in the PostgreSQL custom resource:

```bash
kubectl edit postgresql/cluster-name
```

### Backup and Restore

The PostgreSQL Operator supports logical backups to S3-compatible storage. To configure backups, set the following options in the ConfigMap:

- `logical_backup_schedule`: Cron schedule for logical backups
- `logical_backup_docker_image`: Backup image to use
- `logical_backup_s3_bucket`: S3 bucket for backups

## Troubleshooting

### Common Issues

1. **Operator pod not starting**: Check the pod logs for errors
   ```bash
   kubectl logs -l name=postgres-operator
   ```

2. **UI not connecting to the operator**: Ensure the operator service is running
   ```bash
   kubectl get svc postgres-operator
   ```

3. **PostgreSQL cluster not being created**: Check the operator logs for errors
   ```bash
   kubectl logs -l name=postgres-operator
   ```

4. **Cannot connect to PostgreSQL**: Check if the PostgreSQL pod is running and the service is created
   ```bash
   kubectl get pods -l cluster-name=cluster-name
   kubectl get svc cluster-name
   ```

### Checking Logs

To check the logs of the PostgreSQL Operator:

```bash
kubectl logs -l name=postgres-operator
```

To check the logs of a PostgreSQL cluster:

```bash
kubectl logs cluster-name-0
```

### Known Limitations

- The PostgreSQL Operator does not support automatic failover across Kubernetes clusters
- The PostgreSQL Operator does not support automatic backup verification
- The PostgreSQL Operator does not support point-in-time recovery (PITR) out of the box

## Undeployment

### Interactive Undeployment

The `undeploy-postgres-operator.sh` script automates the removal with interactive prompts:

```bash
# Make the script executable (if not already)
chmod +x undeploy-postgres-operator.sh

# Run the undeployment script
./undeploy-postgres-operator.sh
```

The script will:
1. Ask if you want to remove the sample PostgreSQL cluster
2. Ask if you want to remove the PostgreSQL Operator UI
3. Remove the PostgreSQL Operator components

### Non-Interactive Undeployment

The `undeploy-postgres-operator-noninteractive.sh` script provides a non-interactive way to remove:

```bash
# Make the script executable (if not already)
chmod +x undeploy-postgres-operator-noninteractive.sh

# Run the undeployment script with options
./undeploy-postgres-operator-noninteractive.sh [--remove-ui] [--remove-sample-cluster]
```

Options:
- `--remove-ui`: Remove the PostgreSQL Operator UI
- `--remove-sample-cluster`: Remove the sample PostgreSQL cluster

### Manual Undeployment

You can also remove the components manually:

```bash
# Remove the operator service
kubectl delete -f postgres-operator/manifests/api-service.yaml

# Remove the operator
kubectl delete -f postgres-operator/manifests/postgres-operator.yaml

# Remove the service account and RBAC resources
kubectl delete -f postgres-operator/manifests/operator-service-account-rbac.yaml

# Remove the ConfigMap
kubectl delete -f postgres-operator/manifests/configmap.yaml

# Optional: Remove the UI
kubectl delete -f postgres-operator/ui/manifests/
```

Note: PersistentVolumeClaims and PersistentVolumes may still exist after undeployment. If you want to completely remove all data, you may need to manually delete these resources.

## References

- [Zalando PostgreSQL Operator Documentation](https://postgres-operator.readthedocs.io/)
- [Zalando PostgreSQL Operator GitHub Repository](https://github.com/zalando/postgres-operator)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)