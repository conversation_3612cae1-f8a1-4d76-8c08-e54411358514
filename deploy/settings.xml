<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                              https://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- Profiles configuration -->
    <profiles>
        <profile>
            <id>securecentral</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <!-- Repository Lotus -->
                <repository>
                    <id>lotus-repo</id>
                    <url>http://10.242.30.45:28081/nexus/repository/maven-public</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>

                <!-- Repository Maven Central -->
                <repository>
                    <id>repo1-central</id>
                    <url>https://repo.maven.apache.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>

            <pluginRepositories>
                <pluginRepository>
                    <id>repo1-central</id>
                    <url>https://repo.maven.apache.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <!-- Mirrors configuration -->
    <mirrors>
        <!-- Mirror for Lotus -->
        <mirror>
            <id>lotus-mirror</id>
            <mirrorOf>lotus-repo</mirrorOf>
            <url>http://10.242.30.45:28081/nexus/repository/maven-public</url>
        </mirror>

        <!-- Mirror for Maven Central -->
        <mirror>
            <id>central-mirror</id>
            <mirrorOf>repo1-central</mirrorOf>
            <url>https://repo.maven.apache.org/maven2</url>
        </mirror>
    </mirrors>

    <!-- Server authentication -->
    <servers>
        <server>
            <id>lotus-repo</id> <!-- Match with `id` in the repository -->
            <username>admin</username>
            <password>Icetea@123</password>
        </server>
        <server>
            <id>repo1-central</id> <!-- Match with `id` in the repository -->
            <username>admin</username>
            <password>Icetea@123</password>
        </server>
    </servers>
</settings>