# K3s DNS Resolution Issue - Findings and Resolution Plan

## Issue Summary

Pods in the EC2 K3s cluster cannot connect to services using service name DNS, while the same functionality works correctly in the local K3s cluster.

## Investigation Findings

### Local K3s Cluster (Working)
- CoreDNS pod is running properly
- kube-dns service is correctly configured with IP **********
- DNS resolution works correctly for service names
- Pods can connect to services using their ClusterIPs

### EC2 K3s Cluster (Not Working)
- CoreDNS pod is running properly
- kube-dns service is correctly configured with IP **********
- DNS resolution fails with connection timeout
- Pods cannot connect to services using their ClusterIPs
- Pods cannot connect to pods on other nodes
- Pods can connect to IPs within their own subnet

## Root Cause

The issue is caused by a network connectivity problem between nodes in the EC2 K3s cluster. Specifically:

1. Pods cannot communicate with pods on other nodes
2. Pods cannot connect to services using their ClusterIPs
3. This prevents DNS resolution as it requires connecting to the kube-dns service

## Resolution Plan

### 1. Check EC2 Security Groups

EC2 security groups must allow all necessary traffic between nodes:

```bash
# Check security groups for the EC2 instances
aws ec2 describe-security-groups --group-ids <security-group-id>

# Ensure that security groups allow all traffic between nodes
# Add rules to allow all traffic between nodes in the same security group
aws ec2 authorize-security-group-ingress \
  --group-id <security-group-id> \
  --protocol all \
  --source-group <security-group-id>
```

### 2. Verify VPC DNS Settings

Ensure that DNS resolution and DNS hostnames are enabled in the VPC:

```bash
# Check VPC DNS settings
aws ec2 describe-vpc-attribute --vpc-id <vpc-id> --attribute enableDnsSupport
aws ec2 describe-vpc-attribute --vpc-id <vpc-id> --attribute enableDnsHostnames

# Enable DNS support and hostnames if not enabled
aws ec2 modify-vpc-attribute --vpc-id <vpc-id> --enable-dns-support
aws ec2 modify-vpc-attribute --vpc-id <vpc-id> --enable-dns-hostnames
```

### 3. Check Network ACLs

Ensure that network ACLs allow the necessary traffic:

```bash
# List network ACLs
aws ec2 describe-network-acls

# Ensure that network ACLs allow UDP and TCP traffic on port 53 (DNS)
# Also ensure that they allow traffic on ports used by Flannel (UDP 8472 for VXLAN)
```

### 4. Reconfigure K3s with Different Flannel Backend

The default Flannel backend might not be suitable for the EC2 environment:

```bash
# Uninstall K3s on all nodes
ssh <node> 'k3s-uninstall.sh'

# Reinstall K3s on the master node with a different Flannel backend
ssh <master-node> 'curl -sfL https://get.k3s.io | INSTALL_K3S_EXEC="--flannel-backend=vxlan" sh -'

# Get the node token from the master node
ssh <master-node> 'sudo cat /var/lib/rancher/k3s/server/node-token'

# Reinstall K3s on worker nodes with the same Flannel backend
ssh <worker-node> 'curl -sfL https://get.k3s.io | K3S_URL=https://<master-ip>:6443 K3S_TOKEN=<node-token> INSTALL_K3S_EXEC="--flannel-backend=vxlan" sh -'
```

### 5. Check for MTU Mismatches

MTU mismatches can cause connectivity issues between nodes:

```bash
# Check MTU on all nodes
ssh <node> 'ip link show | grep mtu'

# If MTUs are different, set a consistent MTU for Flannel
# Edit the flannel ConfigMap
kubectl edit cm -n kube-system flannel-config

# Add or modify the net-conf.json field to include the MTU
# "net-conf.json": "{\"Network\":\"*********/16\",\"Backend\":{\"Type\":\"vxlan\",\"VNI\":1,\"MTU\":1450}}"
```

## Verification Steps

After implementing any of the above solutions:

1. Create a test pod:
   ```bash
   kubectl run dnsutils --image=gcr.io/kubernetes-e2e-test-images/dnsutils:1.3 -- sleep 3600
   ```

2. Test DNS resolution:
   ```bash
   kubectl exec -i -t dnsutils -- nslookup kubernetes.default
   kubectl exec -i -t dnsutils -- nslookup <service-name>
   ```

3. Test connectivity to services:
   ```bash
   kubectl exec -i -t dnsutils -- ping -c 3 <service-cluster-ip>
   ```

4. Test connectivity to pods on other nodes:
   ```bash
   kubectl exec -i -t dnsutils -- ping -c 3 <pod-ip-on-different-node>
   ```

## Recommended Approach

Based on the investigation, the most likely solution is to check and update the EC2 security groups to ensure they allow all necessary traffic between nodes. If that doesn't resolve the issue, proceed with the other steps in the resolution plan.

The most effective approach would be to:

1. First check and update EC2 security groups
2. Verify VPC DNS settings
3. If issues persist, reconfigure K3s with a different Flannel backend

## Conclusion

The DNS resolution issue in the EC2 K3s cluster is caused by a network connectivity problem between nodes. By addressing the EC2-specific networking requirements and reconfiguring K3s with appropriate network settings, you should be able to resolve the issue and enable DNS resolution for service names.