# PostgreSQL Authentication Issue Resolution

## Issue Description

The external-api service is failing to connect to the PostgreSQL database with the following error:

```
FATAL: password authentication failed for user "spot_user"
```

This error occurs because the password stored in Vault (which is used by the application) doesn't match the password generated by the PostgreSQL operator (which is stored in a Kubernetes secret).

## Root Cause

The Zalando PostgreSQL operator generates random passwords for database users and stores them in Kubernetes secrets. These passwords need to be synchronized with the secrets stored in Vault, which are used by the application through the External Secrets Operator.

The password mismatch occurs because:

1. The PostgreSQL operator generates a random password for the `spot_user` and stores it in a Kubernetes secret named `spot-user.bitcello-spot-cluster.credentials.postgresql.acid.zalan.do`.
2. The external-api application retrieves its database credentials from Vault through the External Secrets Operator.
3. The password in Vault doesn't match the password generated by the PostgreSQL operator.

## Solution

To resolve this issue, you need to update the Vault secret with the correct PostgreSQL password. A script has been provided to help with this process:

```bash
./update-spot-db-password.sh
```

This script will:

1. Retrieve the PostgreSQL password from the Kubernetes secret.
2. Provide instructions on how to update the Vault secret with the correct password.

## Prevention

To prevent this issue from occurring in the future, consider implementing one of the following approaches:

1. **Automated Synchronization**: Create a Kubernetes CronJob that periodically synchronizes the PostgreSQL passwords with Vault.
2. **Custom Resource Controller**: Develop a custom controller that watches for changes to PostgreSQL secrets and updates Vault accordingly.
3. **Manual Process Documentation**: Document the process for updating Vault secrets after PostgreSQL password rotations.

## References

- [Zalando PostgreSQL Operator Documentation](https://postgres-operator.readthedocs.io/)
- [External Secrets Operator Documentation](https://external-secrets.io/latest/)
- [Vault Documentation](https://www.vaultproject.io/docs)