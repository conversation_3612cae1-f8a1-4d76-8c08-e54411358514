# Vault Deployment to Bitcello-Master Node

This document explains how to deploy HashiCorp Vault on the bitcello-master node in the Kubernetes cluster.

## Changes Made

The following changes were made to the Vault deployment configuration:

1. Modified the nodeSelector in both deployment.yaml and deployment-dev.yaml to target the bitcello-master node instead of bitcello-worker:
   ```yaml
   nodeSelector:
     kubernetes.io/hostname: bitcello-master
   ```

2. Created a deployment script (`deploy-vault-to-master.sh`) to automate the deployment process.

## Deployment Instructions

### Prerequisites

- Access to the Kubernetes cluster
- kubectl configured to communicate with the cluster
- Sufficient resources on the bitcello-master node

### Deployment Steps

1. Make the deployment script executable:
   ```bash
   chmod +x deploy-vault-to-master.sh
   ```

2. Deploy Vault in standard mode:
   ```bash
   ./deploy-vault-to-master.sh
   ```

   Or deploy Vault in development mode:
   ```bash
   ./deploy-vault-to-master.sh dev
   ```

3. Verify the deployment:
   ```bash
   kubectl get pods -n vault
   kubectl get svc -n vault
   ```

### Accessing Vault

- Within the cluster: `http://vault.vault.svc.cluster.local:8200`
- For development mode, the root token is: `root`
- For standard mode, you'll need to initialize and unseal Vault:
  ```bash
  # Get the pod name
  POD_NAME=$(kubectl get pods -n vault -l app=vault -o jsonpath='{.items[0].metadata.name}')
  
  # Initialize Vault
  kubectl exec -it $POD_NAME -n vault -- vault operator init
  
  # Unseal Vault (run this command multiple times with different unseal keys)
  kubectl exec -it $POD_NAME -n vault -- vault operator unseal
  ```

## Troubleshooting

If the deployment fails, the script will provide diagnostic information. Common issues include:

1. PVC not binding:
   - Check if the local-path storage class is available
   - Verify that the bitcello-master node has sufficient disk space

2. Pod not starting:
   - Check the pod events: `kubectl describe pod -l app=vault -n vault`
   - Check the logs: `kubectl logs -l app=vault -n vault`

3. Vault not initializing:
   - Check if the pod is running: `kubectl get pods -n vault`
   - Check the Vault logs: `kubectl logs -l app=vault -n vault`

## Additional Resources

- [HashiCorp Vault Documentation](https://www.vaultproject.io/docs)
- [Vault Kubernetes Deployment Guide](https://www.vaultproject.io/docs/platform/k8s)