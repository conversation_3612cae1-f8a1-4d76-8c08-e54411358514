# Bitcello Infrastructure Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture Diagram](#architecture-diagram)
3. [Services](#services)
   - [Admin Service](#admin-service)
   - [Wallet Service](#wallet-service)
   - [Exchange Service](#exchange-service)
   - [Exchange API Service](#exchange-api-service)
   - [Exchange Core Service](#exchange-core-service)
   - [External API Service](#external-api-service)
   - [Future Core Service](#future-core-service)
   - [Market Service](#market-service)
   - [Core Service](#core-service)
   - [Frontend Service](#frontend-service)
4. [Infrastructure Components](#infrastructure-components)
   - [PostgreSQL](#postgresql)
   - [Redis](#redis)
   - [Kafka Stack](#kafka-stack)
   - [MongoDB](#mongodb)
   - [Consul](#consul)
   - [Vault](#vault)
   - [Minio](#minio)
   - [Jenkins](#jenkins)
   - [Nexus](#nexus)
   - [Rancher](#rancher)
   - [Identity](#identity)
   - [Observability Stack](#observability-stack)
5. [Deployment](#deployment)
   - [Jenkins Pipeline Deployment](#jenkins-pipeline-deployment)
   - [Manual Deployment](#manual-deployment)
6. [Configuration](#configuration)
   - [Environment Variables](#environment-variables)
   - [Kubernetes Resources](#kubernetes-resources)
7. [Maintenance](#maintenance)
   - [Scaling Services](#scaling-services)
   - [Version Upgrades](#version-upgrades)
   - [Configuration Changes](#configuration-changes)
8. [Network Architecture](#network-architecture)
9. [Troubleshooting](#troubleshooting)

## Overview

Bitcello is a comprehensive cryptocurrency exchange platform built on a microservices architecture. The platform is deployed on Kubernetes and consists of multiple services that work together to provide a complete exchange solution. This document provides a detailed overview of the Bitcello infrastructure, including all services and resources.

## Architecture Diagram

```
+----------------------------------------------------------------------------------------------------------------------+
|                                                Bitcello Platform                                                      |
+----------------------------------------------------------------------------------------------------------------------+
                                                        |
                                                        v
+----------------------------------------------------------------------------------------------------------------------+
|                                                    Services                                                           |
+----------------------------------------------------------------------------------------------------------------------+
|                                                                                                                      |
|  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+
|  |  Admin Service |  |  Wallet Service|  |Exchange Service|  |Exchange API Svc|  |Exchange Core   |  |External API Svc|
|  |  Port: 6010    |  |  Port: 6009    |  |  Port: 6001    |  |  Port: 6002    |  |  Port: 6003    |  |  Port: 6004    |
|  +-------+--------+  +-------+--------+  +-------+--------+  +-------+--------+  +-------+--------+  +-------+--------+
|          |                   |                   |                   |                   |                   |
|  +-------+--------+  +-------+--------+  +-------+--------+  +-------+--------+                             |
|  |Future Core Svc |  |  Market Service|  |  Core Service  |  |Frontend Service|                             |
|  |  Port: 6005    |  |  Port: 6006    |  |  Port: 6007    |  |                |                             |
|  +-------+--------+  +-------+--------+  +-------+--------+  +-------+--------+                             |
|          |                   |                   |                   |                                       |
+----------|-------------------|-------------------|-------------------|---------------------------------------+
           |                   |                   |                   |                                       |
           v                   v                   v                   v                                       v
+----------------------------------------------------------------------------------------------------------------------+
|                                           Infrastructure Components                                                   |
+----------------------------------------------------------------------------------------------------------------------+
|                                                                                                                      |
|  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+
|  |   PostgreSQL   |  |     Redis      |  |  Kafka Stack   |  |    MongoDB     |  |     Consul     |  |     Vault      |
|  | (Data Storage) |  | (Cache/PubSub) |  | (Messaging)    |  | (NoSQL DB)     |  | (Service Disc) |  | (Secrets)      |
|  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+
|                                                                                                                      |
|  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+
|  |     Minio      |  |    Jenkins     |  |     Nexus      |  |    Rancher     |  |    Identity    |  | Observability  |
|  | (Object Store) |  |    (CI/CD)     |  | (Artifacts)    |  | (K8s Mgmt)     |  | (Auth)         |  | (Monitoring)   |
|  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+  +----------------+
|                                                                                                                      |
+----------------------------------------------------------------------------------------------------------------------+
```

### Service Interaction Diagram

```
                                     +----------------+
                                     |  Frontend UI   |
                                     +--------+-------+
                                              |
                                              v
+----------------+    +----------------+    +----------------+    +----------------+
|  External API  |<-->|  Exchange API  |<-->|  Exchange Core |<-->|  Market Data   |
+--------+-------+    +--------+-------+    +--------+-------+    +----------------+
         ^                     ^                     ^
         |                     |                     |
         v                     v                     v
+----------------+    +----------------+    +----------------+    +----------------+
|  Wallet Service|<-->|  Core Service  |<-->| Future Service |<-->|  Admin Service |
+--------+-------+    +----------------+    +----------------+    +----------------+
         |
         v
+----------------------------------------------------------------------------------------------------------------------+
|                                           Infrastructure Layer                                                        |
| PostgreSQL | Redis | Kafka | MongoDB | Consul | Vault | Minio | Jenkins | Nexus | Rancher | Identity | Observability |
+----------------------------------------------------------------------------------------------------------------------+
```

## Services

### Admin Service
- **Description**: Administrative interface and API for managing the exchange
- **Port**: 6010
- **NodePort**: 30110
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/admin`

### Wallet Service
- **Description**: Manages cryptocurrency wallets and transactions
- **Port**: 6009
- **NodePort**: 30109
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/wallet`

### Exchange Service
- **Description**: Core exchange functionality
- **Port**: 6001
- **NodePort**: 30101
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/exchange`

### Exchange API Service
- **Description**: API for exchange operations
- **Port**: 6002
- **NodePort**: 30102
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/exchange-api`

### Exchange Core Service
- **Description**: Core exchange engine
- **Port**: 6003
- **NodePort**: 30103
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/exchange-core`

### External API Service
- **Description**: External-facing API
- **Port**: 6004
- **NodePort**: 30104
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/external-api`

### Future Core Service
- **Description**: Futures trading functionality
- **Port**: 6005
- **NodePort**: 30105
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/future`

### Market Service
- **Description**: Market data and order book management
- **Port**: 6006
- **NodePort**: 30106
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/market`

### Core Service
- **Description**: Core business logic and shared functionality
- **Port**: 6007
- **NodePort**: 30107
- **Dependencies**: PostgreSQL, Redis, Kafka, MongoDB, Consul
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/core`

### Frontend Service
- **Description**: User interface for the exchange platform
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/bitcello-services/fe-rec-user`

## Infrastructure Components

### PostgreSQL
- **Description**: Relational database for persistent storage
- **Usage**: Stores user data, transaction records, order history, and other structured data
- **Deployment**: Managed by PostgreSQL Operator

### Redis
- **Description**: In-memory data store for caching and session management
- **Usage**: Caches frequently accessed data, manages user sessions, and provides pub/sub functionality
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/redis`

### Kafka Stack
- **Description**: Message broker for event-driven communication
- **Components**:
  - Kafka: Message broker
  - Zookeeper: Coordination service
  - Schema Registry: Manages Avro schemas
  - Kafka UI: Web interface for Kafka management
- **Usage**: Enables asynchronous communication between services
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/kafka-stack`

### MongoDB
- **Description**: NoSQL database for flexible data storage
- **Usage**: Stores unstructured or semi-structured data
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/mongodb`

### Consul
- **Description**: Service discovery and configuration management
- **Usage**: Enables services to discover and connect to each other, manages configuration
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/consul`

### Vault
- **Description**: Secret management
- **Usage**: Securely stores and manages sensitive information like API keys, passwords, and certificates
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/vault`
- **Integration**: Uses External Secrets Operator to integrate with Kubernetes

### Minio
- **Description**: Object storage
- **Usage**: Stores files, backups, and other binary data
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/minio`

### Jenkins
- **Description**: CI/CD pipeline
- **Usage**: Automates building, testing, and deploying services
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/jenkins`
- **Shared Library**: Contains reusable pipeline code

### Nexus
- **Description**: Artifact repository
- **Usage**: Stores Docker images, JAR files, and other artifacts
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/nexus`

### Rancher
- **Description**: Kubernetes management
- **Usage**: Provides a web interface for managing Kubernetes clusters
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/rancher`

### Identity
- **Description**: Authentication and authorization
- **Usage**: Manages user identities and access control
- **Deployment Path**: `bitcello-cex-deployment/k8s-deployment/infrastructure/identity`

### Observability Stack
- **Description**: Monitoring and logging
- **Components**:
  - Grafana: Visualization and dashboards
  - Prometheus: Metrics collection and alerting
  - Loki: Log aggregation
  - Tempo: Distributed tracing
  - OpenTelemetry Collector: Telemetry collection
- **Deployment Path**: `o11y-deployment`

## Deployment

### Jenkins Pipeline Deployment

The recommended way to deploy Bitcello services is using the Jenkins pipelines. Each service has its own Jenkinsfile that automates the entire deployment process:

1. Clones the source code from the Git repository
2. Builds the application and packages it into a JAR
3. Builds a Docker image and pushes it to the Nexus registry
4. Deploys the application to Kubernetes

To deploy a service using Jenkins:

1. Navigate to the Jenkins dashboard
2. Create a new pipeline job
3. Configure the job to use the Jenkinsfile from the service's directory
4. Run the pipeline

### Manual Deployment

For manual deployment, follow these steps for each service:

1. Set the required environment variables (see [Environment Variables](#environment-variables))
2. Run the deployment script:
   ```bash
   cd bitcello-cex-deployment/k8s-deployment/bitcello-services/<service-name>
   ./deploy.sh
   ```

## Configuration

### Environment Variables

The following environment variables are required for deploying each service:

| Variable | Description |
|----------|-------------|
| COMMIT_HASH | Git commit hash used as the Docker image tag |
| DATASOURCE_URL | Database URL |
| DATASOURCE_USERNAME | Database username |
| DATASOURCE_PASSWORD | Database password |
| REDIS_HOST | Redis host |
| REDIS_PORT | Redis port |
| REDIS_PASSWORD | Redis password |
| KAFKA_BOOTSTRAP | Kafka bootstrap servers |
| SPRING_MONGODB_URI | MongoDB URI |
| CONSUL_HOST | Consul host |
| CONSUL_PORT | Consul port |

### Kubernetes Resources

Each service deployment creates the following Kubernetes resources:

- **Deployment**: Creates pods with the service container
- **Service**: Exposes the API on a specific port
- **Secret**: Stores sensitive configuration data

## Maintenance

### Scaling Services

To scale a service, update the `replicas` field in the deployment.yaml file:

```yaml
spec:
  replicas: 3  # Change this value
```

Then apply the changes:
```bash
kubectl apply -f deployment.yaml
```

### Version Upgrades

To upgrade a service to a new version:

1. Update the Git repository with the new code
2. Run the Jenkins pipeline for the service
3. The pipeline will build a new Docker image with the latest commit hash and deploy it

### Configuration Changes

To change the configuration of a service:

1. Update the environment variables in the deployment.yaml file
2. Apply the changes:
   ```bash
   kubectl apply -f deployment.yaml
   ```

## Network Architecture

The Bitcello platform uses a microservices architecture deployed on Kubernetes, with services communicating through various protocols and mechanisms.

### Network Topology

```
                                   +-------------------+
                                   |   Load Balancer   |
                                   +--------+----------+
                                            |
                                            v
+------------------+              +-------------------+
|  External Users  +------------->|   Ingress/API     |
+------------------+              |   Gateway         |
                                  +--------+----------+
                                           |
                                           v
                   +--------------------------------------------------+
                   |                Kubernetes Cluster                 |
                   |                                                  |
                   |  +----------------+       +----------------+     |
                   |  |  Service Mesh  |       |  Namespaces    |     |
                   |  +----------------+       +----------------+     |
                   |                                                  |
                   |  +----------------+       +----------------+     |
                   |  | Network Policy |       |  Service Disc. |     |
                   |  +----------------+       +----------------+     |
                   +--------------------------------------------------+
```

### Communication Patterns

The Bitcello services use the following communication patterns:

1. **Synchronous Communication**:
   - REST APIs: Services expose REST endpoints for direct communication
   - gRPC: Used for high-performance, low-latency service-to-service communication

2. **Asynchronous Communication**:
   - Kafka: Event-driven communication between services
   - Message patterns: Publish/Subscribe, Request/Reply, Command/Query

3. **Service Discovery**:
   - Consul: Services register themselves and discover other services
   - Kubernetes Services: DNS-based service discovery within the cluster

### Security Measures

1. **Network Security**:
   - Network Policies: Control traffic flow between services
   - TLS: Encrypt communication between services
   - mTLS: Mutual TLS authentication between services

2. **Authentication & Authorization**:
   - Identity Service: Centralized authentication and authorization
   - JWT Tokens: Secure communication between services
   - API Keys: Secure external API access

3. **Data Security**:
   - Vault: Secure storage of sensitive information
   - Encryption: Data encryption at rest and in transit

### External Communication

1. **Inbound Traffic**:
   - Load Balancer: Distributes external traffic
   - Ingress Controller: Routes HTTP/HTTPS traffic to services
   - API Gateway: Manages API access, rate limiting, and authentication

2. **Outbound Traffic**:
   - Egress Controller: Controls outbound traffic
   - Proxy: Mediates communication with external services

### Network Monitoring

1. **Observability**:
   - Prometheus: Collects network metrics
   - Grafana: Visualizes network performance
   - Loki: Aggregates network logs
   - Tempo: Distributed tracing for network requests

2. **Alerting**:
   - Alert Manager: Notifies of network issues
   - Health Checks: Monitors service availability

## Troubleshooting

Common issues and their solutions:

1. **Pod Startup Failure**:
   - Check pod logs: `kubectl logs <pod-name>`
   - Verify environment variables and secrets

2. **Service Unavailable**:
   - Check if the pod is running: `kubectl get pods`
   - Verify the service is created: `kubectl get services`
   - Check if the NodePort is accessible

3. **Database Connection Issues**:
   - Verify database credentials in secrets
   - Check if the database is accessible from the Kubernetes cluster

To check the logs of a service:

```bash
# Get the pod name
kubectl get pods | grep <service-name>

# View the logs
kubectl logs <pod-name>

# Follow the logs
kubectl logs -f <pod-name>
```
