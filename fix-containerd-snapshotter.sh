#!/bin/bash

# Script to fix containerd snapshotter issues in K3s
# Error: failed to start sandbox: rpc error: code = Unknown desc = failed to stat parent: 
# stat /var/lib/rancher/k3s/agent/containerd/io.containerd.snapshotter.v1.overlayfs/snapshots/1/fs: no such file or directory

set -e

echo "=== K3s Containerd Snapshotter Fix ==="
echo

# Check if running as root
if [ "$(id -u)" -ne 0 ]; then
    echo "This script must be run as root. Please use sudo."
    exit 1
fi

# Detect if this is a server or agent node
if systemctl is-active --quiet k3s; then
    K3S_SERVICE="k3s"
    echo "Detected K3s server node"
elif systemctl is-active --quiet k3s-agent; then
    K3S_SERVICE="k3s-agent"
    echo "Detected K3s agent node"
else
    echo "K3s service not detected. Please ensure K3s is installed."
    exit 1
fi

echo "Stopping $K3S_SERVICE service..."
systemctl stop $K3S_SERVICE

echo "Checking disk space..."
df -h /var

# Backup containerd directories
echo "Creating backup of containerd directories..."
TIMESTAMP=$(date +%Y%m%d%H%M%S)
BACKUP_DIR="/var/lib/rancher/k3s/agent/containerd-backup-$TIMESTAMP"
mkdir -p $BACKUP_DIR

# Only backup if the directory exists
if [ -d "/var/lib/rancher/k3s/agent/containerd" ]; then
    cp -r /var/lib/rancher/k3s/agent/containerd $BACKUP_DIR/
    echo "Backup created at $BACKUP_DIR"
else
    echo "Containerd directory not found, no backup needed"
fi

# Clean up containerd state
echo "Cleaning up containerd state..."
rm -rf /var/lib/rancher/k3s/agent/containerd/io.containerd.snapshotter.v1.overlayfs/snapshots/*

# Ensure proper permissions
echo "Setting proper permissions..."
mkdir -p /var/lib/rancher/k3s/agent/containerd/io.containerd.snapshotter.v1.overlayfs/snapshots
chown -R root:root /var/lib/rancher/k3s/agent/containerd

# Start K3s service
echo "Starting $K3S_SERVICE service..."
systemctl start $K3S_SERVICE

# Wait for service to be fully up
echo "Waiting for $K3S_SERVICE to be ready..."
sleep 10

# Check service status
echo "Checking $K3S_SERVICE status..."
systemctl status $K3S_SERVICE --no-pager

echo
echo "=== Fix Complete ==="
echo
echo "The containerd snapshotter directories have been cleaned up and the $K3S_SERVICE service has been restarted."
echo "If you continue to experience issues, please check the logs with:"
echo "  journalctl -u $K3S_SERVICE -f"
echo
echo "You may need to wait a few minutes for all pods to restart properly."
echo