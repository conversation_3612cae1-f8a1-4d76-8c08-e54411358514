# K3s Containerd Snapshotter Troubleshooting Guide

## Issue Description

The following error occurs when attempting to create pods in a K3s cluster:

```
Failed to create pod sandbox: rpc error: code = Unknown desc = failed to start sandbox "b08b502ac4d120e2ff73df32e88b034869e518c81b885048ac0e3cc777a302b9": failed to create containerd container: failed to stat parent: stat /var/lib/rancher/k3s/agent/containerd/io.containerd.snapshotter.v1.overlayfs/snapshots/1/fs: no such file or directory
```

This error indicates that the containerd snapshotter is unable to find a required directory in the overlay filesystem. This can happen due to:

1. Corrupted containerd state
2. Disk space issues
3. Improper shutdown of K3s
4. Filesystem permission problems
5. Underlying storage issues

## Solution

A script has been created to fix this issue by cleaning up the containerd snapshotter state and restarting the K3s service.

### Using the Fix Script

1. Copy the `fix-containerd-snapshotter.sh` script to the affected K3s node
2. Make the script executable:
   ```bash
   chmod +x fix-containerd-snapshotter.sh
   ```
3. Run the script with sudo:
   ```bash
   sudo ./fix-containerd-snapshotter.sh
   ```

The script will:
- Detect whether it's running on a K3s server or agent node
- Stop the K3s service
- Create a backup of the existing containerd directories
- Clean up the containerd snapshotter state
- Ensure proper permissions
- Restart the K3s service

### Manual Fix

If you prefer to fix the issue manually, follow these steps:

1. Stop the K3s service:
   ```bash
   # For server node
   sudo systemctl stop k3s
   
   # For agent node
   sudo systemctl stop k3s-agent
   ```

2. Backup the containerd directories:
   ```bash
   TIMESTAMP=$(date +%Y%m%d%H%M%S)
   sudo mkdir -p /var/lib/rancher/k3s/agent/containerd-backup-$TIMESTAMP
   sudo cp -r /var/lib/rancher/k3s/agent/containerd /var/lib/rancher/k3s/agent/containerd-backup-$TIMESTAMP/
   ```

3. Clean up the containerd snapshotter state:
   ```bash
   sudo rm -rf /var/lib/rancher/k3s/agent/containerd/io.containerd.snapshotter.v1.overlayfs/snapshots/*
   ```

4. Ensure proper permissions:
   ```bash
   sudo mkdir -p /var/lib/rancher/k3s/agent/containerd/io.containerd.snapshotter.v1.overlayfs/snapshots
   sudo chown -R root:root /var/lib/rancher/k3s/agent/containerd
   ```

5. Start the K3s service:
   ```bash
   # For server node
   sudo systemctl start k3s
   
   # For agent node
   sudo systemctl start k3s-agent
   ```

## Prevention

To prevent this issue from occurring in the future:

1. Ensure proper shutdown of K3s before system reboots:
   ```bash
   sudo systemctl stop k3s   # or k3s-agent
   ```

2. Monitor disk space regularly:
   ```bash
   df -h /var
   ```

3. Consider setting up periodic maintenance to clean up unused containerd resources:
   ```bash
   # Add to crontab to run weekly
   0 0 * * 0 /usr/local/bin/k3s crictl rmi --prune
   ```

4. Ensure the filesystem has proper permissions:
   ```bash
   sudo chown -R root:root /var/lib/rancher/k3s
   ```

## Troubleshooting

If the issue persists after running the fix script:

1. Check K3s logs:
   ```bash
   sudo journalctl -u k3s -f   # or k3s-agent
   ```

2. Check containerd logs:
   ```bash
   sudo journalctl -t containerd
   ```

3. Verify disk space and inode usage:
   ```bash
   df -h /var
   df -i /var
   ```

4. Check for filesystem errors:
   ```bash
   sudo fsck -f /dev/sdX   # Replace sdX with the appropriate device
   ```

5. As a last resort, consider reinstalling K3s:
   ```bash
   # Uninstall K3s
   /usr/local/bin/k3s-uninstall.sh   # or k3s-agent-uninstall.sh
   
   # Reinstall K3s
   curl -sfL https://get.k3s.io | sh -
   ```

## Additional Resources

- [K3s Documentation](https://docs.k3s.io/)
- [Containerd Documentation](https://containerd.io/docs/)
- [Kubernetes Troubleshooting Guide](https://kubernetes.io/docs/tasks/debug-application-cluster/troubleshooting/)